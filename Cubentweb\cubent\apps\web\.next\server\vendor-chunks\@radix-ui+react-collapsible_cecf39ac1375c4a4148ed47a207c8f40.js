"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40";
exports.ids = ["vendor-chunks/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40/node_modules/@radix-ui/react-collapsible/dist/index.mjs":
/*!*****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40/node_modules/@radix-ui/react-collapsible/dist/index.mjs ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collapsible: () => (/* binding */ Collapsible),\n/* harmony export */   CollapsibleContent: () => (/* binding */ CollapsibleContent),\n/* harmony export */   CollapsibleTrigger: () => (/* binding */ CollapsibleTrigger),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createCollapsibleScope: () => (/* binding */ createCollapsibleScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-context@1.1_efc475efe2315f1e47666d242c3ea3f4/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-control_038f968d6df614ae636f20523f1cb043/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-layout-_ac935108a899bda0f74539c291abe4dd/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-compose-ref_3a8f72d8524cae11dbbe71796c2b6a49/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-primitive@2_0f3a82528133a7d37b0572d0c112c6a5/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-presence@1._5ce11ab2a6bdc144f66b9a99b0ba8012/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@19.1.5_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Collapsible,CollapsibleContent,CollapsibleTrigger,Content,Root,Trigger,createCollapsibleScope auto */ // src/collapsible.tsx\n\n\n\n\n\n\n\n\n\n\nvar COLLAPSIBLE_NAME = \"Collapsible\";\nvar [createCollapsibleContext, createCollapsibleScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(COLLAPSIBLE_NAME);\nvar [CollapsibleProvider, useCollapsibleContext] = createCollapsibleContext(COLLAPSIBLE_NAME);\nvar Collapsible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCollapsible, open: openProp, defaultOpen, disabled, onOpenChange, ...collapsibleProps } = props;\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: COLLAPSIBLE_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollapsibleProvider, {\n        scope: __scopeCollapsible,\n        disabled,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"Collapsible.useCallback\": ()=>setOpen({\n                    \"Collapsible.useCallback\": (prevOpen)=>!prevOpen\n                }[\"Collapsible.useCallback\"])\n        }[\"Collapsible.useCallback\"], [\n            setOpen\n        ]),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            \"data-state\": getState(open),\n            \"data-disabled\": disabled ? \"\" : void 0,\n            ...collapsibleProps,\n            ref: forwardedRef\n        })\n    });\n});\nCollapsible.displayName = COLLAPSIBLE_NAME;\nvar TRIGGER_NAME = \"CollapsibleTrigger\";\nvar CollapsibleTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCollapsible, ...triggerProps } = props;\n    const context = useCollapsibleContext(TRIGGER_NAME, __scopeCollapsible);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n        type: \"button\",\n        \"aria-controls\": context.contentId,\n        \"aria-expanded\": context.open || false,\n        \"data-state\": getState(context.open),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        disabled: context.disabled,\n        ...triggerProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n});\nCollapsibleTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"CollapsibleContent\";\nvar CollapsibleContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, props.__scopeCollapsible);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__.Presence, {\n        present: forceMount || context.open,\n        children: ({ present })=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollapsibleContentImpl, {\n                ...contentProps,\n                ref: forwardedRef,\n                present\n            })\n    });\n});\nCollapsibleContent.displayName = CONTENT_NAME;\nvar CollapsibleContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCollapsible, present, children, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, __scopeCollapsible);\n    const [isPresent, setIsPresent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(present);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_8__.useComposedRefs)(forwardedRef, ref);\n    const heightRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const height = heightRef.current;\n    const widthRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const width = widthRef.current;\n    const isOpen = context.open || isPresent;\n    const isMountAnimationPreventedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isOpen);\n    const originalStylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"CollapsibleContentImpl.useEffect\": ()=>{\n            const rAF = requestAnimationFrame({\n                \"CollapsibleContentImpl.useEffect.rAF\": ()=>isMountAnimationPreventedRef.current = false\n            }[\"CollapsibleContentImpl.useEffect.rAF\"]);\n            return ({\n                \"CollapsibleContentImpl.useEffect\": ()=>cancelAnimationFrame(rAF)\n            })[\"CollapsibleContentImpl.useEffect\"];\n        }\n    }[\"CollapsibleContentImpl.useEffect\"], []);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"CollapsibleContentImpl.useLayoutEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                originalStylesRef.current = originalStylesRef.current || {\n                    transitionDuration: node.style.transitionDuration,\n                    animationName: node.style.animationName\n                };\n                node.style.transitionDuration = \"0s\";\n                node.style.animationName = \"none\";\n                const rect = node.getBoundingClientRect();\n                heightRef.current = rect.height;\n                widthRef.current = rect.width;\n                if (!isMountAnimationPreventedRef.current) {\n                    node.style.transitionDuration = originalStylesRef.current.transitionDuration;\n                    node.style.animationName = originalStylesRef.current.animationName;\n                }\n                setIsPresent(present);\n            }\n        }\n    }[\"CollapsibleContentImpl.useLayoutEffect\"], [\n        context.open,\n        present\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": getState(context.open),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        id: context.contentId,\n        hidden: !isOpen,\n        ...contentProps,\n        ref: composedRefs,\n        style: {\n            [`--radix-collapsible-content-height`]: height ? `${height}px` : void 0,\n            [`--radix-collapsible-content-width`]: width ? `${width}px` : void 0,\n            ...props.style\n        },\n        children: isOpen && children\n    });\n});\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar Root = Collapsible;\nvar Trigger = CollapsibleTrigger;\nvar Content = CollapsibleContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40/node_modules/@radix-ui/react-collapsible/dist/index.mjs\n");

/***/ })

};
;