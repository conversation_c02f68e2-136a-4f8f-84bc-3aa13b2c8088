"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConnectNames = exports.ConnectTypes = exports.AttributeNames = void 0;\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"CONNECT_TYPE\"] = \"connect.type\";\n    AttributeNames[\"CONNECT_NAME\"] = \"connect.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\nvar ConnectTypes;\n(function (ConnectTypes) {\n    ConnectTypes[\"MIDDLEWARE\"] = \"middleware\";\n    ConnectTypes[\"REQUEST_HANDLER\"] = \"request_handler\";\n})(ConnectTypes = exports.ConnectTypes || (exports.ConnectTypes = {}));\nvar ConnectNames;\n(function (ConnectNames) {\n    ConnectNames[\"MIDDLEWARE\"] = \"middleware\";\n    ConnectNames[\"REQUEST_HANDLER\"] = \"request handler\";\n})(ConnectNames = exports.ConnectNames || (exports.ConnectNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\"), exports);\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYjgyZTFmZTM5YjU3YzJjZTFiYjk2MzYzMTIxNjZlMzYvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1jb25uZWN0L2J1aWxkL3NyYy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsb0NBQW9DLGdCQUFnQjtBQUN2RixDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYSxtQkFBTyxDQUFDLHdOQUF3QjtBQUM3QyxhQUFhLG1CQUFPLENBQUMsOE1BQW1CO0FBQ3hDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2I4MmUxZmUzOWI1N2MyY2UxYmI5NjM2MzEyMTY2ZTM2XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tY29ubmVjdFxcYnVpbGRcXHNyY1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xudmFyIF9fY3JlYXRlQmluZGluZyA9ICh0aGlzICYmIHRoaXMuX19jcmVhdGVCaW5kaW5nKSB8fCAoT2JqZWN0LmNyZWF0ZSA/IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIGsyLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24oKSB7IHJldHVybiBtW2tdOyB9IH0pO1xufSkgOiAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIG9bazJdID0gbVtrXTtcbn0pKTtcbnZhciBfX2V4cG9ydFN0YXIgPSAodGhpcyAmJiB0aGlzLl9fZXhwb3J0U3RhcikgfHwgZnVuY3Rpb24obSwgZXhwb3J0cykge1xuICAgIGZvciAodmFyIHAgaW4gbSkgaWYgKHAgIT09IFwiZGVmYXVsdFwiICYmICFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZXhwb3J0cywgcCkpIF9fY3JlYXRlQmluZGluZyhleHBvcnRzLCBtLCBwKTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vZW51bXMvQXR0cmlidXRlTmFtZXNcIiksIGV4cG9ydHMpO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL2luc3RydW1lbnRhdGlvblwiKSwgZXhwb3J0cyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConnectInstrumentation = exports.ANONYMOUS_NAME = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js\");\nexports.ANONYMOUS_NAME = 'anonymous';\n/** Connect instrumentation for OpenTelemetry */\nclass ConnectInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('connect', ['>=3.0.0 <4'], moduleExports => {\n                return this._patchConstructor(moduleExports);\n            }),\n        ];\n    }\n    _patchApp(patchedApp) {\n        if (!(0, instrumentation_1.isWrapped)(patchedApp.use)) {\n            this._wrap(patchedApp, 'use', this._patchUse.bind(this));\n        }\n        if (!(0, instrumentation_1.isWrapped)(patchedApp.handle)) {\n            this._wrap(patchedApp, 'handle', this._patchHandle.bind(this));\n        }\n    }\n    _patchConstructor(original) {\n        const instrumentation = this;\n        return function (...args) {\n            const app = original.apply(this, args);\n            instrumentation._patchApp(app);\n            return app;\n        };\n    }\n    _patchNext(next, finishSpan) {\n        return function nextFunction(err) {\n            const result = next.apply(this, [err]);\n            finishSpan();\n            return result;\n        };\n    }\n    _startSpan(routeName, middleWare) {\n        let connectType;\n        let connectName;\n        let connectTypeName;\n        if (routeName) {\n            connectType = AttributeNames_1.ConnectTypes.REQUEST_HANDLER;\n            connectTypeName = AttributeNames_1.ConnectNames.REQUEST_HANDLER;\n            connectName = routeName;\n        }\n        else {\n            connectType = AttributeNames_1.ConnectTypes.MIDDLEWARE;\n            connectTypeName = AttributeNames_1.ConnectNames.MIDDLEWARE;\n            connectName = middleWare.name || exports.ANONYMOUS_NAME;\n        }\n        const spanName = `${connectTypeName} - ${connectName}`;\n        const options = {\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: routeName.length > 0 ? routeName : '/',\n                [AttributeNames_1.AttributeNames.CONNECT_TYPE]: connectType,\n                [AttributeNames_1.AttributeNames.CONNECT_NAME]: connectName,\n            },\n        };\n        return this.tracer.startSpan(spanName, options);\n    }\n    _patchMiddleware(routeName, middleWare) {\n        const instrumentation = this;\n        const isErrorMiddleware = middleWare.length === 4;\n        function patchedMiddleware() {\n            if (!instrumentation.isEnabled()) {\n                return middleWare.apply(this, arguments);\n            }\n            const [reqArgIdx, resArgIdx, nextArgIdx] = isErrorMiddleware\n                ? [1, 2, 3]\n                : [0, 1, 2];\n            const req = arguments[reqArgIdx];\n            const res = arguments[resArgIdx];\n            const next = arguments[nextArgIdx];\n            (0, utils_1.replaceCurrentStackRoute)(req, routeName);\n            const rpcMetadata = (0, core_1.getRPCMetadata)(api_1.context.active());\n            if (routeName && (rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP) {\n                rpcMetadata.route = (0, utils_1.generateRoute)(req);\n            }\n            let spanName = '';\n            if (routeName) {\n                spanName = `request handler - ${routeName}`;\n            }\n            else {\n                spanName = `middleware - ${middleWare.name || exports.ANONYMOUS_NAME}`;\n            }\n            const span = instrumentation._startSpan(routeName, middleWare);\n            instrumentation._diag.debug('start span', spanName);\n            let spanFinished = false;\n            function finishSpan() {\n                if (!spanFinished) {\n                    spanFinished = true;\n                    instrumentation._diag.debug(`finishing span ${span.name}`);\n                    span.end();\n                }\n                else {\n                    instrumentation._diag.debug(`span ${span.name} - already finished`);\n                }\n                res.removeListener('close', finishSpan);\n            }\n            res.addListener('close', finishSpan);\n            arguments[nextArgIdx] = instrumentation._patchNext(next, finishSpan);\n            return middleWare.apply(this, arguments);\n        }\n        Object.defineProperty(patchedMiddleware, 'length', {\n            value: middleWare.length,\n            writable: false,\n            configurable: true,\n        });\n        return patchedMiddleware;\n    }\n    _patchUse(original) {\n        const instrumentation = this;\n        return function (...args) {\n            const middleWare = args[args.length - 1];\n            const routeName = (args[args.length - 2] || '');\n            args[args.length - 1] = instrumentation._patchMiddleware(routeName, middleWare);\n            return original.apply(this, args);\n        };\n    }\n    _patchHandle(original) {\n        const instrumentation = this;\n        return function () {\n            const [reqIdx, outIdx] = [0, 2];\n            const req = arguments[reqIdx];\n            const out = arguments[outIdx];\n            const completeStack = (0, utils_1.addNewStackLayer)(req);\n            if (typeof out === 'function') {\n                arguments[outIdx] = instrumentation._patchOut(out, completeStack);\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    _patchOut(out, completeStack) {\n        return function nextFunction(...args) {\n            completeStack();\n            return Reflect.apply(out, this, args);\n        };\n    }\n}\nexports.ConnectInstrumentation = ConnectInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports._LAYERS_STORE_PROPERTY = void 0;\nexports._LAYERS_STORE_PROPERTY = Symbol('opentelemetry.instrumentation-connect.request-route-stack');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.generateRoute = exports.replaceCurrentStackRoute = exports.addNewStackLayer = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js\");\nconst addNewStackLayer = (request) => {\n    if (Array.isArray(request[internal_types_1._LAYERS_STORE_PROPERTY]) === false) {\n        Object.defineProperty(request, internal_types_1._LAYERS_STORE_PROPERTY, {\n            enumerable: false,\n            value: [],\n        });\n    }\n    request[internal_types_1._LAYERS_STORE_PROPERTY].push('/');\n    const stackLength = request[internal_types_1._LAYERS_STORE_PROPERTY].length;\n    return () => {\n        if (stackLength === request[internal_types_1._LAYERS_STORE_PROPERTY].length) {\n            request[internal_types_1._LAYERS_STORE_PROPERTY].pop();\n        }\n        else {\n            api_1.diag.warn('Connect: Trying to pop the stack multiple time');\n        }\n    };\n};\nexports.addNewStackLayer = addNewStackLayer;\nconst replaceCurrentStackRoute = (request, newRoute) => {\n    if (newRoute) {\n        request[internal_types_1._LAYERS_STORE_PROPERTY].splice(-1, 1, newRoute);\n    }\n};\nexports.replaceCurrentStackRoute = replaceCurrentStackRoute;\n// generate route from existing stack on request object.\n// splash between stack layer will be deduped\n// [\"/first/\", \"/second\", \"/third/\"] => /first/second/third/\nconst generateRoute = (request) => {\n    return request[internal_types_1._LAYERS_STORE_PROPERTY].reduce((acc, sub) => acc.replace(/\\/+$/, '') + sub);\n};\nexports.generateRoute = generateRoute;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.43.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-connect';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYjgyZTFmZTM5YjU3YzJjZTFiYjk2MzYzMTIxNjZlMzYvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1jb25uZWN0L2J1aWxkL3NyYy92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG9CQUFvQixHQUFHLHVCQUF1QjtBQUM5QztBQUNBLHVCQUF1QjtBQUN2QixvQkFBb0I7QUFDcEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYjgyZTFmZTM5YjU3YzJjZTFiYjk2MzYzMTIxNjZlMzZcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1jb25uZWN0XFxidWlsZFxcc3JjXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuUEFDS0FHRV9OQU1FID0gZXhwb3J0cy5QQUNLQUdFX1ZFUlNJT04gPSB2b2lkIDA7XG4vLyB0aGlzIGlzIGF1dG9nZW5lcmF0ZWQgZmlsZSwgc2VlIHNjcmlwdHMvdmVyc2lvbi11cGRhdGUuanNcbmV4cG9ydHMuUEFDS0FHRV9WRVJTSU9OID0gJzAuNDMuMSc7XG5leHBvcnRzLlBBQ0tBR0VfTkFNRSA9ICdAb3BlbnRlbGVtZXRyeS9pbnN0cnVtZW50YXRpb24tY29ubmVjdCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConnectNames = exports.ConnectTypes = exports.AttributeNames = void 0;\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"CONNECT_TYPE\"] = \"connect.type\";\n    AttributeNames[\"CONNECT_NAME\"] = \"connect.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\nvar ConnectTypes;\n(function (ConnectTypes) {\n    ConnectTypes[\"MIDDLEWARE\"] = \"middleware\";\n    ConnectTypes[\"REQUEST_HANDLER\"] = \"request_handler\";\n})(ConnectTypes = exports.ConnectTypes || (exports.ConnectTypes = {}));\nvar ConnectNames;\n(function (ConnectNames) {\n    ConnectNames[\"MIDDLEWARE\"] = \"middleware\";\n    ConnectNames[\"REQUEST_HANDLER\"] = \"request handler\";\n})(ConnectNames = exports.ConnectNames || (exports.ConnectNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\"), exports);\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConnectInstrumentation = exports.ANONYMOUS_NAME = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js\");\nexports.ANONYMOUS_NAME = 'anonymous';\n/** Connect instrumentation for OpenTelemetry */\nclass ConnectInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('connect', ['>=3.0.0 <4'], moduleExports => {\n                return this._patchConstructor(moduleExports);\n            }),\n        ];\n    }\n    _patchApp(patchedApp) {\n        if (!(0, instrumentation_1.isWrapped)(patchedApp.use)) {\n            this._wrap(patchedApp, 'use', this._patchUse.bind(this));\n        }\n        if (!(0, instrumentation_1.isWrapped)(patchedApp.handle)) {\n            this._wrap(patchedApp, 'handle', this._patchHandle.bind(this));\n        }\n    }\n    _patchConstructor(original) {\n        const instrumentation = this;\n        return function (...args) {\n            const app = original.apply(this, args);\n            instrumentation._patchApp(app);\n            return app;\n        };\n    }\n    _patchNext(next, finishSpan) {\n        return function nextFunction(err) {\n            const result = next.apply(this, [err]);\n            finishSpan();\n            return result;\n        };\n    }\n    _startSpan(routeName, middleWare) {\n        let connectType;\n        let connectName;\n        let connectTypeName;\n        if (routeName) {\n            connectType = AttributeNames_1.ConnectTypes.REQUEST_HANDLER;\n            connectTypeName = AttributeNames_1.ConnectNames.REQUEST_HANDLER;\n            connectName = routeName;\n        }\n        else {\n            connectType = AttributeNames_1.ConnectTypes.MIDDLEWARE;\n            connectTypeName = AttributeNames_1.ConnectNames.MIDDLEWARE;\n            connectName = middleWare.name || exports.ANONYMOUS_NAME;\n        }\n        const spanName = `${connectTypeName} - ${connectName}`;\n        const options = {\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: routeName.length > 0 ? routeName : '/',\n                [AttributeNames_1.AttributeNames.CONNECT_TYPE]: connectType,\n                [AttributeNames_1.AttributeNames.CONNECT_NAME]: connectName,\n            },\n        };\n        return this.tracer.startSpan(spanName, options);\n    }\n    _patchMiddleware(routeName, middleWare) {\n        const instrumentation = this;\n        const isErrorMiddleware = middleWare.length === 4;\n        function patchedMiddleware() {\n            if (!instrumentation.isEnabled()) {\n                return middleWare.apply(this, arguments);\n            }\n            const [reqArgIdx, resArgIdx, nextArgIdx] = isErrorMiddleware\n                ? [1, 2, 3]\n                : [0, 1, 2];\n            const req = arguments[reqArgIdx];\n            const res = arguments[resArgIdx];\n            const next = arguments[nextArgIdx];\n            (0, utils_1.replaceCurrentStackRoute)(req, routeName);\n            const rpcMetadata = (0, core_1.getRPCMetadata)(api_1.context.active());\n            if (routeName && (rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP) {\n                rpcMetadata.route = (0, utils_1.generateRoute)(req);\n            }\n            let spanName = '';\n            if (routeName) {\n                spanName = `request handler - ${routeName}`;\n            }\n            else {\n                spanName = `middleware - ${middleWare.name || exports.ANONYMOUS_NAME}`;\n            }\n            const span = instrumentation._startSpan(routeName, middleWare);\n            instrumentation._diag.debug('start span', spanName);\n            let spanFinished = false;\n            function finishSpan() {\n                if (!spanFinished) {\n                    spanFinished = true;\n                    instrumentation._diag.debug(`finishing span ${span.name}`);\n                    span.end();\n                }\n                else {\n                    instrumentation._diag.debug(`span ${span.name} - already finished`);\n                }\n                res.removeListener('close', finishSpan);\n            }\n            res.addListener('close', finishSpan);\n            arguments[nextArgIdx] = instrumentation._patchNext(next, finishSpan);\n            return middleWare.apply(this, arguments);\n        }\n        Object.defineProperty(patchedMiddleware, 'length', {\n            value: middleWare.length,\n            writable: false,\n            configurable: true,\n        });\n        return patchedMiddleware;\n    }\n    _patchUse(original) {\n        const instrumentation = this;\n        return function (...args) {\n            const middleWare = args[args.length - 1];\n            const routeName = (args[args.length - 2] || '');\n            args[args.length - 1] = instrumentation._patchMiddleware(routeName, middleWare);\n            return original.apply(this, args);\n        };\n    }\n    _patchHandle(original) {\n        const instrumentation = this;\n        return function () {\n            const [reqIdx, outIdx] = [0, 2];\n            const req = arguments[reqIdx];\n            const out = arguments[outIdx];\n            const completeStack = (0, utils_1.addNewStackLayer)(req);\n            if (typeof out === 'function') {\n                arguments[outIdx] = instrumentation._patchOut(out, completeStack);\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    _patchOut(out, completeStack) {\n        return function nextFunction(...args) {\n            completeStack();\n            return Reflect.apply(out, this, args);\n        };\n    }\n}\nexports.ConnectInstrumentation = ConnectInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports._LAYERS_STORE_PROPERTY = void 0;\nexports._LAYERS_STORE_PROPERTY = Symbol('opentelemetry.instrumentation-connect.request-route-stack');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iODJlMWZlMzliNTdjMmNlMWJiOTYzNjMxMjE2NmUzNi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWNvbm5lY3QvYnVpbGQvc3JjL2ludGVybmFsLXR5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDhCQUE4QjtBQUM5Qiw4QkFBOEI7QUFDOUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYjgyZTFmZTM5YjU3YzJjZTFiYjk2MzYzMTIxNjZlMzZcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1jb25uZWN0XFxidWlsZFxcc3JjXFxpbnRlcm5hbC10eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLl9MQVlFUlNfU1RPUkVfUFJPUEVSVFkgPSB2b2lkIDA7XG5leHBvcnRzLl9MQVlFUlNfU1RPUkVfUFJPUEVSVFkgPSBTeW1ib2woJ29wZW50ZWxlbWV0cnkuaW5zdHJ1bWVudGF0aW9uLWNvbm5lY3QucmVxdWVzdC1yb3V0ZS1zdGFjaycpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW50ZXJuYWwtdHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.generateRoute = exports.replaceCurrentStackRoute = exports.addNewStackLayer = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js\");\nconst addNewStackLayer = (request) => {\n    if (Array.isArray(request[internal_types_1._LAYERS_STORE_PROPERTY]) === false) {\n        Object.defineProperty(request, internal_types_1._LAYERS_STORE_PROPERTY, {\n            enumerable: false,\n            value: [],\n        });\n    }\n    request[internal_types_1._LAYERS_STORE_PROPERTY].push('/');\n    const stackLength = request[internal_types_1._LAYERS_STORE_PROPERTY].length;\n    return () => {\n        if (stackLength === request[internal_types_1._LAYERS_STORE_PROPERTY].length) {\n            request[internal_types_1._LAYERS_STORE_PROPERTY].pop();\n        }\n        else {\n            api_1.diag.warn('Connect: Trying to pop the stack multiple time');\n        }\n    };\n};\nexports.addNewStackLayer = addNewStackLayer;\nconst replaceCurrentStackRoute = (request, newRoute) => {\n    if (newRoute) {\n        request[internal_types_1._LAYERS_STORE_PROPERTY].splice(-1, 1, newRoute);\n    }\n};\nexports.replaceCurrentStackRoute = replaceCurrentStackRoute;\n// generate route from existing stack on request object.\n// splash between stack layer will be deduped\n// [\"/first/\", \"/second\", \"/third/\"] => /first/second/third/\nconst generateRoute = (request) => {\n    return request[internal_types_1._LAYERS_STORE_PROPERTY].reduce((acc, sub) => acc.replace(/\\/+$/, '') + sub);\n};\nexports.generateRoute = generateRoute;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.43.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-connect';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConnectNames = exports.ConnectTypes = exports.AttributeNames = void 0;\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"CONNECT_TYPE\"] = \"connect.type\";\n    AttributeNames[\"CONNECT_NAME\"] = \"connect.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\nvar ConnectTypes;\n(function (ConnectTypes) {\n    ConnectTypes[\"MIDDLEWARE\"] = \"middleware\";\n    ConnectTypes[\"REQUEST_HANDLER\"] = \"request_handler\";\n})(ConnectTypes = exports.ConnectTypes || (exports.ConnectTypes = {}));\nvar ConnectNames;\n(function (ConnectNames) {\n    ConnectNames[\"MIDDLEWARE\"] = \"middleware\";\n    ConnectNames[\"REQUEST_HANDLER\"] = \"request handler\";\n})(ConnectNames = exports.ConnectNames || (exports.ConnectNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\"), exports);\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConnectInstrumentation = exports.ANONYMOUS_NAME = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/enums/AttributeNames.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js\");\nexports.ANONYMOUS_NAME = 'anonymous';\n/** Connect instrumentation for OpenTelemetry */\nclass ConnectInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('connect', ['>=3.0.0 <4'], moduleExports => {\n                return this._patchConstructor(moduleExports);\n            }),\n        ];\n    }\n    _patchApp(patchedApp) {\n        if (!(0, instrumentation_1.isWrapped)(patchedApp.use)) {\n            this._wrap(patchedApp, 'use', this._patchUse.bind(this));\n        }\n        if (!(0, instrumentation_1.isWrapped)(patchedApp.handle)) {\n            this._wrap(patchedApp, 'handle', this._patchHandle.bind(this));\n        }\n    }\n    _patchConstructor(original) {\n        const instrumentation = this;\n        return function (...args) {\n            const app = original.apply(this, args);\n            instrumentation._patchApp(app);\n            return app;\n        };\n    }\n    _patchNext(next, finishSpan) {\n        return function nextFunction(err) {\n            const result = next.apply(this, [err]);\n            finishSpan();\n            return result;\n        };\n    }\n    _startSpan(routeName, middleWare) {\n        let connectType;\n        let connectName;\n        let connectTypeName;\n        if (routeName) {\n            connectType = AttributeNames_1.ConnectTypes.REQUEST_HANDLER;\n            connectTypeName = AttributeNames_1.ConnectNames.REQUEST_HANDLER;\n            connectName = routeName;\n        }\n        else {\n            connectType = AttributeNames_1.ConnectTypes.MIDDLEWARE;\n            connectTypeName = AttributeNames_1.ConnectNames.MIDDLEWARE;\n            connectName = middleWare.name || exports.ANONYMOUS_NAME;\n        }\n        const spanName = `${connectTypeName} - ${connectName}`;\n        const options = {\n            attributes: {\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: routeName.length > 0 ? routeName : '/',\n                [AttributeNames_1.AttributeNames.CONNECT_TYPE]: connectType,\n                [AttributeNames_1.AttributeNames.CONNECT_NAME]: connectName,\n            },\n        };\n        return this.tracer.startSpan(spanName, options);\n    }\n    _patchMiddleware(routeName, middleWare) {\n        const instrumentation = this;\n        const isErrorMiddleware = middleWare.length === 4;\n        function patchedMiddleware() {\n            if (!instrumentation.isEnabled()) {\n                return middleWare.apply(this, arguments);\n            }\n            const [reqArgIdx, resArgIdx, nextArgIdx] = isErrorMiddleware\n                ? [1, 2, 3]\n                : [0, 1, 2];\n            const req = arguments[reqArgIdx];\n            const res = arguments[resArgIdx];\n            const next = arguments[nextArgIdx];\n            (0, utils_1.replaceCurrentStackRoute)(req, routeName);\n            const rpcMetadata = (0, core_1.getRPCMetadata)(api_1.context.active());\n            if (routeName && (rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP) {\n                rpcMetadata.route = (0, utils_1.generateRoute)(req);\n            }\n            let spanName = '';\n            if (routeName) {\n                spanName = `request handler - ${routeName}`;\n            }\n            else {\n                spanName = `middleware - ${middleWare.name || exports.ANONYMOUS_NAME}`;\n            }\n            const span = instrumentation._startSpan(routeName, middleWare);\n            instrumentation._diag.debug('start span', spanName);\n            let spanFinished = false;\n            function finishSpan() {\n                if (!spanFinished) {\n                    spanFinished = true;\n                    instrumentation._diag.debug(`finishing span ${span.name}`);\n                    span.end();\n                }\n                else {\n                    instrumentation._diag.debug(`span ${span.name} - already finished`);\n                }\n                res.removeListener('close', finishSpan);\n            }\n            res.addListener('close', finishSpan);\n            arguments[nextArgIdx] = instrumentation._patchNext(next, finishSpan);\n            return middleWare.apply(this, arguments);\n        }\n        Object.defineProperty(patchedMiddleware, 'length', {\n            value: middleWare.length,\n            writable: false,\n            configurable: true,\n        });\n        return patchedMiddleware;\n    }\n    _patchUse(original) {\n        const instrumentation = this;\n        return function (...args) {\n            const middleWare = args[args.length - 1];\n            const routeName = (args[args.length - 2] || '');\n            args[args.length - 1] = instrumentation._patchMiddleware(routeName, middleWare);\n            return original.apply(this, args);\n        };\n    }\n    _patchHandle(original) {\n        const instrumentation = this;\n        return function () {\n            const [reqIdx, outIdx] = [0, 2];\n            const req = arguments[reqIdx];\n            const out = arguments[outIdx];\n            const completeStack = (0, utils_1.addNewStackLayer)(req);\n            if (typeof out === 'function') {\n                arguments[outIdx] = instrumentation._patchOut(out, completeStack);\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    _patchOut(out, completeStack) {\n        return function nextFunction(...args) {\n            completeStack();\n            return Reflect.apply(out, this, args);\n        };\n    }\n}\nexports.ConnectInstrumentation = ConnectInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports._LAYERS_STORE_PROPERTY = void 0;\nexports._LAYERS_STORE_PROPERTY = Symbol('opentelemetry.instrumentation-connect.request-route-stack');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.generateRoute = exports.replaceCurrentStackRoute = exports.addNewStackLayer = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/internal-types.js\");\nconst addNewStackLayer = (request) => {\n    if (Array.isArray(request[internal_types_1._LAYERS_STORE_PROPERTY]) === false) {\n        Object.defineProperty(request, internal_types_1._LAYERS_STORE_PROPERTY, {\n            enumerable: false,\n            value: [],\n        });\n    }\n    request[internal_types_1._LAYERS_STORE_PROPERTY].push('/');\n    const stackLength = request[internal_types_1._LAYERS_STORE_PROPERTY].length;\n    return () => {\n        if (stackLength === request[internal_types_1._LAYERS_STORE_PROPERTY].length) {\n            request[internal_types_1._LAYERS_STORE_PROPERTY].pop();\n        }\n        else {\n            api_1.diag.warn('Connect: Trying to pop the stack multiple time');\n        }\n    };\n};\nexports.addNewStackLayer = addNewStackLayer;\nconst replaceCurrentStackRoute = (request, newRoute) => {\n    if (newRoute) {\n        request[internal_types_1._LAYERS_STORE_PROPERTY].splice(-1, 1, newRoute);\n    }\n};\nexports.replaceCurrentStackRoute = replaceCurrentStackRoute;\n// generate route from existing stack on request object.\n// splash between stack layer will be deduped\n// [\"/first/\", \"/second\", \"/third/\"] => /first/second/third/\nconst generateRoute = (request) => {\n    return request[internal_types_1._LAYERS_STORE_PROPERTY].reduce((acc, sub) => acc.replace(/\\/+$/, '') + sub);\n};\nexports.generateRoute = generateRoute;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.43.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-connect';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b82e1fe39b57c2ce1bb9636312166e36/node_modules/@opentelemetry/instrumentation-connect/build/src/version.js\n");

/***/ })

};
;