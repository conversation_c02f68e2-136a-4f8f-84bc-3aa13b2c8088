"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"EXPRESS_TYPE\"] = \"express.type\";\n    AttributeNames[\"EXPRESS_NAME\"] = \"express.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ExpressLayerType = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar ExpressLayerType;\n(function (ExpressLayerType) {\n    ExpressLayerType[\"ROUTER\"] = \"router\";\n    ExpressLayerType[\"MIDDLEWARE\"] = \"middleware\";\n    ExpressLayerType[\"REQUEST_HANDLER\"] = \"request_handler\";\n})(ExpressLayerType = exports.ExpressLayerType || (exports.ExpressLayerType = {}));\n//# sourceMappingURL=ExpressLayerType.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/ExpressLayerType */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMGRiNDVkMDRhMzI2NDAwZDFhMGVmZjFjYTQ1ZjcwOTUvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1leHByZXNzL2J1aWxkL3NyYy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsb0NBQW9DLGdCQUFnQjtBQUN2RixDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYSxtQkFBTyxDQUFDLDhNQUFtQjtBQUN4QyxhQUFhLG1CQUFPLENBQUMsNE5BQTBCO0FBQy9DLGFBQWEsbUJBQU8sQ0FBQyx3TkFBd0I7QUFDN0MsYUFBYSxtQkFBTyxDQUFDLDBMQUFTO0FBQzlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzBkYjQ1ZDA0YTMyNjQwMGQxYTBlZmYxY2E0NWY3MDk1XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tZXhwcmVzc1xcYnVpbGRcXHNyY1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xudmFyIF9fY3JlYXRlQmluZGluZyA9ICh0aGlzICYmIHRoaXMuX19jcmVhdGVCaW5kaW5nKSB8fCAoT2JqZWN0LmNyZWF0ZSA/IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIGsyLCB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24oKSB7IHJldHVybiBtW2tdOyB9IH0pO1xufSkgOiAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIG9bazJdID0gbVtrXTtcbn0pKTtcbnZhciBfX2V4cG9ydFN0YXIgPSAodGhpcyAmJiB0aGlzLl9fZXhwb3J0U3RhcikgfHwgZnVuY3Rpb24obSwgZXhwb3J0cykge1xuICAgIGZvciAodmFyIHAgaW4gbSkgaWYgKHAgIT09IFwiZGVmYXVsdFwiICYmICFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZXhwb3J0cywgcCkpIF9fY3JlYXRlQmluZGluZyhleHBvcnRzLCBtLCBwKTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vaW5zdHJ1bWVudGF0aW9uXCIpLCBleHBvcnRzKTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9lbnVtcy9FeHByZXNzTGF5ZXJUeXBlXCIpLCBleHBvcnRzKTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9lbnVtcy9BdHRyaWJ1dGVOYW1lc1wiKSwgZXhwb3J0cyk7XG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vdHlwZXNcIiksIGV4cG9ydHMpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ExpressInstrumentation = void 0;\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst ExpressLayerType_1 = __webpack_require__(/*! ./enums/ExpressLayerType */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/version.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js\");\n/** Express instrumentation for OpenTelemetry */\nclass ExpressInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('express', ['>=4.0.0 <5'], moduleExports => {\n                const routerProto = moduleExports.Router;\n                // patch express.Router.route\n                if ((0, instrumentation_1.isWrapped)(routerProto.route)) {\n                    this._unwrap(routerProto, 'route');\n                }\n                this._wrap(routerProto, 'route', this._getRoutePatch());\n                // patch express.Router.use\n                if ((0, instrumentation_1.isWrapped)(routerProto.use)) {\n                    this._unwrap(routerProto, 'use');\n                }\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                this._wrap(routerProto, 'use', this._getRouterUsePatch());\n                // patch express.Application.use\n                if ((0, instrumentation_1.isWrapped)(moduleExports.application.use)) {\n                    this._unwrap(moduleExports.application, 'use');\n                }\n                this._wrap(moduleExports.application, 'use', \n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                this._getAppUsePatch());\n                return moduleExports;\n            }, moduleExports => {\n                if (moduleExports === undefined)\n                    return;\n                const routerProto = moduleExports.Router;\n                this._unwrap(routerProto, 'route');\n                this._unwrap(routerProto, 'use');\n                this._unwrap(moduleExports.application, 'use');\n            }),\n        ];\n    }\n    /**\n     * Get the patch for Router.route function\n     */\n    _getRoutePatch() {\n        const instrumentation = this;\n        return function (original) {\n            return function route_trace(...args) {\n                const route = original.apply(this, args);\n                const layer = this.stack[this.stack.length - 1];\n                instrumentation._applyPatch(layer, (0, utils_1.getLayerPath)(args));\n                return route;\n            };\n        };\n    }\n    /**\n     * Get the patch for Router.use function\n     */\n    _getRouterUsePatch() {\n        const instrumentation = this;\n        return function (original) {\n            return function use(...args) {\n                const route = original.apply(this, args);\n                const layer = this.stack[this.stack.length - 1];\n                instrumentation._applyPatch(layer, (0, utils_1.getLayerPath)(args));\n                return route;\n            };\n        };\n    }\n    /**\n     * Get the patch for Application.use function\n     */\n    _getAppUsePatch() {\n        const instrumentation = this;\n        return function (original) {\n            return function use(...args) {\n                const route = original.apply(this, args);\n                const layer = this._router.stack[this._router.stack.length - 1];\n                instrumentation._applyPatch(layer, (0, utils_1.getLayerPath)(args));\n                return route;\n            };\n        };\n    }\n    /** Patch each express layer to create span and propagate context */\n    _applyPatch(layer, layerPath) {\n        const instrumentation = this;\n        // avoid patching multiple times the same layer\n        if (layer[internal_types_1.kLayerPatched] === true)\n            return;\n        layer[internal_types_1.kLayerPatched] = true;\n        this._wrap(layer, 'handle', original => {\n            // TODO: instrument error handlers\n            if (original.length === 4)\n                return original;\n            const patched = function (req, res) {\n                (0, utils_1.storeLayerPath)(req, layerPath);\n                const route = req[internal_types_1._LAYERS_STORE_PROPERTY]\n                    .filter(path => path !== '/' && path !== '/*')\n                    .join('')\n                    // remove duplicate slashes to normalize route\n                    .replace(/\\/{2,}/g, '/');\n                const attributes = {\n                    [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: route.length > 0 ? route : '/',\n                };\n                const metadata = (0, utils_1.getLayerMetadata)(route, layer, layerPath);\n                const type = metadata.attributes[AttributeNames_1.AttributeNames.EXPRESS_TYPE];\n                const rpcMetadata = (0, core_1.getRPCMetadata)(api_1.context.active());\n                if ((rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP) {\n                    rpcMetadata.route = route || '/';\n                }\n                // verify against the config if the layer should be ignored\n                if ((0, utils_1.isLayerIgnored)(metadata.name, type, instrumentation.getConfig())) {\n                    if (type === ExpressLayerType_1.ExpressLayerType.MIDDLEWARE) {\n                        req[internal_types_1._LAYERS_STORE_PROPERTY].pop();\n                    }\n                    return original.apply(this, arguments);\n                }\n                if (api_1.trace.getSpan(api_1.context.active()) === undefined) {\n                    return original.apply(this, arguments);\n                }\n                const spanName = instrumentation._getSpanName({\n                    request: req,\n                    layerType: type,\n                    route,\n                }, metadata.name);\n                const span = instrumentation.tracer.startSpan(spanName, {\n                    attributes: Object.assign(attributes, metadata.attributes),\n                });\n                const { requestHook } = instrumentation.getConfig();\n                if (requestHook) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => requestHook(span, {\n                        request: req,\n                        layerType: type,\n                        route,\n                    }), e => {\n                        if (e) {\n                            api_1.diag.error('express instrumentation: request hook failed', e);\n                        }\n                    }, true);\n                }\n                let spanHasEnded = false;\n                if (metadata.attributes[AttributeNames_1.AttributeNames.EXPRESS_TYPE] !==\n                    ExpressLayerType_1.ExpressLayerType.MIDDLEWARE) {\n                    span.end();\n                    spanHasEnded = true;\n                }\n                // listener for response.on('finish')\n                const onResponseFinish = () => {\n                    if (spanHasEnded === false) {\n                        spanHasEnded = true;\n                        span.end();\n                    }\n                };\n                // verify we have a callback\n                const args = Array.from(arguments);\n                const callbackIdx = args.findIndex(arg => typeof arg === 'function');\n                if (callbackIdx >= 0) {\n                    arguments[callbackIdx] = function () {\n                        var _a;\n                        // express considers anything but an empty value, \"route\" or \"router\"\n                        // passed to its callback to be an error\n                        const maybeError = arguments[0];\n                        const isError = ![undefined, null, 'route', 'router'].includes(maybeError);\n                        if (!spanHasEnded && isError) {\n                            const [error, message] = (0, utils_1.asErrorAndMessage)(maybeError);\n                            span.recordException(error);\n                            span.setStatus({\n                                code: api_1.SpanStatusCode.ERROR,\n                                message,\n                            });\n                        }\n                        if (spanHasEnded === false) {\n                            spanHasEnded = true;\n                            (_a = req.res) === null || _a === void 0 ? void 0 : _a.removeListener('finish', onResponseFinish);\n                            span.end();\n                        }\n                        if (!(req.route && isError)) {\n                            req[internal_types_1._LAYERS_STORE_PROPERTY].pop();\n                        }\n                        const callback = args[callbackIdx];\n                        return callback.apply(this, arguments);\n                    };\n                }\n                try {\n                    return original.apply(this, arguments);\n                }\n                catch (anyError) {\n                    const [error, message] = (0, utils_1.asErrorAndMessage)(anyError);\n                    span.recordException(error);\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message,\n                    });\n                    throw anyError;\n                }\n                finally {\n                    /**\n                     * At this point if the callback wasn't called, that means either the\n                     * layer is asynchronous (so it will call the callback later on) or that\n                     * the layer directly end the http response, so we'll hook into the \"finish\"\n                     * event to handle the later case.\n                     */\n                    if (!spanHasEnded) {\n                        res.once('finish', onResponseFinish);\n                    }\n                }\n            };\n            // `handle` isn't just a regular function in some cases. It also contains\n            // some properties holding metadata and state so we need to proxy them\n            // through through patched function\n            // ref: https://github.com/open-telemetry/opentelemetry-js-contrib/issues/1950\n            // Also some apps/libs do their own patching before OTEL and have these properties\n            // in the proptotype. So we use a `for...in` loop to get own properties and also\n            // any enumerable prop in the prototype chain\n            // ref: https://github.com/open-telemetry/opentelemetry-js-contrib/issues/2271\n            for (const key in original) {\n                Object.defineProperty(patched, key, {\n                    get() {\n                        return original[key];\n                    },\n                    set(value) {\n                        original[key] = value;\n                    },\n                });\n            }\n            return patched;\n        });\n    }\n    _getSpanName(info, defaultName) {\n        var _a;\n        const { spanNameHook } = this.getConfig();\n        if (!(spanNameHook instanceof Function)) {\n            return defaultName;\n        }\n        try {\n            return (_a = spanNameHook(info, defaultName)) !== null && _a !== void 0 ? _a : defaultName;\n        }\n        catch (err) {\n            api_1.diag.error('express instrumentation: error calling span name rewrite hook', err);\n            return defaultName;\n        }\n    }\n}\nexports.ExpressInstrumentation = ExpressInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports._LAYERS_STORE_PROPERTY = exports.kLayerPatched = void 0;\n/**\n * This symbol is used to mark express layer as being already instrumented\n * since its possible to use a given layer multiple times (ex: middlewares)\n */\nexports.kLayerPatched = Symbol('express-layer-patched');\n/**\n * This const define where on the `request` object the Instrumentation will mount the\n * current stack of express layer.\n *\n * It is necessary because express doesn't store the different layers\n * (ie: middleware, router etc) that it called to get to the current layer.\n * Given that, the only way to know the route of a given layer is to\n * store the path of where each previous layer has been mounted.\n *\n * ex: bodyParser > auth middleware > /users router > get /:id\n *  in this case the stack would be: [\"/users\", \"/:id\"]\n *\n * ex2: bodyParser > /api router > /v1 router > /users router > get /:id\n *  stack: [\"/api\", \"/v1\", \"/users\", \":id\"]\n *\n */\nexports._LAYERS_STORE_PROPERTY = '__ot_middlewares';\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMGRiNDVkMDRhMzI2NDAwZDFhMGVmZjFjYTQ1ZjcwOTUvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1leHByZXNzL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wZGI0NWQwNGEzMjY0MDBkMWEwZWZmMWNhNDVmNzA5NVxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWV4cHJlc3NcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getLayerPath = exports.asErrorAndMessage = exports.isLayerIgnored = exports.getLayerMetadata = exports.getRouterPath = exports.storeLayerPath = void 0;\nconst ExpressLayerType_1 = __webpack_require__(/*! ./enums/ExpressLayerType */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js\");\n/**\n * Store layers path in the request to be able to construct route later\n * @param request The request where\n * @param [value] the value to push into the array\n */\nconst storeLayerPath = (request, value) => {\n    if (Array.isArray(request[internal_types_1._LAYERS_STORE_PROPERTY]) === false) {\n        Object.defineProperty(request, internal_types_1._LAYERS_STORE_PROPERTY, {\n            enumerable: false,\n            value: [],\n        });\n    }\n    if (value === undefined)\n        return;\n    request[internal_types_1._LAYERS_STORE_PROPERTY].push(value);\n};\nexports.storeLayerPath = storeLayerPath;\n/**\n * Recursively search the router path from layer stack\n * @param path The path to reconstruct\n * @param layer The layer to reconstruct from\n * @returns The reconstructed path\n */\nconst getRouterPath = (path, layer) => {\n    var _a, _b, _c, _d;\n    const stackLayer = (_b = (_a = layer.handle) === null || _a === void 0 ? void 0 : _a.stack) === null || _b === void 0 ? void 0 : _b[0];\n    if ((_c = stackLayer === null || stackLayer === void 0 ? void 0 : stackLayer.route) === null || _c === void 0 ? void 0 : _c.path) {\n        return `${path}${stackLayer.route.path}`;\n    }\n    if ((_d = stackLayer === null || stackLayer === void 0 ? void 0 : stackLayer.handle) === null || _d === void 0 ? void 0 : _d.stack) {\n        return (0, exports.getRouterPath)(path, stackLayer);\n    }\n    return path;\n};\nexports.getRouterPath = getRouterPath;\n/**\n * Parse express layer context to retrieve a name and attributes.\n * @param route The route of the layer\n * @param layer Express layer\n * @param [layerPath] if present, the path on which the layer has been mounted\n */\nconst getLayerMetadata = (route, layer, layerPath) => {\n    var _a;\n    if (layer.name === 'router') {\n        const maybeRouterPath = (0, exports.getRouterPath)('', layer);\n        const extractedRouterPath = maybeRouterPath\n            ? maybeRouterPath\n            : layerPath || route || '/';\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.EXPRESS_NAME]: extractedRouterPath,\n                [AttributeNames_1.AttributeNames.EXPRESS_TYPE]: ExpressLayerType_1.ExpressLayerType.ROUTER,\n            },\n            name: `router - ${extractedRouterPath}`,\n        };\n    }\n    else if (layer.name === 'bound dispatch') {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.EXPRESS_NAME]: (_a = (route || layerPath)) !== null && _a !== void 0 ? _a : 'request handler',\n                [AttributeNames_1.AttributeNames.EXPRESS_TYPE]: ExpressLayerType_1.ExpressLayerType.REQUEST_HANDLER,\n            },\n            name: `request handler${layer.path ? ` - ${route || layerPath}` : ''}`,\n        };\n    }\n    else {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.EXPRESS_NAME]: layer.name,\n                [AttributeNames_1.AttributeNames.EXPRESS_TYPE]: ExpressLayerType_1.ExpressLayerType.MIDDLEWARE,\n            },\n            name: `middleware - ${layer.name}`,\n        };\n    }\n};\nexports.getLayerMetadata = getLayerMetadata;\n/**\n * Check whether the given obj match pattern\n * @param constant e.g URL of request\n * @param obj obj to inspect\n * @param pattern Match pattern\n */\nconst satisfiesPattern = (constant, pattern) => {\n    if (typeof pattern === 'string') {\n        return pattern === constant;\n    }\n    else if (pattern instanceof RegExp) {\n        return pattern.test(constant);\n    }\n    else if (typeof pattern === 'function') {\n        return pattern(constant);\n    }\n    else {\n        throw new TypeError('Pattern is in unsupported datatype');\n    }\n};\n/**\n * Check whether the given request is ignored by configuration\n * It will not re-throw exceptions from `list` provided by the client\n * @param constant e.g URL of request\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nconst isLayerIgnored = (name, type, config) => {\n    var _a;\n    if (Array.isArray(config === null || config === void 0 ? void 0 : config.ignoreLayersType) &&\n        ((_a = config === null || config === void 0 ? void 0 : config.ignoreLayersType) === null || _a === void 0 ? void 0 : _a.includes(type))) {\n        return true;\n    }\n    if (Array.isArray(config === null || config === void 0 ? void 0 : config.ignoreLayers) === false)\n        return false;\n    try {\n        for (const pattern of config.ignoreLayers) {\n            if (satisfiesPattern(name, pattern)) {\n                return true;\n            }\n        }\n    }\n    catch (e) {\n        /* catch block*/\n    }\n    return false;\n};\nexports.isLayerIgnored = isLayerIgnored;\n/**\n * Converts a user-provided error value into an error and error message pair\n *\n * @param error - User-provided error value\n * @returns Both an Error or string representation of the value and an error message\n */\nconst asErrorAndMessage = (error) => error instanceof Error\n    ? [error, error.message]\n    : [String(error), String(error)];\nexports.asErrorAndMessage = asErrorAndMessage;\n/**\n * Extracts the layer path from the route arguments\n *\n * @param args - Arguments of the route\n * @returns The layer path\n */\nconst getLayerPath = (args) => {\n    const firstArg = args[0];\n    if (Array.isArray(firstArg)) {\n        return firstArg.map(arg => extractLayerPathSegment(arg) || '').join(',');\n    }\n    return extractLayerPathSegment(firstArg);\n};\nexports.getLayerPath = getLayerPath;\nconst extractLayerPathSegment = (arg) => {\n    if (typeof arg === 'string') {\n        return arg;\n    }\n    if (arg instanceof RegExp || typeof arg === 'number') {\n        return arg.toString();\n    }\n    return;\n};\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-express';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"EXPRESS_TYPE\"] = \"express.type\";\n    AttributeNames[\"EXPRESS_NAME\"] = \"express.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ExpressLayerType = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar ExpressLayerType;\n(function (ExpressLayerType) {\n    ExpressLayerType[\"ROUTER\"] = \"router\";\n    ExpressLayerType[\"MIDDLEWARE\"] = \"middleware\";\n    ExpressLayerType[\"REQUEST_HANDLER\"] = \"request_handler\";\n})(ExpressLayerType = exports.ExpressLayerType || (exports.ExpressLayerType = {}));\n//# sourceMappingURL=ExpressLayerType.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/ExpressLayerType */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ExpressInstrumentation = void 0;\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst ExpressLayerType_1 = __webpack_require__(/*! ./enums/ExpressLayerType */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/version.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js\");\n/** Express instrumentation for OpenTelemetry */\nclass ExpressInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('express', ['>=4.0.0 <5'], moduleExports => {\n                const routerProto = moduleExports.Router;\n                // patch express.Router.route\n                if ((0, instrumentation_1.isWrapped)(routerProto.route)) {\n                    this._unwrap(routerProto, 'route');\n                }\n                this._wrap(routerProto, 'route', this._getRoutePatch());\n                // patch express.Router.use\n                if ((0, instrumentation_1.isWrapped)(routerProto.use)) {\n                    this._unwrap(routerProto, 'use');\n                }\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                this._wrap(routerProto, 'use', this._getRouterUsePatch());\n                // patch express.Application.use\n                if ((0, instrumentation_1.isWrapped)(moduleExports.application.use)) {\n                    this._unwrap(moduleExports.application, 'use');\n                }\n                this._wrap(moduleExports.application, 'use', \n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                this._getAppUsePatch());\n                return moduleExports;\n            }, moduleExports => {\n                if (moduleExports === undefined)\n                    return;\n                const routerProto = moduleExports.Router;\n                this._unwrap(routerProto, 'route');\n                this._unwrap(routerProto, 'use');\n                this._unwrap(moduleExports.application, 'use');\n            }),\n        ];\n    }\n    /**\n     * Get the patch for Router.route function\n     */\n    _getRoutePatch() {\n        const instrumentation = this;\n        return function (original) {\n            return function route_trace(...args) {\n                const route = original.apply(this, args);\n                const layer = this.stack[this.stack.length - 1];\n                instrumentation._applyPatch(layer, (0, utils_1.getLayerPath)(args));\n                return route;\n            };\n        };\n    }\n    /**\n     * Get the patch for Router.use function\n     */\n    _getRouterUsePatch() {\n        const instrumentation = this;\n        return function (original) {\n            return function use(...args) {\n                const route = original.apply(this, args);\n                const layer = this.stack[this.stack.length - 1];\n                instrumentation._applyPatch(layer, (0, utils_1.getLayerPath)(args));\n                return route;\n            };\n        };\n    }\n    /**\n     * Get the patch for Application.use function\n     */\n    _getAppUsePatch() {\n        const instrumentation = this;\n        return function (original) {\n            return function use(...args) {\n                const route = original.apply(this, args);\n                const layer = this._router.stack[this._router.stack.length - 1];\n                instrumentation._applyPatch(layer, (0, utils_1.getLayerPath)(args));\n                return route;\n            };\n        };\n    }\n    /** Patch each express layer to create span and propagate context */\n    _applyPatch(layer, layerPath) {\n        const instrumentation = this;\n        // avoid patching multiple times the same layer\n        if (layer[internal_types_1.kLayerPatched] === true)\n            return;\n        layer[internal_types_1.kLayerPatched] = true;\n        this._wrap(layer, 'handle', original => {\n            // TODO: instrument error handlers\n            if (original.length === 4)\n                return original;\n            const patched = function (req, res) {\n                (0, utils_1.storeLayerPath)(req, layerPath);\n                const route = req[internal_types_1._LAYERS_STORE_PROPERTY]\n                    .filter(path => path !== '/' && path !== '/*')\n                    .join('')\n                    // remove duplicate slashes to normalize route\n                    .replace(/\\/{2,}/g, '/');\n                const attributes = {\n                    [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: route.length > 0 ? route : '/',\n                };\n                const metadata = (0, utils_1.getLayerMetadata)(route, layer, layerPath);\n                const type = metadata.attributes[AttributeNames_1.AttributeNames.EXPRESS_TYPE];\n                const rpcMetadata = (0, core_1.getRPCMetadata)(api_1.context.active());\n                if ((rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP) {\n                    rpcMetadata.route = route || '/';\n                }\n                // verify against the config if the layer should be ignored\n                if ((0, utils_1.isLayerIgnored)(metadata.name, type, instrumentation.getConfig())) {\n                    if (type === ExpressLayerType_1.ExpressLayerType.MIDDLEWARE) {\n                        req[internal_types_1._LAYERS_STORE_PROPERTY].pop();\n                    }\n                    return original.apply(this, arguments);\n                }\n                if (api_1.trace.getSpan(api_1.context.active()) === undefined) {\n                    return original.apply(this, arguments);\n                }\n                const spanName = instrumentation._getSpanName({\n                    request: req,\n                    layerType: type,\n                    route,\n                }, metadata.name);\n                const span = instrumentation.tracer.startSpan(spanName, {\n                    attributes: Object.assign(attributes, metadata.attributes),\n                });\n                const { requestHook } = instrumentation.getConfig();\n                if (requestHook) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => requestHook(span, {\n                        request: req,\n                        layerType: type,\n                        route,\n                    }), e => {\n                        if (e) {\n                            api_1.diag.error('express instrumentation: request hook failed', e);\n                        }\n                    }, true);\n                }\n                let spanHasEnded = false;\n                if (metadata.attributes[AttributeNames_1.AttributeNames.EXPRESS_TYPE] !==\n                    ExpressLayerType_1.ExpressLayerType.MIDDLEWARE) {\n                    span.end();\n                    spanHasEnded = true;\n                }\n                // listener for response.on('finish')\n                const onResponseFinish = () => {\n                    if (spanHasEnded === false) {\n                        spanHasEnded = true;\n                        span.end();\n                    }\n                };\n                // verify we have a callback\n                const args = Array.from(arguments);\n                const callbackIdx = args.findIndex(arg => typeof arg === 'function');\n                if (callbackIdx >= 0) {\n                    arguments[callbackIdx] = function () {\n                        var _a;\n                        // express considers anything but an empty value, \"route\" or \"router\"\n                        // passed to its callback to be an error\n                        const maybeError = arguments[0];\n                        const isError = ![undefined, null, 'route', 'router'].includes(maybeError);\n                        if (!spanHasEnded && isError) {\n                            const [error, message] = (0, utils_1.asErrorAndMessage)(maybeError);\n                            span.recordException(error);\n                            span.setStatus({\n                                code: api_1.SpanStatusCode.ERROR,\n                                message,\n                            });\n                        }\n                        if (spanHasEnded === false) {\n                            spanHasEnded = true;\n                            (_a = req.res) === null || _a === void 0 ? void 0 : _a.removeListener('finish', onResponseFinish);\n                            span.end();\n                        }\n                        if (!(req.route && isError)) {\n                            req[internal_types_1._LAYERS_STORE_PROPERTY].pop();\n                        }\n                        const callback = args[callbackIdx];\n                        return callback.apply(this, arguments);\n                    };\n                }\n                try {\n                    return original.apply(this, arguments);\n                }\n                catch (anyError) {\n                    const [error, message] = (0, utils_1.asErrorAndMessage)(anyError);\n                    span.recordException(error);\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message,\n                    });\n                    throw anyError;\n                }\n                finally {\n                    /**\n                     * At this point if the callback wasn't called, that means either the\n                     * layer is asynchronous (so it will call the callback later on) or that\n                     * the layer directly end the http response, so we'll hook into the \"finish\"\n                     * event to handle the later case.\n                     */\n                    if (!spanHasEnded) {\n                        res.once('finish', onResponseFinish);\n                    }\n                }\n            };\n            // `handle` isn't just a regular function in some cases. It also contains\n            // some properties holding metadata and state so we need to proxy them\n            // through through patched function\n            // ref: https://github.com/open-telemetry/opentelemetry-js-contrib/issues/1950\n            // Also some apps/libs do their own patching before OTEL and have these properties\n            // in the proptotype. So we use a `for...in` loop to get own properties and also\n            // any enumerable prop in the prototype chain\n            // ref: https://github.com/open-telemetry/opentelemetry-js-contrib/issues/2271\n            for (const key in original) {\n                Object.defineProperty(patched, key, {\n                    get() {\n                        return original[key];\n                    },\n                    set(value) {\n                        original[key] = value;\n                    },\n                });\n            }\n            return patched;\n        });\n    }\n    _getSpanName(info, defaultName) {\n        var _a;\n        const { spanNameHook } = this.getConfig();\n        if (!(spanNameHook instanceof Function)) {\n            return defaultName;\n        }\n        try {\n            return (_a = spanNameHook(info, defaultName)) !== null && _a !== void 0 ? _a : defaultName;\n        }\n        catch (err) {\n            api_1.diag.error('express instrumentation: error calling span name rewrite hook', err);\n            return defaultName;\n        }\n    }\n}\nexports.ExpressInstrumentation = ExpressInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wZGI0NWQwNGEzMjY0MDBkMWEwZWZmMWNhNDVmNzA5NS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWV4cHJlc3MvYnVpbGQvc3JjL2luc3RydW1lbnRhdGlvbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCw4QkFBOEI7QUFDOUIsZUFBZSxtQkFBTyxDQUFDLG1LQUFxQjtBQUM1QyxjQUFjLG1CQUFPLENBQUMsc0lBQW9CO0FBQzFDLDJCQUEyQixtQkFBTyxDQUFDLHFOQUEwQjtBQUM3RCx5QkFBeUIsbUJBQU8sQ0FBQyxpTkFBd0I7QUFDekQsZ0JBQWdCLG1CQUFPLENBQUMsbUxBQVM7QUFDakM7QUFDQSxrQkFBa0IsbUJBQU8sQ0FBQyx1TEFBVztBQUNyQywwQkFBMEIsbUJBQU8sQ0FBQyxrTUFBZ0M7QUFDbEUsK0JBQStCLG1CQUFPLENBQUMsMExBQXFDO0FBQzVFLHlCQUF5QixtQkFBTyxDQUFDLHFNQUFrQjtBQUNuRDtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxHQUFHO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQix3QkFBd0IsY0FBYztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixlQUFlO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QjtBQUM5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wZGI0NWQwNGEzMjY0MDBkMWEwZWZmMWNhNDVmNzA5NVxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWV4cHJlc3NcXGJ1aWxkXFxzcmNcXGluc3RydW1lbnRhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkV4cHJlc3NJbnN0cnVtZW50YXRpb24gPSB2b2lkIDA7XG5jb25zdCBjb3JlXzEgPSByZXF1aXJlKFwiQG9wZW50ZWxlbWV0cnkvY29yZVwiKTtcbmNvbnN0IGFwaV8xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L2FwaVwiKTtcbmNvbnN0IEV4cHJlc3NMYXllclR5cGVfMSA9IHJlcXVpcmUoXCIuL2VudW1zL0V4cHJlc3NMYXllclR5cGVcIik7XG5jb25zdCBBdHRyaWJ1dGVOYW1lc18xID0gcmVxdWlyZShcIi4vZW51bXMvQXR0cmlidXRlTmFtZXNcIik7XG5jb25zdCB1dGlsc18xID0gcmVxdWlyZShcIi4vdXRpbHNcIik7XG4vKiogQGtuaXBpZ25vcmUgKi9cbmNvbnN0IHZlcnNpb25fMSA9IHJlcXVpcmUoXCIuL3ZlcnNpb25cIik7XG5jb25zdCBpbnN0cnVtZW50YXRpb25fMSA9IHJlcXVpcmUoXCJAb3BlbnRlbGVtZXRyeS9pbnN0cnVtZW50YXRpb25cIik7XG5jb25zdCBzZW1hbnRpY19jb252ZW50aW9uc18xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L3NlbWFudGljLWNvbnZlbnRpb25zXCIpO1xuY29uc3QgaW50ZXJuYWxfdHlwZXNfMSA9IHJlcXVpcmUoXCIuL2ludGVybmFsLXR5cGVzXCIpO1xuLyoqIEV4cHJlc3MgaW5zdHJ1bWVudGF0aW9uIGZvciBPcGVuVGVsZW1ldHJ5ICovXG5jbGFzcyBFeHByZXNzSW5zdHJ1bWVudGF0aW9uIGV4dGVuZHMgaW5zdHJ1bWVudGF0aW9uXzEuSW5zdHJ1bWVudGF0aW9uQmFzZSB7XG4gICAgY29uc3RydWN0b3IoY29uZmlnID0ge30pIHtcbiAgICAgICAgc3VwZXIodmVyc2lvbl8xLlBBQ0tBR0VfTkFNRSwgdmVyc2lvbl8xLlBBQ0tBR0VfVkVSU0lPTiwgY29uZmlnKTtcbiAgICB9XG4gICAgaW5pdCgpIHtcbiAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgIG5ldyBpbnN0cnVtZW50YXRpb25fMS5JbnN0cnVtZW50YXRpb25Ob2RlTW9kdWxlRGVmaW5pdGlvbignZXhwcmVzcycsIFsnPj00LjAuMCA8NSddLCBtb2R1bGVFeHBvcnRzID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCByb3V0ZXJQcm90byA9IG1vZHVsZUV4cG9ydHMuUm91dGVyO1xuICAgICAgICAgICAgICAgIC8vIHBhdGNoIGV4cHJlc3MuUm91dGVyLnJvdXRlXG4gICAgICAgICAgICAgICAgaWYgKCgwLCBpbnN0cnVtZW50YXRpb25fMS5pc1dyYXBwZWQpKHJvdXRlclByb3RvLnJvdXRlKSkge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLl91bndyYXAocm91dGVyUHJvdG8sICdyb3V0ZScpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB0aGlzLl93cmFwKHJvdXRlclByb3RvLCAncm91dGUnLCB0aGlzLl9nZXRSb3V0ZVBhdGNoKCkpO1xuICAgICAgICAgICAgICAgIC8vIHBhdGNoIGV4cHJlc3MuUm91dGVyLnVzZVxuICAgICAgICAgICAgICAgIGlmICgoMCwgaW5zdHJ1bWVudGF0aW9uXzEuaXNXcmFwcGVkKShyb3V0ZXJQcm90by51c2UpKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChyb3V0ZXJQcm90bywgJ3VzZScpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLWV4cGxpY2l0LWFueVxuICAgICAgICAgICAgICAgIHRoaXMuX3dyYXAocm91dGVyUHJvdG8sICd1c2UnLCB0aGlzLl9nZXRSb3V0ZXJVc2VQYXRjaCgpKTtcbiAgICAgICAgICAgICAgICAvLyBwYXRjaCBleHByZXNzLkFwcGxpY2F0aW9uLnVzZVxuICAgICAgICAgICAgICAgIGlmICgoMCwgaW5zdHJ1bWVudGF0aW9uXzEuaXNXcmFwcGVkKShtb2R1bGVFeHBvcnRzLmFwcGxpY2F0aW9uLnVzZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5fdW53cmFwKG1vZHVsZUV4cG9ydHMuYXBwbGljYXRpb24sICd1c2UnKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdGhpcy5fd3JhcChtb2R1bGVFeHBvcnRzLmFwcGxpY2F0aW9uLCAndXNlJywgXG4gICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby1leHBsaWNpdC1hbnlcbiAgICAgICAgICAgICAgICB0aGlzLl9nZXRBcHBVc2VQYXRjaCgpKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gbW9kdWxlRXhwb3J0cztcbiAgICAgICAgICAgIH0sIG1vZHVsZUV4cG9ydHMgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChtb2R1bGVFeHBvcnRzID09PSB1bmRlZmluZWQpXG4gICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICBjb25zdCByb3V0ZXJQcm90byA9IG1vZHVsZUV4cG9ydHMuUm91dGVyO1xuICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChyb3V0ZXJQcm90bywgJ3JvdXRlJyk7XG4gICAgICAgICAgICAgICAgdGhpcy5fdW53cmFwKHJvdXRlclByb3RvLCAndXNlJyk7XG4gICAgICAgICAgICAgICAgdGhpcy5fdW53cmFwKG1vZHVsZUV4cG9ydHMuYXBwbGljYXRpb24sICd1c2UnKTtcbiAgICAgICAgICAgIH0pLFxuICAgICAgICBdO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgdGhlIHBhdGNoIGZvciBSb3V0ZXIucm91dGUgZnVuY3Rpb25cbiAgICAgKi9cbiAgICBfZ2V0Um91dGVQYXRjaCgpIHtcbiAgICAgICAgY29uc3QgaW5zdHJ1bWVudGF0aW9uID0gdGhpcztcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uIChvcmlnaW5hbCkge1xuICAgICAgICAgICAgcmV0dXJuIGZ1bmN0aW9uIHJvdXRlX3RyYWNlKC4uLmFyZ3MpIHtcbiAgICAgICAgICAgICAgICBjb25zdCByb3V0ZSA9IG9yaWdpbmFsLmFwcGx5KHRoaXMsIGFyZ3MpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGxheWVyID0gdGhpcy5zdGFja1t0aGlzLnN0YWNrLmxlbmd0aCAtIDFdO1xuICAgICAgICAgICAgICAgIGluc3RydW1lbnRhdGlvbi5fYXBwbHlQYXRjaChsYXllciwgKDAsIHV0aWxzXzEuZ2V0TGF5ZXJQYXRoKShhcmdzKSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHJvdXRlO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHRoZSBwYXRjaCBmb3IgUm91dGVyLnVzZSBmdW5jdGlvblxuICAgICAqL1xuICAgIF9nZXRSb3V0ZXJVc2VQYXRjaCgpIHtcbiAgICAgICAgY29uc3QgaW5zdHJ1bWVudGF0aW9uID0gdGhpcztcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uIChvcmlnaW5hbCkge1xuICAgICAgICAgICAgcmV0dXJuIGZ1bmN0aW9uIHVzZSguLi5hcmdzKSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgcm91dGUgPSBvcmlnaW5hbC5hcHBseSh0aGlzLCBhcmdzKTtcbiAgICAgICAgICAgICAgICBjb25zdCBsYXllciA9IHRoaXMuc3RhY2tbdGhpcy5zdGFjay5sZW5ndGggLSAxXTtcbiAgICAgICAgICAgICAgICBpbnN0cnVtZW50YXRpb24uX2FwcGx5UGF0Y2gobGF5ZXIsICgwLCB1dGlsc18xLmdldExheWVyUGF0aCkoYXJncykpO1xuICAgICAgICAgICAgICAgIHJldHVybiByb3V0ZTtcbiAgICAgICAgICAgIH07XG4gICAgICAgIH07XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCB0aGUgcGF0Y2ggZm9yIEFwcGxpY2F0aW9uLnVzZSBmdW5jdGlvblxuICAgICAqL1xuICAgIF9nZXRBcHBVc2VQYXRjaCgpIHtcbiAgICAgICAgY29uc3QgaW5zdHJ1bWVudGF0aW9uID0gdGhpcztcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uIChvcmlnaW5hbCkge1xuICAgICAgICAgICAgcmV0dXJuIGZ1bmN0aW9uIHVzZSguLi5hcmdzKSB7XG4gICAgICAgICAgICAgICAgY29uc3Qgcm91dGUgPSBvcmlnaW5hbC5hcHBseSh0aGlzLCBhcmdzKTtcbiAgICAgICAgICAgICAgICBjb25zdCBsYXllciA9IHRoaXMuX3JvdXRlci5zdGFja1t0aGlzLl9yb3V0ZXIuc3RhY2subGVuZ3RoIC0gMV07XG4gICAgICAgICAgICAgICAgaW5zdHJ1bWVudGF0aW9uLl9hcHBseVBhdGNoKGxheWVyLCAoMCwgdXRpbHNfMS5nZXRMYXllclBhdGgpKGFyZ3MpKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gcm91dGU7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9O1xuICAgIH1cbiAgICAvKiogUGF0Y2ggZWFjaCBleHByZXNzIGxheWVyIHRvIGNyZWF0ZSBzcGFuIGFuZCBwcm9wYWdhdGUgY29udGV4dCAqL1xuICAgIF9hcHBseVBhdGNoKGxheWVyLCBsYXllclBhdGgpIHtcbiAgICAgICAgY29uc3QgaW5zdHJ1bWVudGF0aW9uID0gdGhpcztcbiAgICAgICAgLy8gYXZvaWQgcGF0Y2hpbmcgbXVsdGlwbGUgdGltZXMgdGhlIHNhbWUgbGF5ZXJcbiAgICAgICAgaWYgKGxheWVyW2ludGVybmFsX3R5cGVzXzEua0xheWVyUGF0Y2hlZF0gPT09IHRydWUpXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIGxheWVyW2ludGVybmFsX3R5cGVzXzEua0xheWVyUGF0Y2hlZF0gPSB0cnVlO1xuICAgICAgICB0aGlzLl93cmFwKGxheWVyLCAnaGFuZGxlJywgb3JpZ2luYWwgPT4ge1xuICAgICAgICAgICAgLy8gVE9ETzogaW5zdHJ1bWVudCBlcnJvciBoYW5kbGVyc1xuICAgICAgICAgICAgaWYgKG9yaWdpbmFsLmxlbmd0aCA9PT0gNClcbiAgICAgICAgICAgICAgICByZXR1cm4gb3JpZ2luYWw7XG4gICAgICAgICAgICBjb25zdCBwYXRjaGVkID0gZnVuY3Rpb24gKHJlcSwgcmVzKSB7XG4gICAgICAgICAgICAgICAgKDAsIHV0aWxzXzEuc3RvcmVMYXllclBhdGgpKHJlcSwgbGF5ZXJQYXRoKTtcbiAgICAgICAgICAgICAgICBjb25zdCByb3V0ZSA9IHJlcVtpbnRlcm5hbF90eXBlc18xLl9MQVlFUlNfU1RPUkVfUFJPUEVSVFldXG4gICAgICAgICAgICAgICAgICAgIC5maWx0ZXIocGF0aCA9PiBwYXRoICE9PSAnLycgJiYgcGF0aCAhPT0gJy8qJylcbiAgICAgICAgICAgICAgICAgICAgLmpvaW4oJycpXG4gICAgICAgICAgICAgICAgICAgIC8vIHJlbW92ZSBkdXBsaWNhdGUgc2xhc2hlcyB0byBub3JtYWxpemUgcm91dGVcbiAgICAgICAgICAgICAgICAgICAgLnJlcGxhY2UoL1xcL3syLH0vZywgJy8nKTtcbiAgICAgICAgICAgICAgICBjb25zdCBhdHRyaWJ1dGVzID0ge1xuICAgICAgICAgICAgICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19IVFRQX1JPVVRFXTogcm91dGUubGVuZ3RoID4gMCA/IHJvdXRlIDogJy8nLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgY29uc3QgbWV0YWRhdGEgPSAoMCwgdXRpbHNfMS5nZXRMYXllck1ldGFkYXRhKShyb3V0ZSwgbGF5ZXIsIGxheWVyUGF0aCk7XG4gICAgICAgICAgICAgICAgY29uc3QgdHlwZSA9IG1ldGFkYXRhLmF0dHJpYnV0ZXNbQXR0cmlidXRlTmFtZXNfMS5BdHRyaWJ1dGVOYW1lcy5FWFBSRVNTX1RZUEVdO1xuICAgICAgICAgICAgICAgIGNvbnN0IHJwY01ldGFkYXRhID0gKDAsIGNvcmVfMS5nZXRSUENNZXRhZGF0YSkoYXBpXzEuY29udGV4dC5hY3RpdmUoKSk7XG4gICAgICAgICAgICAgICAgaWYgKChycGNNZXRhZGF0YSA9PT0gbnVsbCB8fCBycGNNZXRhZGF0YSA9PT0gdm9pZCAwID8gdm9pZCAwIDogcnBjTWV0YWRhdGEudHlwZSkgPT09IGNvcmVfMS5SUENUeXBlLkhUVFApIHtcbiAgICAgICAgICAgICAgICAgICAgcnBjTWV0YWRhdGEucm91dGUgPSByb3V0ZSB8fCAnLyc7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIHZlcmlmeSBhZ2FpbnN0IHRoZSBjb25maWcgaWYgdGhlIGxheWVyIHNob3VsZCBiZSBpZ25vcmVkXG4gICAgICAgICAgICAgICAgaWYgKCgwLCB1dGlsc18xLmlzTGF5ZXJJZ25vcmVkKShtZXRhZGF0YS5uYW1lLCB0eXBlLCBpbnN0cnVtZW50YXRpb24uZ2V0Q29uZmlnKCkpKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICh0eXBlID09PSBFeHByZXNzTGF5ZXJUeXBlXzEuRXhwcmVzc0xheWVyVHlwZS5NSURETEVXQVJFKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXFbaW50ZXJuYWxfdHlwZXNfMS5fTEFZRVJTX1NUT1JFX1BST1BFUlRZXS5wb3AoKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gb3JpZ2luYWwuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGFwaV8xLnRyYWNlLmdldFNwYW4oYXBpXzEuY29udGV4dC5hY3RpdmUoKSkgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gb3JpZ2luYWwuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3Qgc3Bhbk5hbWUgPSBpbnN0cnVtZW50YXRpb24uX2dldFNwYW5OYW1lKHtcbiAgICAgICAgICAgICAgICAgICAgcmVxdWVzdDogcmVxLFxuICAgICAgICAgICAgICAgICAgICBsYXllclR5cGU6IHR5cGUsXG4gICAgICAgICAgICAgICAgICAgIHJvdXRlLFxuICAgICAgICAgICAgICAgIH0sIG1ldGFkYXRhLm5hbWUpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHNwYW4gPSBpbnN0cnVtZW50YXRpb24udHJhY2VyLnN0YXJ0U3BhbihzcGFuTmFtZSwge1xuICAgICAgICAgICAgICAgICAgICBhdHRyaWJ1dGVzOiBPYmplY3QuYXNzaWduKGF0dHJpYnV0ZXMsIG1ldGFkYXRhLmF0dHJpYnV0ZXMpLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgcmVxdWVzdEhvb2sgfSA9IGluc3RydW1lbnRhdGlvbi5nZXRDb25maWcoKTtcbiAgICAgICAgICAgICAgICBpZiAocmVxdWVzdEhvb2spIHtcbiAgICAgICAgICAgICAgICAgICAgKDAsIGluc3RydW1lbnRhdGlvbl8xLnNhZmVFeGVjdXRlSW5UaGVNaWRkbGUpKCgpID0+IHJlcXVlc3RIb29rKHNwYW4sIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlcXVlc3Q6IHJlcSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGxheWVyVHlwZTogdHlwZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHJvdXRlLFxuICAgICAgICAgICAgICAgICAgICB9KSwgZSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFwaV8xLmRpYWcuZXJyb3IoJ2V4cHJlc3MgaW5zdHJ1bWVudGF0aW9uOiByZXF1ZXN0IGhvb2sgZmFpbGVkJywgZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH0sIHRydWUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBsZXQgc3Bhbkhhc0VuZGVkID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgaWYgKG1ldGFkYXRhLmF0dHJpYnV0ZXNbQXR0cmlidXRlTmFtZXNfMS5BdHRyaWJ1dGVOYW1lcy5FWFBSRVNTX1RZUEVdICE9PVxuICAgICAgICAgICAgICAgICAgICBFeHByZXNzTGF5ZXJUeXBlXzEuRXhwcmVzc0xheWVyVHlwZS5NSURETEVXQVJFKSB7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4uZW5kKCk7XG4gICAgICAgICAgICAgICAgICAgIHNwYW5IYXNFbmRlZCA9IHRydWU7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIGxpc3RlbmVyIGZvciByZXNwb25zZS5vbignZmluaXNoJylcbiAgICAgICAgICAgICAgICBjb25zdCBvblJlc3BvbnNlRmluaXNoID0gKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAoc3Bhbkhhc0VuZGVkID09PSBmYWxzZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3Bhbkhhc0VuZGVkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNwYW4uZW5kKCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIC8vIHZlcmlmeSB3ZSBoYXZlIGEgY2FsbGJhY2tcbiAgICAgICAgICAgICAgICBjb25zdCBhcmdzID0gQXJyYXkuZnJvbShhcmd1bWVudHMpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGNhbGxiYWNrSWR4ID0gYXJncy5maW5kSW5kZXgoYXJnID0+IHR5cGVvZiBhcmcgPT09ICdmdW5jdGlvbicpO1xuICAgICAgICAgICAgICAgIGlmIChjYWxsYmFja0lkeCA+PSAwKSB7XG4gICAgICAgICAgICAgICAgICAgIGFyZ3VtZW50c1tjYWxsYmFja0lkeF0gPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YXIgX2E7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBleHByZXNzIGNvbnNpZGVycyBhbnl0aGluZyBidXQgYW4gZW1wdHkgdmFsdWUsIFwicm91dGVcIiBvciBcInJvdXRlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBwYXNzZWQgdG8gaXRzIGNhbGxiYWNrIHRvIGJlIGFuIGVycm9yXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtYXliZUVycm9yID0gYXJndW1lbnRzWzBdO1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgaXNFcnJvciA9ICFbdW5kZWZpbmVkLCBudWxsLCAncm91dGUnLCAncm91dGVyJ10uaW5jbHVkZXMobWF5YmVFcnJvcik7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXNwYW5IYXNFbmRlZCAmJiBpc0Vycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgW2Vycm9yLCBtZXNzYWdlXSA9ICgwLCB1dGlsc18xLmFzRXJyb3JBbmRNZXNzYWdlKShtYXliZUVycm9yKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcGFuLnJlY29yZEV4Y2VwdGlvbihlcnJvcik7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Bhbi5zZXRTdGF0dXMoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2RlOiBhcGlfMS5TcGFuU3RhdHVzQ29kZS5FUlJPUixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChzcGFuSGFzRW5kZWQgPT09IGZhbHNlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Bhbkhhc0VuZGVkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAoX2EgPSByZXEucmVzKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EucmVtb3ZlTGlzdGVuZXIoJ2ZpbmlzaCcsIG9uUmVzcG9uc2VGaW5pc2gpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNwYW4uZW5kKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIShyZXEucm91dGUgJiYgaXNFcnJvcikpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXFbaW50ZXJuYWxfdHlwZXNfMS5fTEFZRVJTX1NUT1JFX1BST1BFUlRZXS5wb3AoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGNhbGxiYWNrID0gYXJnc1tjYWxsYmFja0lkeF07XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gY2FsbGJhY2suYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG9yaWdpbmFsLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhdGNoIChhbnlFcnJvcikge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBbZXJyb3IsIG1lc3NhZ2VdID0gKDAsIHV0aWxzXzEuYXNFcnJvckFuZE1lc3NhZ2UpKGFueUVycm9yKTtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbi5yZWNvcmRFeGNlcHRpb24oZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICBzcGFuLnNldFN0YXR1cyh7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb2RlOiBhcGlfMS5TcGFuU3RhdHVzQ29kZS5FUlJPUixcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2UsXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBhbnlFcnJvcjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgICAgIC8qKlxuICAgICAgICAgICAgICAgICAgICAgKiBBdCB0aGlzIHBvaW50IGlmIHRoZSBjYWxsYmFjayB3YXNuJ3QgY2FsbGVkLCB0aGF0IG1lYW5zIGVpdGhlciB0aGVcbiAgICAgICAgICAgICAgICAgICAgICogbGF5ZXIgaXMgYXN5bmNocm9ub3VzIChzbyBpdCB3aWxsIGNhbGwgdGhlIGNhbGxiYWNrIGxhdGVyIG9uKSBvciB0aGF0XG4gICAgICAgICAgICAgICAgICAgICAqIHRoZSBsYXllciBkaXJlY3RseSBlbmQgdGhlIGh0dHAgcmVzcG9uc2UsIHNvIHdlJ2xsIGhvb2sgaW50byB0aGUgXCJmaW5pc2hcIlxuICAgICAgICAgICAgICAgICAgICAgKiBldmVudCB0byBoYW5kbGUgdGhlIGxhdGVyIGNhc2UuXG4gICAgICAgICAgICAgICAgICAgICAqL1xuICAgICAgICAgICAgICAgICAgICBpZiAoIXNwYW5IYXNFbmRlZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVzLm9uY2UoJ2ZpbmlzaCcsIG9uUmVzcG9uc2VGaW5pc2gpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIC8vIGBoYW5kbGVgIGlzbid0IGp1c3QgYSByZWd1bGFyIGZ1bmN0aW9uIGluIHNvbWUgY2FzZXMuIEl0IGFsc28gY29udGFpbnNcbiAgICAgICAgICAgIC8vIHNvbWUgcHJvcGVydGllcyBob2xkaW5nIG1ldGFkYXRhIGFuZCBzdGF0ZSBzbyB3ZSBuZWVkIHRvIHByb3h5IHRoZW1cbiAgICAgICAgICAgIC8vIHRocm91Z2ggdGhyb3VnaCBwYXRjaGVkIGZ1bmN0aW9uXG4gICAgICAgICAgICAvLyByZWY6IGh0dHBzOi8vZ2l0aHViLmNvbS9vcGVuLXRlbGVtZXRyeS9vcGVudGVsZW1ldHJ5LWpzLWNvbnRyaWIvaXNzdWVzLzE5NTBcbiAgICAgICAgICAgIC8vIEFsc28gc29tZSBhcHBzL2xpYnMgZG8gdGhlaXIgb3duIHBhdGNoaW5nIGJlZm9yZSBPVEVMIGFuZCBoYXZlIHRoZXNlIHByb3BlcnRpZXNcbiAgICAgICAgICAgIC8vIGluIHRoZSBwcm9wdG90eXBlLiBTbyB3ZSB1c2UgYSBgZm9yLi4uaW5gIGxvb3AgdG8gZ2V0IG93biBwcm9wZXJ0aWVzIGFuZCBhbHNvXG4gICAgICAgICAgICAvLyBhbnkgZW51bWVyYWJsZSBwcm9wIGluIHRoZSBwcm90b3R5cGUgY2hhaW5cbiAgICAgICAgICAgIC8vIHJlZjogaHR0cHM6Ly9naXRodWIuY29tL29wZW4tdGVsZW1ldHJ5L29wZW50ZWxlbWV0cnktanMtY29udHJpYi9pc3N1ZXMvMjI3MVxuICAgICAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gb3JpZ2luYWwpIHtcbiAgICAgICAgICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocGF0Y2hlZCwga2V5LCB7XG4gICAgICAgICAgICAgICAgICAgIGdldCgpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBvcmlnaW5hbFtrZXldO1xuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICBzZXQodmFsdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG9yaWdpbmFsW2tleV0gPSB2YWx1ZTtcbiAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBwYXRjaGVkO1xuICAgICAgICB9KTtcbiAgICB9XG4gICAgX2dldFNwYW5OYW1lKGluZm8sIGRlZmF1bHROYW1lKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgY29uc3QgeyBzcGFuTmFtZUhvb2sgfSA9IHRoaXMuZ2V0Q29uZmlnKCk7XG4gICAgICAgIGlmICghKHNwYW5OYW1lSG9vayBpbnN0YW5jZW9mIEZ1bmN0aW9uKSkge1xuICAgICAgICAgICAgcmV0dXJuIGRlZmF1bHROYW1lO1xuICAgICAgICB9XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gKF9hID0gc3Bhbk5hbWVIb29rKGluZm8sIGRlZmF1bHROYW1lKSkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogZGVmYXVsdE5hbWU7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgYXBpXzEuZGlhZy5lcnJvcignZXhwcmVzcyBpbnN0cnVtZW50YXRpb246IGVycm9yIGNhbGxpbmcgc3BhbiBuYW1lIHJld3JpdGUgaG9vaycsIGVycik7XG4gICAgICAgICAgICByZXR1cm4gZGVmYXVsdE5hbWU7XG4gICAgICAgIH1cbiAgICB9XG59XG5leHBvcnRzLkV4cHJlc3NJbnN0cnVtZW50YXRpb24gPSBFeHByZXNzSW5zdHJ1bWVudGF0aW9uO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5zdHJ1bWVudGF0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports._LAYERS_STORE_PROPERTY = exports.kLayerPatched = void 0;\n/**\n * This symbol is used to mark express layer as being already instrumented\n * since its possible to use a given layer multiple times (ex: middlewares)\n */\nexports.kLayerPatched = Symbol('express-layer-patched');\n/**\n * This const define where on the `request` object the Instrumentation will mount the\n * current stack of express layer.\n *\n * It is necessary because express doesn't store the different layers\n * (ie: middleware, router etc) that it called to get to the current layer.\n * Given that, the only way to know the route of a given layer is to\n * store the path of where each previous layer has been mounted.\n *\n * ex: bodyParser > auth middleware > /users router > get /:id\n *  in this case the stack would be: [\"/users\", \"/:id\"]\n *\n * ex2: bodyParser > /api router > /v1 router > /users router > get /:id\n *  stack: [\"/api\", \"/v1\", \"/users\", \":id\"]\n *\n */\nexports._LAYERS_STORE_PROPERTY = '__ot_middlewares';\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wZGI0NWQwNGEzMjY0MDBkMWEwZWZmMWNhNDVmNzA5NS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWV4cHJlc3MvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzBkYjQ1ZDA0YTMyNjQwMGQxYTBlZmYxY2E0NWY3MDk1XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tZXhwcmVzc1xcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getLayerPath = exports.asErrorAndMessage = exports.isLayerIgnored = exports.getLayerMetadata = exports.getRouterPath = exports.storeLayerPath = void 0;\nconst ExpressLayerType_1 = __webpack_require__(/*! ./enums/ExpressLayerType */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js\");\n/**\n * Store layers path in the request to be able to construct route later\n * @param request The request where\n * @param [value] the value to push into the array\n */\nconst storeLayerPath = (request, value) => {\n    if (Array.isArray(request[internal_types_1._LAYERS_STORE_PROPERTY]) === false) {\n        Object.defineProperty(request, internal_types_1._LAYERS_STORE_PROPERTY, {\n            enumerable: false,\n            value: [],\n        });\n    }\n    if (value === undefined)\n        return;\n    request[internal_types_1._LAYERS_STORE_PROPERTY].push(value);\n};\nexports.storeLayerPath = storeLayerPath;\n/**\n * Recursively search the router path from layer stack\n * @param path The path to reconstruct\n * @param layer The layer to reconstruct from\n * @returns The reconstructed path\n */\nconst getRouterPath = (path, layer) => {\n    var _a, _b, _c, _d;\n    const stackLayer = (_b = (_a = layer.handle) === null || _a === void 0 ? void 0 : _a.stack) === null || _b === void 0 ? void 0 : _b[0];\n    if ((_c = stackLayer === null || stackLayer === void 0 ? void 0 : stackLayer.route) === null || _c === void 0 ? void 0 : _c.path) {\n        return `${path}${stackLayer.route.path}`;\n    }\n    if ((_d = stackLayer === null || stackLayer === void 0 ? void 0 : stackLayer.handle) === null || _d === void 0 ? void 0 : _d.stack) {\n        return (0, exports.getRouterPath)(path, stackLayer);\n    }\n    return path;\n};\nexports.getRouterPath = getRouterPath;\n/**\n * Parse express layer context to retrieve a name and attributes.\n * @param route The route of the layer\n * @param layer Express layer\n * @param [layerPath] if present, the path on which the layer has been mounted\n */\nconst getLayerMetadata = (route, layer, layerPath) => {\n    var _a;\n    if (layer.name === 'router') {\n        const maybeRouterPath = (0, exports.getRouterPath)('', layer);\n        const extractedRouterPath = maybeRouterPath\n            ? maybeRouterPath\n            : layerPath || route || '/';\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.EXPRESS_NAME]: extractedRouterPath,\n                [AttributeNames_1.AttributeNames.EXPRESS_TYPE]: ExpressLayerType_1.ExpressLayerType.ROUTER,\n            },\n            name: `router - ${extractedRouterPath}`,\n        };\n    }\n    else if (layer.name === 'bound dispatch') {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.EXPRESS_NAME]: (_a = (route || layerPath)) !== null && _a !== void 0 ? _a : 'request handler',\n                [AttributeNames_1.AttributeNames.EXPRESS_TYPE]: ExpressLayerType_1.ExpressLayerType.REQUEST_HANDLER,\n            },\n            name: `request handler${layer.path ? ` - ${route || layerPath}` : ''}`,\n        };\n    }\n    else {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.EXPRESS_NAME]: layer.name,\n                [AttributeNames_1.AttributeNames.EXPRESS_TYPE]: ExpressLayerType_1.ExpressLayerType.MIDDLEWARE,\n            },\n            name: `middleware - ${layer.name}`,\n        };\n    }\n};\nexports.getLayerMetadata = getLayerMetadata;\n/**\n * Check whether the given obj match pattern\n * @param constant e.g URL of request\n * @param obj obj to inspect\n * @param pattern Match pattern\n */\nconst satisfiesPattern = (constant, pattern) => {\n    if (typeof pattern === 'string') {\n        return pattern === constant;\n    }\n    else if (pattern instanceof RegExp) {\n        return pattern.test(constant);\n    }\n    else if (typeof pattern === 'function') {\n        return pattern(constant);\n    }\n    else {\n        throw new TypeError('Pattern is in unsupported datatype');\n    }\n};\n/**\n * Check whether the given request is ignored by configuration\n * It will not re-throw exceptions from `list` provided by the client\n * @param constant e.g URL of request\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nconst isLayerIgnored = (name, type, config) => {\n    var _a;\n    if (Array.isArray(config === null || config === void 0 ? void 0 : config.ignoreLayersType) &&\n        ((_a = config === null || config === void 0 ? void 0 : config.ignoreLayersType) === null || _a === void 0 ? void 0 : _a.includes(type))) {\n        return true;\n    }\n    if (Array.isArray(config === null || config === void 0 ? void 0 : config.ignoreLayers) === false)\n        return false;\n    try {\n        for (const pattern of config.ignoreLayers) {\n            if (satisfiesPattern(name, pattern)) {\n                return true;\n            }\n        }\n    }\n    catch (e) {\n        /* catch block*/\n    }\n    return false;\n};\nexports.isLayerIgnored = isLayerIgnored;\n/**\n * Converts a user-provided error value into an error and error message pair\n *\n * @param error - User-provided error value\n * @returns Both an Error or string representation of the value and an error message\n */\nconst asErrorAndMessage = (error) => error instanceof Error\n    ? [error, error.message]\n    : [String(error), String(error)];\nexports.asErrorAndMessage = asErrorAndMessage;\n/**\n * Extracts the layer path from the route arguments\n *\n * @param args - Arguments of the route\n * @returns The layer path\n */\nconst getLayerPath = (args) => {\n    const firstArg = args[0];\n    if (Array.isArray(firstArg)) {\n        return firstArg.map(arg => extractLayerPathSegment(arg) || '').join(',');\n    }\n    return extractLayerPathSegment(firstArg);\n};\nexports.getLayerPath = getLayerPath;\nconst extractLayerPathSegment = (arg) => {\n    if (typeof arg === 'string') {\n        return arg;\n    }\n    if (arg instanceof RegExp || typeof arg === 'number') {\n        return arg.toString();\n    }\n    return;\n};\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-express';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"EXPRESS_TYPE\"] = \"express.type\";\n    AttributeNames[\"EXPRESS_NAME\"] = \"express.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ExpressLayerType = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar ExpressLayerType;\n(function (ExpressLayerType) {\n    ExpressLayerType[\"ROUTER\"] = \"router\";\n    ExpressLayerType[\"MIDDLEWARE\"] = \"middleware\";\n    ExpressLayerType[\"REQUEST_HANDLER\"] = \"request_handler\";\n})(ExpressLayerType = exports.ExpressLayerType || (exports.ExpressLayerType = {}));\n//# sourceMappingURL=ExpressLayerType.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/ExpressLayerType */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ExpressInstrumentation = void 0;\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst ExpressLayerType_1 = __webpack_require__(/*! ./enums/ExpressLayerType */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/version.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js\");\n/** Express instrumentation for OpenTelemetry */\nclass ExpressInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('express', ['>=4.0.0 <5'], moduleExports => {\n                const routerProto = moduleExports.Router;\n                // patch express.Router.route\n                if ((0, instrumentation_1.isWrapped)(routerProto.route)) {\n                    this._unwrap(routerProto, 'route');\n                }\n                this._wrap(routerProto, 'route', this._getRoutePatch());\n                // patch express.Router.use\n                if ((0, instrumentation_1.isWrapped)(routerProto.use)) {\n                    this._unwrap(routerProto, 'use');\n                }\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                this._wrap(routerProto, 'use', this._getRouterUsePatch());\n                // patch express.Application.use\n                if ((0, instrumentation_1.isWrapped)(moduleExports.application.use)) {\n                    this._unwrap(moduleExports.application, 'use');\n                }\n                this._wrap(moduleExports.application, 'use', \n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                this._getAppUsePatch());\n                return moduleExports;\n            }, moduleExports => {\n                if (moduleExports === undefined)\n                    return;\n                const routerProto = moduleExports.Router;\n                this._unwrap(routerProto, 'route');\n                this._unwrap(routerProto, 'use');\n                this._unwrap(moduleExports.application, 'use');\n            }),\n        ];\n    }\n    /**\n     * Get the patch for Router.route function\n     */\n    _getRoutePatch() {\n        const instrumentation = this;\n        return function (original) {\n            return function route_trace(...args) {\n                const route = original.apply(this, args);\n                const layer = this.stack[this.stack.length - 1];\n                instrumentation._applyPatch(layer, (0, utils_1.getLayerPath)(args));\n                return route;\n            };\n        };\n    }\n    /**\n     * Get the patch for Router.use function\n     */\n    _getRouterUsePatch() {\n        const instrumentation = this;\n        return function (original) {\n            return function use(...args) {\n                const route = original.apply(this, args);\n                const layer = this.stack[this.stack.length - 1];\n                instrumentation._applyPatch(layer, (0, utils_1.getLayerPath)(args));\n                return route;\n            };\n        };\n    }\n    /**\n     * Get the patch for Application.use function\n     */\n    _getAppUsePatch() {\n        const instrumentation = this;\n        return function (original) {\n            return function use(...args) {\n                const route = original.apply(this, args);\n                const layer = this._router.stack[this._router.stack.length - 1];\n                instrumentation._applyPatch(layer, (0, utils_1.getLayerPath)(args));\n                return route;\n            };\n        };\n    }\n    /** Patch each express layer to create span and propagate context */\n    _applyPatch(layer, layerPath) {\n        const instrumentation = this;\n        // avoid patching multiple times the same layer\n        if (layer[internal_types_1.kLayerPatched] === true)\n            return;\n        layer[internal_types_1.kLayerPatched] = true;\n        this._wrap(layer, 'handle', original => {\n            // TODO: instrument error handlers\n            if (original.length === 4)\n                return original;\n            const patched = function (req, res) {\n                (0, utils_1.storeLayerPath)(req, layerPath);\n                const route = req[internal_types_1._LAYERS_STORE_PROPERTY]\n                    .filter(path => path !== '/' && path !== '/*')\n                    .join('')\n                    // remove duplicate slashes to normalize route\n                    .replace(/\\/{2,}/g, '/');\n                const attributes = {\n                    [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: route.length > 0 ? route : '/',\n                };\n                const metadata = (0, utils_1.getLayerMetadata)(route, layer, layerPath);\n                const type = metadata.attributes[AttributeNames_1.AttributeNames.EXPRESS_TYPE];\n                const rpcMetadata = (0, core_1.getRPCMetadata)(api_1.context.active());\n                if ((rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP) {\n                    rpcMetadata.route = route || '/';\n                }\n                // verify against the config if the layer should be ignored\n                if ((0, utils_1.isLayerIgnored)(metadata.name, type, instrumentation.getConfig())) {\n                    if (type === ExpressLayerType_1.ExpressLayerType.MIDDLEWARE) {\n                        req[internal_types_1._LAYERS_STORE_PROPERTY].pop();\n                    }\n                    return original.apply(this, arguments);\n                }\n                if (api_1.trace.getSpan(api_1.context.active()) === undefined) {\n                    return original.apply(this, arguments);\n                }\n                const spanName = instrumentation._getSpanName({\n                    request: req,\n                    layerType: type,\n                    route,\n                }, metadata.name);\n                const span = instrumentation.tracer.startSpan(spanName, {\n                    attributes: Object.assign(attributes, metadata.attributes),\n                });\n                const { requestHook } = instrumentation.getConfig();\n                if (requestHook) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => requestHook(span, {\n                        request: req,\n                        layerType: type,\n                        route,\n                    }), e => {\n                        if (e) {\n                            api_1.diag.error('express instrumentation: request hook failed', e);\n                        }\n                    }, true);\n                }\n                let spanHasEnded = false;\n                if (metadata.attributes[AttributeNames_1.AttributeNames.EXPRESS_TYPE] !==\n                    ExpressLayerType_1.ExpressLayerType.MIDDLEWARE) {\n                    span.end();\n                    spanHasEnded = true;\n                }\n                // listener for response.on('finish')\n                const onResponseFinish = () => {\n                    if (spanHasEnded === false) {\n                        spanHasEnded = true;\n                        span.end();\n                    }\n                };\n                // verify we have a callback\n                const args = Array.from(arguments);\n                const callbackIdx = args.findIndex(arg => typeof arg === 'function');\n                if (callbackIdx >= 0) {\n                    arguments[callbackIdx] = function () {\n                        var _a;\n                        // express considers anything but an empty value, \"route\" or \"router\"\n                        // passed to its callback to be an error\n                        const maybeError = arguments[0];\n                        const isError = ![undefined, null, 'route', 'router'].includes(maybeError);\n                        if (!spanHasEnded && isError) {\n                            const [error, message] = (0, utils_1.asErrorAndMessage)(maybeError);\n                            span.recordException(error);\n                            span.setStatus({\n                                code: api_1.SpanStatusCode.ERROR,\n                                message,\n                            });\n                        }\n                        if (spanHasEnded === false) {\n                            spanHasEnded = true;\n                            (_a = req.res) === null || _a === void 0 ? void 0 : _a.removeListener('finish', onResponseFinish);\n                            span.end();\n                        }\n                        if (!(req.route && isError)) {\n                            req[internal_types_1._LAYERS_STORE_PROPERTY].pop();\n                        }\n                        const callback = args[callbackIdx];\n                        return callback.apply(this, arguments);\n                    };\n                }\n                try {\n                    return original.apply(this, arguments);\n                }\n                catch (anyError) {\n                    const [error, message] = (0, utils_1.asErrorAndMessage)(anyError);\n                    span.recordException(error);\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message,\n                    });\n                    throw anyError;\n                }\n                finally {\n                    /**\n                     * At this point if the callback wasn't called, that means either the\n                     * layer is asynchronous (so it will call the callback later on) or that\n                     * the layer directly end the http response, so we'll hook into the \"finish\"\n                     * event to handle the later case.\n                     */\n                    if (!spanHasEnded) {\n                        res.once('finish', onResponseFinish);\n                    }\n                }\n            };\n            // `handle` isn't just a regular function in some cases. It also contains\n            // some properties holding metadata and state so we need to proxy them\n            // through through patched function\n            // ref: https://github.com/open-telemetry/opentelemetry-js-contrib/issues/1950\n            // Also some apps/libs do their own patching before OTEL and have these properties\n            // in the proptotype. So we use a `for...in` loop to get own properties and also\n            // any enumerable prop in the prototype chain\n            // ref: https://github.com/open-telemetry/opentelemetry-js-contrib/issues/2271\n            for (const key in original) {\n                Object.defineProperty(patched, key, {\n                    get() {\n                        return original[key];\n                    },\n                    set(value) {\n                        original[key] = value;\n                    },\n                });\n            }\n            return patched;\n        });\n    }\n    _getSpanName(info, defaultName) {\n        var _a;\n        const { spanNameHook } = this.getConfig();\n        if (!(spanNameHook instanceof Function)) {\n            return defaultName;\n        }\n        try {\n            return (_a = spanNameHook(info, defaultName)) !== null && _a !== void 0 ? _a : defaultName;\n        }\n        catch (err) {\n            api_1.diag.error('express instrumentation: error calling span name rewrite hook', err);\n            return defaultName;\n        }\n    }\n}\nexports.ExpressInstrumentation = ExpressInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports._LAYERS_STORE_PROPERTY = exports.kLayerPatched = void 0;\n/**\n * This symbol is used to mark express layer as being already instrumented\n * since its possible to use a given layer multiple times (ex: middlewares)\n */\nexports.kLayerPatched = Symbol('express-layer-patched');\n/**\n * This const define where on the `request` object the Instrumentation will mount the\n * current stack of express layer.\n *\n * It is necessary because express doesn't store the different layers\n * (ie: middleware, router etc) that it called to get to the current layer.\n * Given that, the only way to know the route of a given layer is to\n * store the path of where each previous layer has been mounted.\n *\n * ex: bodyParser > auth middleware > /users router > get /:id\n *  in this case the stack would be: [\"/users\", \"/:id\"]\n *\n * ex2: bodyParser > /api router > /v1 router > /users router > get /:id\n *  stack: [\"/api\", \"/v1\", \"/users\", \":id\"]\n *\n */\nexports._LAYERS_STORE_PROPERTY = '__ot_middlewares';\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wZGI0NWQwNGEzMjY0MDBkMWEwZWZmMWNhNDVmNzA5NS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWV4cHJlc3MvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzBkYjQ1ZDA0YTMyNjQwMGQxYTBlZmYxY2E0NWY3MDk1XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tZXhwcmVzc1xcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getLayerPath = exports.asErrorAndMessage = exports.isLayerIgnored = exports.getLayerMetadata = exports.getRouterPath = exports.storeLayerPath = void 0;\nconst ExpressLayerType_1 = __webpack_require__(/*! ./enums/ExpressLayerType */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/ExpressLayerType.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/enums/AttributeNames.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/internal-types.js\");\n/**\n * Store layers path in the request to be able to construct route later\n * @param request The request where\n * @param [value] the value to push into the array\n */\nconst storeLayerPath = (request, value) => {\n    if (Array.isArray(request[internal_types_1._LAYERS_STORE_PROPERTY]) === false) {\n        Object.defineProperty(request, internal_types_1._LAYERS_STORE_PROPERTY, {\n            enumerable: false,\n            value: [],\n        });\n    }\n    if (value === undefined)\n        return;\n    request[internal_types_1._LAYERS_STORE_PROPERTY].push(value);\n};\nexports.storeLayerPath = storeLayerPath;\n/**\n * Recursively search the router path from layer stack\n * @param path The path to reconstruct\n * @param layer The layer to reconstruct from\n * @returns The reconstructed path\n */\nconst getRouterPath = (path, layer) => {\n    var _a, _b, _c, _d;\n    const stackLayer = (_b = (_a = layer.handle) === null || _a === void 0 ? void 0 : _a.stack) === null || _b === void 0 ? void 0 : _b[0];\n    if ((_c = stackLayer === null || stackLayer === void 0 ? void 0 : stackLayer.route) === null || _c === void 0 ? void 0 : _c.path) {\n        return `${path}${stackLayer.route.path}`;\n    }\n    if ((_d = stackLayer === null || stackLayer === void 0 ? void 0 : stackLayer.handle) === null || _d === void 0 ? void 0 : _d.stack) {\n        return (0, exports.getRouterPath)(path, stackLayer);\n    }\n    return path;\n};\nexports.getRouterPath = getRouterPath;\n/**\n * Parse express layer context to retrieve a name and attributes.\n * @param route The route of the layer\n * @param layer Express layer\n * @param [layerPath] if present, the path on which the layer has been mounted\n */\nconst getLayerMetadata = (route, layer, layerPath) => {\n    var _a;\n    if (layer.name === 'router') {\n        const maybeRouterPath = (0, exports.getRouterPath)('', layer);\n        const extractedRouterPath = maybeRouterPath\n            ? maybeRouterPath\n            : layerPath || route || '/';\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.EXPRESS_NAME]: extractedRouterPath,\n                [AttributeNames_1.AttributeNames.EXPRESS_TYPE]: ExpressLayerType_1.ExpressLayerType.ROUTER,\n            },\n            name: `router - ${extractedRouterPath}`,\n        };\n    }\n    else if (layer.name === 'bound dispatch') {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.EXPRESS_NAME]: (_a = (route || layerPath)) !== null && _a !== void 0 ? _a : 'request handler',\n                [AttributeNames_1.AttributeNames.EXPRESS_TYPE]: ExpressLayerType_1.ExpressLayerType.REQUEST_HANDLER,\n            },\n            name: `request handler${layer.path ? ` - ${route || layerPath}` : ''}`,\n        };\n    }\n    else {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.EXPRESS_NAME]: layer.name,\n                [AttributeNames_1.AttributeNames.EXPRESS_TYPE]: ExpressLayerType_1.ExpressLayerType.MIDDLEWARE,\n            },\n            name: `middleware - ${layer.name}`,\n        };\n    }\n};\nexports.getLayerMetadata = getLayerMetadata;\n/**\n * Check whether the given obj match pattern\n * @param constant e.g URL of request\n * @param obj obj to inspect\n * @param pattern Match pattern\n */\nconst satisfiesPattern = (constant, pattern) => {\n    if (typeof pattern === 'string') {\n        return pattern === constant;\n    }\n    else if (pattern instanceof RegExp) {\n        return pattern.test(constant);\n    }\n    else if (typeof pattern === 'function') {\n        return pattern(constant);\n    }\n    else {\n        throw new TypeError('Pattern is in unsupported datatype');\n    }\n};\n/**\n * Check whether the given request is ignored by configuration\n * It will not re-throw exceptions from `list` provided by the client\n * @param constant e.g URL of request\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nconst isLayerIgnored = (name, type, config) => {\n    var _a;\n    if (Array.isArray(config === null || config === void 0 ? void 0 : config.ignoreLayersType) &&\n        ((_a = config === null || config === void 0 ? void 0 : config.ignoreLayersType) === null || _a === void 0 ? void 0 : _a.includes(type))) {\n        return true;\n    }\n    if (Array.isArray(config === null || config === void 0 ? void 0 : config.ignoreLayers) === false)\n        return false;\n    try {\n        for (const pattern of config.ignoreLayers) {\n            if (satisfiesPattern(name, pattern)) {\n                return true;\n            }\n        }\n    }\n    catch (e) {\n        /* catch block*/\n    }\n    return false;\n};\nexports.isLayerIgnored = isLayerIgnored;\n/**\n * Converts a user-provided error value into an error and error message pair\n *\n * @param error - User-provided error value\n * @returns Both an Error or string representation of the value and an error message\n */\nconst asErrorAndMessage = (error) => error instanceof Error\n    ? [error, error.message]\n    : [String(error), String(error)];\nexports.asErrorAndMessage = asErrorAndMessage;\n/**\n * Extracts the layer path from the route arguments\n *\n * @param args - Arguments of the route\n * @returns The layer path\n */\nconst getLayerPath = (args) => {\n    const firstArg = args[0];\n    if (Array.isArray(firstArg)) {\n        return firstArg.map(arg => extractLayerPathSegment(arg) || '').join(',');\n    }\n    return extractLayerPathSegment(firstArg);\n};\nexports.getLayerPath = getLayerPath;\nconst extractLayerPathSegment = (arg) => {\n    if (typeof arg === 'string') {\n        return arg;\n    }\n    if (arg instanceof RegExp || typeof arg === 'number') {\n        return arg.toString();\n    }\n    return;\n};\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-express';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_0db45d04a326400d1a0eff1ca45f7095/node_modules/@opentelemetry/instrumentation-express/build/src/version.js\n");

/***/ })

};
;