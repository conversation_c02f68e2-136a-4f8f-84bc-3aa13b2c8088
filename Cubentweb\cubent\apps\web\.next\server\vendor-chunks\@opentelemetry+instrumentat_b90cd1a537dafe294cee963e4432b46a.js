"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Postgresql specific attributes not covered by semantic conventions\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"PG_VALUES\"] = \"db.postgresql.values\";\n    AttributeNames[\"PG_PLAN\"] = \"db.postgresql.plan\";\n    AttributeNames[\"IDLE_TIMEOUT_MILLIS\"] = \"db.postgresql.idle.timeout.millis\";\n    AttributeNames[\"MAX_CLIENT\"] = \"db.postgresql.max.client\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SpanNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Contains span names produced by instrumentation\nvar SpanNames;\n(function (SpanNames) {\n    SpanNames[\"QUERY_PREFIX\"] = \"pg.query\";\n    SpanNames[\"CONNECT\"] = \"pg.connect\";\n    SpanNames[\"POOL_CONNECT\"] = \"pg-pool.connect\";\n})(SpanNames = exports.SpanNames || (exports.SpanNames = {}));\n//# sourceMappingURL=SpanNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PgInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js\");\nconst utils = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js\");\nconst sql_common_1 = __webpack_require__(/*! @opentelemetry/sql-common */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js\");\nconst SpanNames_1 = __webpack_require__(/*! ./enums/SpanNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst semconv_1 = __webpack_require__(/*! ./semconv */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\");\nfunction extractModuleExports(module) {\n    return module[Symbol.toStringTag] === 'Module'\n        ? module.default // ESM\n        : module; // CommonJS\n}\nclass PgInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        // Pool events connect, acquire, release and remove can be called\n        // multiple times without changing the values of total, idle and waiting\n        // connections. The _connectionsCounter is used to keep track of latest\n        // values and only update the metrics _connectionsCount and _connectionPendingRequests\n        // when the value change.\n        this._connectionsCounter = {\n            used: 0,\n            idle: 0,\n            pending: 0,\n        };\n    }\n    _updateMetricInstruments() {\n        this._operationDuration = this.meter.createHistogram(semconv_1.METRIC_DB_CLIENT_OPERATION_DURATION, {\n            description: 'Duration of database client operations.',\n            unit: 's',\n            valueType: api_1.ValueType.DOUBLE,\n            advice: {\n                explicitBucketBoundaries: [\n                    0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10,\n                ],\n            },\n        });\n        this._connectionsCounter = {\n            idle: 0,\n            pending: 0,\n            used: 0,\n        };\n        this._connectionsCount = this.meter.createUpDownCounter(semconv_1.METRIC_DB_CLIENT_CONNECTION_COUNT, {\n            description: 'The number of connections that are currently in state described by the state attribute.',\n            unit: '{connection}',\n        });\n        this._connectionPendingRequests = this.meter.createUpDownCounter(semconv_1.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS, {\n            description: 'The number of current pending requests for an open connection.',\n            unit: '{connection}',\n        });\n    }\n    init() {\n        const SUPPORTED_PG_VERSIONS = ['>=8.0.3 <9'];\n        const modulePgNativeClient = new instrumentation_1.InstrumentationNodeModuleFile('pg/lib/native/client.js', SUPPORTED_PG_VERSIONS, this._patchPgClient.bind(this), this._unpatchPgClient.bind(this));\n        const modulePgClient = new instrumentation_1.InstrumentationNodeModuleFile('pg/lib/client.js', SUPPORTED_PG_VERSIONS, this._patchPgClient.bind(this), this._unpatchPgClient.bind(this));\n        const modulePG = new instrumentation_1.InstrumentationNodeModuleDefinition('pg', SUPPORTED_PG_VERSIONS, (module) => {\n            const moduleExports = extractModuleExports(module);\n            this._patchPgClient(moduleExports.Client);\n            return module;\n        }, (module) => {\n            const moduleExports = extractModuleExports(module);\n            this._unpatchPgClient(moduleExports.Client);\n            return module;\n        }, [modulePgClient, modulePgNativeClient]);\n        const modulePGPool = new instrumentation_1.InstrumentationNodeModuleDefinition('pg-pool', ['>=2.0.0 <4'], (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                this._unwrap(moduleExports.prototype, 'connect');\n            }\n            this._wrap(moduleExports.prototype, 'connect', this._getPoolConnectPatch());\n            return moduleExports;\n        }, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                this._unwrap(moduleExports.prototype, 'connect');\n            }\n        });\n        return [modulePG, modulePGPool];\n    }\n    _patchPgClient(module) {\n        if (!module) {\n            return;\n        }\n        const moduleExports = extractModuleExports(module);\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.query)) {\n            this._unwrap(moduleExports.prototype, 'query');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n            this._unwrap(moduleExports.prototype, 'connect');\n        }\n        this._wrap(moduleExports.prototype, 'query', this._getClientQueryPatch());\n        this._wrap(moduleExports.prototype, 'connect', this._getClientConnectPatch());\n        return module;\n    }\n    _unpatchPgClient(module) {\n        const moduleExports = extractModuleExports(module);\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.query)) {\n            this._unwrap(moduleExports.prototype, 'query');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n            this._unwrap(moduleExports.prototype, 'connect');\n        }\n        return module;\n    }\n    _getClientConnectPatch() {\n        const plugin = this;\n        return (original) => {\n            return function connect(callback) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return original.call(this, callback);\n                }\n                const span = plugin.tracer.startSpan(SpanNames_1.SpanNames.CONNECT, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: utils.getSemanticAttributesFromConnection(this),\n                });\n                if (callback) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    callback = utils.patchClientConnectCallback(span, callback);\n                    if (parentSpan) {\n                        callback = api_1.context.bind(api_1.context.active(), callback);\n                    }\n                }\n                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return original.call(this, callback);\n                });\n                return handleConnectResult(span, connectResult);\n            };\n        };\n    }\n    recordOperationDuration(attributes, startTime) {\n        const metricsAttributes = {};\n        const keysToCopy = [\n            semantic_conventions_1.SEMATTRS_DB_SYSTEM,\n            semconv_1.ATTR_DB_NAMESPACE,\n            semantic_conventions_1.ATTR_ERROR_TYPE,\n            semantic_conventions_1.ATTR_SERVER_PORT,\n            semantic_conventions_1.ATTR_SERVER_ADDRESS,\n            semconv_1.ATTR_DB_OPERATION_NAME,\n        ];\n        keysToCopy.forEach(key => {\n            if (key in attributes) {\n                metricsAttributes[key] = attributes[key];\n            }\n        });\n        const durationSeconds = (0, core_1.hrTimeToMilliseconds)((0, core_1.hrTimeDuration)(startTime, (0, core_1.hrTime)())) / 1000;\n        this._operationDuration.record(durationSeconds, metricsAttributes);\n    }\n    _getClientQueryPatch() {\n        const plugin = this;\n        return (original) => {\n            this._diag.debug('Patching pg.Client.prototype.query');\n            return function query(...args) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return original.apply(this, args);\n                }\n                const startTime = (0, core_1.hrTime)();\n                // client.query(text, cb?), client.query(text, values, cb?), and\n                // client.query(configObj, cb?) are all valid signatures. We construct\n                // a queryConfig obj from all (valid) signatures to build the span in a\n                // unified way. We verify that we at least have query text, and code\n                // defensively when dealing with `queryConfig` after that (to handle all\n                // the other invalid cases, like a non-array for values being provided).\n                // The type casts here reflect only what we've actually validated.\n                const arg0 = args[0];\n                const firstArgIsString = typeof arg0 === 'string';\n                const firstArgIsQueryObjectWithText = utils.isObjectWithTextString(arg0);\n                // TODO: remove the `as ...` casts below when the TS version is upgraded.\n                // Newer TS versions will use the result of firstArgIsQueryObjectWithText\n                // to properly narrow arg0, but TS 4.3.5 does not.\n                const queryConfig = firstArgIsString\n                    ? {\n                        text: arg0,\n                        values: Array.isArray(args[1]) ? args[1] : undefined,\n                    }\n                    : firstArgIsQueryObjectWithText\n                        ? arg0\n                        : undefined;\n                const attributes = {\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n                    [semconv_1.ATTR_DB_NAMESPACE]: this.database,\n                    [semantic_conventions_1.ATTR_SERVER_PORT]: this.connectionParameters.port,\n                    [semantic_conventions_1.ATTR_SERVER_ADDRESS]: this.connectionParameters.host,\n                };\n                if (queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.text) {\n                    attributes[semconv_1.ATTR_DB_OPERATION_NAME] =\n                        utils.parseNormalizedOperationName(queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.text);\n                }\n                const recordDuration = () => {\n                    plugin.recordOperationDuration(attributes, startTime);\n                };\n                const instrumentationConfig = plugin.getConfig();\n                const span = utils.handleConfigQuery.call(this, plugin.tracer, instrumentationConfig, queryConfig);\n                // Modify query text w/ a tracing comment before invoking original for\n                // tracing, but only if args[0] has one of our expected shapes.\n                if (instrumentationConfig.addSqlCommenterCommentToQueries) {\n                    if (firstArgIsString) {\n                        args[0] = (0, sql_common_1.addSqlCommenterComment)(span, arg0);\n                    }\n                    else if (firstArgIsQueryObjectWithText && !('name' in arg0)) {\n                        // In the case of a query object, we need to ensure there's no name field\n                        // as this indicates a prepared query, where the comment would remain the same\n                        // for every invocation and contain an outdated trace context.\n                        args[0] = Object.assign(Object.assign({}, arg0), { text: (0, sql_common_1.addSqlCommenterComment)(span, arg0.text) });\n                    }\n                }\n                // Bind callback (if any) to parent span (if any)\n                if (args.length > 0) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    if (typeof args[args.length - 1] === 'function') {\n                        // Patch ParameterQuery callback\n                        args[args.length - 1] = utils.patchCallback(instrumentationConfig, span, args[args.length - 1], // nb: not type safe.\n                        attributes, recordDuration);\n                        // If a parent span exists, bind the callback\n                        if (parentSpan) {\n                            args[args.length - 1] = api_1.context.bind(api_1.context.active(), args[args.length - 1]);\n                        }\n                    }\n                    else if (typeof (queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.callback) === 'function') {\n                        // Patch ConfigQuery callback\n                        let callback = utils.patchCallback(plugin.getConfig(), span, queryConfig.callback, // nb: not type safe.\n                        attributes, recordDuration);\n                        // If a parent span existed, bind the callback\n                        if (parentSpan) {\n                            callback = api_1.context.bind(api_1.context.active(), callback);\n                        }\n                        args[0].callback = callback;\n                    }\n                }\n                const { requestHook } = instrumentationConfig;\n                if (typeof requestHook === 'function' && queryConfig) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                        // pick keys to expose explicitly, so we're not leaking pg package\n                        // internals that are subject to change\n                        const { database, host, port, user } = this.connectionParameters;\n                        const connection = { database, host, port, user };\n                        requestHook(span, {\n                            connection,\n                            query: {\n                                text: queryConfig.text,\n                                // nb: if `client.query` is called with illegal arguments\n                                // (e.g., if `queryConfig.values` is passed explicitly, but a\n                                // non-array is given), then the type casts will be wrong. But\n                                // we leave it up to the queryHook to handle that, and we\n                                // catch and swallow any errors it throws. The other options\n                                // are all worse. E.g., we could leave `queryConfig.values`\n                                // and `queryConfig.name` as `unknown`, but then the hook body\n                                // would be forced to validate (or cast) them before using\n                                // them, which seems incredibly cumbersome given that these\n                                // casts will be correct 99.9% of the time -- and pg.query\n                                // will immediately throw during development in the other .1%\n                                // of cases. Alternatively, we could simply skip calling the\n                                // hook when `values` or `name` don't have the expected type,\n                                // but that would add unnecessary validation overhead to every\n                                // hook invocation and possibly be even more confusing/unexpected.\n                                values: queryConfig.values,\n                                name: queryConfig.name,\n                            },\n                        });\n                    }, err => {\n                        if (err) {\n                            plugin._diag.error('Error running query hook', err);\n                        }\n                    }, true);\n                }\n                let result;\n                try {\n                    result = original.apply(this, args);\n                }\n                catch (e) {\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: utils.getErrorMessage(e),\n                    });\n                    span.end();\n                    throw e;\n                }\n                // Bind promise to parent span and end the span\n                if (result instanceof Promise) {\n                    return result\n                        .then((result) => {\n                        // Return a pass-along promise which ends the span and then goes to user's orig resolvers\n                        return new Promise(resolve => {\n                            utils.handleExecutionResult(plugin.getConfig(), span, result);\n                            recordDuration();\n                            span.end();\n                            resolve(result);\n                        });\n                    })\n                        .catch((error) => {\n                        return new Promise((_, reject) => {\n                            span.setStatus({\n                                code: api_1.SpanStatusCode.ERROR,\n                                message: error.message,\n                            });\n                            recordDuration();\n                            span.end();\n                            reject(error);\n                        });\n                    });\n                }\n                // else returns void\n                return result; // void\n            };\n        };\n    }\n    _setPoolConnectEventListeners(pgPool) {\n        if (pgPool[internal_types_1.EVENT_LISTENERS_SET])\n            return;\n        const poolName = utils.getPoolName(pgPool.options);\n        pgPool.on('connect', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('acquire', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('remove', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('release', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool[internal_types_1.EVENT_LISTENERS_SET] = true;\n    }\n    _getPoolConnectPatch() {\n        const plugin = this;\n        return (originalConnect) => {\n            return function connect(callback) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return originalConnect.call(this, callback);\n                }\n                // setup span\n                const span = plugin.tracer.startSpan(SpanNames_1.SpanNames.POOL_CONNECT, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: utils.getSemanticAttributesFromPool(this.options),\n                });\n                plugin._setPoolConnectEventListeners(this);\n                if (callback) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    callback = utils.patchCallbackPGPool(span, callback);\n                    // If a parent span exists, bind the callback\n                    if (parentSpan) {\n                        callback = api_1.context.bind(api_1.context.active(), callback);\n                    }\n                }\n                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return originalConnect.call(this, callback);\n                });\n                return handleConnectResult(span, connectResult);\n            };\n        };\n    }\n}\nexports.PgInstrumentation = PgInstrumentation;\nfunction handleConnectResult(span, connectResult) {\n    if (!(connectResult instanceof Promise)) {\n        return connectResult;\n    }\n    const connectResultPromise = connectResult;\n    return api_1.context.bind(api_1.context.active(), connectResultPromise\n        .then(result => {\n        span.end();\n        return result;\n    })\n        .catch((error) => {\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: utils.getErrorMessage(error),\n        });\n        span.end();\n        return Promise.reject(error);\n    }));\n}\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYjkwY2QxYTUzN2RhZmUyOTRjZWU5NjNlNDQzMmI0NmEvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1wZy9idWlsZC9zcmMvaW5zdHJ1bWVudGF0aW9uLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsbUJBQU8sQ0FBQyx5TUFBZ0M7QUFDbEUsY0FBYyxtQkFBTyxDQUFDLDZJQUFvQjtBQUMxQyx5QkFBeUIsbUJBQU8sQ0FBQyx1TUFBa0I7QUFDbkQsY0FBYyxtQkFBTyxDQUFDLHFMQUFTO0FBQy9CLHFCQUFxQixtQkFBTyxDQUFDLDRMQUEyQjtBQUN4RDtBQUNBLGtCQUFrQixtQkFBTyxDQUFDLHlMQUFXO0FBQ3JDLG9CQUFvQixtQkFBTyxDQUFDLHlNQUFtQjtBQUMvQyxlQUFlLG1CQUFPLENBQUMsMEtBQXFCO0FBQzVDLCtCQUErQixtQkFBTyxDQUFDLGlNQUFxQztBQUM1RSxrQkFBa0IsbUJBQU8sQ0FBQyx5TEFBVztBQUNyQztBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLFdBQVc7QUFDL0IsU0FBUztBQUNUO0FBQ0E7QUFDQSxvQkFBb0IsV0FBVztBQUMvQixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnRUFBZ0UsV0FBVyxpRUFBaUU7QUFDNUk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsY0FBYztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyw2QkFBNkI7QUFDN0QsNkNBQTZDO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3Qix5QkFBeUI7QUFDekIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekIscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YVxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLXBnXFxidWlsZFxcc3JjXFxpbnN0cnVtZW50YXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlBnSW5zdHJ1bWVudGF0aW9uID0gdm9pZCAwO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbmNvbnN0IGluc3RydW1lbnRhdGlvbl8xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvblwiKTtcbmNvbnN0IGFwaV8xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L2FwaVwiKTtcbmNvbnN0IGludGVybmFsX3R5cGVzXzEgPSByZXF1aXJlKFwiLi9pbnRlcm5hbC10eXBlc1wiKTtcbmNvbnN0IHV0aWxzID0gcmVxdWlyZShcIi4vdXRpbHNcIik7XG5jb25zdCBzcWxfY29tbW9uXzEgPSByZXF1aXJlKFwiQG9wZW50ZWxlbWV0cnkvc3FsLWNvbW1vblwiKTtcbi8qKiBAa25pcGlnbm9yZSAqL1xuY29uc3QgdmVyc2lvbl8xID0gcmVxdWlyZShcIi4vdmVyc2lvblwiKTtcbmNvbnN0IFNwYW5OYW1lc18xID0gcmVxdWlyZShcIi4vZW51bXMvU3Bhbk5hbWVzXCIpO1xuY29uc3QgY29yZV8xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L2NvcmVcIik7XG5jb25zdCBzZW1hbnRpY19jb252ZW50aW9uc18xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L3NlbWFudGljLWNvbnZlbnRpb25zXCIpO1xuY29uc3Qgc2VtY29udl8xID0gcmVxdWlyZShcIi4vc2VtY29udlwiKTtcbmZ1bmN0aW9uIGV4dHJhY3RNb2R1bGVFeHBvcnRzKG1vZHVsZSkge1xuICAgIHJldHVybiBtb2R1bGVbU3ltYm9sLnRvU3RyaW5nVGFnXSA9PT0gJ01vZHVsZSdcbiAgICAgICAgPyBtb2R1bGUuZGVmYXVsdCAvLyBFU01cbiAgICAgICAgOiBtb2R1bGU7IC8vIENvbW1vbkpTXG59XG5jbGFzcyBQZ0luc3RydW1lbnRhdGlvbiBleHRlbmRzIGluc3RydW1lbnRhdGlvbl8xLkluc3RydW1lbnRhdGlvbkJhc2Uge1xuICAgIGNvbnN0cnVjdG9yKGNvbmZpZyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHZlcnNpb25fMS5QQUNLQUdFX05BTUUsIHZlcnNpb25fMS5QQUNLQUdFX1ZFUlNJT04sIGNvbmZpZyk7XG4gICAgICAgIC8vIFBvb2wgZXZlbnRzIGNvbm5lY3QsIGFjcXVpcmUsIHJlbGVhc2UgYW5kIHJlbW92ZSBjYW4gYmUgY2FsbGVkXG4gICAgICAgIC8vIG11bHRpcGxlIHRpbWVzIHdpdGhvdXQgY2hhbmdpbmcgdGhlIHZhbHVlcyBvZiB0b3RhbCwgaWRsZSBhbmQgd2FpdGluZ1xuICAgICAgICAvLyBjb25uZWN0aW9ucy4gVGhlIF9jb25uZWN0aW9uc0NvdW50ZXIgaXMgdXNlZCB0byBrZWVwIHRyYWNrIG9mIGxhdGVzdFxuICAgICAgICAvLyB2YWx1ZXMgYW5kIG9ubHkgdXBkYXRlIHRoZSBtZXRyaWNzIF9jb25uZWN0aW9uc0NvdW50IGFuZCBfY29ubmVjdGlvblBlbmRpbmdSZXF1ZXN0c1xuICAgICAgICAvLyB3aGVuIHRoZSB2YWx1ZSBjaGFuZ2UuXG4gICAgICAgIHRoaXMuX2Nvbm5lY3Rpb25zQ291bnRlciA9IHtcbiAgICAgICAgICAgIHVzZWQ6IDAsXG4gICAgICAgICAgICBpZGxlOiAwLFxuICAgICAgICAgICAgcGVuZGluZzogMCxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgX3VwZGF0ZU1ldHJpY0luc3RydW1lbnRzKCkge1xuICAgICAgICB0aGlzLl9vcGVyYXRpb25EdXJhdGlvbiA9IHRoaXMubWV0ZXIuY3JlYXRlSGlzdG9ncmFtKHNlbWNvbnZfMS5NRVRSSUNfREJfQ0xJRU5UX09QRVJBVElPTl9EVVJBVElPTiwge1xuICAgICAgICAgICAgZGVzY3JpcHRpb246ICdEdXJhdGlvbiBvZiBkYXRhYmFzZSBjbGllbnQgb3BlcmF0aW9ucy4nLFxuICAgICAgICAgICAgdW5pdDogJ3MnLFxuICAgICAgICAgICAgdmFsdWVUeXBlOiBhcGlfMS5WYWx1ZVR5cGUuRE9VQkxFLFxuICAgICAgICAgICAgYWR2aWNlOiB7XG4gICAgICAgICAgICAgICAgZXhwbGljaXRCdWNrZXRCb3VuZGFyaWVzOiBbXG4gICAgICAgICAgICAgICAgICAgIDAuMDAxLCAwLjAwNSwgMC4wMSwgMC4wNSwgMC4xLCAwLjUsIDEsIDUsIDEwLFxuICAgICAgICAgICAgICAgIF0sXG4gICAgICAgICAgICB9LFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5fY29ubmVjdGlvbnNDb3VudGVyID0ge1xuICAgICAgICAgICAgaWRsZTogMCxcbiAgICAgICAgICAgIHBlbmRpbmc6IDAsXG4gICAgICAgICAgICB1c2VkOiAwLFxuICAgICAgICB9O1xuICAgICAgICB0aGlzLl9jb25uZWN0aW9uc0NvdW50ID0gdGhpcy5tZXRlci5jcmVhdGVVcERvd25Db3VudGVyKHNlbWNvbnZfMS5NRVRSSUNfREJfQ0xJRU5UX0NPTk5FQ1RJT05fQ09VTlQsIHtcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnVGhlIG51bWJlciBvZiBjb25uZWN0aW9ucyB0aGF0IGFyZSBjdXJyZW50bHkgaW4gc3RhdGUgZGVzY3JpYmVkIGJ5IHRoZSBzdGF0ZSBhdHRyaWJ1dGUuJyxcbiAgICAgICAgICAgIHVuaXQ6ICd7Y29ubmVjdGlvbn0nLFxuICAgICAgICB9KTtcbiAgICAgICAgdGhpcy5fY29ubmVjdGlvblBlbmRpbmdSZXF1ZXN0cyA9IHRoaXMubWV0ZXIuY3JlYXRlVXBEb3duQ291bnRlcihzZW1jb252XzEuTUVUUklDX0RCX0NMSUVOVF9DT05ORUNUSU9OX1BFTkRJTkdfUkVRVUVTVFMsIHtcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnVGhlIG51bWJlciBvZiBjdXJyZW50IHBlbmRpbmcgcmVxdWVzdHMgZm9yIGFuIG9wZW4gY29ubmVjdGlvbi4nLFxuICAgICAgICAgICAgdW5pdDogJ3tjb25uZWN0aW9ufScsXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBpbml0KCkge1xuICAgICAgICBjb25zdCBTVVBQT1JURURfUEdfVkVSU0lPTlMgPSBbJz49OC4wLjMgPDknXTtcbiAgICAgICAgY29uc3QgbW9kdWxlUGdOYXRpdmVDbGllbnQgPSBuZXcgaW5zdHJ1bWVudGF0aW9uXzEuSW5zdHJ1bWVudGF0aW9uTm9kZU1vZHVsZUZpbGUoJ3BnL2xpYi9uYXRpdmUvY2xpZW50LmpzJywgU1VQUE9SVEVEX1BHX1ZFUlNJT05TLCB0aGlzLl9wYXRjaFBnQ2xpZW50LmJpbmQodGhpcyksIHRoaXMuX3VucGF0Y2hQZ0NsaWVudC5iaW5kKHRoaXMpKTtcbiAgICAgICAgY29uc3QgbW9kdWxlUGdDbGllbnQgPSBuZXcgaW5zdHJ1bWVudGF0aW9uXzEuSW5zdHJ1bWVudGF0aW9uTm9kZU1vZHVsZUZpbGUoJ3BnL2xpYi9jbGllbnQuanMnLCBTVVBQT1JURURfUEdfVkVSU0lPTlMsIHRoaXMuX3BhdGNoUGdDbGllbnQuYmluZCh0aGlzKSwgdGhpcy5fdW5wYXRjaFBnQ2xpZW50LmJpbmQodGhpcykpO1xuICAgICAgICBjb25zdCBtb2R1bGVQRyA9IG5ldyBpbnN0cnVtZW50YXRpb25fMS5JbnN0cnVtZW50YXRpb25Ob2RlTW9kdWxlRGVmaW5pdGlvbigncGcnLCBTVVBQT1JURURfUEdfVkVSU0lPTlMsIChtb2R1bGUpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IG1vZHVsZUV4cG9ydHMgPSBleHRyYWN0TW9kdWxlRXhwb3J0cyhtb2R1bGUpO1xuICAgICAgICAgICAgdGhpcy5fcGF0Y2hQZ0NsaWVudChtb2R1bGVFeHBvcnRzLkNsaWVudCk7XG4gICAgICAgICAgICByZXR1cm4gbW9kdWxlO1xuICAgICAgICB9LCAobW9kdWxlKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBtb2R1bGVFeHBvcnRzID0gZXh0cmFjdE1vZHVsZUV4cG9ydHMobW9kdWxlKTtcbiAgICAgICAgICAgIHRoaXMuX3VucGF0Y2hQZ0NsaWVudChtb2R1bGVFeHBvcnRzLkNsaWVudCk7XG4gICAgICAgICAgICByZXR1cm4gbW9kdWxlO1xuICAgICAgICB9LCBbbW9kdWxlUGdDbGllbnQsIG1vZHVsZVBnTmF0aXZlQ2xpZW50XSk7XG4gICAgICAgIGNvbnN0IG1vZHVsZVBHUG9vbCA9IG5ldyBpbnN0cnVtZW50YXRpb25fMS5JbnN0cnVtZW50YXRpb25Ob2RlTW9kdWxlRGVmaW5pdGlvbigncGctcG9vbCcsIFsnPj0yLjAuMCA8NCddLCAobW9kdWxlRXhwb3J0cykgPT4ge1xuICAgICAgICAgICAgaWYgKCgwLCBpbnN0cnVtZW50YXRpb25fMS5pc1dyYXBwZWQpKG1vZHVsZUV4cG9ydHMucHJvdG90eXBlLmNvbm5lY3QpKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5fdW53cmFwKG1vZHVsZUV4cG9ydHMucHJvdG90eXBlLCAnY29ubmVjdCcpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5fd3JhcChtb2R1bGVFeHBvcnRzLnByb3RvdHlwZSwgJ2Nvbm5lY3QnLCB0aGlzLl9nZXRQb29sQ29ubmVjdFBhdGNoKCkpO1xuICAgICAgICAgICAgcmV0dXJuIG1vZHVsZUV4cG9ydHM7XG4gICAgICAgIH0sIChtb2R1bGVFeHBvcnRzKSA9PiB7XG4gICAgICAgICAgICBpZiAoKDAsIGluc3RydW1lbnRhdGlvbl8xLmlzV3JhcHBlZCkobW9kdWxlRXhwb3J0cy5wcm90b3R5cGUuY29ubmVjdCkpIHtcbiAgICAgICAgICAgICAgICB0aGlzLl91bndyYXAobW9kdWxlRXhwb3J0cy5wcm90b3R5cGUsICdjb25uZWN0Jyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gW21vZHVsZVBHLCBtb2R1bGVQR1Bvb2xdO1xuICAgIH1cbiAgICBfcGF0Y2hQZ0NsaWVudChtb2R1bGUpIHtcbiAgICAgICAgaWYgKCFtb2R1bGUpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBtb2R1bGVFeHBvcnRzID0gZXh0cmFjdE1vZHVsZUV4cG9ydHMobW9kdWxlKTtcbiAgICAgICAgaWYgKCgwLCBpbnN0cnVtZW50YXRpb25fMS5pc1dyYXBwZWQpKG1vZHVsZUV4cG9ydHMucHJvdG90eXBlLnF1ZXJ5KSkge1xuICAgICAgICAgICAgdGhpcy5fdW53cmFwKG1vZHVsZUV4cG9ydHMucHJvdG90eXBlLCAncXVlcnknKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoKDAsIGluc3RydW1lbnRhdGlvbl8xLmlzV3JhcHBlZCkobW9kdWxlRXhwb3J0cy5wcm90b3R5cGUuY29ubmVjdCkpIHtcbiAgICAgICAgICAgIHRoaXMuX3Vud3JhcChtb2R1bGVFeHBvcnRzLnByb3RvdHlwZSwgJ2Nvbm5lY3QnKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl93cmFwKG1vZHVsZUV4cG9ydHMucHJvdG90eXBlLCAncXVlcnknLCB0aGlzLl9nZXRDbGllbnRRdWVyeVBhdGNoKCkpO1xuICAgICAgICB0aGlzLl93cmFwKG1vZHVsZUV4cG9ydHMucHJvdG90eXBlLCAnY29ubmVjdCcsIHRoaXMuX2dldENsaWVudENvbm5lY3RQYXRjaCgpKTtcbiAgICAgICAgcmV0dXJuIG1vZHVsZTtcbiAgICB9XG4gICAgX3VucGF0Y2hQZ0NsaWVudChtb2R1bGUpIHtcbiAgICAgICAgY29uc3QgbW9kdWxlRXhwb3J0cyA9IGV4dHJhY3RNb2R1bGVFeHBvcnRzKG1vZHVsZSk7XG4gICAgICAgIGlmICgoMCwgaW5zdHJ1bWVudGF0aW9uXzEuaXNXcmFwcGVkKShtb2R1bGVFeHBvcnRzLnByb3RvdHlwZS5xdWVyeSkpIHtcbiAgICAgICAgICAgIHRoaXMuX3Vud3JhcChtb2R1bGVFeHBvcnRzLnByb3RvdHlwZSwgJ3F1ZXJ5Jyk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCgwLCBpbnN0cnVtZW50YXRpb25fMS5pc1dyYXBwZWQpKG1vZHVsZUV4cG9ydHMucHJvdG90eXBlLmNvbm5lY3QpKSB7XG4gICAgICAgICAgICB0aGlzLl91bndyYXAobW9kdWxlRXhwb3J0cy5wcm90b3R5cGUsICdjb25uZWN0Jyk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG1vZHVsZTtcbiAgICB9XG4gICAgX2dldENsaWVudENvbm5lY3RQYXRjaCgpIHtcbiAgICAgICAgY29uc3QgcGx1Z2luID0gdGhpcztcbiAgICAgICAgcmV0dXJuIChvcmlnaW5hbCkgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIGZ1bmN0aW9uIGNvbm5lY3QoY2FsbGJhY2spIHtcbiAgICAgICAgICAgICAgICBpZiAodXRpbHMuc2hvdWxkU2tpcEluc3RydW1lbnRhdGlvbihwbHVnaW4uZ2V0Q29uZmlnKCkpKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBvcmlnaW5hbC5jYWxsKHRoaXMsIGNhbGxiYWNrKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3Qgc3BhbiA9IHBsdWdpbi50cmFjZXIuc3RhcnRTcGFuKFNwYW5OYW1lc18xLlNwYW5OYW1lcy5DT05ORUNULCB7XG4gICAgICAgICAgICAgICAgICAgIGtpbmQ6IGFwaV8xLlNwYW5LaW5kLkNMSUVOVCxcbiAgICAgICAgICAgICAgICAgICAgYXR0cmlidXRlczogdXRpbHMuZ2V0U2VtYW50aWNBdHRyaWJ1dGVzRnJvbUNvbm5lY3Rpb24odGhpcyksXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgaWYgKGNhbGxiYWNrKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHBhcmVudFNwYW4gPSBhcGlfMS50cmFjZS5nZXRTcGFuKGFwaV8xLmNvbnRleHQuYWN0aXZlKCkpO1xuICAgICAgICAgICAgICAgICAgICBjYWxsYmFjayA9IHV0aWxzLnBhdGNoQ2xpZW50Q29ubmVjdENhbGxiYWNrKHNwYW4sIGNhbGxiYWNrKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHBhcmVudFNwYW4pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhbGxiYWNrID0gYXBpXzEuY29udGV4dC5iaW5kKGFwaV8xLmNvbnRleHQuYWN0aXZlKCksIGNhbGxiYWNrKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCBjb25uZWN0UmVzdWx0ID0gYXBpXzEuY29udGV4dC53aXRoKGFwaV8xLnRyYWNlLnNldFNwYW4oYXBpXzEuY29udGV4dC5hY3RpdmUoKSwgc3BhbiksICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG9yaWdpbmFsLmNhbGwodGhpcywgY2FsbGJhY2spO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIHJldHVybiBoYW5kbGVDb25uZWN0UmVzdWx0KHNwYW4sIGNvbm5lY3RSZXN1bHQpO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfTtcbiAgICB9XG4gICAgcmVjb3JkT3BlcmF0aW9uRHVyYXRpb24oYXR0cmlidXRlcywgc3RhcnRUaW1lKSB7XG4gICAgICAgIGNvbnN0IG1ldHJpY3NBdHRyaWJ1dGVzID0ge307XG4gICAgICAgIGNvbnN0IGtleXNUb0NvcHkgPSBbXG4gICAgICAgICAgICBzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX1NZU1RFTSxcbiAgICAgICAgICAgIHNlbWNvbnZfMS5BVFRSX0RCX05BTUVTUEFDRSxcbiAgICAgICAgICAgIHNlbWFudGljX2NvbnZlbnRpb25zXzEuQVRUUl9FUlJPUl9UWVBFLFxuICAgICAgICAgICAgc2VtYW50aWNfY29udmVudGlvbnNfMS5BVFRSX1NFUlZFUl9QT1JULFxuICAgICAgICAgICAgc2VtYW50aWNfY29udmVudGlvbnNfMS5BVFRSX1NFUlZFUl9BRERSRVNTLFxuICAgICAgICAgICAgc2VtY29udl8xLkFUVFJfREJfT1BFUkFUSU9OX05BTUUsXG4gICAgICAgIF07XG4gICAgICAgIGtleXNUb0NvcHkuZm9yRWFjaChrZXkgPT4ge1xuICAgICAgICAgICAgaWYgKGtleSBpbiBhdHRyaWJ1dGVzKSB7XG4gICAgICAgICAgICAgICAgbWV0cmljc0F0dHJpYnV0ZXNba2V5XSA9IGF0dHJpYnV0ZXNba2V5XTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IGR1cmF0aW9uU2Vjb25kcyA9ICgwLCBjb3JlXzEuaHJUaW1lVG9NaWxsaXNlY29uZHMpKCgwLCBjb3JlXzEuaHJUaW1lRHVyYXRpb24pKHN0YXJ0VGltZSwgKDAsIGNvcmVfMS5oclRpbWUpKCkpKSAvIDEwMDA7XG4gICAgICAgIHRoaXMuX29wZXJhdGlvbkR1cmF0aW9uLnJlY29yZChkdXJhdGlvblNlY29uZHMsIG1ldHJpY3NBdHRyaWJ1dGVzKTtcbiAgICB9XG4gICAgX2dldENsaWVudFF1ZXJ5UGF0Y2goKSB7XG4gICAgICAgIGNvbnN0IHBsdWdpbiA9IHRoaXM7XG4gICAgICAgIHJldHVybiAob3JpZ2luYWwpID0+IHtcbiAgICAgICAgICAgIHRoaXMuX2RpYWcuZGVidWcoJ1BhdGNoaW5nIHBnLkNsaWVudC5wcm90b3R5cGUucXVlcnknKTtcbiAgICAgICAgICAgIHJldHVybiBmdW5jdGlvbiBxdWVyeSguLi5hcmdzKSB7XG4gICAgICAgICAgICAgICAgaWYgKHV0aWxzLnNob3VsZFNraXBJbnN0cnVtZW50YXRpb24ocGx1Z2luLmdldENvbmZpZygpKSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gb3JpZ2luYWwuYXBwbHkodGhpcywgYXJncyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IHN0YXJ0VGltZSA9ICgwLCBjb3JlXzEuaHJUaW1lKSgpO1xuICAgICAgICAgICAgICAgIC8vIGNsaWVudC5xdWVyeSh0ZXh0LCBjYj8pLCBjbGllbnQucXVlcnkodGV4dCwgdmFsdWVzLCBjYj8pLCBhbmRcbiAgICAgICAgICAgICAgICAvLyBjbGllbnQucXVlcnkoY29uZmlnT2JqLCBjYj8pIGFyZSBhbGwgdmFsaWQgc2lnbmF0dXJlcy4gV2UgY29uc3RydWN0XG4gICAgICAgICAgICAgICAgLy8gYSBxdWVyeUNvbmZpZyBvYmogZnJvbSBhbGwgKHZhbGlkKSBzaWduYXR1cmVzIHRvIGJ1aWxkIHRoZSBzcGFuIGluIGFcbiAgICAgICAgICAgICAgICAvLyB1bmlmaWVkIHdheS4gV2UgdmVyaWZ5IHRoYXQgd2UgYXQgbGVhc3QgaGF2ZSBxdWVyeSB0ZXh0LCBhbmQgY29kZVxuICAgICAgICAgICAgICAgIC8vIGRlZmVuc2l2ZWx5IHdoZW4gZGVhbGluZyB3aXRoIGBxdWVyeUNvbmZpZ2AgYWZ0ZXIgdGhhdCAodG8gaGFuZGxlIGFsbFxuICAgICAgICAgICAgICAgIC8vIHRoZSBvdGhlciBpbnZhbGlkIGNhc2VzLCBsaWtlIGEgbm9uLWFycmF5IGZvciB2YWx1ZXMgYmVpbmcgcHJvdmlkZWQpLlxuICAgICAgICAgICAgICAgIC8vIFRoZSB0eXBlIGNhc3RzIGhlcmUgcmVmbGVjdCBvbmx5IHdoYXQgd2UndmUgYWN0dWFsbHkgdmFsaWRhdGVkLlxuICAgICAgICAgICAgICAgIGNvbnN0IGFyZzAgPSBhcmdzWzBdO1xuICAgICAgICAgICAgICAgIGNvbnN0IGZpcnN0QXJnSXNTdHJpbmcgPSB0eXBlb2YgYXJnMCA9PT0gJ3N0cmluZyc7XG4gICAgICAgICAgICAgICAgY29uc3QgZmlyc3RBcmdJc1F1ZXJ5T2JqZWN0V2l0aFRleHQgPSB1dGlscy5pc09iamVjdFdpdGhUZXh0U3RyaW5nKGFyZzApO1xuICAgICAgICAgICAgICAgIC8vIFRPRE86IHJlbW92ZSB0aGUgYGFzIC4uLmAgY2FzdHMgYmVsb3cgd2hlbiB0aGUgVFMgdmVyc2lvbiBpcyB1cGdyYWRlZC5cbiAgICAgICAgICAgICAgICAvLyBOZXdlciBUUyB2ZXJzaW9ucyB3aWxsIHVzZSB0aGUgcmVzdWx0IG9mIGZpcnN0QXJnSXNRdWVyeU9iamVjdFdpdGhUZXh0XG4gICAgICAgICAgICAgICAgLy8gdG8gcHJvcGVybHkgbmFycm93IGFyZzAsIGJ1dCBUUyA0LjMuNSBkb2VzIG5vdC5cbiAgICAgICAgICAgICAgICBjb25zdCBxdWVyeUNvbmZpZyA9IGZpcnN0QXJnSXNTdHJpbmdcbiAgICAgICAgICAgICAgICAgICAgPyB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0ZXh0OiBhcmcwLFxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWVzOiBBcnJheS5pc0FycmF5KGFyZ3NbMV0pID8gYXJnc1sxXSA6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICA6IGZpcnN0QXJnSXNRdWVyeU9iamVjdFdpdGhUZXh0XG4gICAgICAgICAgICAgICAgICAgICAgICA/IGFyZzBcbiAgICAgICAgICAgICAgICAgICAgICAgIDogdW5kZWZpbmVkO1xuICAgICAgICAgICAgICAgIGNvbnN0IGF0dHJpYnV0ZXMgPSB7XG4gICAgICAgICAgICAgICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX1NZU1RFTV06IHNlbWFudGljX2NvbnZlbnRpb25zXzEuREJTWVNURU1WQUxVRVNfUE9TVEdSRVNRTCxcbiAgICAgICAgICAgICAgICAgICAgW3NlbWNvbnZfMS5BVFRSX0RCX05BTUVTUEFDRV06IHRoaXMuZGF0YWJhc2UsXG4gICAgICAgICAgICAgICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLkFUVFJfU0VSVkVSX1BPUlRdOiB0aGlzLmNvbm5lY3Rpb25QYXJhbWV0ZXJzLnBvcnQsXG4gICAgICAgICAgICAgICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLkFUVFJfU0VSVkVSX0FERFJFU1NdOiB0aGlzLmNvbm5lY3Rpb25QYXJhbWV0ZXJzLmhvc3QsXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICBpZiAocXVlcnlDb25maWcgPT09IG51bGwgfHwgcXVlcnlDb25maWcgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHF1ZXJ5Q29uZmlnLnRleHQpIHtcbiAgICAgICAgICAgICAgICAgICAgYXR0cmlidXRlc1tzZW1jb252XzEuQVRUUl9EQl9PUEVSQVRJT05fTkFNRV0gPVxuICAgICAgICAgICAgICAgICAgICAgICAgdXRpbHMucGFyc2VOb3JtYWxpemVkT3BlcmF0aW9uTmFtZShxdWVyeUNvbmZpZyA9PT0gbnVsbCB8fCBxdWVyeUNvbmZpZyA9PT0gdm9pZCAwID8gdm9pZCAwIDogcXVlcnlDb25maWcudGV4dCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IHJlY29yZER1cmF0aW9uID0gKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBwbHVnaW4ucmVjb3JkT3BlcmF0aW9uRHVyYXRpb24oYXR0cmlidXRlcywgc3RhcnRUaW1lKTtcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIGNvbnN0IGluc3RydW1lbnRhdGlvbkNvbmZpZyA9IHBsdWdpbi5nZXRDb25maWcoKTtcbiAgICAgICAgICAgICAgICBjb25zdCBzcGFuID0gdXRpbHMuaGFuZGxlQ29uZmlnUXVlcnkuY2FsbCh0aGlzLCBwbHVnaW4udHJhY2VyLCBpbnN0cnVtZW50YXRpb25Db25maWcsIHF1ZXJ5Q29uZmlnKTtcbiAgICAgICAgICAgICAgICAvLyBNb2RpZnkgcXVlcnkgdGV4dCB3LyBhIHRyYWNpbmcgY29tbWVudCBiZWZvcmUgaW52b2tpbmcgb3JpZ2luYWwgZm9yXG4gICAgICAgICAgICAgICAgLy8gdHJhY2luZywgYnV0IG9ubHkgaWYgYXJnc1swXSBoYXMgb25lIG9mIG91ciBleHBlY3RlZCBzaGFwZXMuXG4gICAgICAgICAgICAgICAgaWYgKGluc3RydW1lbnRhdGlvbkNvbmZpZy5hZGRTcWxDb21tZW50ZXJDb21tZW50VG9RdWVyaWVzKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChmaXJzdEFyZ0lzU3RyaW5nKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhcmdzWzBdID0gKDAsIHNxbF9jb21tb25fMS5hZGRTcWxDb21tZW50ZXJDb21tZW50KShzcGFuLCBhcmcwKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIGlmIChmaXJzdEFyZ0lzUXVlcnlPYmplY3RXaXRoVGV4dCAmJiAhKCduYW1lJyBpbiBhcmcwKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gSW4gdGhlIGNhc2Ugb2YgYSBxdWVyeSBvYmplY3QsIHdlIG5lZWQgdG8gZW5zdXJlIHRoZXJlJ3Mgbm8gbmFtZSBmaWVsZFxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gYXMgdGhpcyBpbmRpY2F0ZXMgYSBwcmVwYXJlZCBxdWVyeSwgd2hlcmUgdGhlIGNvbW1lbnQgd291bGQgcmVtYWluIHRoZSBzYW1lXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBmb3IgZXZlcnkgaW52b2NhdGlvbiBhbmQgY29udGFpbiBhbiBvdXRkYXRlZCB0cmFjZSBjb250ZXh0LlxuICAgICAgICAgICAgICAgICAgICAgICAgYXJnc1swXSA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgYXJnMCksIHsgdGV4dDogKDAsIHNxbF9jb21tb25fMS5hZGRTcWxDb21tZW50ZXJDb21tZW50KShzcGFuLCBhcmcwLnRleHQpIH0pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIEJpbmQgY2FsbGJhY2sgKGlmIGFueSkgdG8gcGFyZW50IHNwYW4gKGlmIGFueSlcbiAgICAgICAgICAgICAgICBpZiAoYXJncy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHBhcmVudFNwYW4gPSBhcGlfMS50cmFjZS5nZXRTcGFuKGFwaV8xLmNvbnRleHQuYWN0aXZlKCkpO1xuICAgICAgICAgICAgICAgICAgICBpZiAodHlwZW9mIGFyZ3NbYXJncy5sZW5ndGggLSAxXSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gUGF0Y2ggUGFyYW1ldGVyUXVlcnkgY2FsbGJhY2tcbiAgICAgICAgICAgICAgICAgICAgICAgIGFyZ3NbYXJncy5sZW5ndGggLSAxXSA9IHV0aWxzLnBhdGNoQ2FsbGJhY2soaW5zdHJ1bWVudGF0aW9uQ29uZmlnLCBzcGFuLCBhcmdzW2FyZ3MubGVuZ3RoIC0gMV0sIC8vIG5iOiBub3QgdHlwZSBzYWZlLlxuICAgICAgICAgICAgICAgICAgICAgICAgYXR0cmlidXRlcywgcmVjb3JkRHVyYXRpb24pO1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gSWYgYSBwYXJlbnQgc3BhbiBleGlzdHMsIGJpbmQgdGhlIGNhbGxiYWNrXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAocGFyZW50U3Bhbikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFyZ3NbYXJncy5sZW5ndGggLSAxXSA9IGFwaV8xLmNvbnRleHQuYmluZChhcGlfMS5jb250ZXh0LmFjdGl2ZSgpLCBhcmdzW2FyZ3MubGVuZ3RoIC0gMV0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2UgaWYgKHR5cGVvZiAocXVlcnlDb25maWcgPT09IG51bGwgfHwgcXVlcnlDb25maWcgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHF1ZXJ5Q29uZmlnLmNhbGxiYWNrKSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gUGF0Y2ggQ29uZmlnUXVlcnkgY2FsbGJhY2tcbiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBjYWxsYmFjayA9IHV0aWxzLnBhdGNoQ2FsbGJhY2socGx1Z2luLmdldENvbmZpZygpLCBzcGFuLCBxdWVyeUNvbmZpZy5jYWxsYmFjaywgLy8gbmI6IG5vdCB0eXBlIHNhZmUuXG4gICAgICAgICAgICAgICAgICAgICAgICBhdHRyaWJ1dGVzLCByZWNvcmREdXJhdGlvbik7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBJZiBhIHBhcmVudCBzcGFuIGV4aXN0ZWQsIGJpbmQgdGhlIGNhbGxiYWNrXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAocGFyZW50U3Bhbikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhbGxiYWNrID0gYXBpXzEuY29udGV4dC5iaW5kKGFwaV8xLmNvbnRleHQuYWN0aXZlKCksIGNhbGxiYWNrKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGFyZ3NbMF0uY2FsbGJhY2sgPSBjYWxsYmFjaztcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCB7IHJlcXVlc3RIb29rIH0gPSBpbnN0cnVtZW50YXRpb25Db25maWc7XG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiByZXF1ZXN0SG9vayA9PT0gJ2Z1bmN0aW9uJyAmJiBxdWVyeUNvbmZpZykge1xuICAgICAgICAgICAgICAgICAgICAoMCwgaW5zdHJ1bWVudGF0aW9uXzEuc2FmZUV4ZWN1dGVJblRoZU1pZGRsZSkoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gcGljayBrZXlzIHRvIGV4cG9zZSBleHBsaWNpdGx5LCBzbyB3ZSdyZSBub3QgbGVha2luZyBwZyBwYWNrYWdlXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBpbnRlcm5hbHMgdGhhdCBhcmUgc3ViamVjdCB0byBjaGFuZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgZGF0YWJhc2UsIGhvc3QsIHBvcnQsIHVzZXIgfSA9IHRoaXMuY29ubmVjdGlvblBhcmFtZXRlcnM7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb25uZWN0aW9uID0geyBkYXRhYmFzZSwgaG9zdCwgcG9ydCwgdXNlciB9O1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWVzdEhvb2soc3Bhbiwge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbm5lY3Rpb24sXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcXVlcnk6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGV4dDogcXVlcnlDb25maWcudGV4dCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gbmI6IGlmIGBjbGllbnQucXVlcnlgIGlzIGNhbGxlZCB3aXRoIGlsbGVnYWwgYXJndW1lbnRzXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIChlLmcuLCBpZiBgcXVlcnlDb25maWcudmFsdWVzYCBpcyBwYXNzZWQgZXhwbGljaXRseSwgYnV0IGFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gbm9uLWFycmF5IGlzIGdpdmVuKSwgdGhlbiB0aGUgdHlwZSBjYXN0cyB3aWxsIGJlIHdyb25nLiBCdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gd2UgbGVhdmUgaXQgdXAgdG8gdGhlIHF1ZXJ5SG9vayB0byBoYW5kbGUgdGhhdCwgYW5kIHdlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGNhdGNoIGFuZCBzd2FsbG93IGFueSBlcnJvcnMgaXQgdGhyb3dzLiBUaGUgb3RoZXIgb3B0aW9uc1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBhcmUgYWxsIHdvcnNlLiBFLmcuLCB3ZSBjb3VsZCBsZWF2ZSBgcXVlcnlDb25maWcudmFsdWVzYFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBhbmQgYHF1ZXJ5Q29uZmlnLm5hbWVgIGFzIGB1bmtub3duYCwgYnV0IHRoZW4gdGhlIGhvb2sgYm9keVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyB3b3VsZCBiZSBmb3JjZWQgdG8gdmFsaWRhdGUgKG9yIGNhc3QpIHRoZW0gYmVmb3JlIHVzaW5nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIHRoZW0sIHdoaWNoIHNlZW1zIGluY3JlZGlibHkgY3VtYmVyc29tZSBnaXZlbiB0aGF0IHRoZXNlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGNhc3RzIHdpbGwgYmUgY29ycmVjdCA5OS45JSBvZiB0aGUgdGltZSAtLSBhbmQgcGcucXVlcnlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gd2lsbCBpbW1lZGlhdGVseSB0aHJvdyBkdXJpbmcgZGV2ZWxvcG1lbnQgaW4gdGhlIG90aGVyIC4xJVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBvZiBjYXNlcy4gQWx0ZXJuYXRpdmVseSwgd2UgY291bGQgc2ltcGx5IHNraXAgY2FsbGluZyB0aGVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gaG9vayB3aGVuIGB2YWx1ZXNgIG9yIGBuYW1lYCBkb24ndCBoYXZlIHRoZSBleHBlY3RlZCB0eXBlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBidXQgdGhhdCB3b3VsZCBhZGQgdW5uZWNlc3NhcnkgdmFsaWRhdGlvbiBvdmVyaGVhZCB0byBldmVyeVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBob29rIGludm9jYXRpb24gYW5kIHBvc3NpYmx5IGJlIGV2ZW4gbW9yZSBjb25mdXNpbmcvdW5leHBlY3RlZC5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWVzOiBxdWVyeUNvbmZpZy52YWx1ZXMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6IHF1ZXJ5Q29uZmlnLm5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB9LCBlcnIgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsdWdpbi5fZGlhZy5lcnJvcignRXJyb3IgcnVubmluZyBxdWVyeSBob29rJywgZXJyKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGxldCByZXN1bHQ7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0ID0gb3JpZ2luYWwuYXBwbHkodGhpcywgYXJncyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4uc2V0U3RhdHVzKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvZGU6IGFwaV8xLlNwYW5TdGF0dXNDb2RlLkVSUk9SLFxuICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogdXRpbHMuZ2V0RXJyb3JNZXNzYWdlKGUpLFxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgc3Bhbi5lbmQoKTtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gQmluZCBwcm9taXNlIHRvIHBhcmVudCBzcGFuIGFuZCBlbmQgdGhlIHNwYW5cbiAgICAgICAgICAgICAgICBpZiAocmVzdWx0IGluc3RhbmNlb2YgUHJvbWlzZSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gcmVzdWx0XG4gICAgICAgICAgICAgICAgICAgICAgICAudGhlbigocmVzdWx0KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBSZXR1cm4gYSBwYXNzLWFsb25nIHByb21pc2Ugd2hpY2ggZW5kcyB0aGUgc3BhbiBhbmQgdGhlbiBnb2VzIHRvIHVzZXIncyBvcmlnIHJlc29sdmVyc1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKHJlc29sdmUgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHV0aWxzLmhhbmRsZUV4ZWN1dGlvblJlc3VsdChwbHVnaW4uZ2V0Q29uZmlnKCksIHNwYW4sIHJlc3VsdCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVjb3JkRHVyYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcGFuLmVuZCgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlc29sdmUocmVzdWx0KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAgICAgLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChfLCByZWplY3QpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzcGFuLnNldFN0YXR1cyh7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvZGU6IGFwaV8xLlNwYW5TdGF0dXNDb2RlLkVSUk9SLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlY29yZER1cmF0aW9uKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Bhbi5lbmQoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZWplY3QoZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyBlbHNlIHJldHVybnMgdm9pZFxuICAgICAgICAgICAgICAgIHJldHVybiByZXN1bHQ7IC8vIHZvaWRcbiAgICAgICAgICAgIH07XG4gICAgICAgIH07XG4gICAgfVxuICAgIF9zZXRQb29sQ29ubmVjdEV2ZW50TGlzdGVuZXJzKHBnUG9vbCkge1xuICAgICAgICBpZiAocGdQb29sW2ludGVybmFsX3R5cGVzXzEuRVZFTlRfTElTVEVORVJTX1NFVF0pXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIGNvbnN0IHBvb2xOYW1lID0gdXRpbHMuZ2V0UG9vbE5hbWUocGdQb29sLm9wdGlvbnMpO1xuICAgICAgICBwZ1Bvb2wub24oJ2Nvbm5lY3QnLCAoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLl9jb25uZWN0aW9uc0NvdW50ZXIgPSB1dGlscy51cGRhdGVDb3VudGVyKHBvb2xOYW1lLCBwZ1Bvb2wsIHRoaXMuX2Nvbm5lY3Rpb25zQ291bnQsIHRoaXMuX2Nvbm5lY3Rpb25QZW5kaW5nUmVxdWVzdHMsIHRoaXMuX2Nvbm5lY3Rpb25zQ291bnRlcik7XG4gICAgICAgIH0pO1xuICAgICAgICBwZ1Bvb2wub24oJ2FjcXVpcmUnLCAoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLl9jb25uZWN0aW9uc0NvdW50ZXIgPSB1dGlscy51cGRhdGVDb3VudGVyKHBvb2xOYW1lLCBwZ1Bvb2wsIHRoaXMuX2Nvbm5lY3Rpb25zQ291bnQsIHRoaXMuX2Nvbm5lY3Rpb25QZW5kaW5nUmVxdWVzdHMsIHRoaXMuX2Nvbm5lY3Rpb25zQ291bnRlcik7XG4gICAgICAgIH0pO1xuICAgICAgICBwZ1Bvb2wub24oJ3JlbW92ZScsICgpID0+IHtcbiAgICAgICAgICAgIHRoaXMuX2Nvbm5lY3Rpb25zQ291bnRlciA9IHV0aWxzLnVwZGF0ZUNvdW50ZXIocG9vbE5hbWUsIHBnUG9vbCwgdGhpcy5fY29ubmVjdGlvbnNDb3VudCwgdGhpcy5fY29ubmVjdGlvblBlbmRpbmdSZXF1ZXN0cywgdGhpcy5fY29ubmVjdGlvbnNDb3VudGVyKTtcbiAgICAgICAgfSk7XG4gICAgICAgIHBnUG9vbC5vbigncmVsZWFzZScsICgpID0+IHtcbiAgICAgICAgICAgIHRoaXMuX2Nvbm5lY3Rpb25zQ291bnRlciA9IHV0aWxzLnVwZGF0ZUNvdW50ZXIocG9vbE5hbWUsIHBnUG9vbCwgdGhpcy5fY29ubmVjdGlvbnNDb3VudCwgdGhpcy5fY29ubmVjdGlvblBlbmRpbmdSZXF1ZXN0cywgdGhpcy5fY29ubmVjdGlvbnNDb3VudGVyKTtcbiAgICAgICAgfSk7XG4gICAgICAgIHBnUG9vbFtpbnRlcm5hbF90eXBlc18xLkVWRU5UX0xJU1RFTkVSU19TRVRdID0gdHJ1ZTtcbiAgICB9XG4gICAgX2dldFBvb2xDb25uZWN0UGF0Y2goKSB7XG4gICAgICAgIGNvbnN0IHBsdWdpbiA9IHRoaXM7XG4gICAgICAgIHJldHVybiAob3JpZ2luYWxDb25uZWN0KSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gZnVuY3Rpb24gY29ubmVjdChjYWxsYmFjaykge1xuICAgICAgICAgICAgICAgIGlmICh1dGlscy5zaG91bGRTa2lwSW5zdHJ1bWVudGF0aW9uKHBsdWdpbi5nZXRDb25maWcoKSkpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG9yaWdpbmFsQ29ubmVjdC5jYWxsKHRoaXMsIGNhbGxiYWNrKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gc2V0dXAgc3BhblxuICAgICAgICAgICAgICAgIGNvbnN0IHNwYW4gPSBwbHVnaW4udHJhY2VyLnN0YXJ0U3BhbihTcGFuTmFtZXNfMS5TcGFuTmFtZXMuUE9PTF9DT05ORUNULCB7XG4gICAgICAgICAgICAgICAgICAgIGtpbmQ6IGFwaV8xLlNwYW5LaW5kLkNMSUVOVCxcbiAgICAgICAgICAgICAgICAgICAgYXR0cmlidXRlczogdXRpbHMuZ2V0U2VtYW50aWNBdHRyaWJ1dGVzRnJvbVBvb2wodGhpcy5vcHRpb25zKSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBwbHVnaW4uX3NldFBvb2xDb25uZWN0RXZlbnRMaXN0ZW5lcnModGhpcyk7XG4gICAgICAgICAgICAgICAgaWYgKGNhbGxiYWNrKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHBhcmVudFNwYW4gPSBhcGlfMS50cmFjZS5nZXRTcGFuKGFwaV8xLmNvbnRleHQuYWN0aXZlKCkpO1xuICAgICAgICAgICAgICAgICAgICBjYWxsYmFjayA9IHV0aWxzLnBhdGNoQ2FsbGJhY2tQR1Bvb2woc3BhbiwgY2FsbGJhY2spO1xuICAgICAgICAgICAgICAgICAgICAvLyBJZiBhIHBhcmVudCBzcGFuIGV4aXN0cywgYmluZCB0aGUgY2FsbGJhY2tcbiAgICAgICAgICAgICAgICAgICAgaWYgKHBhcmVudFNwYW4pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNhbGxiYWNrID0gYXBpXzEuY29udGV4dC5iaW5kKGFwaV8xLmNvbnRleHQuYWN0aXZlKCksIGNhbGxiYWNrKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCBjb25uZWN0UmVzdWx0ID0gYXBpXzEuY29udGV4dC53aXRoKGFwaV8xLnRyYWNlLnNldFNwYW4oYXBpXzEuY29udGV4dC5hY3RpdmUoKSwgc3BhbiksICgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG9yaWdpbmFsQ29ubmVjdC5jYWxsKHRoaXMsIGNhbGxiYWNrKTtcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm4gaGFuZGxlQ29ubmVjdFJlc3VsdChzcGFuLCBjb25uZWN0UmVzdWx0KTtcbiAgICAgICAgICAgIH07XG4gICAgICAgIH07XG4gICAgfVxufVxuZXhwb3J0cy5QZ0luc3RydW1lbnRhdGlvbiA9IFBnSW5zdHJ1bWVudGF0aW9uO1xuZnVuY3Rpb24gaGFuZGxlQ29ubmVjdFJlc3VsdChzcGFuLCBjb25uZWN0UmVzdWx0KSB7XG4gICAgaWYgKCEoY29ubmVjdFJlc3VsdCBpbnN0YW5jZW9mIFByb21pc2UpKSB7XG4gICAgICAgIHJldHVybiBjb25uZWN0UmVzdWx0O1xuICAgIH1cbiAgICBjb25zdCBjb25uZWN0UmVzdWx0UHJvbWlzZSA9IGNvbm5lY3RSZXN1bHQ7XG4gICAgcmV0dXJuIGFwaV8xLmNvbnRleHQuYmluZChhcGlfMS5jb250ZXh0LmFjdGl2ZSgpLCBjb25uZWN0UmVzdWx0UHJvbWlzZVxuICAgICAgICAudGhlbihyZXN1bHQgPT4ge1xuICAgICAgICBzcGFuLmVuZCgpO1xuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH0pXG4gICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHtcbiAgICAgICAgc3Bhbi5zZXRTdGF0dXMoe1xuICAgICAgICAgICAgY29kZTogYXBpXzEuU3BhblN0YXR1c0NvZGUuRVJST1IsXG4gICAgICAgICAgICBtZXNzYWdlOiB1dGlscy5nZXRFcnJvck1lc3NhZ2UoZXJyb3IpLFxuICAgICAgICB9KTtcbiAgICAgICAgc3Bhbi5lbmQoKTtcbiAgICAgICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTtcbiAgICB9KSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbnN0cnVtZW50YXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.EVENT_LISTENERS_SET = void 0;\nexports.EVENT_LISTENERS_SET = Symbol('opentelemetry.instrumentation.pg.eventListenersSet');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.METRIC_DB_CLIENT_OPERATION_DURATION = exports.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = exports.METRIC_DB_CLIENT_CONNECTION_COUNT = exports.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = exports.DB_CLIENT_CONNECTION_STATE_VALUE_USED = exports.ATTR_DB_OPERATION_NAME = exports.ATTR_DB_NAMESPACE = exports.ATTR_DB_CLIENT_CONNECTION_STATE = exports.ATTR_DB_CLIENT_CONNECTION_POOL_NAME = void 0;\n/**\n * The name of the connection pool; unique within the instrumented application. In case the connection pool implementation doesn't provide a name, instrumentation **SHOULD** use a combination of parameters that would make the name unique, for example, combining attributes `server.address`, `server.port`, and `db.namespace`, formatted as `server.address:server.port/db.namespace`. Instrumentations that generate connection pool name following different patterns **SHOULD** document it.\n *\n * @example myDataSource\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_CLIENT_CONNECTION_POOL_NAME = 'db.client.connection.pool.name';\n/**\n * The state of a connection in the pool\n *\n * @example idle\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_CLIENT_CONNECTION_STATE = 'db.client.connection.state';\n/**\n * The name of the database, fully qualified within the server address and port.\n *\n * @example customers\n * @example test.users\n *\n * @note If a database system has multiple namespace components, they **SHOULD** be concatenated (potentially using database system specific conventions) from most general to most specific namespace component, and more specific namespaces **SHOULD NOT** be captured without the more general namespaces, to ensure that \"startswith\" queries for the more general namespaces will be valid.\n * Semantic conventions for individual database systems **SHOULD** document what `db.namespace` means in the context of that system.\n * It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_NAMESPACE = 'db.namespace';\n/**\n * The name of the operation or command being executed.\n *\n * @example findAndModify\n * @example HMSET\n * @example SELECT\n *\n * @note It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * If the operation name is parsed from the query text, it **SHOULD** be the first operation name found in the query.\n * For batch operations, if the individual operations are known to have the same operation name then that operation name **SHOULD** be used prepended by `BATCH `, otherwise `db.operation.name` **SHOULD** be `BATCH` or some other database system specific term if more applicable.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_OPERATION_NAME = 'db.operation.name';\n/**\n * Enum value \"used\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexports.DB_CLIENT_CONNECTION_STATE_VALUE_USED = 'used';\n/**\n * Enum value \"idle\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexports.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = 'idle';\n/**\n * The number of connections that are currently in state described by the `state` attribute\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_CONNECTION_COUNT = 'db.client.connection.count';\n/**\n * The number of current pending requests for an open connection\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = 'db.client.connection.pending_requests';\n/**\n * Duration of database client operations.\n *\n * @note Batch operations **SHOULD** be recorded as a single operation.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_OPERATION_DURATION = 'db.client.operation.duration';\n//# sourceMappingURL=semconv.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYjkwY2QxYTUzN2RhZmUyOTRjZWU5NjNlNDQzMmI0NmEvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1wZy9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYjkwY2QxYTUzN2RhZmUyOTRjZWU5NjNlNDQzMmI0NmFcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1wZ1xcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isObjectWithTextString = exports.getErrorMessage = exports.patchClientConnectCallback = exports.patchCallbackPGPool = exports.updateCounter = exports.getPoolName = exports.patchCallback = exports.handleExecutionResult = exports.handleConfigQuery = exports.shouldSkipInstrumentation = exports.getSemanticAttributesFromPool = exports.getSemanticAttributesFromConnection = exports.getConnectionString = exports.parseNormalizedOperationName = exports.getQuerySpanName = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst semconv_1 = __webpack_require__(/*! ./semconv */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst SpanNames_1 = __webpack_require__(/*! ./enums/SpanNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\");\n/**\n * Helper function to get a low cardinality span name from whatever info we have\n * about the query.\n *\n * This is tricky, because we don't have most of the information (table name,\n * operation name, etc) the spec recommends using to build a low-cardinality\n * value w/o parsing. So, we use db.name and assume that, if the query's a named\n * prepared statement, those `name` values will be low cardinality. If we don't\n * have a named prepared statement, we try to parse an operation (despite the\n * spec's warnings).\n *\n * @params dbName The name of the db against which this query is being issued,\n *   which could be missing if no db name was given at the time that the\n *   connection was established.\n * @params queryConfig Information we have about the query being issued, typed\n *   to reflect only the validation we've actually done on the args to\n *   `client.query()`. This will be undefined if `client.query()` was called\n *   with invalid arguments.\n */\nfunction getQuerySpanName(dbName, queryConfig) {\n    // NB: when the query config is invalid, we omit the dbName too, so that\n    // someone (or some tool) reading the span name doesn't misinterpret the\n    // dbName as being a prepared statement or sql commit name.\n    if (!queryConfig)\n        return SpanNames_1.SpanNames.QUERY_PREFIX;\n    // Either the name of a prepared statement; or an attempted parse\n    // of the SQL command, normalized to uppercase; or unknown.\n    const command = typeof queryConfig.name === 'string' && queryConfig.name\n        ? queryConfig.name\n        : parseNormalizedOperationName(queryConfig.text);\n    return `${SpanNames_1.SpanNames.QUERY_PREFIX}:${command}${dbName ? ` ${dbName}` : ''}`;\n}\nexports.getQuerySpanName = getQuerySpanName;\nfunction parseNormalizedOperationName(queryText) {\n    const indexOfFirstSpace = queryText.indexOf(' ');\n    let sqlCommand = indexOfFirstSpace === -1\n        ? queryText\n        : queryText.slice(0, indexOfFirstSpace);\n    sqlCommand = sqlCommand.toUpperCase();\n    // Handle query text being \"COMMIT;\", which has an extra semicolon before the space.\n    return sqlCommand.endsWith(';') ? sqlCommand.slice(0, -1) : sqlCommand;\n}\nexports.parseNormalizedOperationName = parseNormalizedOperationName;\nfunction getConnectionString(params) {\n    const host = params.host || 'localhost';\n    const port = params.port || 5432;\n    const database = params.database || '';\n    return `postgresql://${host}:${port}/${database}`;\n}\nexports.getConnectionString = getConnectionString;\nfunction getPort(port) {\n    // Port may be NaN as parseInt() is used on the value, passing null will result in NaN being parsed.\n    // https://github.com/brianc/node-postgres/blob/2a8efbee09a284be12748ed3962bc9b816965e36/packages/pg/lib/connection-parameters.js#L66\n    if (Number.isInteger(port)) {\n        return port;\n    }\n    // Unable to find the default used in pg code, so falling back to 'undefined'.\n    return undefined;\n}\nfunction getSemanticAttributesFromConnection(params) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: params.database,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getConnectionString(params),\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: params.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: getPort(params.port),\n        [semantic_conventions_1.SEMATTRS_DB_USER]: params.user,\n    };\n}\nexports.getSemanticAttributesFromConnection = getSemanticAttributesFromConnection;\nfunction getSemanticAttributesFromPool(params) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: params.database,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getConnectionString(params),\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: params.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: getPort(params.port),\n        [semantic_conventions_1.SEMATTRS_DB_USER]: params.user,\n        [AttributeNames_1.AttributeNames.IDLE_TIMEOUT_MILLIS]: params.idleTimeoutMillis,\n        [AttributeNames_1.AttributeNames.MAX_CLIENT]: params.maxClient,\n    };\n}\nexports.getSemanticAttributesFromPool = getSemanticAttributesFromPool;\nfunction shouldSkipInstrumentation(instrumentationConfig) {\n    return (instrumentationConfig.requireParentSpan === true &&\n        api_1.trace.getSpan(api_1.context.active()) === undefined);\n}\nexports.shouldSkipInstrumentation = shouldSkipInstrumentation;\n// Create a span from our normalized queryConfig object,\n// or return a basic span if no queryConfig was given/could be created.\nfunction handleConfigQuery(tracer, instrumentationConfig, queryConfig) {\n    // Create child span.\n    const { connectionParameters } = this;\n    const dbName = connectionParameters.database;\n    const spanName = getQuerySpanName(dbName, queryConfig);\n    const span = tracer.startSpan(spanName, {\n        kind: api_1.SpanKind.CLIENT,\n        attributes: getSemanticAttributesFromConnection(connectionParameters),\n    });\n    if (!queryConfig) {\n        return span;\n    }\n    // Set attributes\n    if (queryConfig.text) {\n        span.setAttribute(semantic_conventions_1.SEMATTRS_DB_STATEMENT, queryConfig.text);\n    }\n    if (instrumentationConfig.enhancedDatabaseReporting &&\n        Array.isArray(queryConfig.values)) {\n        try {\n            const convertedValues = queryConfig.values.map(value => {\n                if (value == null) {\n                    return 'null';\n                }\n                else if (value instanceof Buffer) {\n                    return value.toString();\n                }\n                else if (typeof value === 'object') {\n                    if (typeof value.toPostgres === 'function') {\n                        return value.toPostgres();\n                    }\n                    return JSON.stringify(value);\n                }\n                else {\n                    //string, number\n                    return value.toString();\n                }\n            });\n            span.setAttribute(AttributeNames_1.AttributeNames.PG_VALUES, convertedValues);\n        }\n        catch (e) {\n            api_1.diag.error('failed to stringify ', queryConfig.values, e);\n        }\n    }\n    // Set plan name attribute, if present\n    if (typeof queryConfig.name === 'string') {\n        span.setAttribute(AttributeNames_1.AttributeNames.PG_PLAN, queryConfig.name);\n    }\n    return span;\n}\nexports.handleConfigQuery = handleConfigQuery;\nfunction handleExecutionResult(config, span, pgResult) {\n    if (typeof config.responseHook === 'function') {\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            config.responseHook(span, {\n                data: pgResult,\n            });\n        }, err => {\n            if (err) {\n                api_1.diag.error('Error running response hook', err);\n            }\n        }, true);\n    }\n}\nexports.handleExecutionResult = handleExecutionResult;\nfunction patchCallback(instrumentationConfig, span, cb, attributes, recordDuration) {\n    return function patchedCallback(err, res) {\n        if (err) {\n            if (Object.prototype.hasOwnProperty.call(err, 'code')) {\n                attributes[semantic_conventions_1.ATTR_ERROR_TYPE] = err['code'];\n            }\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        else {\n            handleExecutionResult(instrumentationConfig, span, res);\n        }\n        recordDuration();\n        span.end();\n        cb.call(this, err, res);\n    };\n}\nexports.patchCallback = patchCallback;\nfunction getPoolName(pool) {\n    let poolName = '';\n    poolName += ((pool === null || pool === void 0 ? void 0 : pool.host) ? `${pool.host}` : 'unknown_host') + ':';\n    poolName += ((pool === null || pool === void 0 ? void 0 : pool.port) ? `${pool.port}` : 'unknown_port') + '/';\n    poolName += (pool === null || pool === void 0 ? void 0 : pool.database) ? `${pool.database}` : 'unknown_database';\n    return poolName.trim();\n}\nexports.getPoolName = getPoolName;\nfunction updateCounter(poolName, pool, connectionCount, connectionPendingRequests, latestCounter) {\n    const all = pool.totalCount;\n    const pending = pool.waitingCount;\n    const idle = pool.idleCount;\n    const used = all - idle;\n    connectionCount.add(used - latestCounter.used, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_STATE]: semconv_1.DB_CLIENT_CONNECTION_STATE_VALUE_USED,\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    connectionCount.add(idle - latestCounter.idle, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_STATE]: semconv_1.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE,\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    connectionPendingRequests.add(pending - latestCounter.pending, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    return { used: used, idle: idle, pending: pending };\n}\nexports.updateCounter = updateCounter;\nfunction patchCallbackPGPool(span, cb) {\n    return function patchedCallback(err, res, done) {\n        if (err) {\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        span.end();\n        cb.call(this, err, res, done);\n    };\n}\nexports.patchCallbackPGPool = patchCallbackPGPool;\nfunction patchClientConnectCallback(span, cb) {\n    return function patchedClientConnectCallback(err) {\n        if (err) {\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        span.end();\n        cb.apply(this, arguments);\n    };\n}\nexports.patchClientConnectCallback = patchClientConnectCallback;\n/**\n * Attempt to get a message string from a thrown value, while being quite\n * defensive, to recognize the fact that, in JS, any kind of value (even\n * primitives) can be thrown.\n */\nfunction getErrorMessage(e) {\n    return typeof e === 'object' && e !== null && 'message' in e\n        ? String(e.message)\n        : undefined;\n}\nexports.getErrorMessage = getErrorMessage;\nfunction isObjectWithTextString(it) {\n    var _a;\n    return (typeof it === 'object' &&\n        typeof ((_a = it) === null || _a === void 0 ? void 0 : _a.text) === 'string');\n}\nexports.isObjectWithTextString = isObjectWithTextString;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYjkwY2QxYTUzN2RhZmUyOTRjZWU5NjNlNDQzMmI0NmEvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1wZy9idWlsZC9zcmMvdXRpbHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsOEJBQThCLEdBQUcsdUJBQXVCLEdBQUcsa0NBQWtDLEdBQUcsMkJBQTJCLEdBQUcscUJBQXFCLEdBQUcsbUJBQW1CLEdBQUcscUJBQXFCLEdBQUcsNkJBQTZCLEdBQUcseUJBQXlCLEdBQUcsaUNBQWlDLEdBQUcscUNBQXFDLEdBQUcsMkNBQTJDLEdBQUcsMkJBQTJCLEdBQUcsb0NBQW9DLEdBQUcsd0JBQXdCO0FBQ3ZkLGNBQWMsbUJBQU8sQ0FBQyw2SUFBb0I7QUFDMUMseUJBQXlCLG1CQUFPLENBQUMsbU5BQXdCO0FBQ3pELCtCQUErQixtQkFBTyxDQUFDLGlNQUFxQztBQUM1RSxrQkFBa0IsbUJBQU8sQ0FBQyx5TEFBVztBQUNyQywwQkFBMEIsbUJBQU8sQ0FBQyx5TUFBZ0M7QUFDbEUsb0JBQW9CLG1CQUFPLENBQUMseU1BQW1CO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdEO0FBQ2hELG9EQUFvRDtBQUNwRDtBQUNBO0FBQ0E7QUFDQSxjQUFjLG1DQUFtQyxHQUFHLFFBQVEsRUFBRSxhQUFhLE9BQU8sT0FBTztBQUN6RjtBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUM7QUFDdkMsaUNBQWlDO0FBQ2pDO0FBQ0Esb0NBQW9DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLEtBQUssR0FBRyxLQUFLLEdBQUcsU0FBUztBQUNwRDtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQztBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHVCQUF1QjtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0EsOEVBQThFLFVBQVU7QUFDeEYsOEVBQThFLFVBQVU7QUFDeEYsaUZBQWlGLGNBQWM7QUFDL0Y7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMLGFBQWE7QUFDYjtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCO0FBQzlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2I5MGNkMWE1MzdkYWZlMjk0Y2VlOTYzZTQ0MzJiNDZhXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tcGdcXGJ1aWxkXFxzcmNcXHV0aWxzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuaXNPYmplY3RXaXRoVGV4dFN0cmluZyA9IGV4cG9ydHMuZ2V0RXJyb3JNZXNzYWdlID0gZXhwb3J0cy5wYXRjaENsaWVudENvbm5lY3RDYWxsYmFjayA9IGV4cG9ydHMucGF0Y2hDYWxsYmFja1BHUG9vbCA9IGV4cG9ydHMudXBkYXRlQ291bnRlciA9IGV4cG9ydHMuZ2V0UG9vbE5hbWUgPSBleHBvcnRzLnBhdGNoQ2FsbGJhY2sgPSBleHBvcnRzLmhhbmRsZUV4ZWN1dGlvblJlc3VsdCA9IGV4cG9ydHMuaGFuZGxlQ29uZmlnUXVlcnkgPSBleHBvcnRzLnNob3VsZFNraXBJbnN0cnVtZW50YXRpb24gPSBleHBvcnRzLmdldFNlbWFudGljQXR0cmlidXRlc0Zyb21Qb29sID0gZXhwb3J0cy5nZXRTZW1hbnRpY0F0dHJpYnV0ZXNGcm9tQ29ubmVjdGlvbiA9IGV4cG9ydHMuZ2V0Q29ubmVjdGlvblN0cmluZyA9IGV4cG9ydHMucGFyc2VOb3JtYWxpemVkT3BlcmF0aW9uTmFtZSA9IGV4cG9ydHMuZ2V0UXVlcnlTcGFuTmFtZSA9IHZvaWQgMDtcbmNvbnN0IGFwaV8xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L2FwaVwiKTtcbmNvbnN0IEF0dHJpYnV0ZU5hbWVzXzEgPSByZXF1aXJlKFwiLi9lbnVtcy9BdHRyaWJ1dGVOYW1lc1wiKTtcbmNvbnN0IHNlbWFudGljX2NvbnZlbnRpb25zXzEgPSByZXF1aXJlKFwiQG9wZW50ZWxlbWV0cnkvc2VtYW50aWMtY29udmVudGlvbnNcIik7XG5jb25zdCBzZW1jb252XzEgPSByZXF1aXJlKFwiLi9zZW1jb252XCIpO1xuY29uc3QgaW5zdHJ1bWVudGF0aW9uXzEgPSByZXF1aXJlKFwiQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uXCIpO1xuY29uc3QgU3Bhbk5hbWVzXzEgPSByZXF1aXJlKFwiLi9lbnVtcy9TcGFuTmFtZXNcIik7XG4vKipcbiAqIEhlbHBlciBmdW5jdGlvbiB0byBnZXQgYSBsb3cgY2FyZGluYWxpdHkgc3BhbiBuYW1lIGZyb20gd2hhdGV2ZXIgaW5mbyB3ZSBoYXZlXG4gKiBhYm91dCB0aGUgcXVlcnkuXG4gKlxuICogVGhpcyBpcyB0cmlja3ksIGJlY2F1c2Ugd2UgZG9uJ3QgaGF2ZSBtb3N0IG9mIHRoZSBpbmZvcm1hdGlvbiAodGFibGUgbmFtZSxcbiAqIG9wZXJhdGlvbiBuYW1lLCBldGMpIHRoZSBzcGVjIHJlY29tbWVuZHMgdXNpbmcgdG8gYnVpbGQgYSBsb3ctY2FyZGluYWxpdHlcbiAqIHZhbHVlIHcvbyBwYXJzaW5nLiBTbywgd2UgdXNlIGRiLm5hbWUgYW5kIGFzc3VtZSB0aGF0LCBpZiB0aGUgcXVlcnkncyBhIG5hbWVkXG4gKiBwcmVwYXJlZCBzdGF0ZW1lbnQsIHRob3NlIGBuYW1lYCB2YWx1ZXMgd2lsbCBiZSBsb3cgY2FyZGluYWxpdHkuIElmIHdlIGRvbid0XG4gKiBoYXZlIGEgbmFtZWQgcHJlcGFyZWQgc3RhdGVtZW50LCB3ZSB0cnkgdG8gcGFyc2UgYW4gb3BlcmF0aW9uIChkZXNwaXRlIHRoZVxuICogc3BlYydzIHdhcm5pbmdzKS5cbiAqXG4gKiBAcGFyYW1zIGRiTmFtZSBUaGUgbmFtZSBvZiB0aGUgZGIgYWdhaW5zdCB3aGljaCB0aGlzIHF1ZXJ5IGlzIGJlaW5nIGlzc3VlZCxcbiAqICAgd2hpY2ggY291bGQgYmUgbWlzc2luZyBpZiBubyBkYiBuYW1lIHdhcyBnaXZlbiBhdCB0aGUgdGltZSB0aGF0IHRoZVxuICogICBjb25uZWN0aW9uIHdhcyBlc3RhYmxpc2hlZC5cbiAqIEBwYXJhbXMgcXVlcnlDb25maWcgSW5mb3JtYXRpb24gd2UgaGF2ZSBhYm91dCB0aGUgcXVlcnkgYmVpbmcgaXNzdWVkLCB0eXBlZFxuICogICB0byByZWZsZWN0IG9ubHkgdGhlIHZhbGlkYXRpb24gd2UndmUgYWN0dWFsbHkgZG9uZSBvbiB0aGUgYXJncyB0b1xuICogICBgY2xpZW50LnF1ZXJ5KClgLiBUaGlzIHdpbGwgYmUgdW5kZWZpbmVkIGlmIGBjbGllbnQucXVlcnkoKWAgd2FzIGNhbGxlZFxuICogICB3aXRoIGludmFsaWQgYXJndW1lbnRzLlxuICovXG5mdW5jdGlvbiBnZXRRdWVyeVNwYW5OYW1lKGRiTmFtZSwgcXVlcnlDb25maWcpIHtcbiAgICAvLyBOQjogd2hlbiB0aGUgcXVlcnkgY29uZmlnIGlzIGludmFsaWQsIHdlIG9taXQgdGhlIGRiTmFtZSB0b28sIHNvIHRoYXRcbiAgICAvLyBzb21lb25lIChvciBzb21lIHRvb2wpIHJlYWRpbmcgdGhlIHNwYW4gbmFtZSBkb2Vzbid0IG1pc2ludGVycHJldCB0aGVcbiAgICAvLyBkYk5hbWUgYXMgYmVpbmcgYSBwcmVwYXJlZCBzdGF0ZW1lbnQgb3Igc3FsIGNvbW1pdCBuYW1lLlxuICAgIGlmICghcXVlcnlDb25maWcpXG4gICAgICAgIHJldHVybiBTcGFuTmFtZXNfMS5TcGFuTmFtZXMuUVVFUllfUFJFRklYO1xuICAgIC8vIEVpdGhlciB0aGUgbmFtZSBvZiBhIHByZXBhcmVkIHN0YXRlbWVudDsgb3IgYW4gYXR0ZW1wdGVkIHBhcnNlXG4gICAgLy8gb2YgdGhlIFNRTCBjb21tYW5kLCBub3JtYWxpemVkIHRvIHVwcGVyY2FzZTsgb3IgdW5rbm93bi5cbiAgICBjb25zdCBjb21tYW5kID0gdHlwZW9mIHF1ZXJ5Q29uZmlnLm5hbWUgPT09ICdzdHJpbmcnICYmIHF1ZXJ5Q29uZmlnLm5hbWVcbiAgICAgICAgPyBxdWVyeUNvbmZpZy5uYW1lXG4gICAgICAgIDogcGFyc2VOb3JtYWxpemVkT3BlcmF0aW9uTmFtZShxdWVyeUNvbmZpZy50ZXh0KTtcbiAgICByZXR1cm4gYCR7U3Bhbk5hbWVzXzEuU3Bhbk5hbWVzLlFVRVJZX1BSRUZJWH06JHtjb21tYW5kfSR7ZGJOYW1lID8gYCAke2RiTmFtZX1gIDogJyd9YDtcbn1cbmV4cG9ydHMuZ2V0UXVlcnlTcGFuTmFtZSA9IGdldFF1ZXJ5U3Bhbk5hbWU7XG5mdW5jdGlvbiBwYXJzZU5vcm1hbGl6ZWRPcGVyYXRpb25OYW1lKHF1ZXJ5VGV4dCkge1xuICAgIGNvbnN0IGluZGV4T2ZGaXJzdFNwYWNlID0gcXVlcnlUZXh0LmluZGV4T2YoJyAnKTtcbiAgICBsZXQgc3FsQ29tbWFuZCA9IGluZGV4T2ZGaXJzdFNwYWNlID09PSAtMVxuICAgICAgICA/IHF1ZXJ5VGV4dFxuICAgICAgICA6IHF1ZXJ5VGV4dC5zbGljZSgwLCBpbmRleE9mRmlyc3RTcGFjZSk7XG4gICAgc3FsQ29tbWFuZCA9IHNxbENvbW1hbmQudG9VcHBlckNhc2UoKTtcbiAgICAvLyBIYW5kbGUgcXVlcnkgdGV4dCBiZWluZyBcIkNPTU1JVDtcIiwgd2hpY2ggaGFzIGFuIGV4dHJhIHNlbWljb2xvbiBiZWZvcmUgdGhlIHNwYWNlLlxuICAgIHJldHVybiBzcWxDb21tYW5kLmVuZHNXaXRoKCc7JykgPyBzcWxDb21tYW5kLnNsaWNlKDAsIC0xKSA6IHNxbENvbW1hbmQ7XG59XG5leHBvcnRzLnBhcnNlTm9ybWFsaXplZE9wZXJhdGlvbk5hbWUgPSBwYXJzZU5vcm1hbGl6ZWRPcGVyYXRpb25OYW1lO1xuZnVuY3Rpb24gZ2V0Q29ubmVjdGlvblN0cmluZyhwYXJhbXMpIHtcbiAgICBjb25zdCBob3N0ID0gcGFyYW1zLmhvc3QgfHwgJ2xvY2FsaG9zdCc7XG4gICAgY29uc3QgcG9ydCA9IHBhcmFtcy5wb3J0IHx8IDU0MzI7XG4gICAgY29uc3QgZGF0YWJhc2UgPSBwYXJhbXMuZGF0YWJhc2UgfHwgJyc7XG4gICAgcmV0dXJuIGBwb3N0Z3Jlc3FsOi8vJHtob3N0fToke3BvcnR9LyR7ZGF0YWJhc2V9YDtcbn1cbmV4cG9ydHMuZ2V0Q29ubmVjdGlvblN0cmluZyA9IGdldENvbm5lY3Rpb25TdHJpbmc7XG5mdW5jdGlvbiBnZXRQb3J0KHBvcnQpIHtcbiAgICAvLyBQb3J0IG1heSBiZSBOYU4gYXMgcGFyc2VJbnQoKSBpcyB1c2VkIG9uIHRoZSB2YWx1ZSwgcGFzc2luZyBudWxsIHdpbGwgcmVzdWx0IGluIE5hTiBiZWluZyBwYXJzZWQuXG4gICAgLy8gaHR0cHM6Ly9naXRodWIuY29tL2JyaWFuYy9ub2RlLXBvc3RncmVzL2Jsb2IvMmE4ZWZiZWUwOWEyODRiZTEyNzQ4ZWQzOTYyYmM5YjgxNjk2NWUzNi9wYWNrYWdlcy9wZy9saWIvY29ubmVjdGlvbi1wYXJhbWV0ZXJzLmpzI0w2NlxuICAgIGlmIChOdW1iZXIuaXNJbnRlZ2VyKHBvcnQpKSB7XG4gICAgICAgIHJldHVybiBwb3J0O1xuICAgIH1cbiAgICAvLyBVbmFibGUgdG8gZmluZCB0aGUgZGVmYXVsdCB1c2VkIGluIHBnIGNvZGUsIHNvIGZhbGxpbmcgYmFjayB0byAndW5kZWZpbmVkJy5cbiAgICByZXR1cm4gdW5kZWZpbmVkO1xufVxuZnVuY3Rpb24gZ2V0U2VtYW50aWNBdHRyaWJ1dGVzRnJvbUNvbm5lY3Rpb24ocGFyYW1zKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfREJfU1lTVEVNXTogc2VtYW50aWNfY29udmVudGlvbnNfMS5EQlNZU1RFTVZBTFVFU19QT1NUR1JFU1FMLFxuICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19EQl9OQU1FXTogcGFyYW1zLmRhdGFiYXNlLFxuICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19EQl9DT05ORUNUSU9OX1NUUklOR106IGdldENvbm5lY3Rpb25TdHJpbmcocGFyYW1zKSxcbiAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfTkVUX1BFRVJfTkFNRV06IHBhcmFtcy5ob3N0LFxuICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19ORVRfUEVFUl9QT1JUXTogZ2V0UG9ydChwYXJhbXMucG9ydCksXG4gICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX1VTRVJdOiBwYXJhbXMudXNlcixcbiAgICB9O1xufVxuZXhwb3J0cy5nZXRTZW1hbnRpY0F0dHJpYnV0ZXNGcm9tQ29ubmVjdGlvbiA9IGdldFNlbWFudGljQXR0cmlidXRlc0Zyb21Db25uZWN0aW9uO1xuZnVuY3Rpb24gZ2V0U2VtYW50aWNBdHRyaWJ1dGVzRnJvbVBvb2wocGFyYW1zKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfREJfU1lTVEVNXTogc2VtYW50aWNfY29udmVudGlvbnNfMS5EQlNZU1RFTVZBTFVFU19QT1NUR1JFU1FMLFxuICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19EQl9OQU1FXTogcGFyYW1zLmRhdGFiYXNlLFxuICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19EQl9DT05ORUNUSU9OX1NUUklOR106IGdldENvbm5lY3Rpb25TdHJpbmcocGFyYW1zKSxcbiAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfTkVUX1BFRVJfTkFNRV06IHBhcmFtcy5ob3N0LFxuICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19ORVRfUEVFUl9QT1JUXTogZ2V0UG9ydChwYXJhbXMucG9ydCksXG4gICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX1VTRVJdOiBwYXJhbXMudXNlcixcbiAgICAgICAgW0F0dHJpYnV0ZU5hbWVzXzEuQXR0cmlidXRlTmFtZXMuSURMRV9USU1FT1VUX01JTExJU106IHBhcmFtcy5pZGxlVGltZW91dE1pbGxpcyxcbiAgICAgICAgW0F0dHJpYnV0ZU5hbWVzXzEuQXR0cmlidXRlTmFtZXMuTUFYX0NMSUVOVF06IHBhcmFtcy5tYXhDbGllbnQsXG4gICAgfTtcbn1cbmV4cG9ydHMuZ2V0U2VtYW50aWNBdHRyaWJ1dGVzRnJvbVBvb2wgPSBnZXRTZW1hbnRpY0F0dHJpYnV0ZXNGcm9tUG9vbDtcbmZ1bmN0aW9uIHNob3VsZFNraXBJbnN0cnVtZW50YXRpb24oaW5zdHJ1bWVudGF0aW9uQ29uZmlnKSB7XG4gICAgcmV0dXJuIChpbnN0cnVtZW50YXRpb25Db25maWcucmVxdWlyZVBhcmVudFNwYW4gPT09IHRydWUgJiZcbiAgICAgICAgYXBpXzEudHJhY2UuZ2V0U3BhbihhcGlfMS5jb250ZXh0LmFjdGl2ZSgpKSA9PT0gdW5kZWZpbmVkKTtcbn1cbmV4cG9ydHMuc2hvdWxkU2tpcEluc3RydW1lbnRhdGlvbiA9IHNob3VsZFNraXBJbnN0cnVtZW50YXRpb247XG4vLyBDcmVhdGUgYSBzcGFuIGZyb20gb3VyIG5vcm1hbGl6ZWQgcXVlcnlDb25maWcgb2JqZWN0LFxuLy8gb3IgcmV0dXJuIGEgYmFzaWMgc3BhbiBpZiBubyBxdWVyeUNvbmZpZyB3YXMgZ2l2ZW4vY291bGQgYmUgY3JlYXRlZC5cbmZ1bmN0aW9uIGhhbmRsZUNvbmZpZ1F1ZXJ5KHRyYWNlciwgaW5zdHJ1bWVudGF0aW9uQ29uZmlnLCBxdWVyeUNvbmZpZykge1xuICAgIC8vIENyZWF0ZSBjaGlsZCBzcGFuLlxuICAgIGNvbnN0IHsgY29ubmVjdGlvblBhcmFtZXRlcnMgfSA9IHRoaXM7XG4gICAgY29uc3QgZGJOYW1lID0gY29ubmVjdGlvblBhcmFtZXRlcnMuZGF0YWJhc2U7XG4gICAgY29uc3Qgc3Bhbk5hbWUgPSBnZXRRdWVyeVNwYW5OYW1lKGRiTmFtZSwgcXVlcnlDb25maWcpO1xuICAgIGNvbnN0IHNwYW4gPSB0cmFjZXIuc3RhcnRTcGFuKHNwYW5OYW1lLCB7XG4gICAgICAgIGtpbmQ6IGFwaV8xLlNwYW5LaW5kLkNMSUVOVCxcbiAgICAgICAgYXR0cmlidXRlczogZ2V0U2VtYW50aWNBdHRyaWJ1dGVzRnJvbUNvbm5lY3Rpb24oY29ubmVjdGlvblBhcmFtZXRlcnMpLFxuICAgIH0pO1xuICAgIGlmICghcXVlcnlDb25maWcpIHtcbiAgICAgICAgcmV0dXJuIHNwYW47XG4gICAgfVxuICAgIC8vIFNldCBhdHRyaWJ1dGVzXG4gICAgaWYgKHF1ZXJ5Q29uZmlnLnRleHQpIHtcbiAgICAgICAgc3Bhbi5zZXRBdHRyaWJ1dGUoc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19EQl9TVEFURU1FTlQsIHF1ZXJ5Q29uZmlnLnRleHQpO1xuICAgIH1cbiAgICBpZiAoaW5zdHJ1bWVudGF0aW9uQ29uZmlnLmVuaGFuY2VkRGF0YWJhc2VSZXBvcnRpbmcgJiZcbiAgICAgICAgQXJyYXkuaXNBcnJheShxdWVyeUNvbmZpZy52YWx1ZXMpKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCBjb252ZXJ0ZWRWYWx1ZXMgPSBxdWVyeUNvbmZpZy52YWx1ZXMubWFwKHZhbHVlID0+IHtcbiAgICAgICAgICAgICAgICBpZiAodmFsdWUgPT0gbnVsbCkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gJ251bGwnO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmICh2YWx1ZSBpbnN0YW5jZW9mIEJ1ZmZlcikge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWUudG9TdHJpbmcoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAodHlwZW9mIHZhbHVlID09PSAnb2JqZWN0Jykge1xuICAgICAgICAgICAgICAgICAgICBpZiAodHlwZW9mIHZhbHVlLnRvUG9zdGdyZXMgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZS50b1Bvc3RncmVzKCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIEpTT04uc3RyaW5naWZ5KHZhbHVlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIC8vc3RyaW5nLCBudW1iZXJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlLnRvU3RyaW5nKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBzcGFuLnNldEF0dHJpYnV0ZShBdHRyaWJ1dGVOYW1lc18xLkF0dHJpYnV0ZU5hbWVzLlBHX1ZBTFVFUywgY29udmVydGVkVmFsdWVzKTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgYXBpXzEuZGlhZy5lcnJvcignZmFpbGVkIHRvIHN0cmluZ2lmeSAnLCBxdWVyeUNvbmZpZy52YWx1ZXMsIGUpO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8vIFNldCBwbGFuIG5hbWUgYXR0cmlidXRlLCBpZiBwcmVzZW50XG4gICAgaWYgKHR5cGVvZiBxdWVyeUNvbmZpZy5uYW1lID09PSAnc3RyaW5nJykge1xuICAgICAgICBzcGFuLnNldEF0dHJpYnV0ZShBdHRyaWJ1dGVOYW1lc18xLkF0dHJpYnV0ZU5hbWVzLlBHX1BMQU4sIHF1ZXJ5Q29uZmlnLm5hbWUpO1xuICAgIH1cbiAgICByZXR1cm4gc3Bhbjtcbn1cbmV4cG9ydHMuaGFuZGxlQ29uZmlnUXVlcnkgPSBoYW5kbGVDb25maWdRdWVyeTtcbmZ1bmN0aW9uIGhhbmRsZUV4ZWN1dGlvblJlc3VsdChjb25maWcsIHNwYW4sIHBnUmVzdWx0KSB7XG4gICAgaWYgKHR5cGVvZiBjb25maWcucmVzcG9uc2VIb29rID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICgwLCBpbnN0cnVtZW50YXRpb25fMS5zYWZlRXhlY3V0ZUluVGhlTWlkZGxlKSgoKSA9PiB7XG4gICAgICAgICAgICBjb25maWcucmVzcG9uc2VIb29rKHNwYW4sIHtcbiAgICAgICAgICAgICAgICBkYXRhOiBwZ1Jlc3VsdCxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9LCBlcnIgPT4ge1xuICAgICAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgICAgIGFwaV8xLmRpYWcuZXJyb3IoJ0Vycm9yIHJ1bm5pbmcgcmVzcG9uc2UgaG9vaycsIGVycik7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sIHRydWUpO1xuICAgIH1cbn1cbmV4cG9ydHMuaGFuZGxlRXhlY3V0aW9uUmVzdWx0ID0gaGFuZGxlRXhlY3V0aW9uUmVzdWx0O1xuZnVuY3Rpb24gcGF0Y2hDYWxsYmFjayhpbnN0cnVtZW50YXRpb25Db25maWcsIHNwYW4sIGNiLCBhdHRyaWJ1dGVzLCByZWNvcmREdXJhdGlvbikge1xuICAgIHJldHVybiBmdW5jdGlvbiBwYXRjaGVkQ2FsbGJhY2soZXJyLCByZXMpIHtcbiAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChlcnIsICdjb2RlJykpIHtcbiAgICAgICAgICAgICAgICBhdHRyaWJ1dGVzW3NlbWFudGljX2NvbnZlbnRpb25zXzEuQVRUUl9FUlJPUl9UWVBFXSA9IGVyclsnY29kZSddO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgc3Bhbi5zZXRTdGF0dXMoe1xuICAgICAgICAgICAgICAgIGNvZGU6IGFwaV8xLlNwYW5TdGF0dXNDb2RlLkVSUk9SLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGVyci5tZXNzYWdlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBoYW5kbGVFeGVjdXRpb25SZXN1bHQoaW5zdHJ1bWVudGF0aW9uQ29uZmlnLCBzcGFuLCByZXMpO1xuICAgICAgICB9XG4gICAgICAgIHJlY29yZER1cmF0aW9uKCk7XG4gICAgICAgIHNwYW4uZW5kKCk7XG4gICAgICAgIGNiLmNhbGwodGhpcywgZXJyLCByZXMpO1xuICAgIH07XG59XG5leHBvcnRzLnBhdGNoQ2FsbGJhY2sgPSBwYXRjaENhbGxiYWNrO1xuZnVuY3Rpb24gZ2V0UG9vbE5hbWUocG9vbCkge1xuICAgIGxldCBwb29sTmFtZSA9ICcnO1xuICAgIHBvb2xOYW1lICs9ICgocG9vbCA9PT0gbnVsbCB8fCBwb29sID09PSB2b2lkIDAgPyB2b2lkIDAgOiBwb29sLmhvc3QpID8gYCR7cG9vbC5ob3N0fWAgOiAndW5rbm93bl9ob3N0JykgKyAnOic7XG4gICAgcG9vbE5hbWUgKz0gKChwb29sID09PSBudWxsIHx8IHBvb2wgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHBvb2wucG9ydCkgPyBgJHtwb29sLnBvcnR9YCA6ICd1bmtub3duX3BvcnQnKSArICcvJztcbiAgICBwb29sTmFtZSArPSAocG9vbCA9PT0gbnVsbCB8fCBwb29sID09PSB2b2lkIDAgPyB2b2lkIDAgOiBwb29sLmRhdGFiYXNlKSA/IGAke3Bvb2wuZGF0YWJhc2V9YCA6ICd1bmtub3duX2RhdGFiYXNlJztcbiAgICByZXR1cm4gcG9vbE5hbWUudHJpbSgpO1xufVxuZXhwb3J0cy5nZXRQb29sTmFtZSA9IGdldFBvb2xOYW1lO1xuZnVuY3Rpb24gdXBkYXRlQ291bnRlcihwb29sTmFtZSwgcG9vbCwgY29ubmVjdGlvbkNvdW50LCBjb25uZWN0aW9uUGVuZGluZ1JlcXVlc3RzLCBsYXRlc3RDb3VudGVyKSB7XG4gICAgY29uc3QgYWxsID0gcG9vbC50b3RhbENvdW50O1xuICAgIGNvbnN0IHBlbmRpbmcgPSBwb29sLndhaXRpbmdDb3VudDtcbiAgICBjb25zdCBpZGxlID0gcG9vbC5pZGxlQ291bnQ7XG4gICAgY29uc3QgdXNlZCA9IGFsbCAtIGlkbGU7XG4gICAgY29ubmVjdGlvbkNvdW50LmFkZCh1c2VkIC0gbGF0ZXN0Q291bnRlci51c2VkLCB7XG4gICAgICAgIFtzZW1jb252XzEuQVRUUl9EQl9DTElFTlRfQ09OTkVDVElPTl9TVEFURV06IHNlbWNvbnZfMS5EQl9DTElFTlRfQ09OTkVDVElPTl9TVEFURV9WQUxVRV9VU0VELFxuICAgICAgICBbc2VtY29udl8xLkFUVFJfREJfQ0xJRU5UX0NPTk5FQ1RJT05fUE9PTF9OQU1FXTogcG9vbE5hbWUsXG4gICAgfSk7XG4gICAgY29ubmVjdGlvbkNvdW50LmFkZChpZGxlIC0gbGF0ZXN0Q291bnRlci5pZGxlLCB7XG4gICAgICAgIFtzZW1jb252XzEuQVRUUl9EQl9DTElFTlRfQ09OTkVDVElPTl9TVEFURV06IHNlbWNvbnZfMS5EQl9DTElFTlRfQ09OTkVDVElPTl9TVEFURV9WQUxVRV9JRExFLFxuICAgICAgICBbc2VtY29udl8xLkFUVFJfREJfQ0xJRU5UX0NPTk5FQ1RJT05fUE9PTF9OQU1FXTogcG9vbE5hbWUsXG4gICAgfSk7XG4gICAgY29ubmVjdGlvblBlbmRpbmdSZXF1ZXN0cy5hZGQocGVuZGluZyAtIGxhdGVzdENvdW50ZXIucGVuZGluZywge1xuICAgICAgICBbc2VtY29udl8xLkFUVFJfREJfQ0xJRU5UX0NPTk5FQ1RJT05fUE9PTF9OQU1FXTogcG9vbE5hbWUsXG4gICAgfSk7XG4gICAgcmV0dXJuIHsgdXNlZDogdXNlZCwgaWRsZTogaWRsZSwgcGVuZGluZzogcGVuZGluZyB9O1xufVxuZXhwb3J0cy51cGRhdGVDb3VudGVyID0gdXBkYXRlQ291bnRlcjtcbmZ1bmN0aW9uIHBhdGNoQ2FsbGJhY2tQR1Bvb2woc3BhbiwgY2IpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gcGF0Y2hlZENhbGxiYWNrKGVyciwgcmVzLCBkb25lKSB7XG4gICAgICAgIGlmIChlcnIpIHtcbiAgICAgICAgICAgIHNwYW4uc2V0U3RhdHVzKHtcbiAgICAgICAgICAgICAgICBjb2RlOiBhcGlfMS5TcGFuU3RhdHVzQ29kZS5FUlJPUixcbiAgICAgICAgICAgICAgICBtZXNzYWdlOiBlcnIubWVzc2FnZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIHNwYW4uZW5kKCk7XG4gICAgICAgIGNiLmNhbGwodGhpcywgZXJyLCByZXMsIGRvbmUpO1xuICAgIH07XG59XG5leHBvcnRzLnBhdGNoQ2FsbGJhY2tQR1Bvb2wgPSBwYXRjaENhbGxiYWNrUEdQb29sO1xuZnVuY3Rpb24gcGF0Y2hDbGllbnRDb25uZWN0Q2FsbGJhY2soc3BhbiwgY2IpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gcGF0Y2hlZENsaWVudENvbm5lY3RDYWxsYmFjayhlcnIpIHtcbiAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgc3Bhbi5zZXRTdGF0dXMoe1xuICAgICAgICAgICAgICAgIGNvZGU6IGFwaV8xLlNwYW5TdGF0dXNDb2RlLkVSUk9SLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGVyci5tZXNzYWdlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgc3Bhbi5lbmQoKTtcbiAgICAgICAgY2IuYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbiAgICB9O1xufVxuZXhwb3J0cy5wYXRjaENsaWVudENvbm5lY3RDYWxsYmFjayA9IHBhdGNoQ2xpZW50Q29ubmVjdENhbGxiYWNrO1xuLyoqXG4gKiBBdHRlbXB0IHRvIGdldCBhIG1lc3NhZ2Ugc3RyaW5nIGZyb20gYSB0aHJvd24gdmFsdWUsIHdoaWxlIGJlaW5nIHF1aXRlXG4gKiBkZWZlbnNpdmUsIHRvIHJlY29nbml6ZSB0aGUgZmFjdCB0aGF0LCBpbiBKUywgYW55IGtpbmQgb2YgdmFsdWUgKGV2ZW5cbiAqIHByaW1pdGl2ZXMpIGNhbiBiZSB0aHJvd24uXG4gKi9cbmZ1bmN0aW9uIGdldEVycm9yTWVzc2FnZShlKSB7XG4gICAgcmV0dXJuIHR5cGVvZiBlID09PSAnb2JqZWN0JyAmJiBlICE9PSBudWxsICYmICdtZXNzYWdlJyBpbiBlXG4gICAgICAgID8gU3RyaW5nKGUubWVzc2FnZSlcbiAgICAgICAgOiB1bmRlZmluZWQ7XG59XG5leHBvcnRzLmdldEVycm9yTWVzc2FnZSA9IGdldEVycm9yTWVzc2FnZTtcbmZ1bmN0aW9uIGlzT2JqZWN0V2l0aFRleHRTdHJpbmcoaXQpIHtcbiAgICB2YXIgX2E7XG4gICAgcmV0dXJuICh0eXBlb2YgaXQgPT09ICdvYmplY3QnICYmXG4gICAgICAgIHR5cGVvZiAoKF9hID0gaXQpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS50ZXh0KSA9PT0gJ3N0cmluZycpO1xufVxuZXhwb3J0cy5pc09iamVjdFdpdGhUZXh0U3RyaW5nID0gaXNPYmplY3RXaXRoVGV4dFN0cmluZztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.51.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-pg';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Postgresql specific attributes not covered by semantic conventions\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"PG_VALUES\"] = \"db.postgresql.values\";\n    AttributeNames[\"PG_PLAN\"] = \"db.postgresql.plan\";\n    AttributeNames[\"IDLE_TIMEOUT_MILLIS\"] = \"db.postgresql.idle.timeout.millis\";\n    AttributeNames[\"MAX_CLIENT\"] = \"db.postgresql.max.client\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SpanNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Contains span names produced by instrumentation\nvar SpanNames;\n(function (SpanNames) {\n    SpanNames[\"QUERY_PREFIX\"] = \"pg.query\";\n    SpanNames[\"CONNECT\"] = \"pg.connect\";\n    SpanNames[\"POOL_CONNECT\"] = \"pg-pool.connect\";\n})(SpanNames = exports.SpanNames || (exports.SpanNames = {}));\n//# sourceMappingURL=SpanNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PgInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js\");\nconst utils = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js\");\nconst sql_common_1 = __webpack_require__(/*! @opentelemetry/sql-common */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js\");\nconst SpanNames_1 = __webpack_require__(/*! ./enums/SpanNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst semconv_1 = __webpack_require__(/*! ./semconv */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\");\nfunction extractModuleExports(module) {\n    return module[Symbol.toStringTag] === 'Module'\n        ? module.default // ESM\n        : module; // CommonJS\n}\nclass PgInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        // Pool events connect, acquire, release and remove can be called\n        // multiple times without changing the values of total, idle and waiting\n        // connections. The _connectionsCounter is used to keep track of latest\n        // values and only update the metrics _connectionsCount and _connectionPendingRequests\n        // when the value change.\n        this._connectionsCounter = {\n            used: 0,\n            idle: 0,\n            pending: 0,\n        };\n    }\n    _updateMetricInstruments() {\n        this._operationDuration = this.meter.createHistogram(semconv_1.METRIC_DB_CLIENT_OPERATION_DURATION, {\n            description: 'Duration of database client operations.',\n            unit: 's',\n            valueType: api_1.ValueType.DOUBLE,\n            advice: {\n                explicitBucketBoundaries: [\n                    0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10,\n                ],\n            },\n        });\n        this._connectionsCounter = {\n            idle: 0,\n            pending: 0,\n            used: 0,\n        };\n        this._connectionsCount = this.meter.createUpDownCounter(semconv_1.METRIC_DB_CLIENT_CONNECTION_COUNT, {\n            description: 'The number of connections that are currently in state described by the state attribute.',\n            unit: '{connection}',\n        });\n        this._connectionPendingRequests = this.meter.createUpDownCounter(semconv_1.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS, {\n            description: 'The number of current pending requests for an open connection.',\n            unit: '{connection}',\n        });\n    }\n    init() {\n        const SUPPORTED_PG_VERSIONS = ['>=8.0.3 <9'];\n        const modulePgNativeClient = new instrumentation_1.InstrumentationNodeModuleFile('pg/lib/native/client.js', SUPPORTED_PG_VERSIONS, this._patchPgClient.bind(this), this._unpatchPgClient.bind(this));\n        const modulePgClient = new instrumentation_1.InstrumentationNodeModuleFile('pg/lib/client.js', SUPPORTED_PG_VERSIONS, this._patchPgClient.bind(this), this._unpatchPgClient.bind(this));\n        const modulePG = new instrumentation_1.InstrumentationNodeModuleDefinition('pg', SUPPORTED_PG_VERSIONS, (module) => {\n            const moduleExports = extractModuleExports(module);\n            this._patchPgClient(moduleExports.Client);\n            return module;\n        }, (module) => {\n            const moduleExports = extractModuleExports(module);\n            this._unpatchPgClient(moduleExports.Client);\n            return module;\n        }, [modulePgClient, modulePgNativeClient]);\n        const modulePGPool = new instrumentation_1.InstrumentationNodeModuleDefinition('pg-pool', ['>=2.0.0 <4'], (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                this._unwrap(moduleExports.prototype, 'connect');\n            }\n            this._wrap(moduleExports.prototype, 'connect', this._getPoolConnectPatch());\n            return moduleExports;\n        }, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                this._unwrap(moduleExports.prototype, 'connect');\n            }\n        });\n        return [modulePG, modulePGPool];\n    }\n    _patchPgClient(module) {\n        if (!module) {\n            return;\n        }\n        const moduleExports = extractModuleExports(module);\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.query)) {\n            this._unwrap(moduleExports.prototype, 'query');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n            this._unwrap(moduleExports.prototype, 'connect');\n        }\n        this._wrap(moduleExports.prototype, 'query', this._getClientQueryPatch());\n        this._wrap(moduleExports.prototype, 'connect', this._getClientConnectPatch());\n        return module;\n    }\n    _unpatchPgClient(module) {\n        const moduleExports = extractModuleExports(module);\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.query)) {\n            this._unwrap(moduleExports.prototype, 'query');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n            this._unwrap(moduleExports.prototype, 'connect');\n        }\n        return module;\n    }\n    _getClientConnectPatch() {\n        const plugin = this;\n        return (original) => {\n            return function connect(callback) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return original.call(this, callback);\n                }\n                const span = plugin.tracer.startSpan(SpanNames_1.SpanNames.CONNECT, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: utils.getSemanticAttributesFromConnection(this),\n                });\n                if (callback) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    callback = utils.patchClientConnectCallback(span, callback);\n                    if (parentSpan) {\n                        callback = api_1.context.bind(api_1.context.active(), callback);\n                    }\n                }\n                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return original.call(this, callback);\n                });\n                return handleConnectResult(span, connectResult);\n            };\n        };\n    }\n    recordOperationDuration(attributes, startTime) {\n        const metricsAttributes = {};\n        const keysToCopy = [\n            semantic_conventions_1.SEMATTRS_DB_SYSTEM,\n            semconv_1.ATTR_DB_NAMESPACE,\n            semantic_conventions_1.ATTR_ERROR_TYPE,\n            semantic_conventions_1.ATTR_SERVER_PORT,\n            semantic_conventions_1.ATTR_SERVER_ADDRESS,\n            semconv_1.ATTR_DB_OPERATION_NAME,\n        ];\n        keysToCopy.forEach(key => {\n            if (key in attributes) {\n                metricsAttributes[key] = attributes[key];\n            }\n        });\n        const durationSeconds = (0, core_1.hrTimeToMilliseconds)((0, core_1.hrTimeDuration)(startTime, (0, core_1.hrTime)())) / 1000;\n        this._operationDuration.record(durationSeconds, metricsAttributes);\n    }\n    _getClientQueryPatch() {\n        const plugin = this;\n        return (original) => {\n            this._diag.debug('Patching pg.Client.prototype.query');\n            return function query(...args) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return original.apply(this, args);\n                }\n                const startTime = (0, core_1.hrTime)();\n                // client.query(text, cb?), client.query(text, values, cb?), and\n                // client.query(configObj, cb?) are all valid signatures. We construct\n                // a queryConfig obj from all (valid) signatures to build the span in a\n                // unified way. We verify that we at least have query text, and code\n                // defensively when dealing with `queryConfig` after that (to handle all\n                // the other invalid cases, like a non-array for values being provided).\n                // The type casts here reflect only what we've actually validated.\n                const arg0 = args[0];\n                const firstArgIsString = typeof arg0 === 'string';\n                const firstArgIsQueryObjectWithText = utils.isObjectWithTextString(arg0);\n                // TODO: remove the `as ...` casts below when the TS version is upgraded.\n                // Newer TS versions will use the result of firstArgIsQueryObjectWithText\n                // to properly narrow arg0, but TS 4.3.5 does not.\n                const queryConfig = firstArgIsString\n                    ? {\n                        text: arg0,\n                        values: Array.isArray(args[1]) ? args[1] : undefined,\n                    }\n                    : firstArgIsQueryObjectWithText\n                        ? arg0\n                        : undefined;\n                const attributes = {\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n                    [semconv_1.ATTR_DB_NAMESPACE]: this.database,\n                    [semantic_conventions_1.ATTR_SERVER_PORT]: this.connectionParameters.port,\n                    [semantic_conventions_1.ATTR_SERVER_ADDRESS]: this.connectionParameters.host,\n                };\n                if (queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.text) {\n                    attributes[semconv_1.ATTR_DB_OPERATION_NAME] =\n                        utils.parseNormalizedOperationName(queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.text);\n                }\n                const recordDuration = () => {\n                    plugin.recordOperationDuration(attributes, startTime);\n                };\n                const instrumentationConfig = plugin.getConfig();\n                const span = utils.handleConfigQuery.call(this, plugin.tracer, instrumentationConfig, queryConfig);\n                // Modify query text w/ a tracing comment before invoking original for\n                // tracing, but only if args[0] has one of our expected shapes.\n                if (instrumentationConfig.addSqlCommenterCommentToQueries) {\n                    if (firstArgIsString) {\n                        args[0] = (0, sql_common_1.addSqlCommenterComment)(span, arg0);\n                    }\n                    else if (firstArgIsQueryObjectWithText && !('name' in arg0)) {\n                        // In the case of a query object, we need to ensure there's no name field\n                        // as this indicates a prepared query, where the comment would remain the same\n                        // for every invocation and contain an outdated trace context.\n                        args[0] = Object.assign(Object.assign({}, arg0), { text: (0, sql_common_1.addSqlCommenterComment)(span, arg0.text) });\n                    }\n                }\n                // Bind callback (if any) to parent span (if any)\n                if (args.length > 0) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    if (typeof args[args.length - 1] === 'function') {\n                        // Patch ParameterQuery callback\n                        args[args.length - 1] = utils.patchCallback(instrumentationConfig, span, args[args.length - 1], // nb: not type safe.\n                        attributes, recordDuration);\n                        // If a parent span exists, bind the callback\n                        if (parentSpan) {\n                            args[args.length - 1] = api_1.context.bind(api_1.context.active(), args[args.length - 1]);\n                        }\n                    }\n                    else if (typeof (queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.callback) === 'function') {\n                        // Patch ConfigQuery callback\n                        let callback = utils.patchCallback(plugin.getConfig(), span, queryConfig.callback, // nb: not type safe.\n                        attributes, recordDuration);\n                        // If a parent span existed, bind the callback\n                        if (parentSpan) {\n                            callback = api_1.context.bind(api_1.context.active(), callback);\n                        }\n                        args[0].callback = callback;\n                    }\n                }\n                const { requestHook } = instrumentationConfig;\n                if (typeof requestHook === 'function' && queryConfig) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                        // pick keys to expose explicitly, so we're not leaking pg package\n                        // internals that are subject to change\n                        const { database, host, port, user } = this.connectionParameters;\n                        const connection = { database, host, port, user };\n                        requestHook(span, {\n                            connection,\n                            query: {\n                                text: queryConfig.text,\n                                // nb: if `client.query` is called with illegal arguments\n                                // (e.g., if `queryConfig.values` is passed explicitly, but a\n                                // non-array is given), then the type casts will be wrong. But\n                                // we leave it up to the queryHook to handle that, and we\n                                // catch and swallow any errors it throws. The other options\n                                // are all worse. E.g., we could leave `queryConfig.values`\n                                // and `queryConfig.name` as `unknown`, but then the hook body\n                                // would be forced to validate (or cast) them before using\n                                // them, which seems incredibly cumbersome given that these\n                                // casts will be correct 99.9% of the time -- and pg.query\n                                // will immediately throw during development in the other .1%\n                                // of cases. Alternatively, we could simply skip calling the\n                                // hook when `values` or `name` don't have the expected type,\n                                // but that would add unnecessary validation overhead to every\n                                // hook invocation and possibly be even more confusing/unexpected.\n                                values: queryConfig.values,\n                                name: queryConfig.name,\n                            },\n                        });\n                    }, err => {\n                        if (err) {\n                            plugin._diag.error('Error running query hook', err);\n                        }\n                    }, true);\n                }\n                let result;\n                try {\n                    result = original.apply(this, args);\n                }\n                catch (e) {\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: utils.getErrorMessage(e),\n                    });\n                    span.end();\n                    throw e;\n                }\n                // Bind promise to parent span and end the span\n                if (result instanceof Promise) {\n                    return result\n                        .then((result) => {\n                        // Return a pass-along promise which ends the span and then goes to user's orig resolvers\n                        return new Promise(resolve => {\n                            utils.handleExecutionResult(plugin.getConfig(), span, result);\n                            recordDuration();\n                            span.end();\n                            resolve(result);\n                        });\n                    })\n                        .catch((error) => {\n                        return new Promise((_, reject) => {\n                            span.setStatus({\n                                code: api_1.SpanStatusCode.ERROR,\n                                message: error.message,\n                            });\n                            recordDuration();\n                            span.end();\n                            reject(error);\n                        });\n                    });\n                }\n                // else returns void\n                return result; // void\n            };\n        };\n    }\n    _setPoolConnectEventListeners(pgPool) {\n        if (pgPool[internal_types_1.EVENT_LISTENERS_SET])\n            return;\n        const poolName = utils.getPoolName(pgPool.options);\n        pgPool.on('connect', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('acquire', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('remove', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('release', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool[internal_types_1.EVENT_LISTENERS_SET] = true;\n    }\n    _getPoolConnectPatch() {\n        const plugin = this;\n        return (originalConnect) => {\n            return function connect(callback) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return originalConnect.call(this, callback);\n                }\n                // setup span\n                const span = plugin.tracer.startSpan(SpanNames_1.SpanNames.POOL_CONNECT, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: utils.getSemanticAttributesFromPool(this.options),\n                });\n                plugin._setPoolConnectEventListeners(this);\n                if (callback) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    callback = utils.patchCallbackPGPool(span, callback);\n                    // If a parent span exists, bind the callback\n                    if (parentSpan) {\n                        callback = api_1.context.bind(api_1.context.active(), callback);\n                    }\n                }\n                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return originalConnect.call(this, callback);\n                });\n                return handleConnectResult(span, connectResult);\n            };\n        };\n    }\n}\nexports.PgInstrumentation = PgInstrumentation;\nfunction handleConnectResult(span, connectResult) {\n    if (!(connectResult instanceof Promise)) {\n        return connectResult;\n    }\n    const connectResultPromise = connectResult;\n    return api_1.context.bind(api_1.context.active(), connectResultPromise\n        .then(result => {\n        span.end();\n        return result;\n    })\n        .catch((error) => {\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: utils.getErrorMessage(error),\n        });\n        span.end();\n        return Promise.reject(error);\n    }));\n}\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.EVENT_LISTENERS_SET = void 0;\nexports.EVENT_LISTENERS_SET = Symbol('opentelemetry.instrumentation.pg.eventListenersSet');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXBnL2J1aWxkL3NyYy9pbnRlcm5hbC10eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCwyQkFBMkI7QUFDM0IsMkJBQTJCO0FBQzNCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2I5MGNkMWE1MzdkYWZlMjk0Y2VlOTYzZTQ0MzJiNDZhXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tcGdcXGJ1aWxkXFxzcmNcXGludGVybmFsLXR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuRVZFTlRfTElTVEVORVJTX1NFVCA9IHZvaWQgMDtcbmV4cG9ydHMuRVZFTlRfTElTVEVORVJTX1NFVCA9IFN5bWJvbCgnb3BlbnRlbGVtZXRyeS5pbnN0cnVtZW50YXRpb24ucGcuZXZlbnRMaXN0ZW5lcnNTZXQnKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWludGVybmFsLXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.METRIC_DB_CLIENT_OPERATION_DURATION = exports.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = exports.METRIC_DB_CLIENT_CONNECTION_COUNT = exports.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = exports.DB_CLIENT_CONNECTION_STATE_VALUE_USED = exports.ATTR_DB_OPERATION_NAME = exports.ATTR_DB_NAMESPACE = exports.ATTR_DB_CLIENT_CONNECTION_STATE = exports.ATTR_DB_CLIENT_CONNECTION_POOL_NAME = void 0;\n/**\n * The name of the connection pool; unique within the instrumented application. In case the connection pool implementation doesn't provide a name, instrumentation **SHOULD** use a combination of parameters that would make the name unique, for example, combining attributes `server.address`, `server.port`, and `db.namespace`, formatted as `server.address:server.port/db.namespace`. Instrumentations that generate connection pool name following different patterns **SHOULD** document it.\n *\n * @example myDataSource\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_CLIENT_CONNECTION_POOL_NAME = 'db.client.connection.pool.name';\n/**\n * The state of a connection in the pool\n *\n * @example idle\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_CLIENT_CONNECTION_STATE = 'db.client.connection.state';\n/**\n * The name of the database, fully qualified within the server address and port.\n *\n * @example customers\n * @example test.users\n *\n * @note If a database system has multiple namespace components, they **SHOULD** be concatenated (potentially using database system specific conventions) from most general to most specific namespace component, and more specific namespaces **SHOULD NOT** be captured without the more general namespaces, to ensure that \"startswith\" queries for the more general namespaces will be valid.\n * Semantic conventions for individual database systems **SHOULD** document what `db.namespace` means in the context of that system.\n * It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_NAMESPACE = 'db.namespace';\n/**\n * The name of the operation or command being executed.\n *\n * @example findAndModify\n * @example HMSET\n * @example SELECT\n *\n * @note It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * If the operation name is parsed from the query text, it **SHOULD** be the first operation name found in the query.\n * For batch operations, if the individual operations are known to have the same operation name then that operation name **SHOULD** be used prepended by `BATCH `, otherwise `db.operation.name` **SHOULD** be `BATCH` or some other database system specific term if more applicable.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_OPERATION_NAME = 'db.operation.name';\n/**\n * Enum value \"used\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexports.DB_CLIENT_CONNECTION_STATE_VALUE_USED = 'used';\n/**\n * Enum value \"idle\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexports.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = 'idle';\n/**\n * The number of connections that are currently in state described by the `state` attribute\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_CONNECTION_COUNT = 'db.client.connection.count';\n/**\n * The number of current pending requests for an open connection\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = 'db.client.connection.pending_requests';\n/**\n * Duration of database client operations.\n *\n * @note Batch operations **SHOULD** be recorded as a single operation.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_OPERATION_DURATION = 'db.client.operation.duration';\n//# sourceMappingURL=semconv.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXBnL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YVxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLXBnXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isObjectWithTextString = exports.getErrorMessage = exports.patchClientConnectCallback = exports.patchCallbackPGPool = exports.updateCounter = exports.getPoolName = exports.patchCallback = exports.handleExecutionResult = exports.handleConfigQuery = exports.shouldSkipInstrumentation = exports.getSemanticAttributesFromPool = exports.getSemanticAttributesFromConnection = exports.getConnectionString = exports.parseNormalizedOperationName = exports.getQuerySpanName = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst semconv_1 = __webpack_require__(/*! ./semconv */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst SpanNames_1 = __webpack_require__(/*! ./enums/SpanNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\");\n/**\n * Helper function to get a low cardinality span name from whatever info we have\n * about the query.\n *\n * This is tricky, because we don't have most of the information (table name,\n * operation name, etc) the spec recommends using to build a low-cardinality\n * value w/o parsing. So, we use db.name and assume that, if the query's a named\n * prepared statement, those `name` values will be low cardinality. If we don't\n * have a named prepared statement, we try to parse an operation (despite the\n * spec's warnings).\n *\n * @params dbName The name of the db against which this query is being issued,\n *   which could be missing if no db name was given at the time that the\n *   connection was established.\n * @params queryConfig Information we have about the query being issued, typed\n *   to reflect only the validation we've actually done on the args to\n *   `client.query()`. This will be undefined if `client.query()` was called\n *   with invalid arguments.\n */\nfunction getQuerySpanName(dbName, queryConfig) {\n    // NB: when the query config is invalid, we omit the dbName too, so that\n    // someone (or some tool) reading the span name doesn't misinterpret the\n    // dbName as being a prepared statement or sql commit name.\n    if (!queryConfig)\n        return SpanNames_1.SpanNames.QUERY_PREFIX;\n    // Either the name of a prepared statement; or an attempted parse\n    // of the SQL command, normalized to uppercase; or unknown.\n    const command = typeof queryConfig.name === 'string' && queryConfig.name\n        ? queryConfig.name\n        : parseNormalizedOperationName(queryConfig.text);\n    return `${SpanNames_1.SpanNames.QUERY_PREFIX}:${command}${dbName ? ` ${dbName}` : ''}`;\n}\nexports.getQuerySpanName = getQuerySpanName;\nfunction parseNormalizedOperationName(queryText) {\n    const indexOfFirstSpace = queryText.indexOf(' ');\n    let sqlCommand = indexOfFirstSpace === -1\n        ? queryText\n        : queryText.slice(0, indexOfFirstSpace);\n    sqlCommand = sqlCommand.toUpperCase();\n    // Handle query text being \"COMMIT;\", which has an extra semicolon before the space.\n    return sqlCommand.endsWith(';') ? sqlCommand.slice(0, -1) : sqlCommand;\n}\nexports.parseNormalizedOperationName = parseNormalizedOperationName;\nfunction getConnectionString(params) {\n    const host = params.host || 'localhost';\n    const port = params.port || 5432;\n    const database = params.database || '';\n    return `postgresql://${host}:${port}/${database}`;\n}\nexports.getConnectionString = getConnectionString;\nfunction getPort(port) {\n    // Port may be NaN as parseInt() is used on the value, passing null will result in NaN being parsed.\n    // https://github.com/brianc/node-postgres/blob/2a8efbee09a284be12748ed3962bc9b816965e36/packages/pg/lib/connection-parameters.js#L66\n    if (Number.isInteger(port)) {\n        return port;\n    }\n    // Unable to find the default used in pg code, so falling back to 'undefined'.\n    return undefined;\n}\nfunction getSemanticAttributesFromConnection(params) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: params.database,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getConnectionString(params),\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: params.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: getPort(params.port),\n        [semantic_conventions_1.SEMATTRS_DB_USER]: params.user,\n    };\n}\nexports.getSemanticAttributesFromConnection = getSemanticAttributesFromConnection;\nfunction getSemanticAttributesFromPool(params) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: params.database,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getConnectionString(params),\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: params.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: getPort(params.port),\n        [semantic_conventions_1.SEMATTRS_DB_USER]: params.user,\n        [AttributeNames_1.AttributeNames.IDLE_TIMEOUT_MILLIS]: params.idleTimeoutMillis,\n        [AttributeNames_1.AttributeNames.MAX_CLIENT]: params.maxClient,\n    };\n}\nexports.getSemanticAttributesFromPool = getSemanticAttributesFromPool;\nfunction shouldSkipInstrumentation(instrumentationConfig) {\n    return (instrumentationConfig.requireParentSpan === true &&\n        api_1.trace.getSpan(api_1.context.active()) === undefined);\n}\nexports.shouldSkipInstrumentation = shouldSkipInstrumentation;\n// Create a span from our normalized queryConfig object,\n// or return a basic span if no queryConfig was given/could be created.\nfunction handleConfigQuery(tracer, instrumentationConfig, queryConfig) {\n    // Create child span.\n    const { connectionParameters } = this;\n    const dbName = connectionParameters.database;\n    const spanName = getQuerySpanName(dbName, queryConfig);\n    const span = tracer.startSpan(spanName, {\n        kind: api_1.SpanKind.CLIENT,\n        attributes: getSemanticAttributesFromConnection(connectionParameters),\n    });\n    if (!queryConfig) {\n        return span;\n    }\n    // Set attributes\n    if (queryConfig.text) {\n        span.setAttribute(semantic_conventions_1.SEMATTRS_DB_STATEMENT, queryConfig.text);\n    }\n    if (instrumentationConfig.enhancedDatabaseReporting &&\n        Array.isArray(queryConfig.values)) {\n        try {\n            const convertedValues = queryConfig.values.map(value => {\n                if (value == null) {\n                    return 'null';\n                }\n                else if (value instanceof Buffer) {\n                    return value.toString();\n                }\n                else if (typeof value === 'object') {\n                    if (typeof value.toPostgres === 'function') {\n                        return value.toPostgres();\n                    }\n                    return JSON.stringify(value);\n                }\n                else {\n                    //string, number\n                    return value.toString();\n                }\n            });\n            span.setAttribute(AttributeNames_1.AttributeNames.PG_VALUES, convertedValues);\n        }\n        catch (e) {\n            api_1.diag.error('failed to stringify ', queryConfig.values, e);\n        }\n    }\n    // Set plan name attribute, if present\n    if (typeof queryConfig.name === 'string') {\n        span.setAttribute(AttributeNames_1.AttributeNames.PG_PLAN, queryConfig.name);\n    }\n    return span;\n}\nexports.handleConfigQuery = handleConfigQuery;\nfunction handleExecutionResult(config, span, pgResult) {\n    if (typeof config.responseHook === 'function') {\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            config.responseHook(span, {\n                data: pgResult,\n            });\n        }, err => {\n            if (err) {\n                api_1.diag.error('Error running response hook', err);\n            }\n        }, true);\n    }\n}\nexports.handleExecutionResult = handleExecutionResult;\nfunction patchCallback(instrumentationConfig, span, cb, attributes, recordDuration) {\n    return function patchedCallback(err, res) {\n        if (err) {\n            if (Object.prototype.hasOwnProperty.call(err, 'code')) {\n                attributes[semantic_conventions_1.ATTR_ERROR_TYPE] = err['code'];\n            }\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        else {\n            handleExecutionResult(instrumentationConfig, span, res);\n        }\n        recordDuration();\n        span.end();\n        cb.call(this, err, res);\n    };\n}\nexports.patchCallback = patchCallback;\nfunction getPoolName(pool) {\n    let poolName = '';\n    poolName += ((pool === null || pool === void 0 ? void 0 : pool.host) ? `${pool.host}` : 'unknown_host') + ':';\n    poolName += ((pool === null || pool === void 0 ? void 0 : pool.port) ? `${pool.port}` : 'unknown_port') + '/';\n    poolName += (pool === null || pool === void 0 ? void 0 : pool.database) ? `${pool.database}` : 'unknown_database';\n    return poolName.trim();\n}\nexports.getPoolName = getPoolName;\nfunction updateCounter(poolName, pool, connectionCount, connectionPendingRequests, latestCounter) {\n    const all = pool.totalCount;\n    const pending = pool.waitingCount;\n    const idle = pool.idleCount;\n    const used = all - idle;\n    connectionCount.add(used - latestCounter.used, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_STATE]: semconv_1.DB_CLIENT_CONNECTION_STATE_VALUE_USED,\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    connectionCount.add(idle - latestCounter.idle, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_STATE]: semconv_1.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE,\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    connectionPendingRequests.add(pending - latestCounter.pending, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    return { used: used, idle: idle, pending: pending };\n}\nexports.updateCounter = updateCounter;\nfunction patchCallbackPGPool(span, cb) {\n    return function patchedCallback(err, res, done) {\n        if (err) {\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        span.end();\n        cb.call(this, err, res, done);\n    };\n}\nexports.patchCallbackPGPool = patchCallbackPGPool;\nfunction patchClientConnectCallback(span, cb) {\n    return function patchedClientConnectCallback(err) {\n        if (err) {\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        span.end();\n        cb.apply(this, arguments);\n    };\n}\nexports.patchClientConnectCallback = patchClientConnectCallback;\n/**\n * Attempt to get a message string from a thrown value, while being quite\n * defensive, to recognize the fact that, in JS, any kind of value (even\n * primitives) can be thrown.\n */\nfunction getErrorMessage(e) {\n    return typeof e === 'object' && e !== null && 'message' in e\n        ? String(e.message)\n        : undefined;\n}\nexports.getErrorMessage = getErrorMessage;\nfunction isObjectWithTextString(it) {\n    var _a;\n    return (typeof it === 'object' &&\n        typeof ((_a = it) === null || _a === void 0 ? void 0 : _a.text) === 'string');\n}\nexports.isObjectWithTextString = isObjectWithTextString;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXBnL2J1aWxkL3NyYy91dGlscy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCw4QkFBOEIsR0FBRyx1QkFBdUIsR0FBRyxrQ0FBa0MsR0FBRywyQkFBMkIsR0FBRyxxQkFBcUIsR0FBRyxtQkFBbUIsR0FBRyxxQkFBcUIsR0FBRyw2QkFBNkIsR0FBRyx5QkFBeUIsR0FBRyxpQ0FBaUMsR0FBRyxxQ0FBcUMsR0FBRywyQ0FBMkMsR0FBRywyQkFBMkIsR0FBRyxvQ0FBb0MsR0FBRyx3QkFBd0I7QUFDdmQsY0FBYyxtQkFBTyxDQUFDLHNJQUFvQjtBQUMxQyx5QkFBeUIsbUJBQU8sQ0FBQyw0TUFBd0I7QUFDekQsK0JBQStCLG1CQUFPLENBQUMsMExBQXFDO0FBQzVFLGtCQUFrQixtQkFBTyxDQUFDLGtMQUFXO0FBQ3JDLDBCQUEwQixtQkFBTyxDQUFDLGtNQUFnQztBQUNsRSxvQkFBb0IsbUJBQU8sQ0FBQyxrTUFBbUI7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0Q7QUFDaEQsb0RBQW9EO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBLGNBQWMsbUNBQW1DLEdBQUcsUUFBUSxFQUFFLGFBQWEsT0FBTyxPQUFPO0FBQ3pGO0FBQ0Esd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QztBQUN2QyxpQ0FBaUM7QUFDakM7QUFDQSxvQ0FBb0M7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsS0FBSyxHQUFHLEtBQUssR0FBRyxTQUFTO0FBQ3BEO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksdUJBQXVCO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQSw4RUFBOEUsVUFBVTtBQUN4Riw4RUFBOEUsVUFBVTtBQUN4RixpRkFBaUYsY0FBYztBQUMvRjtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsYUFBYTtBQUNiO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYjkwY2QxYTUzN2RhZmUyOTRjZWU5NjNlNDQzMmI0NmFcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1wZ1xcYnVpbGRcXHNyY1xcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5pc09iamVjdFdpdGhUZXh0U3RyaW5nID0gZXhwb3J0cy5nZXRFcnJvck1lc3NhZ2UgPSBleHBvcnRzLnBhdGNoQ2xpZW50Q29ubmVjdENhbGxiYWNrID0gZXhwb3J0cy5wYXRjaENhbGxiYWNrUEdQb29sID0gZXhwb3J0cy51cGRhdGVDb3VudGVyID0gZXhwb3J0cy5nZXRQb29sTmFtZSA9IGV4cG9ydHMucGF0Y2hDYWxsYmFjayA9IGV4cG9ydHMuaGFuZGxlRXhlY3V0aW9uUmVzdWx0ID0gZXhwb3J0cy5oYW5kbGVDb25maWdRdWVyeSA9IGV4cG9ydHMuc2hvdWxkU2tpcEluc3RydW1lbnRhdGlvbiA9IGV4cG9ydHMuZ2V0U2VtYW50aWNBdHRyaWJ1dGVzRnJvbVBvb2wgPSBleHBvcnRzLmdldFNlbWFudGljQXR0cmlidXRlc0Zyb21Db25uZWN0aW9uID0gZXhwb3J0cy5nZXRDb25uZWN0aW9uU3RyaW5nID0gZXhwb3J0cy5wYXJzZU5vcm1hbGl6ZWRPcGVyYXRpb25OYW1lID0gZXhwb3J0cy5nZXRRdWVyeVNwYW5OYW1lID0gdm9pZCAwO1xuY29uc3QgYXBpXzEgPSByZXF1aXJlKFwiQG9wZW50ZWxlbWV0cnkvYXBpXCIpO1xuY29uc3QgQXR0cmlidXRlTmFtZXNfMSA9IHJlcXVpcmUoXCIuL2VudW1zL0F0dHJpYnV0ZU5hbWVzXCIpO1xuY29uc3Qgc2VtYW50aWNfY29udmVudGlvbnNfMSA9IHJlcXVpcmUoXCJAb3BlbnRlbGVtZXRyeS9zZW1hbnRpYy1jb252ZW50aW9uc1wiKTtcbmNvbnN0IHNlbWNvbnZfMSA9IHJlcXVpcmUoXCIuL3NlbWNvbnZcIik7XG5jb25zdCBpbnN0cnVtZW50YXRpb25fMSA9IHJlcXVpcmUoXCJAb3BlbnRlbGVtZXRyeS9pbnN0cnVtZW50YXRpb25cIik7XG5jb25zdCBTcGFuTmFtZXNfMSA9IHJlcXVpcmUoXCIuL2VudW1zL1NwYW5OYW1lc1wiKTtcbi8qKlxuICogSGVscGVyIGZ1bmN0aW9uIHRvIGdldCBhIGxvdyBjYXJkaW5hbGl0eSBzcGFuIG5hbWUgZnJvbSB3aGF0ZXZlciBpbmZvIHdlIGhhdmVcbiAqIGFib3V0IHRoZSBxdWVyeS5cbiAqXG4gKiBUaGlzIGlzIHRyaWNreSwgYmVjYXVzZSB3ZSBkb24ndCBoYXZlIG1vc3Qgb2YgdGhlIGluZm9ybWF0aW9uICh0YWJsZSBuYW1lLFxuICogb3BlcmF0aW9uIG5hbWUsIGV0YykgdGhlIHNwZWMgcmVjb21tZW5kcyB1c2luZyB0byBidWlsZCBhIGxvdy1jYXJkaW5hbGl0eVxuICogdmFsdWUgdy9vIHBhcnNpbmcuIFNvLCB3ZSB1c2UgZGIubmFtZSBhbmQgYXNzdW1lIHRoYXQsIGlmIHRoZSBxdWVyeSdzIGEgbmFtZWRcbiAqIHByZXBhcmVkIHN0YXRlbWVudCwgdGhvc2UgYG5hbWVgIHZhbHVlcyB3aWxsIGJlIGxvdyBjYXJkaW5hbGl0eS4gSWYgd2UgZG9uJ3RcbiAqIGhhdmUgYSBuYW1lZCBwcmVwYXJlZCBzdGF0ZW1lbnQsIHdlIHRyeSB0byBwYXJzZSBhbiBvcGVyYXRpb24gKGRlc3BpdGUgdGhlXG4gKiBzcGVjJ3Mgd2FybmluZ3MpLlxuICpcbiAqIEBwYXJhbXMgZGJOYW1lIFRoZSBuYW1lIG9mIHRoZSBkYiBhZ2FpbnN0IHdoaWNoIHRoaXMgcXVlcnkgaXMgYmVpbmcgaXNzdWVkLFxuICogICB3aGljaCBjb3VsZCBiZSBtaXNzaW5nIGlmIG5vIGRiIG5hbWUgd2FzIGdpdmVuIGF0IHRoZSB0aW1lIHRoYXQgdGhlXG4gKiAgIGNvbm5lY3Rpb24gd2FzIGVzdGFibGlzaGVkLlxuICogQHBhcmFtcyBxdWVyeUNvbmZpZyBJbmZvcm1hdGlvbiB3ZSBoYXZlIGFib3V0IHRoZSBxdWVyeSBiZWluZyBpc3N1ZWQsIHR5cGVkXG4gKiAgIHRvIHJlZmxlY3Qgb25seSB0aGUgdmFsaWRhdGlvbiB3ZSd2ZSBhY3R1YWxseSBkb25lIG9uIHRoZSBhcmdzIHRvXG4gKiAgIGBjbGllbnQucXVlcnkoKWAuIFRoaXMgd2lsbCBiZSB1bmRlZmluZWQgaWYgYGNsaWVudC5xdWVyeSgpYCB3YXMgY2FsbGVkXG4gKiAgIHdpdGggaW52YWxpZCBhcmd1bWVudHMuXG4gKi9cbmZ1bmN0aW9uIGdldFF1ZXJ5U3Bhbk5hbWUoZGJOYW1lLCBxdWVyeUNvbmZpZykge1xuICAgIC8vIE5COiB3aGVuIHRoZSBxdWVyeSBjb25maWcgaXMgaW52YWxpZCwgd2Ugb21pdCB0aGUgZGJOYW1lIHRvbywgc28gdGhhdFxuICAgIC8vIHNvbWVvbmUgKG9yIHNvbWUgdG9vbCkgcmVhZGluZyB0aGUgc3BhbiBuYW1lIGRvZXNuJ3QgbWlzaW50ZXJwcmV0IHRoZVxuICAgIC8vIGRiTmFtZSBhcyBiZWluZyBhIHByZXBhcmVkIHN0YXRlbWVudCBvciBzcWwgY29tbWl0IG5hbWUuXG4gICAgaWYgKCFxdWVyeUNvbmZpZylcbiAgICAgICAgcmV0dXJuIFNwYW5OYW1lc18xLlNwYW5OYW1lcy5RVUVSWV9QUkVGSVg7XG4gICAgLy8gRWl0aGVyIHRoZSBuYW1lIG9mIGEgcHJlcGFyZWQgc3RhdGVtZW50OyBvciBhbiBhdHRlbXB0ZWQgcGFyc2VcbiAgICAvLyBvZiB0aGUgU1FMIGNvbW1hbmQsIG5vcm1hbGl6ZWQgdG8gdXBwZXJjYXNlOyBvciB1bmtub3duLlxuICAgIGNvbnN0IGNvbW1hbmQgPSB0eXBlb2YgcXVlcnlDb25maWcubmFtZSA9PT0gJ3N0cmluZycgJiYgcXVlcnlDb25maWcubmFtZVxuICAgICAgICA/IHF1ZXJ5Q29uZmlnLm5hbWVcbiAgICAgICAgOiBwYXJzZU5vcm1hbGl6ZWRPcGVyYXRpb25OYW1lKHF1ZXJ5Q29uZmlnLnRleHQpO1xuICAgIHJldHVybiBgJHtTcGFuTmFtZXNfMS5TcGFuTmFtZXMuUVVFUllfUFJFRklYfToke2NvbW1hbmR9JHtkYk5hbWUgPyBgICR7ZGJOYW1lfWAgOiAnJ31gO1xufVxuZXhwb3J0cy5nZXRRdWVyeVNwYW5OYW1lID0gZ2V0UXVlcnlTcGFuTmFtZTtcbmZ1bmN0aW9uIHBhcnNlTm9ybWFsaXplZE9wZXJhdGlvbk5hbWUocXVlcnlUZXh0KSB7XG4gICAgY29uc3QgaW5kZXhPZkZpcnN0U3BhY2UgPSBxdWVyeVRleHQuaW5kZXhPZignICcpO1xuICAgIGxldCBzcWxDb21tYW5kID0gaW5kZXhPZkZpcnN0U3BhY2UgPT09IC0xXG4gICAgICAgID8gcXVlcnlUZXh0XG4gICAgICAgIDogcXVlcnlUZXh0LnNsaWNlKDAsIGluZGV4T2ZGaXJzdFNwYWNlKTtcbiAgICBzcWxDb21tYW5kID0gc3FsQ29tbWFuZC50b1VwcGVyQ2FzZSgpO1xuICAgIC8vIEhhbmRsZSBxdWVyeSB0ZXh0IGJlaW5nIFwiQ09NTUlUO1wiLCB3aGljaCBoYXMgYW4gZXh0cmEgc2VtaWNvbG9uIGJlZm9yZSB0aGUgc3BhY2UuXG4gICAgcmV0dXJuIHNxbENvbW1hbmQuZW5kc1dpdGgoJzsnKSA/IHNxbENvbW1hbmQuc2xpY2UoMCwgLTEpIDogc3FsQ29tbWFuZDtcbn1cbmV4cG9ydHMucGFyc2VOb3JtYWxpemVkT3BlcmF0aW9uTmFtZSA9IHBhcnNlTm9ybWFsaXplZE9wZXJhdGlvbk5hbWU7XG5mdW5jdGlvbiBnZXRDb25uZWN0aW9uU3RyaW5nKHBhcmFtcykge1xuICAgIGNvbnN0IGhvc3QgPSBwYXJhbXMuaG9zdCB8fCAnbG9jYWxob3N0JztcbiAgICBjb25zdCBwb3J0ID0gcGFyYW1zLnBvcnQgfHwgNTQzMjtcbiAgICBjb25zdCBkYXRhYmFzZSA9IHBhcmFtcy5kYXRhYmFzZSB8fCAnJztcbiAgICByZXR1cm4gYHBvc3RncmVzcWw6Ly8ke2hvc3R9OiR7cG9ydH0vJHtkYXRhYmFzZX1gO1xufVxuZXhwb3J0cy5nZXRDb25uZWN0aW9uU3RyaW5nID0gZ2V0Q29ubmVjdGlvblN0cmluZztcbmZ1bmN0aW9uIGdldFBvcnQocG9ydCkge1xuICAgIC8vIFBvcnQgbWF5IGJlIE5hTiBhcyBwYXJzZUludCgpIGlzIHVzZWQgb24gdGhlIHZhbHVlLCBwYXNzaW5nIG51bGwgd2lsbCByZXN1bHQgaW4gTmFOIGJlaW5nIHBhcnNlZC5cbiAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vYnJpYW5jL25vZGUtcG9zdGdyZXMvYmxvYi8yYThlZmJlZTA5YTI4NGJlMTI3NDhlZDM5NjJiYzliODE2OTY1ZTM2L3BhY2thZ2VzL3BnL2xpYi9jb25uZWN0aW9uLXBhcmFtZXRlcnMuanMjTDY2XG4gICAgaWYgKE51bWJlci5pc0ludGVnZXIocG9ydCkpIHtcbiAgICAgICAgcmV0dXJuIHBvcnQ7XG4gICAgfVxuICAgIC8vIFVuYWJsZSB0byBmaW5kIHRoZSBkZWZhdWx0IHVzZWQgaW4gcGcgY29kZSwgc28gZmFsbGluZyBiYWNrIHRvICd1bmRlZmluZWQnLlxuICAgIHJldHVybiB1bmRlZmluZWQ7XG59XG5mdW5jdGlvbiBnZXRTZW1hbnRpY0F0dHJpYnV0ZXNGcm9tQ29ubmVjdGlvbihwYXJhbXMpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19EQl9TWVNURU1dOiBzZW1hbnRpY19jb252ZW50aW9uc18xLkRCU1lTVEVNVkFMVUVTX1BPU1RHUkVTUUwsXG4gICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX05BTUVdOiBwYXJhbXMuZGF0YWJhc2UsXG4gICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX0NPTk5FQ1RJT05fU1RSSU5HXTogZ2V0Q29ubmVjdGlvblN0cmluZyhwYXJhbXMpLFxuICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19ORVRfUEVFUl9OQU1FXTogcGFyYW1zLmhvc3QsXG4gICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX05FVF9QRUVSX1BPUlRdOiBnZXRQb3J0KHBhcmFtcy5wb3J0KSxcbiAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfREJfVVNFUl06IHBhcmFtcy51c2VyLFxuICAgIH07XG59XG5leHBvcnRzLmdldFNlbWFudGljQXR0cmlidXRlc0Zyb21Db25uZWN0aW9uID0gZ2V0U2VtYW50aWNBdHRyaWJ1dGVzRnJvbUNvbm5lY3Rpb247XG5mdW5jdGlvbiBnZXRTZW1hbnRpY0F0dHJpYnV0ZXNGcm9tUG9vbChwYXJhbXMpIHtcbiAgICByZXR1cm4ge1xuICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19EQl9TWVNURU1dOiBzZW1hbnRpY19jb252ZW50aW9uc18xLkRCU1lTVEVNVkFMVUVTX1BPU1RHUkVTUUwsXG4gICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX05BTUVdOiBwYXJhbXMuZGF0YWJhc2UsXG4gICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX0NPTk5FQ1RJT05fU1RSSU5HXTogZ2V0Q29ubmVjdGlvblN0cmluZyhwYXJhbXMpLFxuICAgICAgICBbc2VtYW50aWNfY29udmVudGlvbnNfMS5TRU1BVFRSU19ORVRfUEVFUl9OQU1FXTogcGFyYW1zLmhvc3QsXG4gICAgICAgIFtzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX05FVF9QRUVSX1BPUlRdOiBnZXRQb3J0KHBhcmFtcy5wb3J0KSxcbiAgICAgICAgW3NlbWFudGljX2NvbnZlbnRpb25zXzEuU0VNQVRUUlNfREJfVVNFUl06IHBhcmFtcy51c2VyLFxuICAgICAgICBbQXR0cmlidXRlTmFtZXNfMS5BdHRyaWJ1dGVOYW1lcy5JRExFX1RJTUVPVVRfTUlMTElTXTogcGFyYW1zLmlkbGVUaW1lb3V0TWlsbGlzLFxuICAgICAgICBbQXR0cmlidXRlTmFtZXNfMS5BdHRyaWJ1dGVOYW1lcy5NQVhfQ0xJRU5UXTogcGFyYW1zLm1heENsaWVudCxcbiAgICB9O1xufVxuZXhwb3J0cy5nZXRTZW1hbnRpY0F0dHJpYnV0ZXNGcm9tUG9vbCA9IGdldFNlbWFudGljQXR0cmlidXRlc0Zyb21Qb29sO1xuZnVuY3Rpb24gc2hvdWxkU2tpcEluc3RydW1lbnRhdGlvbihpbnN0cnVtZW50YXRpb25Db25maWcpIHtcbiAgICByZXR1cm4gKGluc3RydW1lbnRhdGlvbkNvbmZpZy5yZXF1aXJlUGFyZW50U3BhbiA9PT0gdHJ1ZSAmJlxuICAgICAgICBhcGlfMS50cmFjZS5nZXRTcGFuKGFwaV8xLmNvbnRleHQuYWN0aXZlKCkpID09PSB1bmRlZmluZWQpO1xufVxuZXhwb3J0cy5zaG91bGRTa2lwSW5zdHJ1bWVudGF0aW9uID0gc2hvdWxkU2tpcEluc3RydW1lbnRhdGlvbjtcbi8vIENyZWF0ZSBhIHNwYW4gZnJvbSBvdXIgbm9ybWFsaXplZCBxdWVyeUNvbmZpZyBvYmplY3QsXG4vLyBvciByZXR1cm4gYSBiYXNpYyBzcGFuIGlmIG5vIHF1ZXJ5Q29uZmlnIHdhcyBnaXZlbi9jb3VsZCBiZSBjcmVhdGVkLlxuZnVuY3Rpb24gaGFuZGxlQ29uZmlnUXVlcnkodHJhY2VyLCBpbnN0cnVtZW50YXRpb25Db25maWcsIHF1ZXJ5Q29uZmlnKSB7XG4gICAgLy8gQ3JlYXRlIGNoaWxkIHNwYW4uXG4gICAgY29uc3QgeyBjb25uZWN0aW9uUGFyYW1ldGVycyB9ID0gdGhpcztcbiAgICBjb25zdCBkYk5hbWUgPSBjb25uZWN0aW9uUGFyYW1ldGVycy5kYXRhYmFzZTtcbiAgICBjb25zdCBzcGFuTmFtZSA9IGdldFF1ZXJ5U3Bhbk5hbWUoZGJOYW1lLCBxdWVyeUNvbmZpZyk7XG4gICAgY29uc3Qgc3BhbiA9IHRyYWNlci5zdGFydFNwYW4oc3Bhbk5hbWUsIHtcbiAgICAgICAga2luZDogYXBpXzEuU3BhbktpbmQuQ0xJRU5ULFxuICAgICAgICBhdHRyaWJ1dGVzOiBnZXRTZW1hbnRpY0F0dHJpYnV0ZXNGcm9tQ29ubmVjdGlvbihjb25uZWN0aW9uUGFyYW1ldGVycyksXG4gICAgfSk7XG4gICAgaWYgKCFxdWVyeUNvbmZpZykge1xuICAgICAgICByZXR1cm4gc3BhbjtcbiAgICB9XG4gICAgLy8gU2V0IGF0dHJpYnV0ZXNcbiAgICBpZiAocXVlcnlDb25maWcudGV4dCkge1xuICAgICAgICBzcGFuLnNldEF0dHJpYnV0ZShzZW1hbnRpY19jb252ZW50aW9uc18xLlNFTUFUVFJTX0RCX1NUQVRFTUVOVCwgcXVlcnlDb25maWcudGV4dCk7XG4gICAgfVxuICAgIGlmIChpbnN0cnVtZW50YXRpb25Db25maWcuZW5oYW5jZWREYXRhYmFzZVJlcG9ydGluZyAmJlxuICAgICAgICBBcnJheS5pc0FycmF5KHF1ZXJ5Q29uZmlnLnZhbHVlcykpIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IGNvbnZlcnRlZFZhbHVlcyA9IHF1ZXJ5Q29uZmlnLnZhbHVlcy5tYXAodmFsdWUgPT4ge1xuICAgICAgICAgICAgICAgIGlmICh2YWx1ZSA9PSBudWxsKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAnbnVsbCc7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKHZhbHVlIGluc3RhbmNlb2YgQnVmZmVyKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB2YWx1ZS50b1N0cmluZygpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmICh0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmICh0eXBlb2YgdmFsdWUudG9Qb3N0Z3JlcyA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlLnRvUG9zdGdyZXMoKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkodmFsdWUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgLy9zdHJpbmcsIG51bWJlclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWUudG9TdHJpbmcoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHNwYW4uc2V0QXR0cmlidXRlKEF0dHJpYnV0ZU5hbWVzXzEuQXR0cmlidXRlTmFtZXMuUEdfVkFMVUVTLCBjb252ZXJ0ZWRWYWx1ZXMpO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICBhcGlfMS5kaWFnLmVycm9yKCdmYWlsZWQgdG8gc3RyaW5naWZ5ICcsIHF1ZXJ5Q29uZmlnLnZhbHVlcywgZSk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLy8gU2V0IHBsYW4gbmFtZSBhdHRyaWJ1dGUsIGlmIHByZXNlbnRcbiAgICBpZiAodHlwZW9mIHF1ZXJ5Q29uZmlnLm5hbWUgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgIHNwYW4uc2V0QXR0cmlidXRlKEF0dHJpYnV0ZU5hbWVzXzEuQXR0cmlidXRlTmFtZXMuUEdfUExBTiwgcXVlcnlDb25maWcubmFtZSk7XG4gICAgfVxuICAgIHJldHVybiBzcGFuO1xufVxuZXhwb3J0cy5oYW5kbGVDb25maWdRdWVyeSA9IGhhbmRsZUNvbmZpZ1F1ZXJ5O1xuZnVuY3Rpb24gaGFuZGxlRXhlY3V0aW9uUmVzdWx0KGNvbmZpZywgc3BhbiwgcGdSZXN1bHQpIHtcbiAgICBpZiAodHlwZW9mIGNvbmZpZy5yZXNwb25zZUhvb2sgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgKDAsIGluc3RydW1lbnRhdGlvbl8xLnNhZmVFeGVjdXRlSW5UaGVNaWRkbGUpKCgpID0+IHtcbiAgICAgICAgICAgIGNvbmZpZy5yZXNwb25zZUhvb2soc3Bhbiwge1xuICAgICAgICAgICAgICAgIGRhdGE6IHBnUmVzdWx0LFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0sIGVyciA9PiB7XG4gICAgICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICAgICAgYXBpXzEuZGlhZy5lcnJvcignRXJyb3IgcnVubmluZyByZXNwb25zZSBob29rJywgZXJyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSwgdHJ1ZSk7XG4gICAgfVxufVxuZXhwb3J0cy5oYW5kbGVFeGVjdXRpb25SZXN1bHQgPSBoYW5kbGVFeGVjdXRpb25SZXN1bHQ7XG5mdW5jdGlvbiBwYXRjaENhbGxiYWNrKGluc3RydW1lbnRhdGlvbkNvbmZpZywgc3BhbiwgY2IsIGF0dHJpYnV0ZXMsIHJlY29yZER1cmF0aW9uKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uIHBhdGNoZWRDYWxsYmFjayhlcnIsIHJlcykge1xuICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGVyciwgJ2NvZGUnKSkge1xuICAgICAgICAgICAgICAgIGF0dHJpYnV0ZXNbc2VtYW50aWNfY29udmVudGlvbnNfMS5BVFRSX0VSUk9SX1RZUEVdID0gZXJyWydjb2RlJ107XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBzcGFuLnNldFN0YXR1cyh7XG4gICAgICAgICAgICAgICAgY29kZTogYXBpXzEuU3BhblN0YXR1c0NvZGUuRVJST1IsXG4gICAgICAgICAgICAgICAgbWVzc2FnZTogZXJyLm1lc3NhZ2UsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGhhbmRsZUV4ZWN1dGlvblJlc3VsdChpbnN0cnVtZW50YXRpb25Db25maWcsIHNwYW4sIHJlcyk7XG4gICAgICAgIH1cbiAgICAgICAgcmVjb3JkRHVyYXRpb24oKTtcbiAgICAgICAgc3Bhbi5lbmQoKTtcbiAgICAgICAgY2IuY2FsbCh0aGlzLCBlcnIsIHJlcyk7XG4gICAgfTtcbn1cbmV4cG9ydHMucGF0Y2hDYWxsYmFjayA9IHBhdGNoQ2FsbGJhY2s7XG5mdW5jdGlvbiBnZXRQb29sTmFtZShwb29sKSB7XG4gICAgbGV0IHBvb2xOYW1lID0gJyc7XG4gICAgcG9vbE5hbWUgKz0gKChwb29sID09PSBudWxsIHx8IHBvb2wgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHBvb2wuaG9zdCkgPyBgJHtwb29sLmhvc3R9YCA6ICd1bmtub3duX2hvc3QnKSArICc6JztcbiAgICBwb29sTmFtZSArPSAoKHBvb2wgPT09IG51bGwgfHwgcG9vbCA9PT0gdm9pZCAwID8gdm9pZCAwIDogcG9vbC5wb3J0KSA/IGAke3Bvb2wucG9ydH1gIDogJ3Vua25vd25fcG9ydCcpICsgJy8nO1xuICAgIHBvb2xOYW1lICs9IChwb29sID09PSBudWxsIHx8IHBvb2wgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHBvb2wuZGF0YWJhc2UpID8gYCR7cG9vbC5kYXRhYmFzZX1gIDogJ3Vua25vd25fZGF0YWJhc2UnO1xuICAgIHJldHVybiBwb29sTmFtZS50cmltKCk7XG59XG5leHBvcnRzLmdldFBvb2xOYW1lID0gZ2V0UG9vbE5hbWU7XG5mdW5jdGlvbiB1cGRhdGVDb3VudGVyKHBvb2xOYW1lLCBwb29sLCBjb25uZWN0aW9uQ291bnQsIGNvbm5lY3Rpb25QZW5kaW5nUmVxdWVzdHMsIGxhdGVzdENvdW50ZXIpIHtcbiAgICBjb25zdCBhbGwgPSBwb29sLnRvdGFsQ291bnQ7XG4gICAgY29uc3QgcGVuZGluZyA9IHBvb2wud2FpdGluZ0NvdW50O1xuICAgIGNvbnN0IGlkbGUgPSBwb29sLmlkbGVDb3VudDtcbiAgICBjb25zdCB1c2VkID0gYWxsIC0gaWRsZTtcbiAgICBjb25uZWN0aW9uQ291bnQuYWRkKHVzZWQgLSBsYXRlc3RDb3VudGVyLnVzZWQsIHtcbiAgICAgICAgW3NlbWNvbnZfMS5BVFRSX0RCX0NMSUVOVF9DT05ORUNUSU9OX1NUQVRFXTogc2VtY29udl8xLkRCX0NMSUVOVF9DT05ORUNUSU9OX1NUQVRFX1ZBTFVFX1VTRUQsXG4gICAgICAgIFtzZW1jb252XzEuQVRUUl9EQl9DTElFTlRfQ09OTkVDVElPTl9QT09MX05BTUVdOiBwb29sTmFtZSxcbiAgICB9KTtcbiAgICBjb25uZWN0aW9uQ291bnQuYWRkKGlkbGUgLSBsYXRlc3RDb3VudGVyLmlkbGUsIHtcbiAgICAgICAgW3NlbWNvbnZfMS5BVFRSX0RCX0NMSUVOVF9DT05ORUNUSU9OX1NUQVRFXTogc2VtY29udl8xLkRCX0NMSUVOVF9DT05ORUNUSU9OX1NUQVRFX1ZBTFVFX0lETEUsXG4gICAgICAgIFtzZW1jb252XzEuQVRUUl9EQl9DTElFTlRfQ09OTkVDVElPTl9QT09MX05BTUVdOiBwb29sTmFtZSxcbiAgICB9KTtcbiAgICBjb25uZWN0aW9uUGVuZGluZ1JlcXVlc3RzLmFkZChwZW5kaW5nIC0gbGF0ZXN0Q291bnRlci5wZW5kaW5nLCB7XG4gICAgICAgIFtzZW1jb252XzEuQVRUUl9EQl9DTElFTlRfQ09OTkVDVElPTl9QT09MX05BTUVdOiBwb29sTmFtZSxcbiAgICB9KTtcbiAgICByZXR1cm4geyB1c2VkOiB1c2VkLCBpZGxlOiBpZGxlLCBwZW5kaW5nOiBwZW5kaW5nIH07XG59XG5leHBvcnRzLnVwZGF0ZUNvdW50ZXIgPSB1cGRhdGVDb3VudGVyO1xuZnVuY3Rpb24gcGF0Y2hDYWxsYmFja1BHUG9vbChzcGFuLCBjYikge1xuICAgIHJldHVybiBmdW5jdGlvbiBwYXRjaGVkQ2FsbGJhY2soZXJyLCByZXMsIGRvbmUpIHtcbiAgICAgICAgaWYgKGVycikge1xuICAgICAgICAgICAgc3Bhbi5zZXRTdGF0dXMoe1xuICAgICAgICAgICAgICAgIGNvZGU6IGFwaV8xLlNwYW5TdGF0dXNDb2RlLkVSUk9SLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGVyci5tZXNzYWdlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgc3Bhbi5lbmQoKTtcbiAgICAgICAgY2IuY2FsbCh0aGlzLCBlcnIsIHJlcywgZG9uZSk7XG4gICAgfTtcbn1cbmV4cG9ydHMucGF0Y2hDYWxsYmFja1BHUG9vbCA9IHBhdGNoQ2FsbGJhY2tQR1Bvb2w7XG5mdW5jdGlvbiBwYXRjaENsaWVudENvbm5lY3RDYWxsYmFjayhzcGFuLCBjYikge1xuICAgIHJldHVybiBmdW5jdGlvbiBwYXRjaGVkQ2xpZW50Q29ubmVjdENhbGxiYWNrKGVycikge1xuICAgICAgICBpZiAoZXJyKSB7XG4gICAgICAgICAgICBzcGFuLnNldFN0YXR1cyh7XG4gICAgICAgICAgICAgICAgY29kZTogYXBpXzEuU3BhblN0YXR1c0NvZGUuRVJST1IsXG4gICAgICAgICAgICAgICAgbWVzc2FnZTogZXJyLm1lc3NhZ2UsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBzcGFuLmVuZCgpO1xuICAgICAgICBjYi5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICAgIH07XG59XG5leHBvcnRzLnBhdGNoQ2xpZW50Q29ubmVjdENhbGxiYWNrID0gcGF0Y2hDbGllbnRDb25uZWN0Q2FsbGJhY2s7XG4vKipcbiAqIEF0dGVtcHQgdG8gZ2V0IGEgbWVzc2FnZSBzdHJpbmcgZnJvbSBhIHRocm93biB2YWx1ZSwgd2hpbGUgYmVpbmcgcXVpdGVcbiAqIGRlZmVuc2l2ZSwgdG8gcmVjb2duaXplIHRoZSBmYWN0IHRoYXQsIGluIEpTLCBhbnkga2luZCBvZiB2YWx1ZSAoZXZlblxuICogcHJpbWl0aXZlcykgY2FuIGJlIHRocm93bi5cbiAqL1xuZnVuY3Rpb24gZ2V0RXJyb3JNZXNzYWdlKGUpIHtcbiAgICByZXR1cm4gdHlwZW9mIGUgPT09ICdvYmplY3QnICYmIGUgIT09IG51bGwgJiYgJ21lc3NhZ2UnIGluIGVcbiAgICAgICAgPyBTdHJpbmcoZS5tZXNzYWdlKVxuICAgICAgICA6IHVuZGVmaW5lZDtcbn1cbmV4cG9ydHMuZ2V0RXJyb3JNZXNzYWdlID0gZ2V0RXJyb3JNZXNzYWdlO1xuZnVuY3Rpb24gaXNPYmplY3RXaXRoVGV4dFN0cmluZyhpdCkge1xuICAgIHZhciBfYTtcbiAgICByZXR1cm4gKHR5cGVvZiBpdCA9PT0gJ29iamVjdCcgJiZcbiAgICAgICAgdHlwZW9mICgoX2EgPSBpdCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnRleHQpID09PSAnc3RyaW5nJyk7XG59XG5leHBvcnRzLmlzT2JqZWN0V2l0aFRleHRTdHJpbmcgPSBpc09iamVjdFdpdGhUZXh0U3RyaW5nO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.51.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-pg';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXBnL2J1aWxkL3NyYy92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG9CQUFvQixHQUFHLHVCQUF1QjtBQUM5QztBQUNBLHVCQUF1QjtBQUN2QixvQkFBb0I7QUFDcEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYjkwY2QxYTUzN2RhZmUyOTRjZWU5NjNlNDQzMmI0NmFcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1wZ1xcYnVpbGRcXHNyY1xcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlBBQ0tBR0VfTkFNRSA9IGV4cG9ydHMuUEFDS0FHRV9WRVJTSU9OID0gdm9pZCAwO1xuLy8gdGhpcyBpcyBhdXRvZ2VuZXJhdGVkIGZpbGUsIHNlZSBzY3JpcHRzL3ZlcnNpb24tdXBkYXRlLmpzXG5leHBvcnRzLlBBQ0tBR0VfVkVSU0lPTiA9ICcwLjUxLjEnO1xuZXhwb3J0cy5QQUNLQUdFX05BTUUgPSAnQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXBnJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Postgresql specific attributes not covered by semantic conventions\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"PG_VALUES\"] = \"db.postgresql.values\";\n    AttributeNames[\"PG_PLAN\"] = \"db.postgresql.plan\";\n    AttributeNames[\"IDLE_TIMEOUT_MILLIS\"] = \"db.postgresql.idle.timeout.millis\";\n    AttributeNames[\"MAX_CLIENT\"] = \"db.postgresql.max.client\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SpanNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Contains span names produced by instrumentation\nvar SpanNames;\n(function (SpanNames) {\n    SpanNames[\"QUERY_PREFIX\"] = \"pg.query\";\n    SpanNames[\"CONNECT\"] = \"pg.connect\";\n    SpanNames[\"POOL_CONNECT\"] = \"pg-pool.connect\";\n})(SpanNames = exports.SpanNames || (exports.SpanNames = {}));\n//# sourceMappingURL=SpanNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXBnL2J1aWxkL3NyYy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsb0NBQW9DLGdCQUFnQjtBQUN2RixDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYSxtQkFBTyxDQUFDLGtNQUFtQjtBQUN4QyxhQUFhLG1CQUFPLENBQUMsOEtBQVM7QUFDOUIsYUFBYSxtQkFBTyxDQUFDLDRNQUF3QjtBQUM3QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YVxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLXBnXFxidWlsZFxcc3JjXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG52YXIgX19jcmVhdGVCaW5kaW5nID0gKHRoaXMgJiYgdGhpcy5fX2NyZWF0ZUJpbmRpbmcpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgazIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfSk7XG59KSA6IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgb1trMl0gPSBtW2tdO1xufSkpO1xudmFyIF9fZXhwb3J0U3RhciA9ICh0aGlzICYmIHRoaXMuX19leHBvcnRTdGFyKSB8fCBmdW5jdGlvbihtLCBleHBvcnRzKSB7XG4gICAgZm9yICh2YXIgcCBpbiBtKSBpZiAocCAhPT0gXCJkZWZhdWx0XCIgJiYgIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChleHBvcnRzLCBwKSkgX19jcmVhdGVCaW5kaW5nKGV4cG9ydHMsIG0sIHApO1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9pbnN0cnVtZW50YXRpb25cIiksIGV4cG9ydHMpO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL3R5cGVzXCIpLCBleHBvcnRzKTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9lbnVtcy9BdHRyaWJ1dGVOYW1lc1wiKSwgZXhwb3J0cyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PgInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js\");\nconst utils = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js\");\nconst sql_common_1 = __webpack_require__(/*! @opentelemetry/sql-common */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js\");\nconst SpanNames_1 = __webpack_require__(/*! ./enums/SpanNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst semconv_1 = __webpack_require__(/*! ./semconv */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\");\nfunction extractModuleExports(module) {\n    return module[Symbol.toStringTag] === 'Module'\n        ? module.default // ESM\n        : module; // CommonJS\n}\nclass PgInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        // Pool events connect, acquire, release and remove can be called\n        // multiple times without changing the values of total, idle and waiting\n        // connections. The _connectionsCounter is used to keep track of latest\n        // values and only update the metrics _connectionsCount and _connectionPendingRequests\n        // when the value change.\n        this._connectionsCounter = {\n            used: 0,\n            idle: 0,\n            pending: 0,\n        };\n    }\n    _updateMetricInstruments() {\n        this._operationDuration = this.meter.createHistogram(semconv_1.METRIC_DB_CLIENT_OPERATION_DURATION, {\n            description: 'Duration of database client operations.',\n            unit: 's',\n            valueType: api_1.ValueType.DOUBLE,\n            advice: {\n                explicitBucketBoundaries: [\n                    0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10,\n                ],\n            },\n        });\n        this._connectionsCounter = {\n            idle: 0,\n            pending: 0,\n            used: 0,\n        };\n        this._connectionsCount = this.meter.createUpDownCounter(semconv_1.METRIC_DB_CLIENT_CONNECTION_COUNT, {\n            description: 'The number of connections that are currently in state described by the state attribute.',\n            unit: '{connection}',\n        });\n        this._connectionPendingRequests = this.meter.createUpDownCounter(semconv_1.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS, {\n            description: 'The number of current pending requests for an open connection.',\n            unit: '{connection}',\n        });\n    }\n    init() {\n        const SUPPORTED_PG_VERSIONS = ['>=8.0.3 <9'];\n        const modulePgNativeClient = new instrumentation_1.InstrumentationNodeModuleFile('pg/lib/native/client.js', SUPPORTED_PG_VERSIONS, this._patchPgClient.bind(this), this._unpatchPgClient.bind(this));\n        const modulePgClient = new instrumentation_1.InstrumentationNodeModuleFile('pg/lib/client.js', SUPPORTED_PG_VERSIONS, this._patchPgClient.bind(this), this._unpatchPgClient.bind(this));\n        const modulePG = new instrumentation_1.InstrumentationNodeModuleDefinition('pg', SUPPORTED_PG_VERSIONS, (module) => {\n            const moduleExports = extractModuleExports(module);\n            this._patchPgClient(moduleExports.Client);\n            return module;\n        }, (module) => {\n            const moduleExports = extractModuleExports(module);\n            this._unpatchPgClient(moduleExports.Client);\n            return module;\n        }, [modulePgClient, modulePgNativeClient]);\n        const modulePGPool = new instrumentation_1.InstrumentationNodeModuleDefinition('pg-pool', ['>=2.0.0 <4'], (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                this._unwrap(moduleExports.prototype, 'connect');\n            }\n            this._wrap(moduleExports.prototype, 'connect', this._getPoolConnectPatch());\n            return moduleExports;\n        }, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n                this._unwrap(moduleExports.prototype, 'connect');\n            }\n        });\n        return [modulePG, modulePGPool];\n    }\n    _patchPgClient(module) {\n        if (!module) {\n            return;\n        }\n        const moduleExports = extractModuleExports(module);\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.query)) {\n            this._unwrap(moduleExports.prototype, 'query');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n            this._unwrap(moduleExports.prototype, 'connect');\n        }\n        this._wrap(moduleExports.prototype, 'query', this._getClientQueryPatch());\n        this._wrap(moduleExports.prototype, 'connect', this._getClientConnectPatch());\n        return module;\n    }\n    _unpatchPgClient(module) {\n        const moduleExports = extractModuleExports(module);\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.query)) {\n            this._unwrap(moduleExports.prototype, 'query');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.connect)) {\n            this._unwrap(moduleExports.prototype, 'connect');\n        }\n        return module;\n    }\n    _getClientConnectPatch() {\n        const plugin = this;\n        return (original) => {\n            return function connect(callback) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return original.call(this, callback);\n                }\n                const span = plugin.tracer.startSpan(SpanNames_1.SpanNames.CONNECT, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: utils.getSemanticAttributesFromConnection(this),\n                });\n                if (callback) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    callback = utils.patchClientConnectCallback(span, callback);\n                    if (parentSpan) {\n                        callback = api_1.context.bind(api_1.context.active(), callback);\n                    }\n                }\n                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return original.call(this, callback);\n                });\n                return handleConnectResult(span, connectResult);\n            };\n        };\n    }\n    recordOperationDuration(attributes, startTime) {\n        const metricsAttributes = {};\n        const keysToCopy = [\n            semantic_conventions_1.SEMATTRS_DB_SYSTEM,\n            semconv_1.ATTR_DB_NAMESPACE,\n            semantic_conventions_1.ATTR_ERROR_TYPE,\n            semantic_conventions_1.ATTR_SERVER_PORT,\n            semantic_conventions_1.ATTR_SERVER_ADDRESS,\n            semconv_1.ATTR_DB_OPERATION_NAME,\n        ];\n        keysToCopy.forEach(key => {\n            if (key in attributes) {\n                metricsAttributes[key] = attributes[key];\n            }\n        });\n        const durationSeconds = (0, core_1.hrTimeToMilliseconds)((0, core_1.hrTimeDuration)(startTime, (0, core_1.hrTime)())) / 1000;\n        this._operationDuration.record(durationSeconds, metricsAttributes);\n    }\n    _getClientQueryPatch() {\n        const plugin = this;\n        return (original) => {\n            this._diag.debug('Patching pg.Client.prototype.query');\n            return function query(...args) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return original.apply(this, args);\n                }\n                const startTime = (0, core_1.hrTime)();\n                // client.query(text, cb?), client.query(text, values, cb?), and\n                // client.query(configObj, cb?) are all valid signatures. We construct\n                // a queryConfig obj from all (valid) signatures to build the span in a\n                // unified way. We verify that we at least have query text, and code\n                // defensively when dealing with `queryConfig` after that (to handle all\n                // the other invalid cases, like a non-array for values being provided).\n                // The type casts here reflect only what we've actually validated.\n                const arg0 = args[0];\n                const firstArgIsString = typeof arg0 === 'string';\n                const firstArgIsQueryObjectWithText = utils.isObjectWithTextString(arg0);\n                // TODO: remove the `as ...` casts below when the TS version is upgraded.\n                // Newer TS versions will use the result of firstArgIsQueryObjectWithText\n                // to properly narrow arg0, but TS 4.3.5 does not.\n                const queryConfig = firstArgIsString\n                    ? {\n                        text: arg0,\n                        values: Array.isArray(args[1]) ? args[1] : undefined,\n                    }\n                    : firstArgIsQueryObjectWithText\n                        ? arg0\n                        : undefined;\n                const attributes = {\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n                    [semconv_1.ATTR_DB_NAMESPACE]: this.database,\n                    [semantic_conventions_1.ATTR_SERVER_PORT]: this.connectionParameters.port,\n                    [semantic_conventions_1.ATTR_SERVER_ADDRESS]: this.connectionParameters.host,\n                };\n                if (queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.text) {\n                    attributes[semconv_1.ATTR_DB_OPERATION_NAME] =\n                        utils.parseNormalizedOperationName(queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.text);\n                }\n                const recordDuration = () => {\n                    plugin.recordOperationDuration(attributes, startTime);\n                };\n                const instrumentationConfig = plugin.getConfig();\n                const span = utils.handleConfigQuery.call(this, plugin.tracer, instrumentationConfig, queryConfig);\n                // Modify query text w/ a tracing comment before invoking original for\n                // tracing, but only if args[0] has one of our expected shapes.\n                if (instrumentationConfig.addSqlCommenterCommentToQueries) {\n                    if (firstArgIsString) {\n                        args[0] = (0, sql_common_1.addSqlCommenterComment)(span, arg0);\n                    }\n                    else if (firstArgIsQueryObjectWithText && !('name' in arg0)) {\n                        // In the case of a query object, we need to ensure there's no name field\n                        // as this indicates a prepared query, where the comment would remain the same\n                        // for every invocation and contain an outdated trace context.\n                        args[0] = Object.assign(Object.assign({}, arg0), { text: (0, sql_common_1.addSqlCommenterComment)(span, arg0.text) });\n                    }\n                }\n                // Bind callback (if any) to parent span (if any)\n                if (args.length > 0) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    if (typeof args[args.length - 1] === 'function') {\n                        // Patch ParameterQuery callback\n                        args[args.length - 1] = utils.patchCallback(instrumentationConfig, span, args[args.length - 1], // nb: not type safe.\n                        attributes, recordDuration);\n                        // If a parent span exists, bind the callback\n                        if (parentSpan) {\n                            args[args.length - 1] = api_1.context.bind(api_1.context.active(), args[args.length - 1]);\n                        }\n                    }\n                    else if (typeof (queryConfig === null || queryConfig === void 0 ? void 0 : queryConfig.callback) === 'function') {\n                        // Patch ConfigQuery callback\n                        let callback = utils.patchCallback(plugin.getConfig(), span, queryConfig.callback, // nb: not type safe.\n                        attributes, recordDuration);\n                        // If a parent span existed, bind the callback\n                        if (parentSpan) {\n                            callback = api_1.context.bind(api_1.context.active(), callback);\n                        }\n                        args[0].callback = callback;\n                    }\n                }\n                const { requestHook } = instrumentationConfig;\n                if (typeof requestHook === 'function' && queryConfig) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                        // pick keys to expose explicitly, so we're not leaking pg package\n                        // internals that are subject to change\n                        const { database, host, port, user } = this.connectionParameters;\n                        const connection = { database, host, port, user };\n                        requestHook(span, {\n                            connection,\n                            query: {\n                                text: queryConfig.text,\n                                // nb: if `client.query` is called with illegal arguments\n                                // (e.g., if `queryConfig.values` is passed explicitly, but a\n                                // non-array is given), then the type casts will be wrong. But\n                                // we leave it up to the queryHook to handle that, and we\n                                // catch and swallow any errors it throws. The other options\n                                // are all worse. E.g., we could leave `queryConfig.values`\n                                // and `queryConfig.name` as `unknown`, but then the hook body\n                                // would be forced to validate (or cast) them before using\n                                // them, which seems incredibly cumbersome given that these\n                                // casts will be correct 99.9% of the time -- and pg.query\n                                // will immediately throw during development in the other .1%\n                                // of cases. Alternatively, we could simply skip calling the\n                                // hook when `values` or `name` don't have the expected type,\n                                // but that would add unnecessary validation overhead to every\n                                // hook invocation and possibly be even more confusing/unexpected.\n                                values: queryConfig.values,\n                                name: queryConfig.name,\n                            },\n                        });\n                    }, err => {\n                        if (err) {\n                            plugin._diag.error('Error running query hook', err);\n                        }\n                    }, true);\n                }\n                let result;\n                try {\n                    result = original.apply(this, args);\n                }\n                catch (e) {\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: utils.getErrorMessage(e),\n                    });\n                    span.end();\n                    throw e;\n                }\n                // Bind promise to parent span and end the span\n                if (result instanceof Promise) {\n                    return result\n                        .then((result) => {\n                        // Return a pass-along promise which ends the span and then goes to user's orig resolvers\n                        return new Promise(resolve => {\n                            utils.handleExecutionResult(plugin.getConfig(), span, result);\n                            recordDuration();\n                            span.end();\n                            resolve(result);\n                        });\n                    })\n                        .catch((error) => {\n                        return new Promise((_, reject) => {\n                            span.setStatus({\n                                code: api_1.SpanStatusCode.ERROR,\n                                message: error.message,\n                            });\n                            recordDuration();\n                            span.end();\n                            reject(error);\n                        });\n                    });\n                }\n                // else returns void\n                return result; // void\n            };\n        };\n    }\n    _setPoolConnectEventListeners(pgPool) {\n        if (pgPool[internal_types_1.EVENT_LISTENERS_SET])\n            return;\n        const poolName = utils.getPoolName(pgPool.options);\n        pgPool.on('connect', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('acquire', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('remove', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool.on('release', () => {\n            this._connectionsCounter = utils.updateCounter(poolName, pgPool, this._connectionsCount, this._connectionPendingRequests, this._connectionsCounter);\n        });\n        pgPool[internal_types_1.EVENT_LISTENERS_SET] = true;\n    }\n    _getPoolConnectPatch() {\n        const plugin = this;\n        return (originalConnect) => {\n            return function connect(callback) {\n                if (utils.shouldSkipInstrumentation(plugin.getConfig())) {\n                    return originalConnect.call(this, callback);\n                }\n                // setup span\n                const span = plugin.tracer.startSpan(SpanNames_1.SpanNames.POOL_CONNECT, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes: utils.getSemanticAttributesFromPool(this.options),\n                });\n                plugin._setPoolConnectEventListeners(this);\n                if (callback) {\n                    const parentSpan = api_1.trace.getSpan(api_1.context.active());\n                    callback = utils.patchCallbackPGPool(span, callback);\n                    // If a parent span exists, bind the callback\n                    if (parentSpan) {\n                        callback = api_1.context.bind(api_1.context.active(), callback);\n                    }\n                }\n                const connectResult = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return originalConnect.call(this, callback);\n                });\n                return handleConnectResult(span, connectResult);\n            };\n        };\n    }\n}\nexports.PgInstrumentation = PgInstrumentation;\nfunction handleConnectResult(span, connectResult) {\n    if (!(connectResult instanceof Promise)) {\n        return connectResult;\n    }\n    const connectResultPromise = connectResult;\n    return api_1.context.bind(api_1.context.active(), connectResultPromise\n        .then(result => {\n        span.end();\n        return result;\n    })\n        .catch((error) => {\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: utils.getErrorMessage(error),\n        });\n        span.end();\n        return Promise.reject(error);\n    }));\n}\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.EVENT_LISTENERS_SET = void 0;\nexports.EVENT_LISTENERS_SET = Symbol('opentelemetry.instrumentation.pg.eventListenersSet');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/internal-types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.METRIC_DB_CLIENT_OPERATION_DURATION = exports.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = exports.METRIC_DB_CLIENT_CONNECTION_COUNT = exports.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = exports.DB_CLIENT_CONNECTION_STATE_VALUE_USED = exports.ATTR_DB_OPERATION_NAME = exports.ATTR_DB_NAMESPACE = exports.ATTR_DB_CLIENT_CONNECTION_STATE = exports.ATTR_DB_CLIENT_CONNECTION_POOL_NAME = void 0;\n/**\n * The name of the connection pool; unique within the instrumented application. In case the connection pool implementation doesn't provide a name, instrumentation **SHOULD** use a combination of parameters that would make the name unique, for example, combining attributes `server.address`, `server.port`, and `db.namespace`, formatted as `server.address:server.port/db.namespace`. Instrumentations that generate connection pool name following different patterns **SHOULD** document it.\n *\n * @example myDataSource\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_CLIENT_CONNECTION_POOL_NAME = 'db.client.connection.pool.name';\n/**\n * The state of a connection in the pool\n *\n * @example idle\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_CLIENT_CONNECTION_STATE = 'db.client.connection.state';\n/**\n * The name of the database, fully qualified within the server address and port.\n *\n * @example customers\n * @example test.users\n *\n * @note If a database system has multiple namespace components, they **SHOULD** be concatenated (potentially using database system specific conventions) from most general to most specific namespace component, and more specific namespaces **SHOULD NOT** be captured without the more general namespaces, to ensure that \"startswith\" queries for the more general namespaces will be valid.\n * Semantic conventions for individual database systems **SHOULD** document what `db.namespace` means in the context of that system.\n * It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_NAMESPACE = 'db.namespace';\n/**\n * The name of the operation or command being executed.\n *\n * @example findAndModify\n * @example HMSET\n * @example SELECT\n *\n * @note It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * If the operation name is parsed from the query text, it **SHOULD** be the first operation name found in the query.\n * For batch operations, if the individual operations are known to have the same operation name then that operation name **SHOULD** be used prepended by `BATCH `, otherwise `db.operation.name` **SHOULD** be `BATCH` or some other database system specific term if more applicable.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.ATTR_DB_OPERATION_NAME = 'db.operation.name';\n/**\n * Enum value \"used\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexports.DB_CLIENT_CONNECTION_STATE_VALUE_USED = 'used';\n/**\n * Enum value \"idle\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexports.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = 'idle';\n/**\n * The number of connections that are currently in state described by the `state` attribute\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_CONNECTION_COUNT = 'db.client.connection.count';\n/**\n * The number of current pending requests for an open connection\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = 'db.client.connection.pending_requests';\n/**\n * Duration of database client operations.\n *\n * @note Batch operations **SHOULD** be recorded as a single operation.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexports.METRIC_DB_CLIENT_OPERATION_DURATION = 'db.client.operation.duration';\n//# sourceMappingURL=semconv.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXBnL2J1aWxkL3NyYy9zZW1jb252LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDJDQUEyQyxHQUFHLG9EQUFvRCxHQUFHLHlDQUF5QyxHQUFHLDZDQUE2QyxHQUFHLDZDQUE2QyxHQUFHLDhCQUE4QixHQUFHLHlCQUF5QixHQUFHLHVDQUF1QyxHQUFHLDJDQUEyQztBQUNuWTtBQUNBLG9DQUFvQztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQSxvQ0FBb0Msc0NBQXNDO0FBQzFFO0FBQ0EsNkNBQTZDO0FBQzdDO0FBQ0Esb0NBQW9DLHNDQUFzQztBQUMxRTtBQUNBLDZDQUE2QztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvREFBb0Q7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkM7QUFDM0MiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYjkwY2QxYTUzN2RhZmUyOTRjZWU5NjNlNDQzMmI0NmFcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1wZ1xcYnVpbGRcXHNyY1xcc2VtY29udi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLk1FVFJJQ19EQl9DTElFTlRfT1BFUkFUSU9OX0RVUkFUSU9OID0gZXhwb3J0cy5NRVRSSUNfREJfQ0xJRU5UX0NPTk5FQ1RJT05fUEVORElOR19SRVFVRVNUUyA9IGV4cG9ydHMuTUVUUklDX0RCX0NMSUVOVF9DT05ORUNUSU9OX0NPVU5UID0gZXhwb3J0cy5EQl9DTElFTlRfQ09OTkVDVElPTl9TVEFURV9WQUxVRV9JRExFID0gZXhwb3J0cy5EQl9DTElFTlRfQ09OTkVDVElPTl9TVEFURV9WQUxVRV9VU0VEID0gZXhwb3J0cy5BVFRSX0RCX09QRVJBVElPTl9OQU1FID0gZXhwb3J0cy5BVFRSX0RCX05BTUVTUEFDRSA9IGV4cG9ydHMuQVRUUl9EQl9DTElFTlRfQ09OTkVDVElPTl9TVEFURSA9IGV4cG9ydHMuQVRUUl9EQl9DTElFTlRfQ09OTkVDVElPTl9QT09MX05BTUUgPSB2b2lkIDA7XG4vKipcbiAqIFRoZSBuYW1lIG9mIHRoZSBjb25uZWN0aW9uIHBvb2w7IHVuaXF1ZSB3aXRoaW4gdGhlIGluc3RydW1lbnRlZCBhcHBsaWNhdGlvbi4gSW4gY2FzZSB0aGUgY29ubmVjdGlvbiBwb29sIGltcGxlbWVudGF0aW9uIGRvZXNuJ3QgcHJvdmlkZSBhIG5hbWUsIGluc3RydW1lbnRhdGlvbiAqKlNIT1VMRCoqIHVzZSBhIGNvbWJpbmF0aW9uIG9mIHBhcmFtZXRlcnMgdGhhdCB3b3VsZCBtYWtlIHRoZSBuYW1lIHVuaXF1ZSwgZm9yIGV4YW1wbGUsIGNvbWJpbmluZyBhdHRyaWJ1dGVzIGBzZXJ2ZXIuYWRkcmVzc2AsIGBzZXJ2ZXIucG9ydGAsIGFuZCBgZGIubmFtZXNwYWNlYCwgZm9ybWF0dGVkIGFzIGBzZXJ2ZXIuYWRkcmVzczpzZXJ2ZXIucG9ydC9kYi5uYW1lc3BhY2VgLiBJbnN0cnVtZW50YXRpb25zIHRoYXQgZ2VuZXJhdGUgY29ubmVjdGlvbiBwb29sIG5hbWUgZm9sbG93aW5nIGRpZmZlcmVudCBwYXR0ZXJucyAqKlNIT1VMRCoqIGRvY3VtZW50IGl0LlxuICpcbiAqIEBleGFtcGxlIG15RGF0YVNvdXJjZVxuICpcbiAqIEBleHBlcmltZW50YWwgVGhpcyBhdHRyaWJ1dGUgaXMgZXhwZXJpbWVudGFsIGFuZCBpcyBzdWJqZWN0IHRvIGJyZWFraW5nIGNoYW5nZXMgaW4gbWlub3IgcmVsZWFzZXMgb2YgYEBvcGVudGVsZW1ldHJ5L3NlbWFudGljLWNvbnZlbnRpb25zYC5cbiAqL1xuZXhwb3J0cy5BVFRSX0RCX0NMSUVOVF9DT05ORUNUSU9OX1BPT0xfTkFNRSA9ICdkYi5jbGllbnQuY29ubmVjdGlvbi5wb29sLm5hbWUnO1xuLyoqXG4gKiBUaGUgc3RhdGUgb2YgYSBjb25uZWN0aW9uIGluIHRoZSBwb29sXG4gKlxuICogQGV4YW1wbGUgaWRsZVxuICpcbiAqIEBleHBlcmltZW50YWwgVGhpcyBhdHRyaWJ1dGUgaXMgZXhwZXJpbWVudGFsIGFuZCBpcyBzdWJqZWN0IHRvIGJyZWFraW5nIGNoYW5nZXMgaW4gbWlub3IgcmVsZWFzZXMgb2YgYEBvcGVudGVsZW1ldHJ5L3NlbWFudGljLWNvbnZlbnRpb25zYC5cbiAqL1xuZXhwb3J0cy5BVFRSX0RCX0NMSUVOVF9DT05ORUNUSU9OX1NUQVRFID0gJ2RiLmNsaWVudC5jb25uZWN0aW9uLnN0YXRlJztcbi8qKlxuICogVGhlIG5hbWUgb2YgdGhlIGRhdGFiYXNlLCBmdWxseSBxdWFsaWZpZWQgd2l0aGluIHRoZSBzZXJ2ZXIgYWRkcmVzcyBhbmQgcG9ydC5cbiAqXG4gKiBAZXhhbXBsZSBjdXN0b21lcnNcbiAqIEBleGFtcGxlIHRlc3QudXNlcnNcbiAqXG4gKiBAbm90ZSBJZiBhIGRhdGFiYXNlIHN5c3RlbSBoYXMgbXVsdGlwbGUgbmFtZXNwYWNlIGNvbXBvbmVudHMsIHRoZXkgKipTSE9VTEQqKiBiZSBjb25jYXRlbmF0ZWQgKHBvdGVudGlhbGx5IHVzaW5nIGRhdGFiYXNlIHN5c3RlbSBzcGVjaWZpYyBjb252ZW50aW9ucykgZnJvbSBtb3N0IGdlbmVyYWwgdG8gbW9zdCBzcGVjaWZpYyBuYW1lc3BhY2UgY29tcG9uZW50LCBhbmQgbW9yZSBzcGVjaWZpYyBuYW1lc3BhY2VzICoqU0hPVUxEIE5PVCoqIGJlIGNhcHR1cmVkIHdpdGhvdXQgdGhlIG1vcmUgZ2VuZXJhbCBuYW1lc3BhY2VzLCB0byBlbnN1cmUgdGhhdCBcInN0YXJ0c3dpdGhcIiBxdWVyaWVzIGZvciB0aGUgbW9yZSBnZW5lcmFsIG5hbWVzcGFjZXMgd2lsbCBiZSB2YWxpZC5cbiAqIFNlbWFudGljIGNvbnZlbnRpb25zIGZvciBpbmRpdmlkdWFsIGRhdGFiYXNlIHN5c3RlbXMgKipTSE9VTEQqKiBkb2N1bWVudCB3aGF0IGBkYi5uYW1lc3BhY2VgIG1lYW5zIGluIHRoZSBjb250ZXh0IG9mIHRoYXQgc3lzdGVtLlxuICogSXQgaXMgKipSRUNPTU1FTkRFRCoqIHRvIGNhcHR1cmUgdGhlIHZhbHVlIGFzIHByb3ZpZGVkIGJ5IHRoZSBhcHBsaWNhdGlvbiB3aXRob3V0IGF0dGVtcHRpbmcgdG8gZG8gYW55IGNhc2Ugbm9ybWFsaXphdGlvbi5cbiAqIFRoaXMgYXR0cmlidXRlIGhhcyBzdGFiaWxpdHkgbGV2ZWwgUkVMRUFTRSBDQU5ESURBVEUuXG4gKlxuICogQGV4cGVyaW1lbnRhbCBUaGlzIGF0dHJpYnV0ZSBpcyBleHBlcmltZW50YWwgYW5kIGlzIHN1YmplY3QgdG8gYnJlYWtpbmcgY2hhbmdlcyBpbiBtaW5vciByZWxlYXNlcyBvZiBgQG9wZW50ZWxlbWV0cnkvc2VtYW50aWMtY29udmVudGlvbnNgLlxuICovXG5leHBvcnRzLkFUVFJfREJfTkFNRVNQQUNFID0gJ2RiLm5hbWVzcGFjZSc7XG4vKipcbiAqIFRoZSBuYW1lIG9mIHRoZSBvcGVyYXRpb24gb3IgY29tbWFuZCBiZWluZyBleGVjdXRlZC5cbiAqXG4gKiBAZXhhbXBsZSBmaW5kQW5kTW9kaWZ5XG4gKiBAZXhhbXBsZSBITVNFVFxuICogQGV4YW1wbGUgU0VMRUNUXG4gKlxuICogQG5vdGUgSXQgaXMgKipSRUNPTU1FTkRFRCoqIHRvIGNhcHR1cmUgdGhlIHZhbHVlIGFzIHByb3ZpZGVkIGJ5IHRoZSBhcHBsaWNhdGlvbiB3aXRob3V0IGF0dGVtcHRpbmcgdG8gZG8gYW55IGNhc2Ugbm9ybWFsaXphdGlvbi5cbiAqIElmIHRoZSBvcGVyYXRpb24gbmFtZSBpcyBwYXJzZWQgZnJvbSB0aGUgcXVlcnkgdGV4dCwgaXQgKipTSE9VTEQqKiBiZSB0aGUgZmlyc3Qgb3BlcmF0aW9uIG5hbWUgZm91bmQgaW4gdGhlIHF1ZXJ5LlxuICogRm9yIGJhdGNoIG9wZXJhdGlvbnMsIGlmIHRoZSBpbmRpdmlkdWFsIG9wZXJhdGlvbnMgYXJlIGtub3duIHRvIGhhdmUgdGhlIHNhbWUgb3BlcmF0aW9uIG5hbWUgdGhlbiB0aGF0IG9wZXJhdGlvbiBuYW1lICoqU0hPVUxEKiogYmUgdXNlZCBwcmVwZW5kZWQgYnkgYEJBVENIIGAsIG90aGVyd2lzZSBgZGIub3BlcmF0aW9uLm5hbWVgICoqU0hPVUxEKiogYmUgYEJBVENIYCBvciBzb21lIG90aGVyIGRhdGFiYXNlIHN5c3RlbSBzcGVjaWZpYyB0ZXJtIGlmIG1vcmUgYXBwbGljYWJsZS5cbiAqIFRoaXMgYXR0cmlidXRlIGhhcyBzdGFiaWxpdHkgbGV2ZWwgUkVMRUFTRSBDQU5ESURBVEUuXG4gKlxuICogQGV4cGVyaW1lbnRhbCBUaGlzIGF0dHJpYnV0ZSBpcyBleHBlcmltZW50YWwgYW5kIGlzIHN1YmplY3QgdG8gYnJlYWtpbmcgY2hhbmdlcyBpbiBtaW5vciByZWxlYXNlcyBvZiBgQG9wZW50ZWxlbWV0cnkvc2VtYW50aWMtY29udmVudGlvbnNgLlxuICovXG5leHBvcnRzLkFUVFJfREJfT1BFUkFUSU9OX05BTUUgPSAnZGIub3BlcmF0aW9uLm5hbWUnO1xuLyoqXG4gKiBFbnVtIHZhbHVlIFwidXNlZFwiIGZvciBhdHRyaWJ1dGUge0BsaW5rIEFUVFJfREJfQ0xJRU5UX0NPTk5FQ1RJT05fU1RBVEV9LlxuICovXG5leHBvcnRzLkRCX0NMSUVOVF9DT05ORUNUSU9OX1NUQVRFX1ZBTFVFX1VTRUQgPSAndXNlZCc7XG4vKipcbiAqIEVudW0gdmFsdWUgXCJpZGxlXCIgZm9yIGF0dHJpYnV0ZSB7QGxpbmsgQVRUUl9EQl9DTElFTlRfQ09OTkVDVElPTl9TVEFURX0uXG4gKi9cbmV4cG9ydHMuREJfQ0xJRU5UX0NPTk5FQ1RJT05fU1RBVEVfVkFMVUVfSURMRSA9ICdpZGxlJztcbi8qKlxuICogVGhlIG51bWJlciBvZiBjb25uZWN0aW9ucyB0aGF0IGFyZSBjdXJyZW50bHkgaW4gc3RhdGUgZGVzY3JpYmVkIGJ5IHRoZSBgc3RhdGVgIGF0dHJpYnV0ZVxuICpcbiAqIEBleHBlcmltZW50YWwgVGhpcyBtZXRyaWMgaXMgZXhwZXJpbWVudGFsIGFuZCBpcyBzdWJqZWN0IHRvIGJyZWFraW5nIGNoYW5nZXMgaW4gbWlub3IgcmVsZWFzZXMgb2YgYEBvcGVudGVsZW1ldHJ5L3NlbWFudGljLWNvbnZlbnRpb25zYC5cbiAqL1xuZXhwb3J0cy5NRVRSSUNfREJfQ0xJRU5UX0NPTk5FQ1RJT05fQ09VTlQgPSAnZGIuY2xpZW50LmNvbm5lY3Rpb24uY291bnQnO1xuLyoqXG4gKiBUaGUgbnVtYmVyIG9mIGN1cnJlbnQgcGVuZGluZyByZXF1ZXN0cyBmb3IgYW4gb3BlbiBjb25uZWN0aW9uXG4gKlxuICogQGV4cGVyaW1lbnRhbCBUaGlzIG1ldHJpYyBpcyBleHBlcmltZW50YWwgYW5kIGlzIHN1YmplY3QgdG8gYnJlYWtpbmcgY2hhbmdlcyBpbiBtaW5vciByZWxlYXNlcyBvZiBgQG9wZW50ZWxlbWV0cnkvc2VtYW50aWMtY29udmVudGlvbnNgLlxuICovXG5leHBvcnRzLk1FVFJJQ19EQl9DTElFTlRfQ09OTkVDVElPTl9QRU5ESU5HX1JFUVVFU1RTID0gJ2RiLmNsaWVudC5jb25uZWN0aW9uLnBlbmRpbmdfcmVxdWVzdHMnO1xuLyoqXG4gKiBEdXJhdGlvbiBvZiBkYXRhYmFzZSBjbGllbnQgb3BlcmF0aW9ucy5cbiAqXG4gKiBAbm90ZSBCYXRjaCBvcGVyYXRpb25zICoqU0hPVUxEKiogYmUgcmVjb3JkZWQgYXMgYSBzaW5nbGUgb3BlcmF0aW9uLlxuICpcbiAqIEBleHBlcmltZW50YWwgVGhpcyBtZXRyaWMgaXMgZXhwZXJpbWVudGFsIGFuZCBpcyBzdWJqZWN0IHRvIGJyZWFraW5nIGNoYW5nZXMgaW4gbWlub3IgcmVsZWFzZXMgb2YgYEBvcGVudGVsZW1ldHJ5L3NlbWFudGljLWNvbnZlbnRpb25zYC5cbiAqL1xuZXhwb3J0cy5NRVRSSUNfREJfQ0xJRU5UX09QRVJBVElPTl9EVVJBVElPTiA9ICdkYi5jbGllbnQub3BlcmF0aW9uLmR1cmF0aW9uJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNlbWNvbnYuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXBnL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iOTBjZDFhNTM3ZGFmZTI5NGNlZTk2M2U0NDMyYjQ2YVxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLXBnXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isObjectWithTextString = exports.getErrorMessage = exports.patchClientConnectCallback = exports.patchCallbackPGPool = exports.updateCounter = exports.getPoolName = exports.patchCallback = exports.handleExecutionResult = exports.handleConfigQuery = exports.shouldSkipInstrumentation = exports.getSemanticAttributesFromPool = exports.getSemanticAttributesFromConnection = exports.getConnectionString = exports.parseNormalizedOperationName = exports.getQuerySpanName = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/AttributeNames.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst semconv_1 = __webpack_require__(/*! ./semconv */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/semconv.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst SpanNames_1 = __webpack_require__(/*! ./enums/SpanNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/enums/SpanNames.js\");\n/**\n * Helper function to get a low cardinality span name from whatever info we have\n * about the query.\n *\n * This is tricky, because we don't have most of the information (table name,\n * operation name, etc) the spec recommends using to build a low-cardinality\n * value w/o parsing. So, we use db.name and assume that, if the query's a named\n * prepared statement, those `name` values will be low cardinality. If we don't\n * have a named prepared statement, we try to parse an operation (despite the\n * spec's warnings).\n *\n * @params dbName The name of the db against which this query is being issued,\n *   which could be missing if no db name was given at the time that the\n *   connection was established.\n * @params queryConfig Information we have about the query being issued, typed\n *   to reflect only the validation we've actually done on the args to\n *   `client.query()`. This will be undefined if `client.query()` was called\n *   with invalid arguments.\n */\nfunction getQuerySpanName(dbName, queryConfig) {\n    // NB: when the query config is invalid, we omit the dbName too, so that\n    // someone (or some tool) reading the span name doesn't misinterpret the\n    // dbName as being a prepared statement or sql commit name.\n    if (!queryConfig)\n        return SpanNames_1.SpanNames.QUERY_PREFIX;\n    // Either the name of a prepared statement; or an attempted parse\n    // of the SQL command, normalized to uppercase; or unknown.\n    const command = typeof queryConfig.name === 'string' && queryConfig.name\n        ? queryConfig.name\n        : parseNormalizedOperationName(queryConfig.text);\n    return `${SpanNames_1.SpanNames.QUERY_PREFIX}:${command}${dbName ? ` ${dbName}` : ''}`;\n}\nexports.getQuerySpanName = getQuerySpanName;\nfunction parseNormalizedOperationName(queryText) {\n    const indexOfFirstSpace = queryText.indexOf(' ');\n    let sqlCommand = indexOfFirstSpace === -1\n        ? queryText\n        : queryText.slice(0, indexOfFirstSpace);\n    sqlCommand = sqlCommand.toUpperCase();\n    // Handle query text being \"COMMIT;\", which has an extra semicolon before the space.\n    return sqlCommand.endsWith(';') ? sqlCommand.slice(0, -1) : sqlCommand;\n}\nexports.parseNormalizedOperationName = parseNormalizedOperationName;\nfunction getConnectionString(params) {\n    const host = params.host || 'localhost';\n    const port = params.port || 5432;\n    const database = params.database || '';\n    return `postgresql://${host}:${port}/${database}`;\n}\nexports.getConnectionString = getConnectionString;\nfunction getPort(port) {\n    // Port may be NaN as parseInt() is used on the value, passing null will result in NaN being parsed.\n    // https://github.com/brianc/node-postgres/blob/2a8efbee09a284be12748ed3962bc9b816965e36/packages/pg/lib/connection-parameters.js#L66\n    if (Number.isInteger(port)) {\n        return port;\n    }\n    // Unable to find the default used in pg code, so falling back to 'undefined'.\n    return undefined;\n}\nfunction getSemanticAttributesFromConnection(params) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: params.database,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getConnectionString(params),\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: params.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: getPort(params.port),\n        [semantic_conventions_1.SEMATTRS_DB_USER]: params.user,\n    };\n}\nexports.getSemanticAttributesFromConnection = getSemanticAttributesFromConnection;\nfunction getSemanticAttributesFromPool(params) {\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL,\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: params.database,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getConnectionString(params),\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: params.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: getPort(params.port),\n        [semantic_conventions_1.SEMATTRS_DB_USER]: params.user,\n        [AttributeNames_1.AttributeNames.IDLE_TIMEOUT_MILLIS]: params.idleTimeoutMillis,\n        [AttributeNames_1.AttributeNames.MAX_CLIENT]: params.maxClient,\n    };\n}\nexports.getSemanticAttributesFromPool = getSemanticAttributesFromPool;\nfunction shouldSkipInstrumentation(instrumentationConfig) {\n    return (instrumentationConfig.requireParentSpan === true &&\n        api_1.trace.getSpan(api_1.context.active()) === undefined);\n}\nexports.shouldSkipInstrumentation = shouldSkipInstrumentation;\n// Create a span from our normalized queryConfig object,\n// or return a basic span if no queryConfig was given/could be created.\nfunction handleConfigQuery(tracer, instrumentationConfig, queryConfig) {\n    // Create child span.\n    const { connectionParameters } = this;\n    const dbName = connectionParameters.database;\n    const spanName = getQuerySpanName(dbName, queryConfig);\n    const span = tracer.startSpan(spanName, {\n        kind: api_1.SpanKind.CLIENT,\n        attributes: getSemanticAttributesFromConnection(connectionParameters),\n    });\n    if (!queryConfig) {\n        return span;\n    }\n    // Set attributes\n    if (queryConfig.text) {\n        span.setAttribute(semantic_conventions_1.SEMATTRS_DB_STATEMENT, queryConfig.text);\n    }\n    if (instrumentationConfig.enhancedDatabaseReporting &&\n        Array.isArray(queryConfig.values)) {\n        try {\n            const convertedValues = queryConfig.values.map(value => {\n                if (value == null) {\n                    return 'null';\n                }\n                else if (value instanceof Buffer) {\n                    return value.toString();\n                }\n                else if (typeof value === 'object') {\n                    if (typeof value.toPostgres === 'function') {\n                        return value.toPostgres();\n                    }\n                    return JSON.stringify(value);\n                }\n                else {\n                    //string, number\n                    return value.toString();\n                }\n            });\n            span.setAttribute(AttributeNames_1.AttributeNames.PG_VALUES, convertedValues);\n        }\n        catch (e) {\n            api_1.diag.error('failed to stringify ', queryConfig.values, e);\n        }\n    }\n    // Set plan name attribute, if present\n    if (typeof queryConfig.name === 'string') {\n        span.setAttribute(AttributeNames_1.AttributeNames.PG_PLAN, queryConfig.name);\n    }\n    return span;\n}\nexports.handleConfigQuery = handleConfigQuery;\nfunction handleExecutionResult(config, span, pgResult) {\n    if (typeof config.responseHook === 'function') {\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            config.responseHook(span, {\n                data: pgResult,\n            });\n        }, err => {\n            if (err) {\n                api_1.diag.error('Error running response hook', err);\n            }\n        }, true);\n    }\n}\nexports.handleExecutionResult = handleExecutionResult;\nfunction patchCallback(instrumentationConfig, span, cb, attributes, recordDuration) {\n    return function patchedCallback(err, res) {\n        if (err) {\n            if (Object.prototype.hasOwnProperty.call(err, 'code')) {\n                attributes[semantic_conventions_1.ATTR_ERROR_TYPE] = err['code'];\n            }\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        else {\n            handleExecutionResult(instrumentationConfig, span, res);\n        }\n        recordDuration();\n        span.end();\n        cb.call(this, err, res);\n    };\n}\nexports.patchCallback = patchCallback;\nfunction getPoolName(pool) {\n    let poolName = '';\n    poolName += ((pool === null || pool === void 0 ? void 0 : pool.host) ? `${pool.host}` : 'unknown_host') + ':';\n    poolName += ((pool === null || pool === void 0 ? void 0 : pool.port) ? `${pool.port}` : 'unknown_port') + '/';\n    poolName += (pool === null || pool === void 0 ? void 0 : pool.database) ? `${pool.database}` : 'unknown_database';\n    return poolName.trim();\n}\nexports.getPoolName = getPoolName;\nfunction updateCounter(poolName, pool, connectionCount, connectionPendingRequests, latestCounter) {\n    const all = pool.totalCount;\n    const pending = pool.waitingCount;\n    const idle = pool.idleCount;\n    const used = all - idle;\n    connectionCount.add(used - latestCounter.used, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_STATE]: semconv_1.DB_CLIENT_CONNECTION_STATE_VALUE_USED,\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    connectionCount.add(idle - latestCounter.idle, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_STATE]: semconv_1.DB_CLIENT_CONNECTION_STATE_VALUE_IDLE,\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    connectionPendingRequests.add(pending - latestCounter.pending, {\n        [semconv_1.ATTR_DB_CLIENT_CONNECTION_POOL_NAME]: poolName,\n    });\n    return { used: used, idle: idle, pending: pending };\n}\nexports.updateCounter = updateCounter;\nfunction patchCallbackPGPool(span, cb) {\n    return function patchedCallback(err, res, done) {\n        if (err) {\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        span.end();\n        cb.call(this, err, res, done);\n    };\n}\nexports.patchCallbackPGPool = patchCallbackPGPool;\nfunction patchClientConnectCallback(span, cb) {\n    return function patchedClientConnectCallback(err) {\n        if (err) {\n            span.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: err.message,\n            });\n        }\n        span.end();\n        cb.apply(this, arguments);\n    };\n}\nexports.patchClientConnectCallback = patchClientConnectCallback;\n/**\n * Attempt to get a message string from a thrown value, while being quite\n * defensive, to recognize the fact that, in JS, any kind of value (even\n * primitives) can be thrown.\n */\nfunction getErrorMessage(e) {\n    return typeof e === 'object' && e !== null && 'message' in e\n        ? String(e.message)\n        : undefined;\n}\nexports.getErrorMessage = getErrorMessage;\nfunction isObjectWithTextString(it) {\n    var _a;\n    return (typeof it === 'object' &&\n        typeof ((_a = it) === null || _a === void 0 ? void 0 : _a.text) === 'string');\n}\nexports.isObjectWithTextString = isObjectWithTextString;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.51.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-pg';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_b90cd1a537dafe294cee963e4432b46a/node_modules/@opentelemetry/instrumentation-pg/build/src/version.js\n");

/***/ })

};
;