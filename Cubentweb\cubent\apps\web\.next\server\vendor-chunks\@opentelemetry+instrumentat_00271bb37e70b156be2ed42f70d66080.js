"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/constants.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/constants.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SUPPORTED_VERSIONS = exports.MODULE_NAME = void 0;\nexports.MODULE_NAME = 'knex';\nexports.SUPPORTED_VERSIONS = [\n    // use \"lib/execution\" for runner.js, \"lib\" for client.js as basepath, latest tested 0.95.6\n    '>=0.22.0 <4',\n    // use \"lib\" as basepath\n    '>=0.10.0 <0.18.0',\n    '>=0.19.0 <0.22.0',\n    // use \"src\" as basepath\n    '>=0.18.0 <0.19.0',\n];\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/constants.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/index.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/index.js ***!
  \*****************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/instrumentation.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/instrumentation.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KnexInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/version.js\");\nconst constants = __webpack_require__(/*! ./constants */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/constants.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst utils = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/utils.js\");\nconst contextSymbol = Symbol('opentelemetry.instrumentation-knex.context');\nconst DEFAULT_CONFIG = {\n    maxQueryLength: 1022,\n    requireParentSpan: false,\n};\nclass KnexInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition(constants.MODULE_NAME, constants.SUPPORTED_VERSIONS);\n        module.files.push(this.getClientNodeModuleFileInstrumentation('src'), this.getClientNodeModuleFileInstrumentation('lib'), this.getRunnerNodeModuleFileInstrumentation('src'), this.getRunnerNodeModuleFileInstrumentation('lib'), this.getRunnerNodeModuleFileInstrumentation('lib/execution'));\n        return module;\n    }\n    getRunnerNodeModuleFileInstrumentation(basePath) {\n        return new instrumentation_1.InstrumentationNodeModuleFile(`knex/${basePath}/runner.js`, constants.SUPPORTED_VERSIONS, (Runner, moduleVersion) => {\n            this.ensureWrapped(Runner.prototype, 'query', this.createQueryWrapper(moduleVersion));\n            return Runner;\n        }, (Runner, moduleVersion) => {\n            this._unwrap(Runner.prototype, 'query');\n            return Runner;\n        });\n    }\n    getClientNodeModuleFileInstrumentation(basePath) {\n        return new instrumentation_1.InstrumentationNodeModuleFile(`knex/${basePath}/client.js`, constants.SUPPORTED_VERSIONS, (Client) => {\n            this.ensureWrapped(Client.prototype, 'queryBuilder', this.storeContext.bind(this));\n            this.ensureWrapped(Client.prototype, 'schemaBuilder', this.storeContext.bind(this));\n            this.ensureWrapped(Client.prototype, 'raw', this.storeContext.bind(this));\n            return Client;\n        }, (Client) => {\n            this._unwrap(Client.prototype, 'queryBuilder');\n            this._unwrap(Client.prototype, 'schemaBuilder');\n            this._unwrap(Client.prototype, 'raw');\n            return Client;\n        });\n    }\n    createQueryWrapper(moduleVersion) {\n        const instrumentation = this;\n        return function wrapQuery(original) {\n            return function wrapped_logging_method(query) {\n                var _a, _b, _c, _d, _e, _f;\n                const config = this.client.config;\n                const table = utils.extractTableName(this.builder);\n                // `method` actually refers to the knex API method - Not exactly \"operation\"\n                // in the spec sense, but matches most of the time.\n                const operation = query === null || query === void 0 ? void 0 : query.method;\n                const name = ((_a = config === null || config === void 0 ? void 0 : config.connection) === null || _a === void 0 ? void 0 : _a.filename) || ((_b = config === null || config === void 0 ? void 0 : config.connection) === null || _b === void 0 ? void 0 : _b.database);\n                const { maxQueryLength } = instrumentation.getConfig();\n                const attributes = {\n                    'knex.version': moduleVersion,\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: utils.mapSystem(config.client),\n                    [semantic_conventions_1.SEMATTRS_DB_SQL_TABLE]: table,\n                    [semantic_conventions_1.SEMATTRS_DB_OPERATION]: operation,\n                    [semantic_conventions_1.SEMATTRS_DB_USER]: (_c = config === null || config === void 0 ? void 0 : config.connection) === null || _c === void 0 ? void 0 : _c.user,\n                    [semantic_conventions_1.SEMATTRS_DB_NAME]: name,\n                    [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: (_d = config === null || config === void 0 ? void 0 : config.connection) === null || _d === void 0 ? void 0 : _d.host,\n                    [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: (_e = config === null || config === void 0 ? void 0 : config.connection) === null || _e === void 0 ? void 0 : _e.port,\n                    [semantic_conventions_1.SEMATTRS_NET_TRANSPORT]: ((_f = config === null || config === void 0 ? void 0 : config.connection) === null || _f === void 0 ? void 0 : _f.filename) === ':memory:' ? 'inproc' : undefined,\n                };\n                if (maxQueryLength) {\n                    // filters both undefined and 0\n                    attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = utils.limitLength(query === null || query === void 0 ? void 0 : query.sql, maxQueryLength);\n                }\n                const parentContext = this.builder[contextSymbol] || api.context.active();\n                const parentSpan = api.trace.getSpan(parentContext);\n                const hasActiveParent = parentSpan && api.trace.isSpanContextValid(parentSpan.spanContext());\n                if (instrumentation._config.requireParentSpan && !hasActiveParent) {\n                    return original.bind(this)(...arguments);\n                }\n                const span = instrumentation.tracer.startSpan(utils.getName(name, operation, table), {\n                    kind: api.SpanKind.CLIENT,\n                    attributes,\n                }, parentContext);\n                const spanContext = api.trace.setSpan(api.context.active(), span);\n                return api.context\n                    .with(spanContext, original, this, ...arguments)\n                    .then((result) => {\n                    span.end();\n                    return result;\n                })\n                    .catch((err) => {\n                    // knex adds full query with all the binding values to the message,\n                    // we want to undo that without changing the original error\n                    const formatter = utils.getFormatter(this);\n                    const fullQuery = formatter(query.sql, query.bindings || []);\n                    const message = err.message.replace(fullQuery + ' - ', '');\n                    const exc = utils.otelExceptionFromKnexError(err, message);\n                    span.recordException(exc);\n                    span.setStatus({ code: api.SpanStatusCode.ERROR, message });\n                    span.end();\n                    throw err;\n                });\n            };\n        };\n    }\n    storeContext(original) {\n        return function wrapped_logging_method() {\n            const builder = original.apply(this, arguments);\n            // Builder is a custom promise type and when awaited it fails to propagate context.\n            // We store the parent context at the moment of initiating the builder\n            // otherwise we'd have nothing to attach the span as a child for in `query`.\n            Object.defineProperty(builder, contextSymbol, {\n                value: api.context.active(),\n            });\n            return builder;\n        };\n    }\n    ensureWrapped(obj, methodName, wrapper) {\n        if ((0, instrumentation_1.isWrapped)(obj[methodName])) {\n            this._unwrap(obj, methodName);\n        }\n        this._wrap(obj, methodName, wrapper);\n    }\n}\nexports.KnexInstrumentation = KnexInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/types.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/types.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMDAyNzFiYjM3ZTcwYjE1NmJlMmVkNDJmNzBkNjYwODAvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1rbmV4L2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wMDI3MWJiMzdlNzBiMTU2YmUyZWQ0MmY3MGQ2NjA4MFxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWtuZXhcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/utils.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/utils.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.extractTableName = exports.limitLength = exports.getName = exports.mapSystem = exports.otelExceptionFromKnexError = exports.getFormatter = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst getFormatter = (runner) => {\n    if (runner) {\n        if (runner.client) {\n            if (runner.client._formatQuery) {\n                return runner.client._formatQuery.bind(runner.client);\n            }\n            else if (runner.client.SqlString) {\n                return runner.client.SqlString.format.bind(runner.client.SqlString);\n            }\n        }\n        if (runner.builder) {\n            return runner.builder.toString.bind(runner.builder);\n        }\n    }\n    return () => '<noop formatter>';\n};\nexports.getFormatter = getFormatter;\nfunction otelExceptionFromKnexError(err, message) {\n    if (!(err && err instanceof Error)) {\n        return err;\n    }\n    return {\n        message,\n        code: err.code,\n        stack: err.stack,\n        name: err.name,\n    };\n}\nexports.otelExceptionFromKnexError = otelExceptionFromKnexError;\nconst systemMap = new Map([\n    ['sqlite3', semantic_conventions_1.DBSYSTEMVALUES_SQLITE],\n    ['pg', semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL],\n]);\nconst mapSystem = (knexSystem) => {\n    return systemMap.get(knexSystem) || knexSystem;\n};\nexports.mapSystem = mapSystem;\nconst getName = (db, operation, table) => {\n    if (operation) {\n        if (table) {\n            return `${operation} ${db}.${table}`;\n        }\n        return `${operation} ${db}`;\n    }\n    return db;\n};\nexports.getName = getName;\nconst limitLength = (str, maxLength) => {\n    if (typeof str === 'string' &&\n        typeof maxLength === 'number' &&\n        0 < maxLength &&\n        maxLength < str.length) {\n        return str.substring(0, maxLength) + '..';\n    }\n    return str;\n};\nexports.limitLength = limitLength;\nconst extractTableName = (builder) => {\n    var _a;\n    const table = (_a = builder === null || builder === void 0 ? void 0 : builder._single) === null || _a === void 0 ? void 0 : _a.table;\n    if (typeof table === 'object') {\n        return (0, exports.extractTableName)(table);\n    }\n    return table;\n};\nexports.extractTableName = extractTableName;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/version.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/version.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.44.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-knex';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/constants.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/constants.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SUPPORTED_VERSIONS = exports.MODULE_NAME = void 0;\nexports.MODULE_NAME = 'knex';\nexports.SUPPORTED_VERSIONS = [\n    // use \"lib/execution\" for runner.js, \"lib\" for client.js as basepath, latest tested 0.95.6\n    '>=0.22.0 <4',\n    // use \"lib\" as basepath\n    '>=0.10.0 <0.18.0',\n    '>=0.19.0 <0.22.0',\n    // use \"src\" as basepath\n    '>=0.18.0 <0.19.0',\n];\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wMDI3MWJiMzdlNzBiMTU2YmUyZWQ0MmY3MGQ2NjA4MC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWtuZXgvYnVpbGQvc3JjL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCwwQkFBMEIsR0FBRyxtQkFBbUI7QUFDaEQsbUJBQW1CO0FBQ25CLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMDAyNzFiYjM3ZTcwYjE1NmJlMmVkNDJmNzBkNjYwODBcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1rbmV4XFxidWlsZFxcc3JjXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5TVVBQT1JURURfVkVSU0lPTlMgPSBleHBvcnRzLk1PRFVMRV9OQU1FID0gdm9pZCAwO1xuZXhwb3J0cy5NT0RVTEVfTkFNRSA9ICdrbmV4JztcbmV4cG9ydHMuU1VQUE9SVEVEX1ZFUlNJT05TID0gW1xuICAgIC8vIHVzZSBcImxpYi9leGVjdXRpb25cIiBmb3IgcnVubmVyLmpzLCBcImxpYlwiIGZvciBjbGllbnQuanMgYXMgYmFzZXBhdGgsIGxhdGVzdCB0ZXN0ZWQgMC45NS42XG4gICAgJz49MC4yMi4wIDw0JyxcbiAgICAvLyB1c2UgXCJsaWJcIiBhcyBiYXNlcGF0aFxuICAgICc+PTAuMTAuMCA8MC4xOC4wJyxcbiAgICAnPj0wLjE5LjAgPDAuMjIuMCcsXG4gICAgLy8gdXNlIFwic3JjXCIgYXMgYmFzZXBhdGhcbiAgICAnPj0wLjE4LjAgPDAuMTkuMCcsXG5dO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/constants.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/index.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/index.js ***!
  \*****************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wMDI3MWJiMzdlNzBiMTU2YmUyZWQ0MmY3MGQ2NjA4MC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWtuZXgvYnVpbGQvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxvQ0FBb0MsZ0JBQWdCO0FBQ3ZGLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxhQUFhLG1CQUFPLENBQUMsb01BQW1CO0FBQ3hDLGFBQWEsbUJBQU8sQ0FBQyxnTEFBUztBQUM5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wMDI3MWJiMzdlNzBiMTU2YmUyZWQ0MmY3MGQ2NjA4MFxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWtuZXhcXGJ1aWxkXFxzcmNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uKCkgeyByZXR1cm4gbVtrXTsgfSB9KTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19leHBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2V4cG9ydFN0YXIpIHx8IGZ1bmN0aW9uKG0sIGV4cG9ydHMpIHtcbiAgICBmb3IgKHZhciBwIGluIG0pIGlmIChwICE9PSBcImRlZmF1bHRcIiAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIHApKSBfX2NyZWF0ZUJpbmRpbmcoZXhwb3J0cywgbSwgcCk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL2luc3RydW1lbnRhdGlvblwiKSwgZXhwb3J0cyk7XG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vdHlwZXNcIiksIGV4cG9ydHMpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/instrumentation.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/instrumentation.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KnexInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/version.js\");\nconst constants = __webpack_require__(/*! ./constants */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/constants.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst utils = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/utils.js\");\nconst contextSymbol = Symbol('opentelemetry.instrumentation-knex.context');\nconst DEFAULT_CONFIG = {\n    maxQueryLength: 1022,\n    requireParentSpan: false,\n};\nclass KnexInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition(constants.MODULE_NAME, constants.SUPPORTED_VERSIONS);\n        module.files.push(this.getClientNodeModuleFileInstrumentation('src'), this.getClientNodeModuleFileInstrumentation('lib'), this.getRunnerNodeModuleFileInstrumentation('src'), this.getRunnerNodeModuleFileInstrumentation('lib'), this.getRunnerNodeModuleFileInstrumentation('lib/execution'));\n        return module;\n    }\n    getRunnerNodeModuleFileInstrumentation(basePath) {\n        return new instrumentation_1.InstrumentationNodeModuleFile(`knex/${basePath}/runner.js`, constants.SUPPORTED_VERSIONS, (Runner, moduleVersion) => {\n            this.ensureWrapped(Runner.prototype, 'query', this.createQueryWrapper(moduleVersion));\n            return Runner;\n        }, (Runner, moduleVersion) => {\n            this._unwrap(Runner.prototype, 'query');\n            return Runner;\n        });\n    }\n    getClientNodeModuleFileInstrumentation(basePath) {\n        return new instrumentation_1.InstrumentationNodeModuleFile(`knex/${basePath}/client.js`, constants.SUPPORTED_VERSIONS, (Client) => {\n            this.ensureWrapped(Client.prototype, 'queryBuilder', this.storeContext.bind(this));\n            this.ensureWrapped(Client.prototype, 'schemaBuilder', this.storeContext.bind(this));\n            this.ensureWrapped(Client.prototype, 'raw', this.storeContext.bind(this));\n            return Client;\n        }, (Client) => {\n            this._unwrap(Client.prototype, 'queryBuilder');\n            this._unwrap(Client.prototype, 'schemaBuilder');\n            this._unwrap(Client.prototype, 'raw');\n            return Client;\n        });\n    }\n    createQueryWrapper(moduleVersion) {\n        const instrumentation = this;\n        return function wrapQuery(original) {\n            return function wrapped_logging_method(query) {\n                var _a, _b, _c, _d, _e, _f;\n                const config = this.client.config;\n                const table = utils.extractTableName(this.builder);\n                // `method` actually refers to the knex API method - Not exactly \"operation\"\n                // in the spec sense, but matches most of the time.\n                const operation = query === null || query === void 0 ? void 0 : query.method;\n                const name = ((_a = config === null || config === void 0 ? void 0 : config.connection) === null || _a === void 0 ? void 0 : _a.filename) || ((_b = config === null || config === void 0 ? void 0 : config.connection) === null || _b === void 0 ? void 0 : _b.database);\n                const { maxQueryLength } = instrumentation.getConfig();\n                const attributes = {\n                    'knex.version': moduleVersion,\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: utils.mapSystem(config.client),\n                    [semantic_conventions_1.SEMATTRS_DB_SQL_TABLE]: table,\n                    [semantic_conventions_1.SEMATTRS_DB_OPERATION]: operation,\n                    [semantic_conventions_1.SEMATTRS_DB_USER]: (_c = config === null || config === void 0 ? void 0 : config.connection) === null || _c === void 0 ? void 0 : _c.user,\n                    [semantic_conventions_1.SEMATTRS_DB_NAME]: name,\n                    [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: (_d = config === null || config === void 0 ? void 0 : config.connection) === null || _d === void 0 ? void 0 : _d.host,\n                    [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: (_e = config === null || config === void 0 ? void 0 : config.connection) === null || _e === void 0 ? void 0 : _e.port,\n                    [semantic_conventions_1.SEMATTRS_NET_TRANSPORT]: ((_f = config === null || config === void 0 ? void 0 : config.connection) === null || _f === void 0 ? void 0 : _f.filename) === ':memory:' ? 'inproc' : undefined,\n                };\n                if (maxQueryLength) {\n                    // filters both undefined and 0\n                    attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = utils.limitLength(query === null || query === void 0 ? void 0 : query.sql, maxQueryLength);\n                }\n                const parentContext = this.builder[contextSymbol] || api.context.active();\n                const parentSpan = api.trace.getSpan(parentContext);\n                const hasActiveParent = parentSpan && api.trace.isSpanContextValid(parentSpan.spanContext());\n                if (instrumentation._config.requireParentSpan && !hasActiveParent) {\n                    return original.bind(this)(...arguments);\n                }\n                const span = instrumentation.tracer.startSpan(utils.getName(name, operation, table), {\n                    kind: api.SpanKind.CLIENT,\n                    attributes,\n                }, parentContext);\n                const spanContext = api.trace.setSpan(api.context.active(), span);\n                return api.context\n                    .with(spanContext, original, this, ...arguments)\n                    .then((result) => {\n                    span.end();\n                    return result;\n                })\n                    .catch((err) => {\n                    // knex adds full query with all the binding values to the message,\n                    // we want to undo that without changing the original error\n                    const formatter = utils.getFormatter(this);\n                    const fullQuery = formatter(query.sql, query.bindings || []);\n                    const message = err.message.replace(fullQuery + ' - ', '');\n                    const exc = utils.otelExceptionFromKnexError(err, message);\n                    span.recordException(exc);\n                    span.setStatus({ code: api.SpanStatusCode.ERROR, message });\n                    span.end();\n                    throw err;\n                });\n            };\n        };\n    }\n    storeContext(original) {\n        return function wrapped_logging_method() {\n            const builder = original.apply(this, arguments);\n            // Builder is a custom promise type and when awaited it fails to propagate context.\n            // We store the parent context at the moment of initiating the builder\n            // otherwise we'd have nothing to attach the span as a child for in `query`.\n            Object.defineProperty(builder, contextSymbol, {\n                value: api.context.active(),\n            });\n            return builder;\n        };\n    }\n    ensureWrapped(obj, methodName, wrapper) {\n        if ((0, instrumentation_1.isWrapped)(obj[methodName])) {\n            this._unwrap(obj, methodName);\n        }\n        this._wrap(obj, methodName, wrapper);\n    }\n}\nexports.KnexInstrumentation = KnexInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/types.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/types.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wMDI3MWJiMzdlNzBiMTU2YmUyZWQ0MmY3MGQ2NjA4MC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWtuZXgvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzAwMjcxYmIzN2U3MGIxNTZiZTJlZDQyZjcwZDY2MDgwXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24ta25leFxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/utils.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/utils.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.extractTableName = exports.limitLength = exports.getName = exports.mapSystem = exports.otelExceptionFromKnexError = exports.getFormatter = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst getFormatter = (runner) => {\n    if (runner) {\n        if (runner.client) {\n            if (runner.client._formatQuery) {\n                return runner.client._formatQuery.bind(runner.client);\n            }\n            else if (runner.client.SqlString) {\n                return runner.client.SqlString.format.bind(runner.client.SqlString);\n            }\n        }\n        if (runner.builder) {\n            return runner.builder.toString.bind(runner.builder);\n        }\n    }\n    return () => '<noop formatter>';\n};\nexports.getFormatter = getFormatter;\nfunction otelExceptionFromKnexError(err, message) {\n    if (!(err && err instanceof Error)) {\n        return err;\n    }\n    return {\n        message,\n        code: err.code,\n        stack: err.stack,\n        name: err.name,\n    };\n}\nexports.otelExceptionFromKnexError = otelExceptionFromKnexError;\nconst systemMap = new Map([\n    ['sqlite3', semantic_conventions_1.DBSYSTEMVALUES_SQLITE],\n    ['pg', semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL],\n]);\nconst mapSystem = (knexSystem) => {\n    return systemMap.get(knexSystem) || knexSystem;\n};\nexports.mapSystem = mapSystem;\nconst getName = (db, operation, table) => {\n    if (operation) {\n        if (table) {\n            return `${operation} ${db}.${table}`;\n        }\n        return `${operation} ${db}`;\n    }\n    return db;\n};\nexports.getName = getName;\nconst limitLength = (str, maxLength) => {\n    if (typeof str === 'string' &&\n        typeof maxLength === 'number' &&\n        0 < maxLength &&\n        maxLength < str.length) {\n        return str.substring(0, maxLength) + '..';\n    }\n    return str;\n};\nexports.limitLength = limitLength;\nconst extractTableName = (builder) => {\n    var _a;\n    const table = (_a = builder === null || builder === void 0 ? void 0 : builder._single) === null || _a === void 0 ? void 0 : _a.table;\n    if (typeof table === 'object') {\n        return (0, exports.extractTableName)(table);\n    }\n    return table;\n};\nexports.extractTableName = extractTableName;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/version.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/version.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.44.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-knex';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/constants.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/constants.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SUPPORTED_VERSIONS = exports.MODULE_NAME = void 0;\nexports.MODULE_NAME = 'knex';\nexports.SUPPORTED_VERSIONS = [\n    // use \"lib/execution\" for runner.js, \"lib\" for client.js as basepath, latest tested 0.95.6\n    '>=0.22.0 <4',\n    // use \"lib\" as basepath\n    '>=0.10.0 <0.18.0',\n    '>=0.19.0 <0.22.0',\n    // use \"src\" as basepath\n    '>=0.18.0 <0.19.0',\n];\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/index.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/index.js ***!
  \*****************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/instrumentation.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/instrumentation.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KnexInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/version.js\");\nconst constants = __webpack_require__(/*! ./constants */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/constants.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst utils = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/utils.js\");\nconst contextSymbol = Symbol('opentelemetry.instrumentation-knex.context');\nconst DEFAULT_CONFIG = {\n    maxQueryLength: 1022,\n    requireParentSpan: false,\n};\nclass KnexInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition(constants.MODULE_NAME, constants.SUPPORTED_VERSIONS);\n        module.files.push(this.getClientNodeModuleFileInstrumentation('src'), this.getClientNodeModuleFileInstrumentation('lib'), this.getRunnerNodeModuleFileInstrumentation('src'), this.getRunnerNodeModuleFileInstrumentation('lib'), this.getRunnerNodeModuleFileInstrumentation('lib/execution'));\n        return module;\n    }\n    getRunnerNodeModuleFileInstrumentation(basePath) {\n        return new instrumentation_1.InstrumentationNodeModuleFile(`knex/${basePath}/runner.js`, constants.SUPPORTED_VERSIONS, (Runner, moduleVersion) => {\n            this.ensureWrapped(Runner.prototype, 'query', this.createQueryWrapper(moduleVersion));\n            return Runner;\n        }, (Runner, moduleVersion) => {\n            this._unwrap(Runner.prototype, 'query');\n            return Runner;\n        });\n    }\n    getClientNodeModuleFileInstrumentation(basePath) {\n        return new instrumentation_1.InstrumentationNodeModuleFile(`knex/${basePath}/client.js`, constants.SUPPORTED_VERSIONS, (Client) => {\n            this.ensureWrapped(Client.prototype, 'queryBuilder', this.storeContext.bind(this));\n            this.ensureWrapped(Client.prototype, 'schemaBuilder', this.storeContext.bind(this));\n            this.ensureWrapped(Client.prototype, 'raw', this.storeContext.bind(this));\n            return Client;\n        }, (Client) => {\n            this._unwrap(Client.prototype, 'queryBuilder');\n            this._unwrap(Client.prototype, 'schemaBuilder');\n            this._unwrap(Client.prototype, 'raw');\n            return Client;\n        });\n    }\n    createQueryWrapper(moduleVersion) {\n        const instrumentation = this;\n        return function wrapQuery(original) {\n            return function wrapped_logging_method(query) {\n                var _a, _b, _c, _d, _e, _f;\n                const config = this.client.config;\n                const table = utils.extractTableName(this.builder);\n                // `method` actually refers to the knex API method - Not exactly \"operation\"\n                // in the spec sense, but matches most of the time.\n                const operation = query === null || query === void 0 ? void 0 : query.method;\n                const name = ((_a = config === null || config === void 0 ? void 0 : config.connection) === null || _a === void 0 ? void 0 : _a.filename) || ((_b = config === null || config === void 0 ? void 0 : config.connection) === null || _b === void 0 ? void 0 : _b.database);\n                const { maxQueryLength } = instrumentation.getConfig();\n                const attributes = {\n                    'knex.version': moduleVersion,\n                    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: utils.mapSystem(config.client),\n                    [semantic_conventions_1.SEMATTRS_DB_SQL_TABLE]: table,\n                    [semantic_conventions_1.SEMATTRS_DB_OPERATION]: operation,\n                    [semantic_conventions_1.SEMATTRS_DB_USER]: (_c = config === null || config === void 0 ? void 0 : config.connection) === null || _c === void 0 ? void 0 : _c.user,\n                    [semantic_conventions_1.SEMATTRS_DB_NAME]: name,\n                    [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: (_d = config === null || config === void 0 ? void 0 : config.connection) === null || _d === void 0 ? void 0 : _d.host,\n                    [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: (_e = config === null || config === void 0 ? void 0 : config.connection) === null || _e === void 0 ? void 0 : _e.port,\n                    [semantic_conventions_1.SEMATTRS_NET_TRANSPORT]: ((_f = config === null || config === void 0 ? void 0 : config.connection) === null || _f === void 0 ? void 0 : _f.filename) === ':memory:' ? 'inproc' : undefined,\n                };\n                if (maxQueryLength) {\n                    // filters both undefined and 0\n                    attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = utils.limitLength(query === null || query === void 0 ? void 0 : query.sql, maxQueryLength);\n                }\n                const parentContext = this.builder[contextSymbol] || api.context.active();\n                const parentSpan = api.trace.getSpan(parentContext);\n                const hasActiveParent = parentSpan && api.trace.isSpanContextValid(parentSpan.spanContext());\n                if (instrumentation._config.requireParentSpan && !hasActiveParent) {\n                    return original.bind(this)(...arguments);\n                }\n                const span = instrumentation.tracer.startSpan(utils.getName(name, operation, table), {\n                    kind: api.SpanKind.CLIENT,\n                    attributes,\n                }, parentContext);\n                const spanContext = api.trace.setSpan(api.context.active(), span);\n                return api.context\n                    .with(spanContext, original, this, ...arguments)\n                    .then((result) => {\n                    span.end();\n                    return result;\n                })\n                    .catch((err) => {\n                    // knex adds full query with all the binding values to the message,\n                    // we want to undo that without changing the original error\n                    const formatter = utils.getFormatter(this);\n                    const fullQuery = formatter(query.sql, query.bindings || []);\n                    const message = err.message.replace(fullQuery + ' - ', '');\n                    const exc = utils.otelExceptionFromKnexError(err, message);\n                    span.recordException(exc);\n                    span.setStatus({ code: api.SpanStatusCode.ERROR, message });\n                    span.end();\n                    throw err;\n                });\n            };\n        };\n    }\n    storeContext(original) {\n        return function wrapped_logging_method() {\n            const builder = original.apply(this, arguments);\n            // Builder is a custom promise type and when awaited it fails to propagate context.\n            // We store the parent context at the moment of initiating the builder\n            // otherwise we'd have nothing to attach the span as a child for in `query`.\n            Object.defineProperty(builder, contextSymbol, {\n                value: api.context.active(),\n            });\n            return builder;\n        };\n    }\n    ensureWrapped(obj, methodName, wrapper) {\n        if ((0, instrumentation_1.isWrapped)(obj[methodName])) {\n            this._unwrap(obj, methodName);\n        }\n        this._wrap(obj, methodName, wrapper);\n    }\n}\nexports.KnexInstrumentation = KnexInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/types.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/types.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wMDI3MWJiMzdlNzBiMTU2YmUyZWQ0MmY3MGQ2NjA4MC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWtuZXgvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzAwMjcxYmIzN2U3MGIxNTZiZTJlZDQyZjcwZDY2MDgwXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24ta25leFxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/utils.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/utils.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.extractTableName = exports.limitLength = exports.getName = exports.mapSystem = exports.otelExceptionFromKnexError = exports.getFormatter = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst getFormatter = (runner) => {\n    if (runner) {\n        if (runner.client) {\n            if (runner.client._formatQuery) {\n                return runner.client._formatQuery.bind(runner.client);\n            }\n            else if (runner.client.SqlString) {\n                return runner.client.SqlString.format.bind(runner.client.SqlString);\n            }\n        }\n        if (runner.builder) {\n            return runner.builder.toString.bind(runner.builder);\n        }\n    }\n    return () => '<noop formatter>';\n};\nexports.getFormatter = getFormatter;\nfunction otelExceptionFromKnexError(err, message) {\n    if (!(err && err instanceof Error)) {\n        return err;\n    }\n    return {\n        message,\n        code: err.code,\n        stack: err.stack,\n        name: err.name,\n    };\n}\nexports.otelExceptionFromKnexError = otelExceptionFromKnexError;\nconst systemMap = new Map([\n    ['sqlite3', semantic_conventions_1.DBSYSTEMVALUES_SQLITE],\n    ['pg', semantic_conventions_1.DBSYSTEMVALUES_POSTGRESQL],\n]);\nconst mapSystem = (knexSystem) => {\n    return systemMap.get(knexSystem) || knexSystem;\n};\nexports.mapSystem = mapSystem;\nconst getName = (db, operation, table) => {\n    if (operation) {\n        if (table) {\n            return `${operation} ${db}.${table}`;\n        }\n        return `${operation} ${db}`;\n    }\n    return db;\n};\nexports.getName = getName;\nconst limitLength = (str, maxLength) => {\n    if (typeof str === 'string' &&\n        typeof maxLength === 'number' &&\n        0 < maxLength &&\n        maxLength < str.length) {\n        return str.substring(0, maxLength) + '..';\n    }\n    return str;\n};\nexports.limitLength = limitLength;\nconst extractTableName = (builder) => {\n    var _a;\n    const table = (_a = builder === null || builder === void 0 ? void 0 : builder._single) === null || _a === void 0 ? void 0 : _a.table;\n    if (typeof table === 'object') {\n        return (0, exports.extractTableName)(table);\n    }\n    return table;\n};\nexports.extractTableName = extractTableName;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/version.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/version.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.44.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-knex';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_00271bb37e70b156be2ed42f70d66080/node_modules/@opentelemetry/instrumentation-knex/build/src/version.js\n");

/***/ })

};
;