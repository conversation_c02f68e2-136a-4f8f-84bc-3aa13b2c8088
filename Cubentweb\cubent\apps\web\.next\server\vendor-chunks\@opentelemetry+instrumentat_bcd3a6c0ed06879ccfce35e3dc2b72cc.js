"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RedisInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js\");\nconst redis_common_1 = __webpack_require__(/*! @opentelemetry/redis-common */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+redis-common@0.36.2/node_modules/@opentelemetry/redis-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst OTEL_OPEN_SPANS = Symbol('opentelemetry.instrumentation.redis.open_spans');\nconst MULTI_COMMAND_OPTIONS = Symbol('opentelemetry.instrumentation.redis.multi_command_options');\nconst DEFAULT_CONFIG = {\n    requireParentSpan: false,\n};\nclass RedisInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        // @node-redis/client is a new package introduced and consumed by 'redis 4.0.x'\n        // on redis@4.1.0 it was changed to @redis/client.\n        // we will instrument both packages\n        return [\n            this._getInstrumentationNodeModuleDefinition('@redis/client'),\n            this._getInstrumentationNodeModuleDefinition('@node-redis/client'),\n        ];\n    }\n    _getInstrumentationNodeModuleDefinition(basePackageName) {\n        const commanderModuleFile = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/commander.js`, ['^1.0.0'], (moduleExports, moduleVersion) => {\n            const transformCommandArguments = moduleExports.transformCommandArguments;\n            if (!transformCommandArguments) {\n                this._diag.error('internal instrumentation error, missing transformCommandArguments function');\n                return moduleExports;\n            }\n            // function name and signature changed in redis 4.1.0 from 'extendWithCommands' to 'attachCommands'\n            // the matching internal package names starts with 1.0.x (for redis 4.0.x)\n            const functionToPatch = (moduleVersion === null || moduleVersion === void 0 ? void 0 : moduleVersion.startsWith('1.0.'))\n                ? 'extendWithCommands'\n                : 'attachCommands';\n            // this is the function that extend a redis client with a list of commands.\n            // the function patches the commandExecutor to record a span\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports[functionToPatch])) {\n                this._unwrap(moduleExports, functionToPatch);\n            }\n            this._wrap(moduleExports, functionToPatch, this._getPatchExtendWithCommands(transformCommandArguments));\n            return moduleExports;\n        }, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.extendWithCommands)) {\n                this._unwrap(moduleExports, 'extendWithCommands');\n            }\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.attachCommands)) {\n                this._unwrap(moduleExports, 'attachCommands');\n            }\n        });\n        const multiCommanderModule = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/client/multi-command.js`, ['^1.0.0'], (moduleExports) => {\n            var _a;\n            const redisClientMultiCommandPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.exec)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'exec');\n            }\n            this._wrap(redisClientMultiCommandPrototype, 'exec', this._getPatchMultiCommandsExec());\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.addCommand)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n            }\n            this._wrap(redisClientMultiCommandPrototype, 'addCommand', this._getPatchMultiCommandsAddCommand());\n            return moduleExports;\n        }, (moduleExports) => {\n            var _a;\n            const redisClientMultiCommandPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.exec)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'exec');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.addCommand)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n            }\n        });\n        const clientIndexModule = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/client/index.js`, ['^1.0.0'], (moduleExports) => {\n            var _a;\n            const redisClientPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            // In some @redis/client versions 'multi' is a method. In later\n            // versions, as of https://github.com/redis/node-redis/pull/2324,\n            // 'MULTI' is a method and 'multi' is a property defined in the\n            // constructor that points to 'MULTI', and therefore it will not\n            // be defined on the prototype.\n            if (redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi) {\n                if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi)) {\n                    this._unwrap(redisClientPrototype, 'multi');\n                }\n                this._wrap(redisClientPrototype, 'multi', this._getPatchRedisClientMulti());\n            }\n            if (redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI) {\n                if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI)) {\n                    this._unwrap(redisClientPrototype, 'MULTI');\n                }\n                this._wrap(redisClientPrototype, 'MULTI', this._getPatchRedisClientMulti());\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.sendCommand)) {\n                this._unwrap(redisClientPrototype, 'sendCommand');\n            }\n            this._wrap(redisClientPrototype, 'sendCommand', this._getPatchRedisClientSendCommand());\n            this._wrap(redisClientPrototype, 'connect', this._getPatchedClientConnect());\n            return moduleExports;\n        }, (moduleExports) => {\n            var _a;\n            const redisClientPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi)) {\n                this._unwrap(redisClientPrototype, 'multi');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI)) {\n                this._unwrap(redisClientPrototype, 'MULTI');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.sendCommand)) {\n                this._unwrap(redisClientPrototype, 'sendCommand');\n            }\n        });\n        return new instrumentation_1.InstrumentationNodeModuleDefinition(basePackageName, ['^1.0.0'], (moduleExports) => {\n            return moduleExports;\n        }, () => { }, [commanderModuleFile, multiCommanderModule, clientIndexModule]);\n    }\n    // serves both for redis 4.0.x where function name is extendWithCommands\n    // and redis ^4.1.0 where function name is attachCommands\n    _getPatchExtendWithCommands(transformCommandArguments) {\n        const plugin = this;\n        return function extendWithCommandsPatchWrapper(original) {\n            return function extendWithCommandsPatch(config) {\n                var _a;\n                if (((_a = config === null || config === void 0 ? void 0 : config.BaseClass) === null || _a === void 0 ? void 0 : _a.name) !== 'RedisClient') {\n                    return original.apply(this, arguments);\n                }\n                const origExecutor = config.executor;\n                config.executor = function (command, args) {\n                    const redisCommandArguments = transformCommandArguments(command, args).args;\n                    return plugin._traceClientCommand(origExecutor, this, arguments, redisCommandArguments);\n                };\n                return original.apply(this, arguments);\n            };\n        };\n    }\n    _getPatchMultiCommandsExec() {\n        const plugin = this;\n        return function execPatchWrapper(original) {\n            return function execPatch() {\n                const execRes = original.apply(this, arguments);\n                if (typeof (execRes === null || execRes === void 0 ? void 0 : execRes.then) !== 'function') {\n                    plugin._diag.error('got non promise result when patching RedisClientMultiCommand.exec');\n                    return execRes;\n                }\n                return execRes\n                    .then((redisRes) => {\n                    const openSpans = this[OTEL_OPEN_SPANS];\n                    plugin._endSpansWithRedisReplies(openSpans, redisRes);\n                    return redisRes;\n                })\n                    .catch((err) => {\n                    const openSpans = this[OTEL_OPEN_SPANS];\n                    if (!openSpans) {\n                        plugin._diag.error('cannot find open spans to end for redis multi command');\n                    }\n                    else {\n                        const replies = err.constructor.name === 'MultiErrorReply'\n                            ? err.replies\n                            : new Array(openSpans.length).fill(err);\n                        plugin._endSpansWithRedisReplies(openSpans, replies);\n                    }\n                    return Promise.reject(err);\n                });\n            };\n        };\n    }\n    _getPatchMultiCommandsAddCommand() {\n        const plugin = this;\n        return function addCommandWrapper(original) {\n            return function addCommandPatch(args) {\n                return plugin._traceClientCommand(original, this, arguments, args);\n            };\n        };\n    }\n    _getPatchRedisClientMulti() {\n        return function multiPatchWrapper(original) {\n            return function multiPatch() {\n                const multiRes = original.apply(this, arguments);\n                multiRes[MULTI_COMMAND_OPTIONS] = this.options;\n                return multiRes;\n            };\n        };\n    }\n    _getPatchRedisClientSendCommand() {\n        const plugin = this;\n        return function sendCommandWrapper(original) {\n            return function sendCommandPatch(args) {\n                return plugin._traceClientCommand(original, this, arguments, args);\n            };\n        };\n    }\n    _getPatchedClientConnect() {\n        const plugin = this;\n        return function connectWrapper(original) {\n            return function patchedConnect() {\n                const options = this.options;\n                const attributes = (0, utils_1.getClientAttributes)(plugin._diag, options);\n                const span = plugin.tracer.startSpan(`${RedisInstrumentation.COMPONENT}-connect`, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes,\n                });\n                const res = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return original.apply(this);\n                });\n                return res\n                    .then((result) => {\n                    span.end();\n                    return result;\n                })\n                    .catch((error) => {\n                    span.recordException(error);\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: error.message,\n                    });\n                    span.end();\n                    return Promise.reject(error);\n                });\n            };\n        };\n    }\n    _traceClientCommand(origFunction, origThis, origArguments, redisCommandArguments) {\n        const hasNoParentSpan = api_1.trace.getSpan(api_1.context.active()) === undefined;\n        if (hasNoParentSpan && this.getConfig().requireParentSpan) {\n            return origFunction.apply(origThis, origArguments);\n        }\n        const clientOptions = origThis.options || origThis[MULTI_COMMAND_OPTIONS];\n        const commandName = redisCommandArguments[0]; // types also allows it to be a Buffer, but in practice it only string\n        const commandArgs = redisCommandArguments.slice(1);\n        const dbStatementSerializer = this.getConfig().dbStatementSerializer || redis_common_1.defaultDbStatementSerializer;\n        const attributes = (0, utils_1.getClientAttributes)(this._diag, clientOptions);\n        try {\n            const dbStatement = dbStatementSerializer(commandName, commandArgs);\n            if (dbStatement != null) {\n                attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatement;\n            }\n        }\n        catch (e) {\n            this._diag.error('dbStatementSerializer throw an exception', e, {\n                commandName,\n            });\n        }\n        const span = this.tracer.startSpan(`${RedisInstrumentation.COMPONENT}-${commandName}`, {\n            kind: api_1.SpanKind.CLIENT,\n            attributes,\n        });\n        const res = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n            return origFunction.apply(origThis, origArguments);\n        });\n        if (typeof (res === null || res === void 0 ? void 0 : res.then) === 'function') {\n            res.then((redisRes) => {\n                this._endSpanWithResponse(span, commandName, commandArgs, redisRes, undefined);\n            }, (err) => {\n                this._endSpanWithResponse(span, commandName, commandArgs, null, err);\n            });\n        }\n        else {\n            const redisClientMultiCommand = res;\n            redisClientMultiCommand[OTEL_OPEN_SPANS] =\n                redisClientMultiCommand[OTEL_OPEN_SPANS] || [];\n            redisClientMultiCommand[OTEL_OPEN_SPANS].push({\n                span,\n                commandName,\n                commandArgs,\n            });\n        }\n        return res;\n    }\n    _endSpansWithRedisReplies(openSpans, replies) {\n        if (!openSpans) {\n            return this._diag.error('cannot find open spans to end for redis multi command');\n        }\n        if (replies.length !== openSpans.length) {\n            return this._diag.error('number of multi command spans does not match response from redis');\n        }\n        for (let i = 0; i < openSpans.length; i++) {\n            const { span, commandName, commandArgs } = openSpans[i];\n            const currCommandRes = replies[i];\n            const [res, err] = currCommandRes instanceof Error\n                ? [null, currCommandRes]\n                : [currCommandRes, undefined];\n            this._endSpanWithResponse(span, commandName, commandArgs, res, err);\n        }\n    }\n    _endSpanWithResponse(span, commandName, commandArgs, response, error) {\n        const { responseHook } = this.getConfig();\n        if (!error && responseHook) {\n            try {\n                responseHook(span, commandName, commandArgs, response);\n            }\n            catch (err) {\n                this._diag.error('responseHook throw an exception', err);\n            }\n        }\n        if (error) {\n            span.recordException(error);\n            span.setStatus({ code: api_1.SpanStatusCode.ERROR, message: error === null || error === void 0 ? void 0 : error.message });\n        }\n        span.end();\n    }\n}\nexports.RedisInstrumentation = RedisInstrumentation;\nRedisInstrumentation.COMPONENT = 'redis';\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYmNkM2E2YzBlZDA2ODc5Y2NmY2UzNWUzZGMyYjcyY2Mvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1yZWRpcy00L2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iY2QzYTZjMGVkMDY4NzljY2ZjZTM1ZTNkYzJiNzJjY1xcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLXJlZGlzLTRcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getClientAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nfunction getClientAttributes(diag, options) {\n    var _a, _b;\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_REDIS,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: (_a = options === null || options === void 0 ? void 0 : options.socket) === null || _a === void 0 ? void 0 : _a.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: (_b = options === null || options === void 0 ? void 0 : options.socket) === null || _b === void 0 ? void 0 : _b.port,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: removeCredentialsFromDBConnectionStringAttribute(diag, options === null || options === void 0 ? void 0 : options.url),\n    };\n}\nexports.getClientAttributes = getClientAttributes;\n/**\n * removeCredentialsFromDBConnectionStringAttribute removes basic auth from url and user_pwd from query string\n *\n * Examples:\n *   redis://user:pass@localhost:6379/mydb => redis://localhost:6379/mydb\n *   redis://localhost:6379?db=mydb&user_pwd=pass => redis://localhost:6379?db=mydb\n */\nfunction removeCredentialsFromDBConnectionStringAttribute(diag, url) {\n    if (typeof url !== 'string' || !url) {\n        return;\n    }\n    try {\n        const u = new URL(url);\n        u.searchParams.delete('user_pwd');\n        u.username = '';\n        u.password = '';\n        return u.href;\n    }\n    catch (err) {\n        diag.error('failed to sanitize redis connection url', err);\n    }\n    return;\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.46.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-redis-4';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iY2QzYTZjMGVkMDY4NzljY2ZjZTM1ZTNkYzJiNzJjYy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXJlZGlzLTQvYnVpbGQvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxvQ0FBb0MsZ0JBQWdCO0FBQ3ZGLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxhQUFhLG1CQUFPLENBQUMsdU1BQW1CO0FBQ3hDLGFBQWEsbUJBQU8sQ0FBQyxtTEFBUztBQUM5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iY2QzYTZjMGVkMDY4NzljY2ZjZTM1ZTNkYzJiNzJjY1xcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLXJlZGlzLTRcXGJ1aWxkXFxzcmNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uKCkgeyByZXR1cm4gbVtrXTsgfSB9KTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19leHBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2V4cG9ydFN0YXIpIHx8IGZ1bmN0aW9uKG0sIGV4cG9ydHMpIHtcbiAgICBmb3IgKHZhciBwIGluIG0pIGlmIChwICE9PSBcImRlZmF1bHRcIiAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIHApKSBfX2NyZWF0ZUJpbmRpbmcoZXhwb3J0cywgbSwgcCk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL2luc3RydW1lbnRhdGlvblwiKSwgZXhwb3J0cyk7XG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vdHlwZXNcIiksIGV4cG9ydHMpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RedisInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js\");\nconst redis_common_1 = __webpack_require__(/*! @opentelemetry/redis-common */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+redis-common@0.36.2/node_modules/@opentelemetry/redis-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst OTEL_OPEN_SPANS = Symbol('opentelemetry.instrumentation.redis.open_spans');\nconst MULTI_COMMAND_OPTIONS = Symbol('opentelemetry.instrumentation.redis.multi_command_options');\nconst DEFAULT_CONFIG = {\n    requireParentSpan: false,\n};\nclass RedisInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        // @node-redis/client is a new package introduced and consumed by 'redis 4.0.x'\n        // on redis@4.1.0 it was changed to @redis/client.\n        // we will instrument both packages\n        return [\n            this._getInstrumentationNodeModuleDefinition('@redis/client'),\n            this._getInstrumentationNodeModuleDefinition('@node-redis/client'),\n        ];\n    }\n    _getInstrumentationNodeModuleDefinition(basePackageName) {\n        const commanderModuleFile = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/commander.js`, ['^1.0.0'], (moduleExports, moduleVersion) => {\n            const transformCommandArguments = moduleExports.transformCommandArguments;\n            if (!transformCommandArguments) {\n                this._diag.error('internal instrumentation error, missing transformCommandArguments function');\n                return moduleExports;\n            }\n            // function name and signature changed in redis 4.1.0 from 'extendWithCommands' to 'attachCommands'\n            // the matching internal package names starts with 1.0.x (for redis 4.0.x)\n            const functionToPatch = (moduleVersion === null || moduleVersion === void 0 ? void 0 : moduleVersion.startsWith('1.0.'))\n                ? 'extendWithCommands'\n                : 'attachCommands';\n            // this is the function that extend a redis client with a list of commands.\n            // the function patches the commandExecutor to record a span\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports[functionToPatch])) {\n                this._unwrap(moduleExports, functionToPatch);\n            }\n            this._wrap(moduleExports, functionToPatch, this._getPatchExtendWithCommands(transformCommandArguments));\n            return moduleExports;\n        }, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.extendWithCommands)) {\n                this._unwrap(moduleExports, 'extendWithCommands');\n            }\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.attachCommands)) {\n                this._unwrap(moduleExports, 'attachCommands');\n            }\n        });\n        const multiCommanderModule = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/client/multi-command.js`, ['^1.0.0'], (moduleExports) => {\n            var _a;\n            const redisClientMultiCommandPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.exec)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'exec');\n            }\n            this._wrap(redisClientMultiCommandPrototype, 'exec', this._getPatchMultiCommandsExec());\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.addCommand)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n            }\n            this._wrap(redisClientMultiCommandPrototype, 'addCommand', this._getPatchMultiCommandsAddCommand());\n            return moduleExports;\n        }, (moduleExports) => {\n            var _a;\n            const redisClientMultiCommandPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.exec)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'exec');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.addCommand)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n            }\n        });\n        const clientIndexModule = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/client/index.js`, ['^1.0.0'], (moduleExports) => {\n            var _a;\n            const redisClientPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            // In some @redis/client versions 'multi' is a method. In later\n            // versions, as of https://github.com/redis/node-redis/pull/2324,\n            // 'MULTI' is a method and 'multi' is a property defined in the\n            // constructor that points to 'MULTI', and therefore it will not\n            // be defined on the prototype.\n            if (redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi) {\n                if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi)) {\n                    this._unwrap(redisClientPrototype, 'multi');\n                }\n                this._wrap(redisClientPrototype, 'multi', this._getPatchRedisClientMulti());\n            }\n            if (redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI) {\n                if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI)) {\n                    this._unwrap(redisClientPrototype, 'MULTI');\n                }\n                this._wrap(redisClientPrototype, 'MULTI', this._getPatchRedisClientMulti());\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.sendCommand)) {\n                this._unwrap(redisClientPrototype, 'sendCommand');\n            }\n            this._wrap(redisClientPrototype, 'sendCommand', this._getPatchRedisClientSendCommand());\n            this._wrap(redisClientPrototype, 'connect', this._getPatchedClientConnect());\n            return moduleExports;\n        }, (moduleExports) => {\n            var _a;\n            const redisClientPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi)) {\n                this._unwrap(redisClientPrototype, 'multi');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI)) {\n                this._unwrap(redisClientPrototype, 'MULTI');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.sendCommand)) {\n                this._unwrap(redisClientPrototype, 'sendCommand');\n            }\n        });\n        return new instrumentation_1.InstrumentationNodeModuleDefinition(basePackageName, ['^1.0.0'], (moduleExports) => {\n            return moduleExports;\n        }, () => { }, [commanderModuleFile, multiCommanderModule, clientIndexModule]);\n    }\n    // serves both for redis 4.0.x where function name is extendWithCommands\n    // and redis ^4.1.0 where function name is attachCommands\n    _getPatchExtendWithCommands(transformCommandArguments) {\n        const plugin = this;\n        return function extendWithCommandsPatchWrapper(original) {\n            return function extendWithCommandsPatch(config) {\n                var _a;\n                if (((_a = config === null || config === void 0 ? void 0 : config.BaseClass) === null || _a === void 0 ? void 0 : _a.name) !== 'RedisClient') {\n                    return original.apply(this, arguments);\n                }\n                const origExecutor = config.executor;\n                config.executor = function (command, args) {\n                    const redisCommandArguments = transformCommandArguments(command, args).args;\n                    return plugin._traceClientCommand(origExecutor, this, arguments, redisCommandArguments);\n                };\n                return original.apply(this, arguments);\n            };\n        };\n    }\n    _getPatchMultiCommandsExec() {\n        const plugin = this;\n        return function execPatchWrapper(original) {\n            return function execPatch() {\n                const execRes = original.apply(this, arguments);\n                if (typeof (execRes === null || execRes === void 0 ? void 0 : execRes.then) !== 'function') {\n                    plugin._diag.error('got non promise result when patching RedisClientMultiCommand.exec');\n                    return execRes;\n                }\n                return execRes\n                    .then((redisRes) => {\n                    const openSpans = this[OTEL_OPEN_SPANS];\n                    plugin._endSpansWithRedisReplies(openSpans, redisRes);\n                    return redisRes;\n                })\n                    .catch((err) => {\n                    const openSpans = this[OTEL_OPEN_SPANS];\n                    if (!openSpans) {\n                        plugin._diag.error('cannot find open spans to end for redis multi command');\n                    }\n                    else {\n                        const replies = err.constructor.name === 'MultiErrorReply'\n                            ? err.replies\n                            : new Array(openSpans.length).fill(err);\n                        plugin._endSpansWithRedisReplies(openSpans, replies);\n                    }\n                    return Promise.reject(err);\n                });\n            };\n        };\n    }\n    _getPatchMultiCommandsAddCommand() {\n        const plugin = this;\n        return function addCommandWrapper(original) {\n            return function addCommandPatch(args) {\n                return plugin._traceClientCommand(original, this, arguments, args);\n            };\n        };\n    }\n    _getPatchRedisClientMulti() {\n        return function multiPatchWrapper(original) {\n            return function multiPatch() {\n                const multiRes = original.apply(this, arguments);\n                multiRes[MULTI_COMMAND_OPTIONS] = this.options;\n                return multiRes;\n            };\n        };\n    }\n    _getPatchRedisClientSendCommand() {\n        const plugin = this;\n        return function sendCommandWrapper(original) {\n            return function sendCommandPatch(args) {\n                return plugin._traceClientCommand(original, this, arguments, args);\n            };\n        };\n    }\n    _getPatchedClientConnect() {\n        const plugin = this;\n        return function connectWrapper(original) {\n            return function patchedConnect() {\n                const options = this.options;\n                const attributes = (0, utils_1.getClientAttributes)(plugin._diag, options);\n                const span = plugin.tracer.startSpan(`${RedisInstrumentation.COMPONENT}-connect`, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes,\n                });\n                const res = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return original.apply(this);\n                });\n                return res\n                    .then((result) => {\n                    span.end();\n                    return result;\n                })\n                    .catch((error) => {\n                    span.recordException(error);\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: error.message,\n                    });\n                    span.end();\n                    return Promise.reject(error);\n                });\n            };\n        };\n    }\n    _traceClientCommand(origFunction, origThis, origArguments, redisCommandArguments) {\n        const hasNoParentSpan = api_1.trace.getSpan(api_1.context.active()) === undefined;\n        if (hasNoParentSpan && this.getConfig().requireParentSpan) {\n            return origFunction.apply(origThis, origArguments);\n        }\n        const clientOptions = origThis.options || origThis[MULTI_COMMAND_OPTIONS];\n        const commandName = redisCommandArguments[0]; // types also allows it to be a Buffer, but in practice it only string\n        const commandArgs = redisCommandArguments.slice(1);\n        const dbStatementSerializer = this.getConfig().dbStatementSerializer || redis_common_1.defaultDbStatementSerializer;\n        const attributes = (0, utils_1.getClientAttributes)(this._diag, clientOptions);\n        try {\n            const dbStatement = dbStatementSerializer(commandName, commandArgs);\n            if (dbStatement != null) {\n                attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatement;\n            }\n        }\n        catch (e) {\n            this._diag.error('dbStatementSerializer throw an exception', e, {\n                commandName,\n            });\n        }\n        const span = this.tracer.startSpan(`${RedisInstrumentation.COMPONENT}-${commandName}`, {\n            kind: api_1.SpanKind.CLIENT,\n            attributes,\n        });\n        const res = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n            return origFunction.apply(origThis, origArguments);\n        });\n        if (typeof (res === null || res === void 0 ? void 0 : res.then) === 'function') {\n            res.then((redisRes) => {\n                this._endSpanWithResponse(span, commandName, commandArgs, redisRes, undefined);\n            }, (err) => {\n                this._endSpanWithResponse(span, commandName, commandArgs, null, err);\n            });\n        }\n        else {\n            const redisClientMultiCommand = res;\n            redisClientMultiCommand[OTEL_OPEN_SPANS] =\n                redisClientMultiCommand[OTEL_OPEN_SPANS] || [];\n            redisClientMultiCommand[OTEL_OPEN_SPANS].push({\n                span,\n                commandName,\n                commandArgs,\n            });\n        }\n        return res;\n    }\n    _endSpansWithRedisReplies(openSpans, replies) {\n        if (!openSpans) {\n            return this._diag.error('cannot find open spans to end for redis multi command');\n        }\n        if (replies.length !== openSpans.length) {\n            return this._diag.error('number of multi command spans does not match response from redis');\n        }\n        for (let i = 0; i < openSpans.length; i++) {\n            const { span, commandName, commandArgs } = openSpans[i];\n            const currCommandRes = replies[i];\n            const [res, err] = currCommandRes instanceof Error\n                ? [null, currCommandRes]\n                : [currCommandRes, undefined];\n            this._endSpanWithResponse(span, commandName, commandArgs, res, err);\n        }\n    }\n    _endSpanWithResponse(span, commandName, commandArgs, response, error) {\n        const { responseHook } = this.getConfig();\n        if (!error && responseHook) {\n            try {\n                responseHook(span, commandName, commandArgs, response);\n            }\n            catch (err) {\n                this._diag.error('responseHook throw an exception', err);\n            }\n        }\n        if (error) {\n            span.recordException(error);\n            span.setStatus({ code: api_1.SpanStatusCode.ERROR, message: error === null || error === void 0 ? void 0 : error.message });\n        }\n        span.end();\n    }\n}\nexports.RedisInstrumentation = RedisInstrumentation;\nRedisInstrumentation.COMPONENT = 'redis';\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iY2QzYTZjMGVkMDY4NzljY2ZjZTM1ZTNkYzJiNzJjYy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXJlZGlzLTQvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2JjZDNhNmMwZWQwNjg3OWNjZmNlMzVlM2RjMmI3MmNjXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tcmVkaXMtNFxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getClientAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nfunction getClientAttributes(diag, options) {\n    var _a, _b;\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_REDIS,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: (_a = options === null || options === void 0 ? void 0 : options.socket) === null || _a === void 0 ? void 0 : _a.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: (_b = options === null || options === void 0 ? void 0 : options.socket) === null || _b === void 0 ? void 0 : _b.port,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: removeCredentialsFromDBConnectionStringAttribute(diag, options === null || options === void 0 ? void 0 : options.url),\n    };\n}\nexports.getClientAttributes = getClientAttributes;\n/**\n * removeCredentialsFromDBConnectionStringAttribute removes basic auth from url and user_pwd from query string\n *\n * Examples:\n *   redis://user:pass@localhost:6379/mydb => redis://localhost:6379/mydb\n *   redis://localhost:6379?db=mydb&user_pwd=pass => redis://localhost:6379?db=mydb\n */\nfunction removeCredentialsFromDBConnectionStringAttribute(diag, url) {\n    if (typeof url !== 'string' || !url) {\n        return;\n    }\n    try {\n        const u = new URL(url);\n        u.searchParams.delete('user_pwd');\n        u.username = '';\n        u.password = '';\n        return u.href;\n    }\n    catch (err) {\n        diag.error('failed to sanitize redis connection url', err);\n    }\n    return;\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.46.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-redis-4';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RedisInstrumentation = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js\");\nconst redis_common_1 = __webpack_require__(/*! @opentelemetry/redis-common */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+redis-common@0.36.2/node_modules/@opentelemetry/redis-common/build/src/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst OTEL_OPEN_SPANS = Symbol('opentelemetry.instrumentation.redis.open_spans');\nconst MULTI_COMMAND_OPTIONS = Symbol('opentelemetry.instrumentation.redis.multi_command_options');\nconst DEFAULT_CONFIG = {\n    requireParentSpan: false,\n};\nclass RedisInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    init() {\n        // @node-redis/client is a new package introduced and consumed by 'redis 4.0.x'\n        // on redis@4.1.0 it was changed to @redis/client.\n        // we will instrument both packages\n        return [\n            this._getInstrumentationNodeModuleDefinition('@redis/client'),\n            this._getInstrumentationNodeModuleDefinition('@node-redis/client'),\n        ];\n    }\n    _getInstrumentationNodeModuleDefinition(basePackageName) {\n        const commanderModuleFile = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/commander.js`, ['^1.0.0'], (moduleExports, moduleVersion) => {\n            const transformCommandArguments = moduleExports.transformCommandArguments;\n            if (!transformCommandArguments) {\n                this._diag.error('internal instrumentation error, missing transformCommandArguments function');\n                return moduleExports;\n            }\n            // function name and signature changed in redis 4.1.0 from 'extendWithCommands' to 'attachCommands'\n            // the matching internal package names starts with 1.0.x (for redis 4.0.x)\n            const functionToPatch = (moduleVersion === null || moduleVersion === void 0 ? void 0 : moduleVersion.startsWith('1.0.'))\n                ? 'extendWithCommands'\n                : 'attachCommands';\n            // this is the function that extend a redis client with a list of commands.\n            // the function patches the commandExecutor to record a span\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports[functionToPatch])) {\n                this._unwrap(moduleExports, functionToPatch);\n            }\n            this._wrap(moduleExports, functionToPatch, this._getPatchExtendWithCommands(transformCommandArguments));\n            return moduleExports;\n        }, (moduleExports) => {\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.extendWithCommands)) {\n                this._unwrap(moduleExports, 'extendWithCommands');\n            }\n            if ((0, instrumentation_1.isWrapped)(moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.attachCommands)) {\n                this._unwrap(moduleExports, 'attachCommands');\n            }\n        });\n        const multiCommanderModule = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/client/multi-command.js`, ['^1.0.0'], (moduleExports) => {\n            var _a;\n            const redisClientMultiCommandPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.exec)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'exec');\n            }\n            this._wrap(redisClientMultiCommandPrototype, 'exec', this._getPatchMultiCommandsExec());\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.addCommand)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n            }\n            this._wrap(redisClientMultiCommandPrototype, 'addCommand', this._getPatchMultiCommandsAddCommand());\n            return moduleExports;\n        }, (moduleExports) => {\n            var _a;\n            const redisClientMultiCommandPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.exec)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'exec');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientMultiCommandPrototype === null || redisClientMultiCommandPrototype === void 0 ? void 0 : redisClientMultiCommandPrototype.addCommand)) {\n                this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n            }\n        });\n        const clientIndexModule = new instrumentation_1.InstrumentationNodeModuleFile(`${basePackageName}/dist/lib/client/index.js`, ['^1.0.0'], (moduleExports) => {\n            var _a;\n            const redisClientPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            // In some @redis/client versions 'multi' is a method. In later\n            // versions, as of https://github.com/redis/node-redis/pull/2324,\n            // 'MULTI' is a method and 'multi' is a property defined in the\n            // constructor that points to 'MULTI', and therefore it will not\n            // be defined on the prototype.\n            if (redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi) {\n                if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi)) {\n                    this._unwrap(redisClientPrototype, 'multi');\n                }\n                this._wrap(redisClientPrototype, 'multi', this._getPatchRedisClientMulti());\n            }\n            if (redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI) {\n                if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI)) {\n                    this._unwrap(redisClientPrototype, 'MULTI');\n                }\n                this._wrap(redisClientPrototype, 'MULTI', this._getPatchRedisClientMulti());\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.sendCommand)) {\n                this._unwrap(redisClientPrototype, 'sendCommand');\n            }\n            this._wrap(redisClientPrototype, 'sendCommand', this._getPatchRedisClientSendCommand());\n            this._wrap(redisClientPrototype, 'connect', this._getPatchedClientConnect());\n            return moduleExports;\n        }, (moduleExports) => {\n            var _a;\n            const redisClientPrototype = (_a = moduleExports === null || moduleExports === void 0 ? void 0 : moduleExports.default) === null || _a === void 0 ? void 0 : _a.prototype;\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.multi)) {\n                this._unwrap(redisClientPrototype, 'multi');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.MULTI)) {\n                this._unwrap(redisClientPrototype, 'MULTI');\n            }\n            if ((0, instrumentation_1.isWrapped)(redisClientPrototype === null || redisClientPrototype === void 0 ? void 0 : redisClientPrototype.sendCommand)) {\n                this._unwrap(redisClientPrototype, 'sendCommand');\n            }\n        });\n        return new instrumentation_1.InstrumentationNodeModuleDefinition(basePackageName, ['^1.0.0'], (moduleExports) => {\n            return moduleExports;\n        }, () => { }, [commanderModuleFile, multiCommanderModule, clientIndexModule]);\n    }\n    // serves both for redis 4.0.x where function name is extendWithCommands\n    // and redis ^4.1.0 where function name is attachCommands\n    _getPatchExtendWithCommands(transformCommandArguments) {\n        const plugin = this;\n        return function extendWithCommandsPatchWrapper(original) {\n            return function extendWithCommandsPatch(config) {\n                var _a;\n                if (((_a = config === null || config === void 0 ? void 0 : config.BaseClass) === null || _a === void 0 ? void 0 : _a.name) !== 'RedisClient') {\n                    return original.apply(this, arguments);\n                }\n                const origExecutor = config.executor;\n                config.executor = function (command, args) {\n                    const redisCommandArguments = transformCommandArguments(command, args).args;\n                    return plugin._traceClientCommand(origExecutor, this, arguments, redisCommandArguments);\n                };\n                return original.apply(this, arguments);\n            };\n        };\n    }\n    _getPatchMultiCommandsExec() {\n        const plugin = this;\n        return function execPatchWrapper(original) {\n            return function execPatch() {\n                const execRes = original.apply(this, arguments);\n                if (typeof (execRes === null || execRes === void 0 ? void 0 : execRes.then) !== 'function') {\n                    plugin._diag.error('got non promise result when patching RedisClientMultiCommand.exec');\n                    return execRes;\n                }\n                return execRes\n                    .then((redisRes) => {\n                    const openSpans = this[OTEL_OPEN_SPANS];\n                    plugin._endSpansWithRedisReplies(openSpans, redisRes);\n                    return redisRes;\n                })\n                    .catch((err) => {\n                    const openSpans = this[OTEL_OPEN_SPANS];\n                    if (!openSpans) {\n                        plugin._diag.error('cannot find open spans to end for redis multi command');\n                    }\n                    else {\n                        const replies = err.constructor.name === 'MultiErrorReply'\n                            ? err.replies\n                            : new Array(openSpans.length).fill(err);\n                        plugin._endSpansWithRedisReplies(openSpans, replies);\n                    }\n                    return Promise.reject(err);\n                });\n            };\n        };\n    }\n    _getPatchMultiCommandsAddCommand() {\n        const plugin = this;\n        return function addCommandWrapper(original) {\n            return function addCommandPatch(args) {\n                return plugin._traceClientCommand(original, this, arguments, args);\n            };\n        };\n    }\n    _getPatchRedisClientMulti() {\n        return function multiPatchWrapper(original) {\n            return function multiPatch() {\n                const multiRes = original.apply(this, arguments);\n                multiRes[MULTI_COMMAND_OPTIONS] = this.options;\n                return multiRes;\n            };\n        };\n    }\n    _getPatchRedisClientSendCommand() {\n        const plugin = this;\n        return function sendCommandWrapper(original) {\n            return function sendCommandPatch(args) {\n                return plugin._traceClientCommand(original, this, arguments, args);\n            };\n        };\n    }\n    _getPatchedClientConnect() {\n        const plugin = this;\n        return function connectWrapper(original) {\n            return function patchedConnect() {\n                const options = this.options;\n                const attributes = (0, utils_1.getClientAttributes)(plugin._diag, options);\n                const span = plugin.tracer.startSpan(`${RedisInstrumentation.COMPONENT}-connect`, {\n                    kind: api_1.SpanKind.CLIENT,\n                    attributes,\n                });\n                const res = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n                    return original.apply(this);\n                });\n                return res\n                    .then((result) => {\n                    span.end();\n                    return result;\n                })\n                    .catch((error) => {\n                    span.recordException(error);\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: error.message,\n                    });\n                    span.end();\n                    return Promise.reject(error);\n                });\n            };\n        };\n    }\n    _traceClientCommand(origFunction, origThis, origArguments, redisCommandArguments) {\n        const hasNoParentSpan = api_1.trace.getSpan(api_1.context.active()) === undefined;\n        if (hasNoParentSpan && this.getConfig().requireParentSpan) {\n            return origFunction.apply(origThis, origArguments);\n        }\n        const clientOptions = origThis.options || origThis[MULTI_COMMAND_OPTIONS];\n        const commandName = redisCommandArguments[0]; // types also allows it to be a Buffer, but in practice it only string\n        const commandArgs = redisCommandArguments.slice(1);\n        const dbStatementSerializer = this.getConfig().dbStatementSerializer || redis_common_1.defaultDbStatementSerializer;\n        const attributes = (0, utils_1.getClientAttributes)(this._diag, clientOptions);\n        try {\n            const dbStatement = dbStatementSerializer(commandName, commandArgs);\n            if (dbStatement != null) {\n                attributes[semantic_conventions_1.SEMATTRS_DB_STATEMENT] = dbStatement;\n            }\n        }\n        catch (e) {\n            this._diag.error('dbStatementSerializer throw an exception', e, {\n                commandName,\n            });\n        }\n        const span = this.tracer.startSpan(`${RedisInstrumentation.COMPONENT}-${commandName}`, {\n            kind: api_1.SpanKind.CLIENT,\n            attributes,\n        });\n        const res = api_1.context.with(api_1.trace.setSpan(api_1.context.active(), span), () => {\n            return origFunction.apply(origThis, origArguments);\n        });\n        if (typeof (res === null || res === void 0 ? void 0 : res.then) === 'function') {\n            res.then((redisRes) => {\n                this._endSpanWithResponse(span, commandName, commandArgs, redisRes, undefined);\n            }, (err) => {\n                this._endSpanWithResponse(span, commandName, commandArgs, null, err);\n            });\n        }\n        else {\n            const redisClientMultiCommand = res;\n            redisClientMultiCommand[OTEL_OPEN_SPANS] =\n                redisClientMultiCommand[OTEL_OPEN_SPANS] || [];\n            redisClientMultiCommand[OTEL_OPEN_SPANS].push({\n                span,\n                commandName,\n                commandArgs,\n            });\n        }\n        return res;\n    }\n    _endSpansWithRedisReplies(openSpans, replies) {\n        if (!openSpans) {\n            return this._diag.error('cannot find open spans to end for redis multi command');\n        }\n        if (replies.length !== openSpans.length) {\n            return this._diag.error('number of multi command spans does not match response from redis');\n        }\n        for (let i = 0; i < openSpans.length; i++) {\n            const { span, commandName, commandArgs } = openSpans[i];\n            const currCommandRes = replies[i];\n            const [res, err] = currCommandRes instanceof Error\n                ? [null, currCommandRes]\n                : [currCommandRes, undefined];\n            this._endSpanWithResponse(span, commandName, commandArgs, res, err);\n        }\n    }\n    _endSpanWithResponse(span, commandName, commandArgs, response, error) {\n        const { responseHook } = this.getConfig();\n        if (!error && responseHook) {\n            try {\n                responseHook(span, commandName, commandArgs, response);\n            }\n            catch (err) {\n                this._diag.error('responseHook throw an exception', err);\n            }\n        }\n        if (error) {\n            span.recordException(error);\n            span.setStatus({ code: api_1.SpanStatusCode.ERROR, message: error === null || error === void 0 ? void 0 : error.message });\n        }\n        span.end();\n    }\n}\nexports.RedisInstrumentation = RedisInstrumentation;\nRedisInstrumentation.COMPONENT = 'redis';\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iY2QzYTZjMGVkMDY4NzljY2ZjZTM1ZTNkYzJiNzJjYy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXJlZGlzLTQvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2JjZDNhNmMwZWQwNjg3OWNjZmNlMzVlM2RjMmI3MmNjXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tcmVkaXMtNFxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getClientAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nfunction getClientAttributes(diag, options) {\n    var _a, _b;\n    return {\n        [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_REDIS,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: (_a = options === null || options === void 0 ? void 0 : options.socket) === null || _a === void 0 ? void 0 : _a.host,\n        [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: (_b = options === null || options === void 0 ? void 0 : options.socket) === null || _b === void 0 ? void 0 : _b.port,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: removeCredentialsFromDBConnectionStringAttribute(diag, options === null || options === void 0 ? void 0 : options.url),\n    };\n}\nexports.getClientAttributes = getClientAttributes;\n/**\n * removeCredentialsFromDBConnectionStringAttribute removes basic auth from url and user_pwd from query string\n *\n * Examples:\n *   redis://user:pass@localhost:6379/mydb => redis://localhost:6379/mydb\n *   redis://localhost:6379?db=mydb&user_pwd=pass => redis://localhost:6379?db=mydb\n */\nfunction removeCredentialsFromDBConnectionStringAttribute(diag, url) {\n    if (typeof url !== 'string' || !url) {\n        return;\n    }\n    try {\n        const u = new URL(url);\n        u.searchParams.delete('user_pwd');\n        u.username = '';\n        u.password = '';\n        return u.href;\n    }\n    catch (err) {\n        diag.error('failed to sanitize redis connection url', err);\n    }\n    return;\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.46.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-redis-4';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_bcd3a6c0ed06879ccfce35e3dc2b72cc/node_modules/@opentelemetry/instrumentation-redis-4/build/src/version.js\n");

/***/ })

};
;