"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc";
exports.ids = ["vendor-chunks/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AbstractAsyncHooksContextManager = void 0;\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst ADD_LISTENER_METHODS = [\n    'addListener',\n    'on',\n    'once',\n    'prependListener',\n    'prependOnceListener',\n];\nclass AbstractAsyncHooksContextManager {\n    constructor() {\n        this._kOtListeners = Symbol('OtListeners');\n        this._wrapped = false;\n    }\n    /**\n     * Binds a the certain context or the active one to the target function and then returns the target\n     * @param context A context (span) to be bind to target\n     * @param target a function or event emitter. When target or one of its callbacks is called,\n     *  the provided context will be used as the active context for the duration of the call.\n     */\n    bind(context, target) {\n        if (target instanceof events_1.EventEmitter) {\n            return this._bindEventEmitter(context, target);\n        }\n        if (typeof target === 'function') {\n            return this._bindFunction(context, target);\n        }\n        return target;\n    }\n    _bindFunction(context, target) {\n        const manager = this;\n        const contextWrapper = function (...args) {\n            return manager.with(context, () => target.apply(this, args));\n        };\n        Object.defineProperty(contextWrapper, 'length', {\n            enumerable: false,\n            configurable: true,\n            writable: false,\n            value: target.length,\n        });\n        /**\n         * It isn't possible to tell Typescript that contextWrapper is the same as T\n         * so we forced to cast as any here.\n         */\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return contextWrapper;\n    }\n    /**\n     * By default, EventEmitter call their callback with their context, which we do\n     * not want, instead we will bind a specific context to all callbacks that\n     * go through it.\n     * @param context the context we want to bind\n     * @param ee EventEmitter an instance of EventEmitter to patch\n     */\n    _bindEventEmitter(context, ee) {\n        const map = this._getPatchMap(ee);\n        if (map !== undefined)\n            return ee;\n        this._createPatchMap(ee);\n        // patch methods that add a listener to propagate context\n        ADD_LISTENER_METHODS.forEach(methodName => {\n            if (ee[methodName] === undefined)\n                return;\n            ee[methodName] = this._patchAddListener(ee, ee[methodName], context);\n        });\n        // patch methods that remove a listener\n        if (typeof ee.removeListener === 'function') {\n            ee.removeListener = this._patchRemoveListener(ee, ee.removeListener);\n        }\n        if (typeof ee.off === 'function') {\n            ee.off = this._patchRemoveListener(ee, ee.off);\n        }\n        // patch method that remove all listeners\n        if (typeof ee.removeAllListeners === 'function') {\n            ee.removeAllListeners = this._patchRemoveAllListeners(ee, ee.removeAllListeners);\n        }\n        return ee;\n    }\n    /**\n     * Patch methods that remove a given listener so that we match the \"patched\"\n     * version of that listener (the one that propagate context).\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     */\n    _patchRemoveListener(ee, original) {\n        const contextManager = this;\n        return function (event, listener) {\n            var _a;\n            const events = (_a = contextManager._getPatchMap(ee)) === null || _a === void 0 ? void 0 : _a[event];\n            if (events === undefined) {\n                return original.call(this, event, listener);\n            }\n            const patchedListener = events.get(listener);\n            return original.call(this, event, patchedListener || listener);\n        };\n    }\n    /**\n     * Patch methods that remove all listeners so we remove our\n     * internal references for a given event.\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     */\n    _patchRemoveAllListeners(ee, original) {\n        const contextManager = this;\n        return function (event) {\n            const map = contextManager._getPatchMap(ee);\n            if (map !== undefined) {\n                if (arguments.length === 0) {\n                    contextManager._createPatchMap(ee);\n                }\n                else if (map[event] !== undefined) {\n                    delete map[event];\n                }\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    /**\n     * Patch methods on an event emitter instance that can add listeners so we\n     * can force them to propagate a given context.\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     * @param [context] context to propagate when calling listeners\n     */\n    _patchAddListener(ee, original, context) {\n        const contextManager = this;\n        return function (event, listener) {\n            /**\n             * This check is required to prevent double-wrapping the listener.\n             * The implementation for ee.once wraps the listener and calls ee.on.\n             * Without this check, we would wrap that wrapped listener.\n             * This causes an issue because ee.removeListener depends on the onceWrapper\n             * to properly remove the listener. If we wrap their wrapper, we break\n             * that detection.\n             */\n            if (contextManager._wrapped) {\n                return original.call(this, event, listener);\n            }\n            let map = contextManager._getPatchMap(ee);\n            if (map === undefined) {\n                map = contextManager._createPatchMap(ee);\n            }\n            let listeners = map[event];\n            if (listeners === undefined) {\n                listeners = new WeakMap();\n                map[event] = listeners;\n            }\n            const patchedListener = contextManager.bind(context, listener);\n            // store a weak reference of the user listener to ours\n            listeners.set(listener, patchedListener);\n            /**\n             * See comment at the start of this function for the explanation of this property.\n             */\n            contextManager._wrapped = true;\n            try {\n                return original.call(this, event, patchedListener);\n            }\n            finally {\n                contextManager._wrapped = false;\n            }\n        };\n    }\n    _createPatchMap(ee) {\n        const map = Object.create(null);\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        ee[this._kOtListeners] = map;\n        return map;\n    }\n    _getPatchMap(ee) {\n        return ee[this._kOtListeners];\n    }\n}\nexports.AbstractAsyncHooksContextManager = AbstractAsyncHooksContextManager;\n//# sourceMappingURL=AbstractAsyncHooksContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncHooksContextManager = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst asyncHooks = __webpack_require__(/*! async_hooks */ \"async_hooks\");\nconst AbstractAsyncHooksContextManager_1 = __webpack_require__(/*! ./AbstractAsyncHooksContextManager */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\");\nclass AsyncHooksContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {\n    constructor() {\n        super();\n        this._contexts = new Map();\n        this._stack = [];\n        this._asyncHook = asyncHooks.createHook({\n            init: this._init.bind(this),\n            before: this._before.bind(this),\n            after: this._after.bind(this),\n            destroy: this._destroy.bind(this),\n            promiseResolve: this._destroy.bind(this),\n        });\n    }\n    active() {\n        var _a;\n        return (_a = this._stack[this._stack.length - 1]) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;\n    }\n    with(context, fn, thisArg, ...args) {\n        this._enterContext(context);\n        try {\n            return fn.call(thisArg, ...args);\n        }\n        finally {\n            this._exitContext();\n        }\n    }\n    enable() {\n        this._asyncHook.enable();\n        return this;\n    }\n    disable() {\n        this._asyncHook.disable();\n        this._contexts.clear();\n        this._stack = [];\n        return this;\n    }\n    /**\n     * Init hook will be called when userland create a async context, setting the\n     * context as the current one if it exist.\n     * @param uid id of the async context\n     * @param type the resource type\n     */\n    _init(uid, type) {\n        // ignore TIMERWRAP as they combine timers with same timeout which can lead to\n        // false context propagation. TIMERWRAP has been removed in node 11\n        // every timer has it's own `Timeout` resource anyway which is used to propagate\n        // context.\n        if (type === 'TIMERWRAP')\n            return;\n        const context = this._stack[this._stack.length - 1];\n        if (context !== undefined) {\n            this._contexts.set(uid, context);\n        }\n    }\n    /**\n     * Destroy hook will be called when a given context is no longer used so we can\n     * remove its attached context.\n     * @param uid uid of the async context\n     */\n    _destroy(uid) {\n        this._contexts.delete(uid);\n    }\n    /**\n     * Before hook is called just before executing a async context.\n     * @param uid uid of the async context\n     */\n    _before(uid) {\n        const context = this._contexts.get(uid);\n        if (context !== undefined) {\n            this._enterContext(context);\n        }\n    }\n    /**\n     * After hook is called just after completing the execution of a async context.\n     */\n    _after() {\n        this._exitContext();\n    }\n    /**\n     * Set the given context as active\n     */\n    _enterContext(context) {\n        this._stack.push(context);\n    }\n    /**\n     * Remove the context at the root of the stack\n     */\n    _exitContext() {\n        this._stack.pop();\n    }\n}\nexports.AsyncHooksContextManager = AsyncHooksContextManager;\n//# sourceMappingURL=AsyncHooksContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncLocalStorageContextManager = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst async_hooks_1 = __webpack_require__(/*! async_hooks */ \"async_hooks\");\nconst AbstractAsyncHooksContextManager_1 = __webpack_require__(/*! ./AbstractAsyncHooksContextManager */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\");\nclass AsyncLocalStorageContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {\n    constructor() {\n        super();\n        this._asyncLocalStorage = new async_hooks_1.AsyncLocalStorage();\n    }\n    active() {\n        var _a;\n        return (_a = this._asyncLocalStorage.getStore()) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;\n    }\n    with(context, fn, thisArg, ...args) {\n        const cb = thisArg == null ? fn : fn.bind(thisArg);\n        return this._asyncLocalStorage.run(context, cb, ...args);\n    }\n    enable() {\n        return this;\n    }\n    disable() {\n        this._asyncLocalStorage.disable();\n        return this;\n    }\n}\nexports.AsyncLocalStorageContextManager = AsyncLocalStorageContextManager;\n//# sourceMappingURL=AsyncLocalStorageContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncLocalStorageContextManager = exports.AsyncHooksContextManager = void 0;\nvar AsyncHooksContextManager_1 = __webpack_require__(/*! ./AsyncHooksContextManager */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js\");\nObject.defineProperty(exports, \"AsyncHooksContextManager\", ({ enumerable: true, get: function () { return AsyncHooksContextManager_1.AsyncHooksContextManager; } }));\nvar AsyncLocalStorageContextManager_1 = __webpack_require__(/*! ./AsyncLocalStorageContextManager */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js\");\nObject.defineProperty(exports, \"AsyncLocalStorageContextManager\", ({ enumerable: true, get: function () { return AsyncLocalStorageContextManager_1.AsyncLocalStorageContextManager; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AbstractAsyncHooksContextManager = void 0;\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst ADD_LISTENER_METHODS = [\n    'addListener',\n    'on',\n    'once',\n    'prependListener',\n    'prependOnceListener',\n];\nclass AbstractAsyncHooksContextManager {\n    constructor() {\n        this._kOtListeners = Symbol('OtListeners');\n        this._wrapped = false;\n    }\n    /**\n     * Binds a the certain context or the active one to the target function and then returns the target\n     * @param context A context (span) to be bind to target\n     * @param target a function or event emitter. When target or one of its callbacks is called,\n     *  the provided context will be used as the active context for the duration of the call.\n     */\n    bind(context, target) {\n        if (target instanceof events_1.EventEmitter) {\n            return this._bindEventEmitter(context, target);\n        }\n        if (typeof target === 'function') {\n            return this._bindFunction(context, target);\n        }\n        return target;\n    }\n    _bindFunction(context, target) {\n        const manager = this;\n        const contextWrapper = function (...args) {\n            return manager.with(context, () => target.apply(this, args));\n        };\n        Object.defineProperty(contextWrapper, 'length', {\n            enumerable: false,\n            configurable: true,\n            writable: false,\n            value: target.length,\n        });\n        /**\n         * It isn't possible to tell Typescript that contextWrapper is the same as T\n         * so we forced to cast as any here.\n         */\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return contextWrapper;\n    }\n    /**\n     * By default, EventEmitter call their callback with their context, which we do\n     * not want, instead we will bind a specific context to all callbacks that\n     * go through it.\n     * @param context the context we want to bind\n     * @param ee EventEmitter an instance of EventEmitter to patch\n     */\n    _bindEventEmitter(context, ee) {\n        const map = this._getPatchMap(ee);\n        if (map !== undefined)\n            return ee;\n        this._createPatchMap(ee);\n        // patch methods that add a listener to propagate context\n        ADD_LISTENER_METHODS.forEach(methodName => {\n            if (ee[methodName] === undefined)\n                return;\n            ee[methodName] = this._patchAddListener(ee, ee[methodName], context);\n        });\n        // patch methods that remove a listener\n        if (typeof ee.removeListener === 'function') {\n            ee.removeListener = this._patchRemoveListener(ee, ee.removeListener);\n        }\n        if (typeof ee.off === 'function') {\n            ee.off = this._patchRemoveListener(ee, ee.off);\n        }\n        // patch method that remove all listeners\n        if (typeof ee.removeAllListeners === 'function') {\n            ee.removeAllListeners = this._patchRemoveAllListeners(ee, ee.removeAllListeners);\n        }\n        return ee;\n    }\n    /**\n     * Patch methods that remove a given listener so that we match the \"patched\"\n     * version of that listener (the one that propagate context).\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     */\n    _patchRemoveListener(ee, original) {\n        const contextManager = this;\n        return function (event, listener) {\n            var _a;\n            const events = (_a = contextManager._getPatchMap(ee)) === null || _a === void 0 ? void 0 : _a[event];\n            if (events === undefined) {\n                return original.call(this, event, listener);\n            }\n            const patchedListener = events.get(listener);\n            return original.call(this, event, patchedListener || listener);\n        };\n    }\n    /**\n     * Patch methods that remove all listeners so we remove our\n     * internal references for a given event.\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     */\n    _patchRemoveAllListeners(ee, original) {\n        const contextManager = this;\n        return function (event) {\n            const map = contextManager._getPatchMap(ee);\n            if (map !== undefined) {\n                if (arguments.length === 0) {\n                    contextManager._createPatchMap(ee);\n                }\n                else if (map[event] !== undefined) {\n                    delete map[event];\n                }\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    /**\n     * Patch methods on an event emitter instance that can add listeners so we\n     * can force them to propagate a given context.\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     * @param [context] context to propagate when calling listeners\n     */\n    _patchAddListener(ee, original, context) {\n        const contextManager = this;\n        return function (event, listener) {\n            /**\n             * This check is required to prevent double-wrapping the listener.\n             * The implementation for ee.once wraps the listener and calls ee.on.\n             * Without this check, we would wrap that wrapped listener.\n             * This causes an issue because ee.removeListener depends on the onceWrapper\n             * to properly remove the listener. If we wrap their wrapper, we break\n             * that detection.\n             */\n            if (contextManager._wrapped) {\n                return original.call(this, event, listener);\n            }\n            let map = contextManager._getPatchMap(ee);\n            if (map === undefined) {\n                map = contextManager._createPatchMap(ee);\n            }\n            let listeners = map[event];\n            if (listeners === undefined) {\n                listeners = new WeakMap();\n                map[event] = listeners;\n            }\n            const patchedListener = contextManager.bind(context, listener);\n            // store a weak reference of the user listener to ours\n            listeners.set(listener, patchedListener);\n            /**\n             * See comment at the start of this function for the explanation of this property.\n             */\n            contextManager._wrapped = true;\n            try {\n                return original.call(this, event, patchedListener);\n            }\n            finally {\n                contextManager._wrapped = false;\n            }\n        };\n    }\n    _createPatchMap(ee) {\n        const map = Object.create(null);\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        ee[this._kOtListeners] = map;\n        return map;\n    }\n    _getPatchMap(ee) {\n        return ee[this._kOtListeners];\n    }\n}\nexports.AbstractAsyncHooksContextManager = AbstractAsyncHooksContextManager;\n//# sourceMappingURL=AbstractAsyncHooksContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncHooksContextManager = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst asyncHooks = __webpack_require__(/*! async_hooks */ \"async_hooks\");\nconst AbstractAsyncHooksContextManager_1 = __webpack_require__(/*! ./AbstractAsyncHooksContextManager */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\");\nclass AsyncHooksContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {\n    constructor() {\n        super();\n        this._contexts = new Map();\n        this._stack = [];\n        this._asyncHook = asyncHooks.createHook({\n            init: this._init.bind(this),\n            before: this._before.bind(this),\n            after: this._after.bind(this),\n            destroy: this._destroy.bind(this),\n            promiseResolve: this._destroy.bind(this),\n        });\n    }\n    active() {\n        var _a;\n        return (_a = this._stack[this._stack.length - 1]) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;\n    }\n    with(context, fn, thisArg, ...args) {\n        this._enterContext(context);\n        try {\n            return fn.call(thisArg, ...args);\n        }\n        finally {\n            this._exitContext();\n        }\n    }\n    enable() {\n        this._asyncHook.enable();\n        return this;\n    }\n    disable() {\n        this._asyncHook.disable();\n        this._contexts.clear();\n        this._stack = [];\n        return this;\n    }\n    /**\n     * Init hook will be called when userland create a async context, setting the\n     * context as the current one if it exist.\n     * @param uid id of the async context\n     * @param type the resource type\n     */\n    _init(uid, type) {\n        // ignore TIMERWRAP as they combine timers with same timeout which can lead to\n        // false context propagation. TIMERWRAP has been removed in node 11\n        // every timer has it's own `Timeout` resource anyway which is used to propagate\n        // context.\n        if (type === 'TIMERWRAP')\n            return;\n        const context = this._stack[this._stack.length - 1];\n        if (context !== undefined) {\n            this._contexts.set(uid, context);\n        }\n    }\n    /**\n     * Destroy hook will be called when a given context is no longer used so we can\n     * remove its attached context.\n     * @param uid uid of the async context\n     */\n    _destroy(uid) {\n        this._contexts.delete(uid);\n    }\n    /**\n     * Before hook is called just before executing a async context.\n     * @param uid uid of the async context\n     */\n    _before(uid) {\n        const context = this._contexts.get(uid);\n        if (context !== undefined) {\n            this._enterContext(context);\n        }\n    }\n    /**\n     * After hook is called just after completing the execution of a async context.\n     */\n    _after() {\n        this._exitContext();\n    }\n    /**\n     * Set the given context as active\n     */\n    _enterContext(context) {\n        this._stack.push(context);\n    }\n    /**\n     * Remove the context at the root of the stack\n     */\n    _exitContext() {\n        this._stack.pop();\n    }\n}\nexports.AsyncHooksContextManager = AsyncHooksContextManager;\n//# sourceMappingURL=AsyncHooksContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncLocalStorageContextManager = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst async_hooks_1 = __webpack_require__(/*! async_hooks */ \"async_hooks\");\nconst AbstractAsyncHooksContextManager_1 = __webpack_require__(/*! ./AbstractAsyncHooksContextManager */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\");\nclass AsyncLocalStorageContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {\n    constructor() {\n        super();\n        this._asyncLocalStorage = new async_hooks_1.AsyncLocalStorage();\n    }\n    active() {\n        var _a;\n        return (_a = this._asyncLocalStorage.getStore()) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;\n    }\n    with(context, fn, thisArg, ...args) {\n        const cb = thisArg == null ? fn : fn.bind(thisArg);\n        return this._asyncLocalStorage.run(context, cb, ...args);\n    }\n    enable() {\n        return this;\n    }\n    disable() {\n        this._asyncLocalStorage.disable();\n        return this;\n    }\n}\nexports.AsyncLocalStorageContextManager = AsyncLocalStorageContextManager;\n//# sourceMappingURL=AsyncLocalStorageContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncLocalStorageContextManager = exports.AsyncHooksContextManager = void 0;\nvar AsyncHooksContextManager_1 = __webpack_require__(/*! ./AsyncHooksContextManager */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js\");\nObject.defineProperty(exports, \"AsyncHooksContextManager\", ({ enumerable: true, get: function () { return AsyncHooksContextManager_1.AsyncHooksContextManager; } }));\nvar AsyncLocalStorageContextManager_1 = __webpack_require__(/*! ./AsyncLocalStorageContextManager */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js\");\nObject.defineProperty(exports, \"AsyncLocalStorageContextManager\", ({ enumerable: true, get: function () { return AsyncLocalStorageContextManager_1.AsyncLocalStorageContextManager; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AbstractAsyncHooksContextManager = void 0;\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst ADD_LISTENER_METHODS = [\n    'addListener',\n    'on',\n    'once',\n    'prependListener',\n    'prependOnceListener',\n];\nclass AbstractAsyncHooksContextManager {\n    constructor() {\n        this._kOtListeners = Symbol('OtListeners');\n        this._wrapped = false;\n    }\n    /**\n     * Binds a the certain context or the active one to the target function and then returns the target\n     * @param context A context (span) to be bind to target\n     * @param target a function or event emitter. When target or one of its callbacks is called,\n     *  the provided context will be used as the active context for the duration of the call.\n     */\n    bind(context, target) {\n        if (target instanceof events_1.EventEmitter) {\n            return this._bindEventEmitter(context, target);\n        }\n        if (typeof target === 'function') {\n            return this._bindFunction(context, target);\n        }\n        return target;\n    }\n    _bindFunction(context, target) {\n        const manager = this;\n        const contextWrapper = function (...args) {\n            return manager.with(context, () => target.apply(this, args));\n        };\n        Object.defineProperty(contextWrapper, 'length', {\n            enumerable: false,\n            configurable: true,\n            writable: false,\n            value: target.length,\n        });\n        /**\n         * It isn't possible to tell Typescript that contextWrapper is the same as T\n         * so we forced to cast as any here.\n         */\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return contextWrapper;\n    }\n    /**\n     * By default, EventEmitter call their callback with their context, which we do\n     * not want, instead we will bind a specific context to all callbacks that\n     * go through it.\n     * @param context the context we want to bind\n     * @param ee EventEmitter an instance of EventEmitter to patch\n     */\n    _bindEventEmitter(context, ee) {\n        const map = this._getPatchMap(ee);\n        if (map !== undefined)\n            return ee;\n        this._createPatchMap(ee);\n        // patch methods that add a listener to propagate context\n        ADD_LISTENER_METHODS.forEach(methodName => {\n            if (ee[methodName] === undefined)\n                return;\n            ee[methodName] = this._patchAddListener(ee, ee[methodName], context);\n        });\n        // patch methods that remove a listener\n        if (typeof ee.removeListener === 'function') {\n            ee.removeListener = this._patchRemoveListener(ee, ee.removeListener);\n        }\n        if (typeof ee.off === 'function') {\n            ee.off = this._patchRemoveListener(ee, ee.off);\n        }\n        // patch method that remove all listeners\n        if (typeof ee.removeAllListeners === 'function') {\n            ee.removeAllListeners = this._patchRemoveAllListeners(ee, ee.removeAllListeners);\n        }\n        return ee;\n    }\n    /**\n     * Patch methods that remove a given listener so that we match the \"patched\"\n     * version of that listener (the one that propagate context).\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     */\n    _patchRemoveListener(ee, original) {\n        const contextManager = this;\n        return function (event, listener) {\n            var _a;\n            const events = (_a = contextManager._getPatchMap(ee)) === null || _a === void 0 ? void 0 : _a[event];\n            if (events === undefined) {\n                return original.call(this, event, listener);\n            }\n            const patchedListener = events.get(listener);\n            return original.call(this, event, patchedListener || listener);\n        };\n    }\n    /**\n     * Patch methods that remove all listeners so we remove our\n     * internal references for a given event.\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     */\n    _patchRemoveAllListeners(ee, original) {\n        const contextManager = this;\n        return function (event) {\n            const map = contextManager._getPatchMap(ee);\n            if (map !== undefined) {\n                if (arguments.length === 0) {\n                    contextManager._createPatchMap(ee);\n                }\n                else if (map[event] !== undefined) {\n                    delete map[event];\n                }\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    /**\n     * Patch methods on an event emitter instance that can add listeners so we\n     * can force them to propagate a given context.\n     * @param ee EventEmitter instance\n     * @param original reference to the patched method\n     * @param [context] context to propagate when calling listeners\n     */\n    _patchAddListener(ee, original, context) {\n        const contextManager = this;\n        return function (event, listener) {\n            /**\n             * This check is required to prevent double-wrapping the listener.\n             * The implementation for ee.once wraps the listener and calls ee.on.\n             * Without this check, we would wrap that wrapped listener.\n             * This causes an issue because ee.removeListener depends on the onceWrapper\n             * to properly remove the listener. If we wrap their wrapper, we break\n             * that detection.\n             */\n            if (contextManager._wrapped) {\n                return original.call(this, event, listener);\n            }\n            let map = contextManager._getPatchMap(ee);\n            if (map === undefined) {\n                map = contextManager._createPatchMap(ee);\n            }\n            let listeners = map[event];\n            if (listeners === undefined) {\n                listeners = new WeakMap();\n                map[event] = listeners;\n            }\n            const patchedListener = contextManager.bind(context, listener);\n            // store a weak reference of the user listener to ours\n            listeners.set(listener, patchedListener);\n            /**\n             * See comment at the start of this function for the explanation of this property.\n             */\n            contextManager._wrapped = true;\n            try {\n                return original.call(this, event, patchedListener);\n            }\n            finally {\n                contextManager._wrapped = false;\n            }\n        };\n    }\n    _createPatchMap(ee) {\n        const map = Object.create(null);\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        ee[this._kOtListeners] = map;\n        return map;\n    }\n    _getPatchMap(ee) {\n        return ee[this._kOtListeners];\n    }\n}\nexports.AbstractAsyncHooksContextManager = AbstractAsyncHooksContextManager;\n//# sourceMappingURL=AbstractAsyncHooksContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncHooksContextManager = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst asyncHooks = __webpack_require__(/*! async_hooks */ \"async_hooks\");\nconst AbstractAsyncHooksContextManager_1 = __webpack_require__(/*! ./AbstractAsyncHooksContextManager */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\");\nclass AsyncHooksContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {\n    constructor() {\n        super();\n        this._contexts = new Map();\n        this._stack = [];\n        this._asyncHook = asyncHooks.createHook({\n            init: this._init.bind(this),\n            before: this._before.bind(this),\n            after: this._after.bind(this),\n            destroy: this._destroy.bind(this),\n            promiseResolve: this._destroy.bind(this),\n        });\n    }\n    active() {\n        var _a;\n        return (_a = this._stack[this._stack.length - 1]) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;\n    }\n    with(context, fn, thisArg, ...args) {\n        this._enterContext(context);\n        try {\n            return fn.call(thisArg, ...args);\n        }\n        finally {\n            this._exitContext();\n        }\n    }\n    enable() {\n        this._asyncHook.enable();\n        return this;\n    }\n    disable() {\n        this._asyncHook.disable();\n        this._contexts.clear();\n        this._stack = [];\n        return this;\n    }\n    /**\n     * Init hook will be called when userland create a async context, setting the\n     * context as the current one if it exist.\n     * @param uid id of the async context\n     * @param type the resource type\n     */\n    _init(uid, type) {\n        // ignore TIMERWRAP as they combine timers with same timeout which can lead to\n        // false context propagation. TIMERWRAP has been removed in node 11\n        // every timer has it's own `Timeout` resource anyway which is used to propagate\n        // context.\n        if (type === 'TIMERWRAP')\n            return;\n        const context = this._stack[this._stack.length - 1];\n        if (context !== undefined) {\n            this._contexts.set(uid, context);\n        }\n    }\n    /**\n     * Destroy hook will be called when a given context is no longer used so we can\n     * remove its attached context.\n     * @param uid uid of the async context\n     */\n    _destroy(uid) {\n        this._contexts.delete(uid);\n    }\n    /**\n     * Before hook is called just before executing a async context.\n     * @param uid uid of the async context\n     */\n    _before(uid) {\n        const context = this._contexts.get(uid);\n        if (context !== undefined) {\n            this._enterContext(context);\n        }\n    }\n    /**\n     * After hook is called just after completing the execution of a async context.\n     */\n    _after() {\n        this._exitContext();\n    }\n    /**\n     * Set the given context as active\n     */\n    _enterContext(context) {\n        this._stack.push(context);\n    }\n    /**\n     * Remove the context at the root of the stack\n     */\n    _exitContext() {\n        this._stack.pop();\n    }\n}\nexports.AsyncHooksContextManager = AsyncHooksContextManager;\n//# sourceMappingURL=AsyncHooksContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js ***!
  \******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncLocalStorageContextManager = void 0;\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst async_hooks_1 = __webpack_require__(/*! async_hooks */ \"async_hooks\");\nconst AbstractAsyncHooksContextManager_1 = __webpack_require__(/*! ./AbstractAsyncHooksContextManager */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js\");\nclass AsyncLocalStorageContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {\n    constructor() {\n        super();\n        this._asyncLocalStorage = new async_hooks_1.AsyncLocalStorage();\n    }\n    active() {\n        var _a;\n        return (_a = this._asyncLocalStorage.getStore()) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;\n    }\n    with(context, fn, thisArg, ...args) {\n        const cb = thisArg == null ? fn : fn.bind(thisArg);\n        return this._asyncLocalStorage.run(context, cb, ...args);\n    }\n    enable() {\n        return this;\n    }\n    disable() {\n        this._asyncLocalStorage.disable();\n        return this;\n    }\n}\nexports.AsyncLocalStorageContextManager = AsyncLocalStorageContextManager;\n//# sourceMappingURL=AsyncLocalStorageContextManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2NvbnRleHQtYXN5bl9iYzM1NTYwY2Q3NjU5ZDljMjNlNDIwYzZkZmY2Y2VmYy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvY29udGV4dC1hc3luYy1ob29rcy9idWlsZC9zcmMvQXN5bmNMb2NhbFN0b3JhZ2VDb250ZXh0TWFuYWdlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx1Q0FBdUM7QUFDdkMsY0FBYyxtQkFBTyxDQUFDLHNJQUFvQjtBQUMxQyxzQkFBc0IsbUJBQU8sQ0FBQyxnQ0FBYTtBQUMzQywyQ0FBMkMsbUJBQU8sQ0FBQyxxT0FBb0M7QUFDdkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDO0FBQ3ZDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkrY29udGV4dC1hc3luX2JjMzU1NjBjZDc2NTlkOWMyM2U0MjBjNmRmZjZjZWZjXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxjb250ZXh0LWFzeW5jLWhvb2tzXFxidWlsZFxcc3JjXFxBc3luY0xvY2FsU3RvcmFnZUNvbnRleHRNYW5hZ2VyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuQXN5bmNMb2NhbFN0b3JhZ2VDb250ZXh0TWFuYWdlciA9IHZvaWQgMDtcbmNvbnN0IGFwaV8xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L2FwaVwiKTtcbmNvbnN0IGFzeW5jX2hvb2tzXzEgPSByZXF1aXJlKFwiYXN5bmNfaG9va3NcIik7XG5jb25zdCBBYnN0cmFjdEFzeW5jSG9va3NDb250ZXh0TWFuYWdlcl8xID0gcmVxdWlyZShcIi4vQWJzdHJhY3RBc3luY0hvb2tzQ29udGV4dE1hbmFnZXJcIik7XG5jbGFzcyBBc3luY0xvY2FsU3RvcmFnZUNvbnRleHRNYW5hZ2VyIGV4dGVuZHMgQWJzdHJhY3RBc3luY0hvb2tzQ29udGV4dE1hbmFnZXJfMS5BYnN0cmFjdEFzeW5jSG9va3NDb250ZXh0TWFuYWdlciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKCk7XG4gICAgICAgIHRoaXMuX2FzeW5jTG9jYWxTdG9yYWdlID0gbmV3IGFzeW5jX2hvb2tzXzEuQXN5bmNMb2NhbFN0b3JhZ2UoKTtcbiAgICB9XG4gICAgYWN0aXZlKCkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIHJldHVybiAoX2EgPSB0aGlzLl9hc3luY0xvY2FsU3RvcmFnZS5nZXRTdG9yZSgpKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBhcGlfMS5ST09UX0NPTlRFWFQ7XG4gICAgfVxuICAgIHdpdGgoY29udGV4dCwgZm4sIHRoaXNBcmcsIC4uLmFyZ3MpIHtcbiAgICAgICAgY29uc3QgY2IgPSB0aGlzQXJnID09IG51bGwgPyBmbiA6IGZuLmJpbmQodGhpc0FyZyk7XG4gICAgICAgIHJldHVybiB0aGlzLl9hc3luY0xvY2FsU3RvcmFnZS5ydW4oY29udGV4dCwgY2IsIC4uLmFyZ3MpO1xuICAgIH1cbiAgICBlbmFibGUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBkaXNhYmxlKCkge1xuICAgICAgICB0aGlzLl9hc3luY0xvY2FsU3RvcmFnZS5kaXNhYmxlKCk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbn1cbmV4cG9ydHMuQXN5bmNMb2NhbFN0b3JhZ2VDb250ZXh0TWFuYWdlciA9IEFzeW5jTG9jYWxTdG9yYWdlQ29udGV4dE1hbmFnZXI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1Bc3luY0xvY2FsU3RvcmFnZUNvbnRleHRNYW5hZ2VyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AsyncLocalStorageContextManager = exports.AsyncHooksContextManager = void 0;\nvar AsyncHooksContextManager_1 = __webpack_require__(/*! ./AsyncHooksContextManager */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js\");\nObject.defineProperty(exports, \"AsyncHooksContextManager\", ({ enumerable: true, get: function () { return AsyncHooksContextManager_1.AsyncHooksContextManager; } }));\nvar AsyncLocalStorageContextManager_1 = __webpack_require__(/*! ./AsyncLocalStorageContextManager */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js\");\nObject.defineProperty(exports, \"AsyncLocalStorageContextManager\", ({ enumerable: true, get: function () { return AsyncLocalStorageContextManager_1.AsyncLocalStorageContextManager; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+context-asyn_bc35560cd7659d9c23e420c6dff6cefc/node_modules/@opentelemetry/context-async-hooks/build/src/index.js\n");

/***/ })

};
;