"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-deep-compare@1.3.0_react@19.1.0";
exports.ids = ["vendor-chunks/use-deep-compare@1.3.0_react@19.1.0"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDeepCompareCallback: () => (/* binding */ useDeepCompareCallback),\n/* harmony export */   useDeepCompareEffect: () => (/* binding */ useDeepCompareEffect),\n/* harmony export */   useDeepCompareImperativeHandle: () => (/* binding */ useDeepCompareImperativeHandle),\n/* harmony export */   useDeepCompareLayoutEffect: () => (/* binding */ useDeepCompareLayoutEffect),\n/* harmony export */   useDeepCompareMemo: () => (/* binding */ useDeepCompareMemo)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var dequal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dequal */ \"(rsc)/../../node_modules/.pnpm/dequal@2.0.3/node_modules/dequal/dist/index.mjs\");\n\n\n\nfunction useDeepCompareMemoize(dependencies) {\n  const dependenciesRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(dependencies);\n  const signalRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(0);\n\n  if (!(0,dequal__WEBPACK_IMPORTED_MODULE_1__.dequal)(dependencies, dependenciesRef.current)) {\n    dependenciesRef.current = dependencies;\n    signalRef.current += 1;\n  }\n\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(() => dependenciesRef.current, [signalRef.current]);\n}\n\n/**\n * `useDeepCompareCallback` will return a memoized version of the callback that\n * only changes if one of the `dependencies` has changed.\n *\n * Warning: `useDeepCompareCallback` should not be used with dependencies that\n * are all primitive values. Use `React.useCallback` instead.\n *\n * @see {@link https://react.dev/reference/react/useCallback}\n */\n\nfunction useDeepCompareCallback(callback, dependencies) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(callback, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * Accepts a function that contains imperative, possibly effectful code.\n *\n * Warning: `useDeepCompareEffect` should not be used with dependencies that\n * are all primitive values. Use `React.useEffect` instead.\n *\n * @see {@link https://react.dev/reference/react/useEffect}\n */\n\nfunction useDeepCompareEffect(effect, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(effect, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * `useDeepCompareImperativeHandle` customizes the instance value that is exposed to parent components when using `ref`.\n * As always, imperative code using refs should be avoided in most cases.\n *\n * `useDeepCompareImperativeHandle` should be used with `React.forwardRef`.\n *\n * It's similar to `useImperativeHandle`, but uses deep comparison on the dependencies.\n *\n * Warning: `useDeepCompareImperativeHandle` should not be used with dependencies that\n * are all primitive values. Use `React.useImperativeHandle` instead.\n *\n * @see {@link https://react.dev/reference/react/useImperativeHandle}\n */\n\nfunction useDeepCompareImperativeHandle(ref, init, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useImperativeHandle(ref, init, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * The signature is identical to `useDeepCompareEffect`, but it fires synchronously after all DOM mutations.\n * Use this to read layout from the DOM and synchronously re-render. Updates scheduled inside\n * `useDeepCompareLayoutEffect` will be flushed synchronously, before the browser has a chance to paint.\n *\n * Prefer the standard `useDeepCompareEffect` when possible to avoid blocking visual updates.\n *\n * If you’re migrating code from a class component, `useDeepCompareLayoutEffect` fires in the same phase as\n * `componentDidMount` and `componentDidUpdate`.\n *\n * Warning: `useDeepCompareLayoutEffect` should not be used with dependencies that\n * are all primitive values. Use `React.useLayoutEffect` instead.\n *\n * @see {@link https://react.dev/reference/react/useLayoutEffect}\n */\n\nfunction useDeepCompareLayoutEffect(effect, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useLayoutEffect(effect, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * `useDeepCompareMemo` will only recompute the memoized value when one of the\n * `dependencies` has changed.\n *\n * Warning: `useDeepCompareMemo` should not be used with dependencies that\n * are all primitive values. Use `React.useMemo` instead.\n *\n * @see {@link https://react.dev/reference/react/useMemo}\n */\n\nfunction useDeepCompareMemo(factory, dependencies) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(factory, useDeepCompareMemoize(dependencies));\n}\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDeepCompareCallback: () => (/* binding */ useDeepCompareCallback),\n/* harmony export */   useDeepCompareEffect: () => (/* binding */ useDeepCompareEffect),\n/* harmony export */   useDeepCompareImperativeHandle: () => (/* binding */ useDeepCompareImperativeHandle),\n/* harmony export */   useDeepCompareLayoutEffect: () => (/* binding */ useDeepCompareLayoutEffect),\n/* harmony export */   useDeepCompareMemo: () => (/* binding */ useDeepCompareMemo)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var dequal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dequal */ \"(ssr)/../../node_modules/.pnpm/dequal@2.0.3/node_modules/dequal/dist/index.mjs\");\n\n\n\nfunction useDeepCompareMemoize(dependencies) {\n  const dependenciesRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(dependencies);\n  const signalRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(0);\n\n  if (!(0,dequal__WEBPACK_IMPORTED_MODULE_1__.dequal)(dependencies, dependenciesRef.current)) {\n    dependenciesRef.current = dependencies;\n    signalRef.current += 1;\n  }\n\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(() => dependenciesRef.current, [signalRef.current]);\n}\n\n/**\n * `useDeepCompareCallback` will return a memoized version of the callback that\n * only changes if one of the `dependencies` has changed.\n *\n * Warning: `useDeepCompareCallback` should not be used with dependencies that\n * are all primitive values. Use `React.useCallback` instead.\n *\n * @see {@link https://react.dev/reference/react/useCallback}\n */\n\nfunction useDeepCompareCallback(callback, dependencies) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(callback, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * Accepts a function that contains imperative, possibly effectful code.\n *\n * Warning: `useDeepCompareEffect` should not be used with dependencies that\n * are all primitive values. Use `React.useEffect` instead.\n *\n * @see {@link https://react.dev/reference/react/useEffect}\n */\n\nfunction useDeepCompareEffect(effect, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(effect, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * `useDeepCompareImperativeHandle` customizes the instance value that is exposed to parent components when using `ref`.\n * As always, imperative code using refs should be avoided in most cases.\n *\n * `useDeepCompareImperativeHandle` should be used with `React.forwardRef`.\n *\n * It's similar to `useImperativeHandle`, but uses deep comparison on the dependencies.\n *\n * Warning: `useDeepCompareImperativeHandle` should not be used with dependencies that\n * are all primitive values. Use `React.useImperativeHandle` instead.\n *\n * @see {@link https://react.dev/reference/react/useImperativeHandle}\n */\n\nfunction useDeepCompareImperativeHandle(ref, init, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useImperativeHandle(ref, init, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * The signature is identical to `useDeepCompareEffect`, but it fires synchronously after all DOM mutations.\n * Use this to read layout from the DOM and synchronously re-render. Updates scheduled inside\n * `useDeepCompareLayoutEffect` will be flushed synchronously, before the browser has a chance to paint.\n *\n * Prefer the standard `useDeepCompareEffect` when possible to avoid blocking visual updates.\n *\n * If you’re migrating code from a class component, `useDeepCompareLayoutEffect` fires in the same phase as\n * `componentDidMount` and `componentDidUpdate`.\n *\n * Warning: `useDeepCompareLayoutEffect` should not be used with dependencies that\n * are all primitive values. Use `React.useLayoutEffect` instead.\n *\n * @see {@link https://react.dev/reference/react/useLayoutEffect}\n */\n\nfunction useDeepCompareLayoutEffect(effect, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useLayoutEffect(effect, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * `useDeepCompareMemo` will only recompute the memoized value when one of the\n * `dependencies` has changed.\n *\n * Warning: `useDeepCompareMemo` should not be used with dependencies that\n * are all primitive values. Use `React.useMemo` instead.\n *\n * @see {@link https://react.dev/reference/react/useMemo}\n */\n\nfunction useDeepCompareMemo(factory, dependencies) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(factory, useDeepCompareMemoize(dependencies));\n}\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/use-deep-compare@1.3.0_react@19.1.0/node_modules/use-deep-compare/dist-web/index.js\n");

/***/ })

};
;