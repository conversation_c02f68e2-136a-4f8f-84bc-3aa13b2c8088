"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MongoDBInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/internal-types.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/version.js\");\nconst DEFAULT_CONFIG = {\n    requireParentSpan: true,\n};\n/** mongodb instrumentation plugin for OpenTelemetry */\nclass MongoDBInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    _updateMetricInstruments() {\n        this._connectionsUsage = this.meter.createUpDownCounter('db.client.connections.usage', {\n            description: 'The number of connections that are currently in state described by the state attribute.',\n            unit: '{connection}',\n        });\n    }\n    init() {\n        const { v3PatchConnection: v3PatchConnection, v3UnpatchConnection: v3UnpatchConnection, } = this._getV3ConnectionPatches();\n        const { v4PatchConnect, v4UnpatchConnect } = this._getV4ConnectPatches();\n        const { v4PatchConnectionCallback, v4PatchConnectionPromise, v4UnpatchConnection, } = this._getV4ConnectionPatches();\n        const { v4PatchConnectionPool, v4UnpatchConnectionPool } = this._getV4ConnectionPoolPatches();\n        const { v4PatchSessions, v4UnpatchSessions } = this._getV4SessionsPatches();\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mongodb', ['>=3.3.0 <4'], undefined, undefined, [\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/core/wireprotocol/index.js', ['>=3.3.0 <4'], v3PatchConnection, v3UnpatchConnection),\n            ]),\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mongodb', ['>=4.0.0 <7'], undefined, undefined, [\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/cmap/connection.js', ['>=4.0.0 <6.4'], v4PatchConnectionCallback, v4UnpatchConnection),\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/cmap/connection.js', ['>=6.4.0 <7'], v4PatchConnectionPromise, v4UnpatchConnection),\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/cmap/connection_pool.js', ['>=4.0.0 <6.4'], v4PatchConnectionPool, v4UnpatchConnectionPool),\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/cmap/connect.js', ['>=4.0.0 <7'], v4PatchConnect, v4UnpatchConnect),\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/sessions.js', ['>=4.0.0 <7'], v4PatchSessions, v4UnpatchSessions),\n            ]),\n        ];\n    }\n    _getV3ConnectionPatches() {\n        return {\n            v3PatchConnection: (moduleExports) => {\n                // patch insert operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.insert)) {\n                    this._unwrap(moduleExports, 'insert');\n                }\n                this._wrap(moduleExports, 'insert', this._getV3PatchOperation('insert'));\n                // patch remove operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.remove)) {\n                    this._unwrap(moduleExports, 'remove');\n                }\n                this._wrap(moduleExports, 'remove', this._getV3PatchOperation('remove'));\n                // patch update operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.update)) {\n                    this._unwrap(moduleExports, 'update');\n                }\n                this._wrap(moduleExports, 'update', this._getV3PatchOperation('update'));\n                // patch other command\n                if ((0, instrumentation_1.isWrapped)(moduleExports.command)) {\n                    this._unwrap(moduleExports, 'command');\n                }\n                this._wrap(moduleExports, 'command', this._getV3PatchCommand());\n                // patch query\n                if ((0, instrumentation_1.isWrapped)(moduleExports.query)) {\n                    this._unwrap(moduleExports, 'query');\n                }\n                this._wrap(moduleExports, 'query', this._getV3PatchFind());\n                // patch get more operation on cursor\n                if ((0, instrumentation_1.isWrapped)(moduleExports.getMore)) {\n                    this._unwrap(moduleExports, 'getMore');\n                }\n                this._wrap(moduleExports, 'getMore', this._getV3PatchCursor());\n                return moduleExports;\n            },\n            v3UnpatchConnection: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports, 'insert');\n                this._unwrap(moduleExports, 'remove');\n                this._unwrap(moduleExports, 'update');\n                this._unwrap(moduleExports, 'command');\n                this._unwrap(moduleExports, 'query');\n                this._unwrap(moduleExports, 'getMore');\n            },\n        };\n    }\n    _getV4SessionsPatches() {\n        return {\n            v4PatchSessions: (moduleExports) => {\n                if ((0, instrumentation_1.isWrapped)(moduleExports.acquire)) {\n                    this._unwrap(moduleExports, 'acquire');\n                }\n                this._wrap(moduleExports.ServerSessionPool.prototype, 'acquire', this._getV4AcquireCommand());\n                if ((0, instrumentation_1.isWrapped)(moduleExports.release)) {\n                    this._unwrap(moduleExports, 'release');\n                }\n                this._wrap(moduleExports.ServerSessionPool.prototype, 'release', this._getV4ReleaseCommand());\n                return moduleExports;\n            },\n            v4UnpatchSessions: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                if ((0, instrumentation_1.isWrapped)(moduleExports.acquire)) {\n                    this._unwrap(moduleExports, 'acquire');\n                }\n                if ((0, instrumentation_1.isWrapped)(moduleExports.release)) {\n                    this._unwrap(moduleExports, 'release');\n                }\n            },\n        };\n    }\n    _getV4AcquireCommand() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchAcquire() {\n                const nSessionsBeforeAcquire = this.sessions.length;\n                const session = original.call(this);\n                const nSessionsAfterAcquire = this.sessions.length;\n                if (nSessionsBeforeAcquire === nSessionsAfterAcquire) {\n                    //no session in the pool. a new session was created and used\n                    instrumentation._connectionsUsage.add(1, {\n                        state: 'used',\n                        'pool.name': instrumentation._poolName,\n                    });\n                }\n                else if (nSessionsBeforeAcquire - 1 === nSessionsAfterAcquire) {\n                    //a session was already in the pool. remove it from the pool and use it.\n                    instrumentation._connectionsUsage.add(-1, {\n                        state: 'idle',\n                        'pool.name': instrumentation._poolName,\n                    });\n                    instrumentation._connectionsUsage.add(1, {\n                        state: 'used',\n                        'pool.name': instrumentation._poolName,\n                    });\n                }\n                return session;\n            };\n        };\n    }\n    _getV4ReleaseCommand() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchRelease(session) {\n                const cmdPromise = original.call(this, session);\n                instrumentation._connectionsUsage.add(-1, {\n                    state: 'used',\n                    'pool.name': instrumentation._poolName,\n                });\n                instrumentation._connectionsUsage.add(1, {\n                    state: 'idle',\n                    'pool.name': instrumentation._poolName,\n                });\n                return cmdPromise;\n            };\n        };\n    }\n    _getV4ConnectionPoolPatches() {\n        return {\n            v4PatchConnectionPool: (moduleExports) => {\n                const poolPrototype = moduleExports.ConnectionPool.prototype;\n                if ((0, instrumentation_1.isWrapped)(poolPrototype.checkOut)) {\n                    this._unwrap(poolPrototype, 'checkOut');\n                }\n                this._wrap(poolPrototype, 'checkOut', this._getV4ConnectionPoolCheckOut());\n                return moduleExports;\n            },\n            v4UnpatchConnectionPool: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports.ConnectionPool.prototype, 'checkOut');\n            },\n        };\n    }\n    _getV4ConnectPatches() {\n        return {\n            v4PatchConnect: (moduleExports) => {\n                if ((0, instrumentation_1.isWrapped)(moduleExports.connect)) {\n                    this._unwrap(moduleExports, 'connect');\n                }\n                this._wrap(moduleExports, 'connect', this._getV4ConnectCommand());\n                return moduleExports;\n            },\n            v4UnpatchConnect: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports, 'connect');\n            },\n        };\n    }\n    // This patch will become unnecessary once\n    // https://jira.mongodb.org/browse/NODE-5639 is done.\n    _getV4ConnectionPoolCheckOut() {\n        return (original) => {\n            return function patchedCheckout(callback) {\n                const patchedCallback = api_1.context.bind(api_1.context.active(), callback);\n                return original.call(this, patchedCallback);\n            };\n        };\n    }\n    _getV4ConnectCommand() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedConnect(options, callback) {\n                // from v6.4 `connect` method only accepts an options param and returns a promise\n                // with the connection\n                if (original.length === 1) {\n                    const result = original.call(this, options);\n                    if (result && typeof result.then === 'function') {\n                        result.then(() => instrumentation.setPoolName(options), \n                        // this handler is set to pass the lint rules\n                        () => undefined);\n                    }\n                    return result;\n                }\n                // Earlier versions expects a callback param and return void\n                const patchedCallback = function (err, conn) {\n                    if (err || !conn) {\n                        callback(err, conn);\n                        return;\n                    }\n                    instrumentation.setPoolName(options);\n                    callback(err, conn);\n                };\n                return original.call(this, options, patchedCallback);\n            };\n        };\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _getV4ConnectionPatches() {\n        return {\n            v4PatchConnectionCallback: (moduleExports) => {\n                // patch insert operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.Connection.prototype.command)) {\n                    this._unwrap(moduleExports.Connection.prototype, 'command');\n                }\n                this._wrap(moduleExports.Connection.prototype, 'command', this._getV4PatchCommandCallback());\n                return moduleExports;\n            },\n            v4PatchConnectionPromise: (moduleExports) => {\n                // patch insert operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.Connection.prototype.command)) {\n                    this._unwrap(moduleExports.Connection.prototype, 'command');\n                }\n                this._wrap(moduleExports.Connection.prototype, 'command', this._getV4PatchCommandPromise());\n                return moduleExports;\n            },\n            v4UnpatchConnection: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports.Connection.prototype, 'command');\n            },\n        };\n    }\n    /** Creates spans for common operations */\n    _getV3PatchOperation(operationName) {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedServerCommand(server, ns, ops, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = typeof options === 'function' ? options : callback;\n                if (skipInstrumentation ||\n                    typeof resultHandler !== 'function' ||\n                    typeof ops !== 'object') {\n                    if (typeof options === 'function') {\n                        return original.call(this, server, ns, ops, options);\n                    }\n                    else {\n                        return original.call(this, server, ns, ops, options, callback);\n                    }\n                }\n                const span = instrumentation.tracer.startSpan(`mongodb.${operationName}`, {\n                    kind: api_1.SpanKind.CLIENT,\n                });\n                instrumentation._populateV3Attributes(span, ns, server, \n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                ops[0], operationName);\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n                // handle when options is the callback to send the correct number of args\n                if (typeof options === 'function') {\n                    return original.call(this, server, ns, ops, patchedCallback);\n                }\n                else {\n                    return original.call(this, server, ns, ops, options, patchedCallback);\n                }\n            };\n        };\n    }\n    /** Creates spans for command operation */\n    _getV3PatchCommand() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedServerCommand(server, ns, cmd, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = typeof options === 'function' ? options : callback;\n                if (skipInstrumentation ||\n                    typeof resultHandler !== 'function' ||\n                    typeof cmd !== 'object') {\n                    if (typeof options === 'function') {\n                        return original.call(this, server, ns, cmd, options);\n                    }\n                    else {\n                        return original.call(this, server, ns, cmd, options, callback);\n                    }\n                }\n                const commandType = MongoDBInstrumentation._getCommandType(cmd);\n                const type = commandType === internal_types_1.MongodbCommandType.UNKNOWN ? 'command' : commandType;\n                const span = instrumentation.tracer.startSpan(`mongodb.${type}`, {\n                    kind: api_1.SpanKind.CLIENT,\n                });\n                const operation = commandType === internal_types_1.MongodbCommandType.UNKNOWN ? undefined : commandType;\n                instrumentation._populateV3Attributes(span, ns, server, cmd, operation);\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n                // handle when options is the callback to send the correct number of args\n                if (typeof options === 'function') {\n                    return original.call(this, server, ns, cmd, patchedCallback);\n                }\n                else {\n                    return original.call(this, server, ns, cmd, options, patchedCallback);\n                }\n            };\n        };\n    }\n    /** Creates spans for command operation */\n    _getV4PatchCommandCallback() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedV4ServerCommand(ns, cmd, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = callback;\n                const commandType = Object.keys(cmd)[0];\n                if (typeof cmd !== 'object' || cmd.ismaster || cmd.hello) {\n                    return original.call(this, ns, cmd, options, callback);\n                }\n                let span = undefined;\n                if (!skipInstrumentation) {\n                    span = instrumentation.tracer.startSpan(`mongodb.${commandType}`, {\n                        kind: api_1.SpanKind.CLIENT,\n                    });\n                    instrumentation._populateV4Attributes(span, this, ns, cmd, commandType);\n                }\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler, this.id, commandType);\n                return original.call(this, ns, cmd, options, patchedCallback);\n            };\n        };\n    }\n    _getV4PatchCommandPromise() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedV4ServerCommand(...args) {\n                const [ns, cmd] = args;\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const commandType = Object.keys(cmd)[0];\n                const resultHandler = () => undefined;\n                if (typeof cmd !== 'object' || cmd.ismaster || cmd.hello) {\n                    return original.apply(this, args);\n                }\n                let span = undefined;\n                if (!skipInstrumentation) {\n                    span = instrumentation.tracer.startSpan(`mongodb.${commandType}`, {\n                        kind: api_1.SpanKind.CLIENT,\n                    });\n                    instrumentation._populateV4Attributes(span, this, ns, cmd, commandType);\n                }\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler, this.id, commandType);\n                const result = original.apply(this, args);\n                result.then((res) => patchedCallback(null, res), (err) => patchedCallback(err));\n                return result;\n            };\n        };\n    }\n    /** Creates spans for find operation */\n    _getV3PatchFind() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedServerCommand(server, ns, cmd, cursorState, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = typeof options === 'function' ? options : callback;\n                if (skipInstrumentation ||\n                    typeof resultHandler !== 'function' ||\n                    typeof cmd !== 'object') {\n                    if (typeof options === 'function') {\n                        return original.call(this, server, ns, cmd, cursorState, options);\n                    }\n                    else {\n                        return original.call(this, server, ns, cmd, cursorState, options, callback);\n                    }\n                }\n                const span = instrumentation.tracer.startSpan('mongodb.find', {\n                    kind: api_1.SpanKind.CLIENT,\n                });\n                instrumentation._populateV3Attributes(span, ns, server, cmd, 'find');\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n                // handle when options is the callback to send the correct number of args\n                if (typeof options === 'function') {\n                    return original.call(this, server, ns, cmd, cursorState, patchedCallback);\n                }\n                else {\n                    return original.call(this, server, ns, cmd, cursorState, options, patchedCallback);\n                }\n            };\n        };\n    }\n    /** Creates spans for find operation */\n    _getV3PatchCursor() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedServerCommand(server, ns, cursorState, batchSize, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = typeof options === 'function' ? options : callback;\n                if (skipInstrumentation || typeof resultHandler !== 'function') {\n                    if (typeof options === 'function') {\n                        return original.call(this, server, ns, cursorState, batchSize, options);\n                    }\n                    else {\n                        return original.call(this, server, ns, cursorState, batchSize, options, callback);\n                    }\n                }\n                const span = instrumentation.tracer.startSpan('mongodb.getMore', {\n                    kind: api_1.SpanKind.CLIENT,\n                });\n                instrumentation._populateV3Attributes(span, ns, server, cursorState.cmd, 'getMore');\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n                // handle when options is the callback to send the correct number of args\n                if (typeof options === 'function') {\n                    return original.call(this, server, ns, cursorState, batchSize, patchedCallback);\n                }\n                else {\n                    return original.call(this, server, ns, cursorState, batchSize, options, patchedCallback);\n                }\n            };\n        };\n    }\n    /**\n     * Get the mongodb command type from the object.\n     * @param command Internal mongodb command object\n     */\n    static _getCommandType(command) {\n        if (command.createIndexes !== undefined) {\n            return internal_types_1.MongodbCommandType.CREATE_INDEXES;\n        }\n        else if (command.findandmodify !== undefined) {\n            return internal_types_1.MongodbCommandType.FIND_AND_MODIFY;\n        }\n        else if (command.ismaster !== undefined) {\n            return internal_types_1.MongodbCommandType.IS_MASTER;\n        }\n        else if (command.count !== undefined) {\n            return internal_types_1.MongodbCommandType.COUNT;\n        }\n        else if (command.aggregate !== undefined) {\n            return internal_types_1.MongodbCommandType.AGGREGATE;\n        }\n        else {\n            return internal_types_1.MongodbCommandType.UNKNOWN;\n        }\n    }\n    /**\n     * Populate span's attributes by fetching related metadata from the context\n     * @param span span to add attributes to\n     * @param connectionCtx mongodb internal connection context\n     * @param ns mongodb namespace\n     * @param command mongodb internal representation of a command\n     */\n    _populateV4Attributes(span, connectionCtx, ns, command, operation) {\n        let host, port;\n        if (connectionCtx) {\n            const hostParts = typeof connectionCtx.address === 'string'\n                ? connectionCtx.address.split(':')\n                : '';\n            if (hostParts.length === 2) {\n                host = hostParts[0];\n                port = hostParts[1];\n            }\n        }\n        // capture parameters within the query as well if enhancedDatabaseReporting is enabled.\n        let commandObj;\n        if ((command === null || command === void 0 ? void 0 : command.documents) && command.documents[0]) {\n            commandObj = command.documents[0];\n        }\n        else if (command === null || command === void 0 ? void 0 : command.cursors) {\n            commandObj = command.cursors;\n        }\n        else {\n            commandObj = command;\n        }\n        this._addAllSpanAttributes(span, ns.db, ns.collection, host, port, commandObj, operation);\n    }\n    /**\n     * Populate span's attributes by fetching related metadata from the context\n     * @param span span to add attributes to\n     * @param ns mongodb namespace\n     * @param topology mongodb internal representation of the network topology\n     * @param command mongodb internal representation of a command\n     */\n    _populateV3Attributes(span, ns, topology, command, operation) {\n        var _a, _b, _c, _d, _e, _f, _g, _h;\n        // add network attributes to determine the remote server\n        let host;\n        let port;\n        if (topology && topology.s) {\n            host = (_b = (_a = topology.s.options) === null || _a === void 0 ? void 0 : _a.host) !== null && _b !== void 0 ? _b : topology.s.host;\n            port = (_e = ((_d = (_c = topology.s.options) === null || _c === void 0 ? void 0 : _c.port) !== null && _d !== void 0 ? _d : topology.s.port)) === null || _e === void 0 ? void 0 : _e.toString();\n            if (host == null || port == null) {\n                const address = (_f = topology.description) === null || _f === void 0 ? void 0 : _f.address;\n                if (address) {\n                    const addressSegments = address.split(':');\n                    host = addressSegments[0];\n                    port = addressSegments[1];\n                }\n            }\n        }\n        // The namespace is a combination of the database name and the name of the\n        // collection or index, like so: [database-name].[collection-or-index-name].\n        // It could be a string or an instance of MongoDBNamespace, as such we\n        // always coerce to a string to extract db and collection.\n        const [dbName, dbCollection] = ns.toString().split('.');\n        // capture parameters within the query as well if enhancedDatabaseReporting is enabled.\n        const commandObj = (_h = (_g = command === null || command === void 0 ? void 0 : command.query) !== null && _g !== void 0 ? _g : command === null || command === void 0 ? void 0 : command.q) !== null && _h !== void 0 ? _h : command;\n        this._addAllSpanAttributes(span, dbName, dbCollection, host, port, commandObj, operation);\n    }\n    _addAllSpanAttributes(span, dbName, dbCollection, host, port, commandObj, operation) {\n        // add database related attributes\n        span.setAttributes({\n            [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MONGODB,\n            [semantic_conventions_1.SEMATTRS_DB_NAME]: dbName,\n            [semantic_conventions_1.SEMATTRS_DB_MONGODB_COLLECTION]: dbCollection,\n            [semantic_conventions_1.SEMATTRS_DB_OPERATION]: operation,\n            [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: `mongodb://${host}:${port}/${dbName}`,\n        });\n        if (host && port) {\n            span.setAttribute(semantic_conventions_1.SEMATTRS_NET_PEER_NAME, host);\n            const portNumber = parseInt(port, 10);\n            if (!isNaN(portNumber)) {\n                span.setAttribute(semantic_conventions_1.SEMATTRS_NET_PEER_PORT, portNumber);\n            }\n        }\n        if (!commandObj)\n            return;\n        const { dbStatementSerializer: configDbStatementSerializer } = this.getConfig();\n        const dbStatementSerializer = typeof configDbStatementSerializer === 'function'\n            ? configDbStatementSerializer\n            : this._defaultDbStatementSerializer.bind(this);\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            const query = dbStatementSerializer(commandObj);\n            span.setAttribute(semantic_conventions_1.SEMATTRS_DB_STATEMENT, query);\n        }, err => {\n            if (err) {\n                this._diag.error('Error running dbStatementSerializer hook', err);\n            }\n        }, true);\n    }\n    _defaultDbStatementSerializer(commandObj) {\n        const { enhancedDatabaseReporting } = this.getConfig();\n        const resultObj = enhancedDatabaseReporting\n            ? commandObj\n            : this._scrubStatement(commandObj);\n        return JSON.stringify(resultObj);\n    }\n    _scrubStatement(value) {\n        if (Array.isArray(value)) {\n            return value.map(element => this._scrubStatement(element));\n        }\n        if (typeof value === 'object' && value !== null) {\n            return Object.fromEntries(Object.entries(value).map(([key, element]) => [\n                key,\n                this._scrubStatement(element),\n            ]));\n        }\n        // A value like string or number, possible contains PII, scrub it\n        return '?';\n    }\n    /**\n     * Triggers the response hook in case it is defined.\n     * @param span The span to add the results to.\n     * @param result The command result\n     */\n    _handleExecutionResult(span, result) {\n        const { responseHook } = this.getConfig();\n        if (typeof responseHook === 'function') {\n            (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                responseHook(span, { data: result });\n            }, err => {\n                if (err) {\n                    this._diag.error('Error running response hook', err);\n                }\n            }, true);\n        }\n    }\n    /**\n     * Ends a created span.\n     * @param span The created span to end.\n     * @param resultHandler A callback function.\n     * @param connectionId: The connection ID of the Command response.\n     */\n    _patchEnd(span, resultHandler, connectionId, commandType) {\n        // mongodb is using \"tick\" when calling a callback, this way the context\n        // in final callback (resultHandler) is lost\n        const activeContext = api_1.context.active();\n        const instrumentation = this;\n        return function patchedEnd(...args) {\n            const error = args[0];\n            if (span) {\n                if (error instanceof Error) {\n                    span === null || span === void 0 ? void 0 : span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: error.message,\n                    });\n                }\n                else {\n                    const result = args[1];\n                    instrumentation._handleExecutionResult(span, result);\n                }\n                span.end();\n            }\n            return api_1.context.with(activeContext, () => {\n                if (commandType === 'endSessions') {\n                    instrumentation._connectionsUsage.add(-1, {\n                        state: 'idle',\n                        'pool.name': instrumentation._poolName,\n                    });\n                }\n                return resultHandler.apply(this, args);\n            });\n        };\n    }\n    setPoolName(options) {\n        var _a, _b;\n        const host = (_a = options.hostAddress) === null || _a === void 0 ? void 0 : _a.host;\n        const port = (_b = options.hostAddress) === null || _b === void 0 ? void 0 : _b.port;\n        const database = options.dbName;\n        const poolName = `mongodb://${host}:${port}/${database}`;\n        this._poolName = poolName;\n    }\n    _checkSkipInstrumentation(currentSpan) {\n        const requireParentSpan = this.getConfig().requireParentSpan;\n        const hasNoParentSpan = currentSpan === undefined;\n        return requireParentSpan === true && hasNoParentSpan;\n    }\n}\nexports.MongoDBInstrumentation = MongoDBInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MongodbCommandType = void 0;\nvar MongodbCommandType;\n(function (MongodbCommandType) {\n    MongodbCommandType[\"CREATE_INDEXES\"] = \"createIndexes\";\n    MongodbCommandType[\"FIND_AND_MODIFY\"] = \"findAndModify\";\n    MongodbCommandType[\"IS_MASTER\"] = \"isMaster\";\n    MongodbCommandType[\"COUNT\"] = \"count\";\n    MongodbCommandType[\"AGGREGATE\"] = \"aggregate\";\n    MongodbCommandType[\"UNKNOWN\"] = \"unknown\";\n})(MongodbCommandType = exports.MongodbCommandType || (exports.MongodbCommandType = {}));\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/internal-types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MongodbCommandType = void 0;\nvar MongodbCommandType;\n(function (MongodbCommandType) {\n    MongodbCommandType[\"CREATE_INDEXES\"] = \"createIndexes\";\n    MongodbCommandType[\"FIND_AND_MODIFY\"] = \"findAndModify\";\n    MongodbCommandType[\"IS_MASTER\"] = \"isMaster\";\n    MongodbCommandType[\"COUNT\"] = \"count\";\n    MongodbCommandType[\"UNKNOWN\"] = \"unknown\";\n})(MongodbCommandType = exports.MongodbCommandType || (exports.MongodbCommandType = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.52.0';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mongodb';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wNzZjOGRhOTE4NzIzZjViYWJkMjYwZDM0Yzc3NmYzOC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLW1vbmdvZGIvYnVpbGQvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxvQ0FBb0MsZ0JBQWdCO0FBQ3ZGLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxhQUFhLG1CQUFPLENBQUMsdU1BQW1CO0FBQ3hDLGFBQWEsbUJBQU8sQ0FBQyxtTEFBUztBQUM5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8wNzZjOGRhOTE4NzIzZjViYWJkMjYwZDM0Yzc3NmYzOFxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLW1vbmdvZGJcXGJ1aWxkXFxzcmNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uKCkgeyByZXR1cm4gbVtrXTsgfSB9KTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19leHBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2V4cG9ydFN0YXIpIHx8IGZ1bmN0aW9uKG0sIGV4cG9ydHMpIHtcbiAgICBmb3IgKHZhciBwIGluIG0pIGlmIChwICE9PSBcImRlZmF1bHRcIiAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGV4cG9ydHMsIHApKSBfX2NyZWF0ZUJpbmRpbmcoZXhwb3J0cywgbSwgcCk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL2luc3RydW1lbnRhdGlvblwiKSwgZXhwb3J0cyk7XG5fX2V4cG9ydFN0YXIocmVxdWlyZShcIi4vdHlwZXNcIiksIGV4cG9ydHMpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MongoDBInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/internal-types.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/version.js\");\nconst DEFAULT_CONFIG = {\n    requireParentSpan: true,\n};\n/** mongodb instrumentation plugin for OpenTelemetry */\nclass MongoDBInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    _updateMetricInstruments() {\n        this._connectionsUsage = this.meter.createUpDownCounter('db.client.connections.usage', {\n            description: 'The number of connections that are currently in state described by the state attribute.',\n            unit: '{connection}',\n        });\n    }\n    init() {\n        const { v3PatchConnection: v3PatchConnection, v3UnpatchConnection: v3UnpatchConnection, } = this._getV3ConnectionPatches();\n        const { v4PatchConnect, v4UnpatchConnect } = this._getV4ConnectPatches();\n        const { v4PatchConnectionCallback, v4PatchConnectionPromise, v4UnpatchConnection, } = this._getV4ConnectionPatches();\n        const { v4PatchConnectionPool, v4UnpatchConnectionPool } = this._getV4ConnectionPoolPatches();\n        const { v4PatchSessions, v4UnpatchSessions } = this._getV4SessionsPatches();\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mongodb', ['>=3.3.0 <4'], undefined, undefined, [\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/core/wireprotocol/index.js', ['>=3.3.0 <4'], v3PatchConnection, v3UnpatchConnection),\n            ]),\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mongodb', ['>=4.0.0 <7'], undefined, undefined, [\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/cmap/connection.js', ['>=4.0.0 <6.4'], v4PatchConnectionCallback, v4UnpatchConnection),\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/cmap/connection.js', ['>=6.4.0 <7'], v4PatchConnectionPromise, v4UnpatchConnection),\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/cmap/connection_pool.js', ['>=4.0.0 <6.4'], v4PatchConnectionPool, v4UnpatchConnectionPool),\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/cmap/connect.js', ['>=4.0.0 <7'], v4PatchConnect, v4UnpatchConnect),\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/sessions.js', ['>=4.0.0 <7'], v4PatchSessions, v4UnpatchSessions),\n            ]),\n        ];\n    }\n    _getV3ConnectionPatches() {\n        return {\n            v3PatchConnection: (moduleExports) => {\n                // patch insert operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.insert)) {\n                    this._unwrap(moduleExports, 'insert');\n                }\n                this._wrap(moduleExports, 'insert', this._getV3PatchOperation('insert'));\n                // patch remove operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.remove)) {\n                    this._unwrap(moduleExports, 'remove');\n                }\n                this._wrap(moduleExports, 'remove', this._getV3PatchOperation('remove'));\n                // patch update operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.update)) {\n                    this._unwrap(moduleExports, 'update');\n                }\n                this._wrap(moduleExports, 'update', this._getV3PatchOperation('update'));\n                // patch other command\n                if ((0, instrumentation_1.isWrapped)(moduleExports.command)) {\n                    this._unwrap(moduleExports, 'command');\n                }\n                this._wrap(moduleExports, 'command', this._getV3PatchCommand());\n                // patch query\n                if ((0, instrumentation_1.isWrapped)(moduleExports.query)) {\n                    this._unwrap(moduleExports, 'query');\n                }\n                this._wrap(moduleExports, 'query', this._getV3PatchFind());\n                // patch get more operation on cursor\n                if ((0, instrumentation_1.isWrapped)(moduleExports.getMore)) {\n                    this._unwrap(moduleExports, 'getMore');\n                }\n                this._wrap(moduleExports, 'getMore', this._getV3PatchCursor());\n                return moduleExports;\n            },\n            v3UnpatchConnection: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports, 'insert');\n                this._unwrap(moduleExports, 'remove');\n                this._unwrap(moduleExports, 'update');\n                this._unwrap(moduleExports, 'command');\n                this._unwrap(moduleExports, 'query');\n                this._unwrap(moduleExports, 'getMore');\n            },\n        };\n    }\n    _getV4SessionsPatches() {\n        return {\n            v4PatchSessions: (moduleExports) => {\n                if ((0, instrumentation_1.isWrapped)(moduleExports.acquire)) {\n                    this._unwrap(moduleExports, 'acquire');\n                }\n                this._wrap(moduleExports.ServerSessionPool.prototype, 'acquire', this._getV4AcquireCommand());\n                if ((0, instrumentation_1.isWrapped)(moduleExports.release)) {\n                    this._unwrap(moduleExports, 'release');\n                }\n                this._wrap(moduleExports.ServerSessionPool.prototype, 'release', this._getV4ReleaseCommand());\n                return moduleExports;\n            },\n            v4UnpatchSessions: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                if ((0, instrumentation_1.isWrapped)(moduleExports.acquire)) {\n                    this._unwrap(moduleExports, 'acquire');\n                }\n                if ((0, instrumentation_1.isWrapped)(moduleExports.release)) {\n                    this._unwrap(moduleExports, 'release');\n                }\n            },\n        };\n    }\n    _getV4AcquireCommand() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchAcquire() {\n                const nSessionsBeforeAcquire = this.sessions.length;\n                const session = original.call(this);\n                const nSessionsAfterAcquire = this.sessions.length;\n                if (nSessionsBeforeAcquire === nSessionsAfterAcquire) {\n                    //no session in the pool. a new session was created and used\n                    instrumentation._connectionsUsage.add(1, {\n                        state: 'used',\n                        'pool.name': instrumentation._poolName,\n                    });\n                }\n                else if (nSessionsBeforeAcquire - 1 === nSessionsAfterAcquire) {\n                    //a session was already in the pool. remove it from the pool and use it.\n                    instrumentation._connectionsUsage.add(-1, {\n                        state: 'idle',\n                        'pool.name': instrumentation._poolName,\n                    });\n                    instrumentation._connectionsUsage.add(1, {\n                        state: 'used',\n                        'pool.name': instrumentation._poolName,\n                    });\n                }\n                return session;\n            };\n        };\n    }\n    _getV4ReleaseCommand() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchRelease(session) {\n                const cmdPromise = original.call(this, session);\n                instrumentation._connectionsUsage.add(-1, {\n                    state: 'used',\n                    'pool.name': instrumentation._poolName,\n                });\n                instrumentation._connectionsUsage.add(1, {\n                    state: 'idle',\n                    'pool.name': instrumentation._poolName,\n                });\n                return cmdPromise;\n            };\n        };\n    }\n    _getV4ConnectionPoolPatches() {\n        return {\n            v4PatchConnectionPool: (moduleExports) => {\n                const poolPrototype = moduleExports.ConnectionPool.prototype;\n                if ((0, instrumentation_1.isWrapped)(poolPrototype.checkOut)) {\n                    this._unwrap(poolPrototype, 'checkOut');\n                }\n                this._wrap(poolPrototype, 'checkOut', this._getV4ConnectionPoolCheckOut());\n                return moduleExports;\n            },\n            v4UnpatchConnectionPool: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports.ConnectionPool.prototype, 'checkOut');\n            },\n        };\n    }\n    _getV4ConnectPatches() {\n        return {\n            v4PatchConnect: (moduleExports) => {\n                if ((0, instrumentation_1.isWrapped)(moduleExports.connect)) {\n                    this._unwrap(moduleExports, 'connect');\n                }\n                this._wrap(moduleExports, 'connect', this._getV4ConnectCommand());\n                return moduleExports;\n            },\n            v4UnpatchConnect: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports, 'connect');\n            },\n        };\n    }\n    // This patch will become unnecessary once\n    // https://jira.mongodb.org/browse/NODE-5639 is done.\n    _getV4ConnectionPoolCheckOut() {\n        return (original) => {\n            return function patchedCheckout(callback) {\n                const patchedCallback = api_1.context.bind(api_1.context.active(), callback);\n                return original.call(this, patchedCallback);\n            };\n        };\n    }\n    _getV4ConnectCommand() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedConnect(options, callback) {\n                // from v6.4 `connect` method only accepts an options param and returns a promise\n                // with the connection\n                if (original.length === 1) {\n                    const result = original.call(this, options);\n                    if (result && typeof result.then === 'function') {\n                        result.then(() => instrumentation.setPoolName(options), \n                        // this handler is set to pass the lint rules\n                        () => undefined);\n                    }\n                    return result;\n                }\n                // Earlier versions expects a callback param and return void\n                const patchedCallback = function (err, conn) {\n                    if (err || !conn) {\n                        callback(err, conn);\n                        return;\n                    }\n                    instrumentation.setPoolName(options);\n                    callback(err, conn);\n                };\n                return original.call(this, options, patchedCallback);\n            };\n        };\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _getV4ConnectionPatches() {\n        return {\n            v4PatchConnectionCallback: (moduleExports) => {\n                // patch insert operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.Connection.prototype.command)) {\n                    this._unwrap(moduleExports.Connection.prototype, 'command');\n                }\n                this._wrap(moduleExports.Connection.prototype, 'command', this._getV4PatchCommandCallback());\n                return moduleExports;\n            },\n            v4PatchConnectionPromise: (moduleExports) => {\n                // patch insert operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.Connection.prototype.command)) {\n                    this._unwrap(moduleExports.Connection.prototype, 'command');\n                }\n                this._wrap(moduleExports.Connection.prototype, 'command', this._getV4PatchCommandPromise());\n                return moduleExports;\n            },\n            v4UnpatchConnection: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports.Connection.prototype, 'command');\n            },\n        };\n    }\n    /** Creates spans for common operations */\n    _getV3PatchOperation(operationName) {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedServerCommand(server, ns, ops, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = typeof options === 'function' ? options : callback;\n                if (skipInstrumentation ||\n                    typeof resultHandler !== 'function' ||\n                    typeof ops !== 'object') {\n                    if (typeof options === 'function') {\n                        return original.call(this, server, ns, ops, options);\n                    }\n                    else {\n                        return original.call(this, server, ns, ops, options, callback);\n                    }\n                }\n                const span = instrumentation.tracer.startSpan(`mongodb.${operationName}`, {\n                    kind: api_1.SpanKind.CLIENT,\n                });\n                instrumentation._populateV3Attributes(span, ns, server, \n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                ops[0], operationName);\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n                // handle when options is the callback to send the correct number of args\n                if (typeof options === 'function') {\n                    return original.call(this, server, ns, ops, patchedCallback);\n                }\n                else {\n                    return original.call(this, server, ns, ops, options, patchedCallback);\n                }\n            };\n        };\n    }\n    /** Creates spans for command operation */\n    _getV3PatchCommand() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedServerCommand(server, ns, cmd, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = typeof options === 'function' ? options : callback;\n                if (skipInstrumentation ||\n                    typeof resultHandler !== 'function' ||\n                    typeof cmd !== 'object') {\n                    if (typeof options === 'function') {\n                        return original.call(this, server, ns, cmd, options);\n                    }\n                    else {\n                        return original.call(this, server, ns, cmd, options, callback);\n                    }\n                }\n                const commandType = MongoDBInstrumentation._getCommandType(cmd);\n                const type = commandType === internal_types_1.MongodbCommandType.UNKNOWN ? 'command' : commandType;\n                const span = instrumentation.tracer.startSpan(`mongodb.${type}`, {\n                    kind: api_1.SpanKind.CLIENT,\n                });\n                const operation = commandType === internal_types_1.MongodbCommandType.UNKNOWN ? undefined : commandType;\n                instrumentation._populateV3Attributes(span, ns, server, cmd, operation);\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n                // handle when options is the callback to send the correct number of args\n                if (typeof options === 'function') {\n                    return original.call(this, server, ns, cmd, patchedCallback);\n                }\n                else {\n                    return original.call(this, server, ns, cmd, options, patchedCallback);\n                }\n            };\n        };\n    }\n    /** Creates spans for command operation */\n    _getV4PatchCommandCallback() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedV4ServerCommand(ns, cmd, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = callback;\n                const commandType = Object.keys(cmd)[0];\n                if (typeof cmd !== 'object' || cmd.ismaster || cmd.hello) {\n                    return original.call(this, ns, cmd, options, callback);\n                }\n                let span = undefined;\n                if (!skipInstrumentation) {\n                    span = instrumentation.tracer.startSpan(`mongodb.${commandType}`, {\n                        kind: api_1.SpanKind.CLIENT,\n                    });\n                    instrumentation._populateV4Attributes(span, this, ns, cmd, commandType);\n                }\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler, this.id, commandType);\n                return original.call(this, ns, cmd, options, patchedCallback);\n            };\n        };\n    }\n    _getV4PatchCommandPromise() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedV4ServerCommand(...args) {\n                const [ns, cmd] = args;\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const commandType = Object.keys(cmd)[0];\n                const resultHandler = () => undefined;\n                if (typeof cmd !== 'object' || cmd.ismaster || cmd.hello) {\n                    return original.apply(this, args);\n                }\n                let span = undefined;\n                if (!skipInstrumentation) {\n                    span = instrumentation.tracer.startSpan(`mongodb.${commandType}`, {\n                        kind: api_1.SpanKind.CLIENT,\n                    });\n                    instrumentation._populateV4Attributes(span, this, ns, cmd, commandType);\n                }\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler, this.id, commandType);\n                const result = original.apply(this, args);\n                result.then((res) => patchedCallback(null, res), (err) => patchedCallback(err));\n                return result;\n            };\n        };\n    }\n    /** Creates spans for find operation */\n    _getV3PatchFind() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedServerCommand(server, ns, cmd, cursorState, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = typeof options === 'function' ? options : callback;\n                if (skipInstrumentation ||\n                    typeof resultHandler !== 'function' ||\n                    typeof cmd !== 'object') {\n                    if (typeof options === 'function') {\n                        return original.call(this, server, ns, cmd, cursorState, options);\n                    }\n                    else {\n                        return original.call(this, server, ns, cmd, cursorState, options, callback);\n                    }\n                }\n                const span = instrumentation.tracer.startSpan('mongodb.find', {\n                    kind: api_1.SpanKind.CLIENT,\n                });\n                instrumentation._populateV3Attributes(span, ns, server, cmd, 'find');\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n                // handle when options is the callback to send the correct number of args\n                if (typeof options === 'function') {\n                    return original.call(this, server, ns, cmd, cursorState, patchedCallback);\n                }\n                else {\n                    return original.call(this, server, ns, cmd, cursorState, options, patchedCallback);\n                }\n            };\n        };\n    }\n    /** Creates spans for find operation */\n    _getV3PatchCursor() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedServerCommand(server, ns, cursorState, batchSize, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = typeof options === 'function' ? options : callback;\n                if (skipInstrumentation || typeof resultHandler !== 'function') {\n                    if (typeof options === 'function') {\n                        return original.call(this, server, ns, cursorState, batchSize, options);\n                    }\n                    else {\n                        return original.call(this, server, ns, cursorState, batchSize, options, callback);\n                    }\n                }\n                const span = instrumentation.tracer.startSpan('mongodb.getMore', {\n                    kind: api_1.SpanKind.CLIENT,\n                });\n                instrumentation._populateV3Attributes(span, ns, server, cursorState.cmd, 'getMore');\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n                // handle when options is the callback to send the correct number of args\n                if (typeof options === 'function') {\n                    return original.call(this, server, ns, cursorState, batchSize, patchedCallback);\n                }\n                else {\n                    return original.call(this, server, ns, cursorState, batchSize, options, patchedCallback);\n                }\n            };\n        };\n    }\n    /**\n     * Get the mongodb command type from the object.\n     * @param command Internal mongodb command object\n     */\n    static _getCommandType(command) {\n        if (command.createIndexes !== undefined) {\n            return internal_types_1.MongodbCommandType.CREATE_INDEXES;\n        }\n        else if (command.findandmodify !== undefined) {\n            return internal_types_1.MongodbCommandType.FIND_AND_MODIFY;\n        }\n        else if (command.ismaster !== undefined) {\n            return internal_types_1.MongodbCommandType.IS_MASTER;\n        }\n        else if (command.count !== undefined) {\n            return internal_types_1.MongodbCommandType.COUNT;\n        }\n        else if (command.aggregate !== undefined) {\n            return internal_types_1.MongodbCommandType.AGGREGATE;\n        }\n        else {\n            return internal_types_1.MongodbCommandType.UNKNOWN;\n        }\n    }\n    /**\n     * Populate span's attributes by fetching related metadata from the context\n     * @param span span to add attributes to\n     * @param connectionCtx mongodb internal connection context\n     * @param ns mongodb namespace\n     * @param command mongodb internal representation of a command\n     */\n    _populateV4Attributes(span, connectionCtx, ns, command, operation) {\n        let host, port;\n        if (connectionCtx) {\n            const hostParts = typeof connectionCtx.address === 'string'\n                ? connectionCtx.address.split(':')\n                : '';\n            if (hostParts.length === 2) {\n                host = hostParts[0];\n                port = hostParts[1];\n            }\n        }\n        // capture parameters within the query as well if enhancedDatabaseReporting is enabled.\n        let commandObj;\n        if ((command === null || command === void 0 ? void 0 : command.documents) && command.documents[0]) {\n            commandObj = command.documents[0];\n        }\n        else if (command === null || command === void 0 ? void 0 : command.cursors) {\n            commandObj = command.cursors;\n        }\n        else {\n            commandObj = command;\n        }\n        this._addAllSpanAttributes(span, ns.db, ns.collection, host, port, commandObj, operation);\n    }\n    /**\n     * Populate span's attributes by fetching related metadata from the context\n     * @param span span to add attributes to\n     * @param ns mongodb namespace\n     * @param topology mongodb internal representation of the network topology\n     * @param command mongodb internal representation of a command\n     */\n    _populateV3Attributes(span, ns, topology, command, operation) {\n        var _a, _b, _c, _d, _e, _f, _g, _h;\n        // add network attributes to determine the remote server\n        let host;\n        let port;\n        if (topology && topology.s) {\n            host = (_b = (_a = topology.s.options) === null || _a === void 0 ? void 0 : _a.host) !== null && _b !== void 0 ? _b : topology.s.host;\n            port = (_e = ((_d = (_c = topology.s.options) === null || _c === void 0 ? void 0 : _c.port) !== null && _d !== void 0 ? _d : topology.s.port)) === null || _e === void 0 ? void 0 : _e.toString();\n            if (host == null || port == null) {\n                const address = (_f = topology.description) === null || _f === void 0 ? void 0 : _f.address;\n                if (address) {\n                    const addressSegments = address.split(':');\n                    host = addressSegments[0];\n                    port = addressSegments[1];\n                }\n            }\n        }\n        // The namespace is a combination of the database name and the name of the\n        // collection or index, like so: [database-name].[collection-or-index-name].\n        // It could be a string or an instance of MongoDBNamespace, as such we\n        // always coerce to a string to extract db and collection.\n        const [dbName, dbCollection] = ns.toString().split('.');\n        // capture parameters within the query as well if enhancedDatabaseReporting is enabled.\n        const commandObj = (_h = (_g = command === null || command === void 0 ? void 0 : command.query) !== null && _g !== void 0 ? _g : command === null || command === void 0 ? void 0 : command.q) !== null && _h !== void 0 ? _h : command;\n        this._addAllSpanAttributes(span, dbName, dbCollection, host, port, commandObj, operation);\n    }\n    _addAllSpanAttributes(span, dbName, dbCollection, host, port, commandObj, operation) {\n        // add database related attributes\n        span.setAttributes({\n            [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MONGODB,\n            [semantic_conventions_1.SEMATTRS_DB_NAME]: dbName,\n            [semantic_conventions_1.SEMATTRS_DB_MONGODB_COLLECTION]: dbCollection,\n            [semantic_conventions_1.SEMATTRS_DB_OPERATION]: operation,\n            [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: `mongodb://${host}:${port}/${dbName}`,\n        });\n        if (host && port) {\n            span.setAttribute(semantic_conventions_1.SEMATTRS_NET_PEER_NAME, host);\n            const portNumber = parseInt(port, 10);\n            if (!isNaN(portNumber)) {\n                span.setAttribute(semantic_conventions_1.SEMATTRS_NET_PEER_PORT, portNumber);\n            }\n        }\n        if (!commandObj)\n            return;\n        const { dbStatementSerializer: configDbStatementSerializer } = this.getConfig();\n        const dbStatementSerializer = typeof configDbStatementSerializer === 'function'\n            ? configDbStatementSerializer\n            : this._defaultDbStatementSerializer.bind(this);\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            const query = dbStatementSerializer(commandObj);\n            span.setAttribute(semantic_conventions_1.SEMATTRS_DB_STATEMENT, query);\n        }, err => {\n            if (err) {\n                this._diag.error('Error running dbStatementSerializer hook', err);\n            }\n        }, true);\n    }\n    _defaultDbStatementSerializer(commandObj) {\n        const { enhancedDatabaseReporting } = this.getConfig();\n        const resultObj = enhancedDatabaseReporting\n            ? commandObj\n            : this._scrubStatement(commandObj);\n        return JSON.stringify(resultObj);\n    }\n    _scrubStatement(value) {\n        if (Array.isArray(value)) {\n            return value.map(element => this._scrubStatement(element));\n        }\n        if (typeof value === 'object' && value !== null) {\n            return Object.fromEntries(Object.entries(value).map(([key, element]) => [\n                key,\n                this._scrubStatement(element),\n            ]));\n        }\n        // A value like string or number, possible contains PII, scrub it\n        return '?';\n    }\n    /**\n     * Triggers the response hook in case it is defined.\n     * @param span The span to add the results to.\n     * @param result The command result\n     */\n    _handleExecutionResult(span, result) {\n        const { responseHook } = this.getConfig();\n        if (typeof responseHook === 'function') {\n            (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                responseHook(span, { data: result });\n            }, err => {\n                if (err) {\n                    this._diag.error('Error running response hook', err);\n                }\n            }, true);\n        }\n    }\n    /**\n     * Ends a created span.\n     * @param span The created span to end.\n     * @param resultHandler A callback function.\n     * @param connectionId: The connection ID of the Command response.\n     */\n    _patchEnd(span, resultHandler, connectionId, commandType) {\n        // mongodb is using \"tick\" when calling a callback, this way the context\n        // in final callback (resultHandler) is lost\n        const activeContext = api_1.context.active();\n        const instrumentation = this;\n        return function patchedEnd(...args) {\n            const error = args[0];\n            if (span) {\n                if (error instanceof Error) {\n                    span === null || span === void 0 ? void 0 : span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: error.message,\n                    });\n                }\n                else {\n                    const result = args[1];\n                    instrumentation._handleExecutionResult(span, result);\n                }\n                span.end();\n            }\n            return api_1.context.with(activeContext, () => {\n                if (commandType === 'endSessions') {\n                    instrumentation._connectionsUsage.add(-1, {\n                        state: 'idle',\n                        'pool.name': instrumentation._poolName,\n                    });\n                }\n                return resultHandler.apply(this, args);\n            });\n        };\n    }\n    setPoolName(options) {\n        var _a, _b;\n        const host = (_a = options.hostAddress) === null || _a === void 0 ? void 0 : _a.host;\n        const port = (_b = options.hostAddress) === null || _b === void 0 ? void 0 : _b.port;\n        const database = options.dbName;\n        const poolName = `mongodb://${host}:${port}/${database}`;\n        this._poolName = poolName;\n    }\n    _checkSkipInstrumentation(currentSpan) {\n        const requireParentSpan = this.getConfig().requireParentSpan;\n        const hasNoParentSpan = currentSpan === undefined;\n        return requireParentSpan === true && hasNoParentSpan;\n    }\n}\nexports.MongoDBInstrumentation = MongoDBInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MongodbCommandType = void 0;\nvar MongodbCommandType;\n(function (MongodbCommandType) {\n    MongodbCommandType[\"CREATE_INDEXES\"] = \"createIndexes\";\n    MongodbCommandType[\"FIND_AND_MODIFY\"] = \"findAndModify\";\n    MongodbCommandType[\"IS_MASTER\"] = \"isMaster\";\n    MongodbCommandType[\"COUNT\"] = \"count\";\n    MongodbCommandType[\"AGGREGATE\"] = \"aggregate\";\n    MongodbCommandType[\"UNKNOWN\"] = \"unknown\";\n})(MongodbCommandType = exports.MongodbCommandType || (exports.MongodbCommandType = {}));\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/internal-types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MongodbCommandType = void 0;\nvar MongodbCommandType;\n(function (MongodbCommandType) {\n    MongodbCommandType[\"CREATE_INDEXES\"] = \"createIndexes\";\n    MongodbCommandType[\"FIND_AND_MODIFY\"] = \"findAndModify\";\n    MongodbCommandType[\"IS_MASTER\"] = \"isMaster\";\n    MongodbCommandType[\"COUNT\"] = \"count\";\n    MongodbCommandType[\"UNKNOWN\"] = \"unknown\";\n})(MongodbCommandType = exports.MongodbCommandType || (exports.MongodbCommandType = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.52.0';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mongodb';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/instrumentation.js":
/*!******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/instrumentation.js ***!
  \******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MongoDBInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/internal-types.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/version.js\");\nconst DEFAULT_CONFIG = {\n    requireParentSpan: true,\n};\n/** mongodb instrumentation plugin for OpenTelemetry */\nclass MongoDBInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, DEFAULT_CONFIG), config));\n    }\n    _updateMetricInstruments() {\n        this._connectionsUsage = this.meter.createUpDownCounter('db.client.connections.usage', {\n            description: 'The number of connections that are currently in state described by the state attribute.',\n            unit: '{connection}',\n        });\n    }\n    init() {\n        const { v3PatchConnection: v3PatchConnection, v3UnpatchConnection: v3UnpatchConnection, } = this._getV3ConnectionPatches();\n        const { v4PatchConnect, v4UnpatchConnect } = this._getV4ConnectPatches();\n        const { v4PatchConnectionCallback, v4PatchConnectionPromise, v4UnpatchConnection, } = this._getV4ConnectionPatches();\n        const { v4PatchConnectionPool, v4UnpatchConnectionPool } = this._getV4ConnectionPoolPatches();\n        const { v4PatchSessions, v4UnpatchSessions } = this._getV4SessionsPatches();\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mongodb', ['>=3.3.0 <4'], undefined, undefined, [\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/core/wireprotocol/index.js', ['>=3.3.0 <4'], v3PatchConnection, v3UnpatchConnection),\n            ]),\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mongodb', ['>=4.0.0 <7'], undefined, undefined, [\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/cmap/connection.js', ['>=4.0.0 <6.4'], v4PatchConnectionCallback, v4UnpatchConnection),\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/cmap/connection.js', ['>=6.4.0 <7'], v4PatchConnectionPromise, v4UnpatchConnection),\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/cmap/connection_pool.js', ['>=4.0.0 <6.4'], v4PatchConnectionPool, v4UnpatchConnectionPool),\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/cmap/connect.js', ['>=4.0.0 <7'], v4PatchConnect, v4UnpatchConnect),\n                new instrumentation_1.InstrumentationNodeModuleFile('mongodb/lib/sessions.js', ['>=4.0.0 <7'], v4PatchSessions, v4UnpatchSessions),\n            ]),\n        ];\n    }\n    _getV3ConnectionPatches() {\n        return {\n            v3PatchConnection: (moduleExports) => {\n                // patch insert operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.insert)) {\n                    this._unwrap(moduleExports, 'insert');\n                }\n                this._wrap(moduleExports, 'insert', this._getV3PatchOperation('insert'));\n                // patch remove operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.remove)) {\n                    this._unwrap(moduleExports, 'remove');\n                }\n                this._wrap(moduleExports, 'remove', this._getV3PatchOperation('remove'));\n                // patch update operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.update)) {\n                    this._unwrap(moduleExports, 'update');\n                }\n                this._wrap(moduleExports, 'update', this._getV3PatchOperation('update'));\n                // patch other command\n                if ((0, instrumentation_1.isWrapped)(moduleExports.command)) {\n                    this._unwrap(moduleExports, 'command');\n                }\n                this._wrap(moduleExports, 'command', this._getV3PatchCommand());\n                // patch query\n                if ((0, instrumentation_1.isWrapped)(moduleExports.query)) {\n                    this._unwrap(moduleExports, 'query');\n                }\n                this._wrap(moduleExports, 'query', this._getV3PatchFind());\n                // patch get more operation on cursor\n                if ((0, instrumentation_1.isWrapped)(moduleExports.getMore)) {\n                    this._unwrap(moduleExports, 'getMore');\n                }\n                this._wrap(moduleExports, 'getMore', this._getV3PatchCursor());\n                return moduleExports;\n            },\n            v3UnpatchConnection: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports, 'insert');\n                this._unwrap(moduleExports, 'remove');\n                this._unwrap(moduleExports, 'update');\n                this._unwrap(moduleExports, 'command');\n                this._unwrap(moduleExports, 'query');\n                this._unwrap(moduleExports, 'getMore');\n            },\n        };\n    }\n    _getV4SessionsPatches() {\n        return {\n            v4PatchSessions: (moduleExports) => {\n                if ((0, instrumentation_1.isWrapped)(moduleExports.acquire)) {\n                    this._unwrap(moduleExports, 'acquire');\n                }\n                this._wrap(moduleExports.ServerSessionPool.prototype, 'acquire', this._getV4AcquireCommand());\n                if ((0, instrumentation_1.isWrapped)(moduleExports.release)) {\n                    this._unwrap(moduleExports, 'release');\n                }\n                this._wrap(moduleExports.ServerSessionPool.prototype, 'release', this._getV4ReleaseCommand());\n                return moduleExports;\n            },\n            v4UnpatchSessions: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                if ((0, instrumentation_1.isWrapped)(moduleExports.acquire)) {\n                    this._unwrap(moduleExports, 'acquire');\n                }\n                if ((0, instrumentation_1.isWrapped)(moduleExports.release)) {\n                    this._unwrap(moduleExports, 'release');\n                }\n            },\n        };\n    }\n    _getV4AcquireCommand() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchAcquire() {\n                const nSessionsBeforeAcquire = this.sessions.length;\n                const session = original.call(this);\n                const nSessionsAfterAcquire = this.sessions.length;\n                if (nSessionsBeforeAcquire === nSessionsAfterAcquire) {\n                    //no session in the pool. a new session was created and used\n                    instrumentation._connectionsUsage.add(1, {\n                        state: 'used',\n                        'pool.name': instrumentation._poolName,\n                    });\n                }\n                else if (nSessionsBeforeAcquire - 1 === nSessionsAfterAcquire) {\n                    //a session was already in the pool. remove it from the pool and use it.\n                    instrumentation._connectionsUsage.add(-1, {\n                        state: 'idle',\n                        'pool.name': instrumentation._poolName,\n                    });\n                    instrumentation._connectionsUsage.add(1, {\n                        state: 'used',\n                        'pool.name': instrumentation._poolName,\n                    });\n                }\n                return session;\n            };\n        };\n    }\n    _getV4ReleaseCommand() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchRelease(session) {\n                const cmdPromise = original.call(this, session);\n                instrumentation._connectionsUsage.add(-1, {\n                    state: 'used',\n                    'pool.name': instrumentation._poolName,\n                });\n                instrumentation._connectionsUsage.add(1, {\n                    state: 'idle',\n                    'pool.name': instrumentation._poolName,\n                });\n                return cmdPromise;\n            };\n        };\n    }\n    _getV4ConnectionPoolPatches() {\n        return {\n            v4PatchConnectionPool: (moduleExports) => {\n                const poolPrototype = moduleExports.ConnectionPool.prototype;\n                if ((0, instrumentation_1.isWrapped)(poolPrototype.checkOut)) {\n                    this._unwrap(poolPrototype, 'checkOut');\n                }\n                this._wrap(poolPrototype, 'checkOut', this._getV4ConnectionPoolCheckOut());\n                return moduleExports;\n            },\n            v4UnpatchConnectionPool: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports.ConnectionPool.prototype, 'checkOut');\n            },\n        };\n    }\n    _getV4ConnectPatches() {\n        return {\n            v4PatchConnect: (moduleExports) => {\n                if ((0, instrumentation_1.isWrapped)(moduleExports.connect)) {\n                    this._unwrap(moduleExports, 'connect');\n                }\n                this._wrap(moduleExports, 'connect', this._getV4ConnectCommand());\n                return moduleExports;\n            },\n            v4UnpatchConnect: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports, 'connect');\n            },\n        };\n    }\n    // This patch will become unnecessary once\n    // https://jira.mongodb.org/browse/NODE-5639 is done.\n    _getV4ConnectionPoolCheckOut() {\n        return (original) => {\n            return function patchedCheckout(callback) {\n                const patchedCallback = api_1.context.bind(api_1.context.active(), callback);\n                return original.call(this, patchedCallback);\n            };\n        };\n    }\n    _getV4ConnectCommand() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedConnect(options, callback) {\n                // from v6.4 `connect` method only accepts an options param and returns a promise\n                // with the connection\n                if (original.length === 1) {\n                    const result = original.call(this, options);\n                    if (result && typeof result.then === 'function') {\n                        result.then(() => instrumentation.setPoolName(options), \n                        // this handler is set to pass the lint rules\n                        () => undefined);\n                    }\n                    return result;\n                }\n                // Earlier versions expects a callback param and return void\n                const patchedCallback = function (err, conn) {\n                    if (err || !conn) {\n                        callback(err, conn);\n                        return;\n                    }\n                    instrumentation.setPoolName(options);\n                    callback(err, conn);\n                };\n                return original.call(this, options, patchedCallback);\n            };\n        };\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _getV4ConnectionPatches() {\n        return {\n            v4PatchConnectionCallback: (moduleExports) => {\n                // patch insert operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.Connection.prototype.command)) {\n                    this._unwrap(moduleExports.Connection.prototype, 'command');\n                }\n                this._wrap(moduleExports.Connection.prototype, 'command', this._getV4PatchCommandCallback());\n                return moduleExports;\n            },\n            v4PatchConnectionPromise: (moduleExports) => {\n                // patch insert operation\n                if ((0, instrumentation_1.isWrapped)(moduleExports.Connection.prototype.command)) {\n                    this._unwrap(moduleExports.Connection.prototype, 'command');\n                }\n                this._wrap(moduleExports.Connection.prototype, 'command', this._getV4PatchCommandPromise());\n                return moduleExports;\n            },\n            v4UnpatchConnection: (moduleExports) => {\n                if (moduleExports === undefined)\n                    return;\n                this._unwrap(moduleExports.Connection.prototype, 'command');\n            },\n        };\n    }\n    /** Creates spans for common operations */\n    _getV3PatchOperation(operationName) {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedServerCommand(server, ns, ops, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = typeof options === 'function' ? options : callback;\n                if (skipInstrumentation ||\n                    typeof resultHandler !== 'function' ||\n                    typeof ops !== 'object') {\n                    if (typeof options === 'function') {\n                        return original.call(this, server, ns, ops, options);\n                    }\n                    else {\n                        return original.call(this, server, ns, ops, options, callback);\n                    }\n                }\n                const span = instrumentation.tracer.startSpan(`mongodb.${operationName}`, {\n                    kind: api_1.SpanKind.CLIENT,\n                });\n                instrumentation._populateV3Attributes(span, ns, server, \n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                ops[0], operationName);\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n                // handle when options is the callback to send the correct number of args\n                if (typeof options === 'function') {\n                    return original.call(this, server, ns, ops, patchedCallback);\n                }\n                else {\n                    return original.call(this, server, ns, ops, options, patchedCallback);\n                }\n            };\n        };\n    }\n    /** Creates spans for command operation */\n    _getV3PatchCommand() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedServerCommand(server, ns, cmd, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = typeof options === 'function' ? options : callback;\n                if (skipInstrumentation ||\n                    typeof resultHandler !== 'function' ||\n                    typeof cmd !== 'object') {\n                    if (typeof options === 'function') {\n                        return original.call(this, server, ns, cmd, options);\n                    }\n                    else {\n                        return original.call(this, server, ns, cmd, options, callback);\n                    }\n                }\n                const commandType = MongoDBInstrumentation._getCommandType(cmd);\n                const type = commandType === internal_types_1.MongodbCommandType.UNKNOWN ? 'command' : commandType;\n                const span = instrumentation.tracer.startSpan(`mongodb.${type}`, {\n                    kind: api_1.SpanKind.CLIENT,\n                });\n                const operation = commandType === internal_types_1.MongodbCommandType.UNKNOWN ? undefined : commandType;\n                instrumentation._populateV3Attributes(span, ns, server, cmd, operation);\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n                // handle when options is the callback to send the correct number of args\n                if (typeof options === 'function') {\n                    return original.call(this, server, ns, cmd, patchedCallback);\n                }\n                else {\n                    return original.call(this, server, ns, cmd, options, patchedCallback);\n                }\n            };\n        };\n    }\n    /** Creates spans for command operation */\n    _getV4PatchCommandCallback() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedV4ServerCommand(ns, cmd, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = callback;\n                const commandType = Object.keys(cmd)[0];\n                if (typeof cmd !== 'object' || cmd.ismaster || cmd.hello) {\n                    return original.call(this, ns, cmd, options, callback);\n                }\n                let span = undefined;\n                if (!skipInstrumentation) {\n                    span = instrumentation.tracer.startSpan(`mongodb.${commandType}`, {\n                        kind: api_1.SpanKind.CLIENT,\n                    });\n                    instrumentation._populateV4Attributes(span, this, ns, cmd, commandType);\n                }\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler, this.id, commandType);\n                return original.call(this, ns, cmd, options, patchedCallback);\n            };\n        };\n    }\n    _getV4PatchCommandPromise() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedV4ServerCommand(...args) {\n                const [ns, cmd] = args;\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const commandType = Object.keys(cmd)[0];\n                const resultHandler = () => undefined;\n                if (typeof cmd !== 'object' || cmd.ismaster || cmd.hello) {\n                    return original.apply(this, args);\n                }\n                let span = undefined;\n                if (!skipInstrumentation) {\n                    span = instrumentation.tracer.startSpan(`mongodb.${commandType}`, {\n                        kind: api_1.SpanKind.CLIENT,\n                    });\n                    instrumentation._populateV4Attributes(span, this, ns, cmd, commandType);\n                }\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler, this.id, commandType);\n                const result = original.apply(this, args);\n                result.then((res) => patchedCallback(null, res), (err) => patchedCallback(err));\n                return result;\n            };\n        };\n    }\n    /** Creates spans for find operation */\n    _getV3PatchFind() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedServerCommand(server, ns, cmd, cursorState, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = typeof options === 'function' ? options : callback;\n                if (skipInstrumentation ||\n                    typeof resultHandler !== 'function' ||\n                    typeof cmd !== 'object') {\n                    if (typeof options === 'function') {\n                        return original.call(this, server, ns, cmd, cursorState, options);\n                    }\n                    else {\n                        return original.call(this, server, ns, cmd, cursorState, options, callback);\n                    }\n                }\n                const span = instrumentation.tracer.startSpan('mongodb.find', {\n                    kind: api_1.SpanKind.CLIENT,\n                });\n                instrumentation._populateV3Attributes(span, ns, server, cmd, 'find');\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n                // handle when options is the callback to send the correct number of args\n                if (typeof options === 'function') {\n                    return original.call(this, server, ns, cmd, cursorState, patchedCallback);\n                }\n                else {\n                    return original.call(this, server, ns, cmd, cursorState, options, patchedCallback);\n                }\n            };\n        };\n    }\n    /** Creates spans for find operation */\n    _getV3PatchCursor() {\n        const instrumentation = this;\n        return (original) => {\n            return function patchedServerCommand(server, ns, cursorState, batchSize, options, callback) {\n                const currentSpan = api_1.trace.getSpan(api_1.context.active());\n                const skipInstrumentation = instrumentation._checkSkipInstrumentation(currentSpan);\n                const resultHandler = typeof options === 'function' ? options : callback;\n                if (skipInstrumentation || typeof resultHandler !== 'function') {\n                    if (typeof options === 'function') {\n                        return original.call(this, server, ns, cursorState, batchSize, options);\n                    }\n                    else {\n                        return original.call(this, server, ns, cursorState, batchSize, options, callback);\n                    }\n                }\n                const span = instrumentation.tracer.startSpan('mongodb.getMore', {\n                    kind: api_1.SpanKind.CLIENT,\n                });\n                instrumentation._populateV3Attributes(span, ns, server, cursorState.cmd, 'getMore');\n                const patchedCallback = instrumentation._patchEnd(span, resultHandler);\n                // handle when options is the callback to send the correct number of args\n                if (typeof options === 'function') {\n                    return original.call(this, server, ns, cursorState, batchSize, patchedCallback);\n                }\n                else {\n                    return original.call(this, server, ns, cursorState, batchSize, options, patchedCallback);\n                }\n            };\n        };\n    }\n    /**\n     * Get the mongodb command type from the object.\n     * @param command Internal mongodb command object\n     */\n    static _getCommandType(command) {\n        if (command.createIndexes !== undefined) {\n            return internal_types_1.MongodbCommandType.CREATE_INDEXES;\n        }\n        else if (command.findandmodify !== undefined) {\n            return internal_types_1.MongodbCommandType.FIND_AND_MODIFY;\n        }\n        else if (command.ismaster !== undefined) {\n            return internal_types_1.MongodbCommandType.IS_MASTER;\n        }\n        else if (command.count !== undefined) {\n            return internal_types_1.MongodbCommandType.COUNT;\n        }\n        else if (command.aggregate !== undefined) {\n            return internal_types_1.MongodbCommandType.AGGREGATE;\n        }\n        else {\n            return internal_types_1.MongodbCommandType.UNKNOWN;\n        }\n    }\n    /**\n     * Populate span's attributes by fetching related metadata from the context\n     * @param span span to add attributes to\n     * @param connectionCtx mongodb internal connection context\n     * @param ns mongodb namespace\n     * @param command mongodb internal representation of a command\n     */\n    _populateV4Attributes(span, connectionCtx, ns, command, operation) {\n        let host, port;\n        if (connectionCtx) {\n            const hostParts = typeof connectionCtx.address === 'string'\n                ? connectionCtx.address.split(':')\n                : '';\n            if (hostParts.length === 2) {\n                host = hostParts[0];\n                port = hostParts[1];\n            }\n        }\n        // capture parameters within the query as well if enhancedDatabaseReporting is enabled.\n        let commandObj;\n        if ((command === null || command === void 0 ? void 0 : command.documents) && command.documents[0]) {\n            commandObj = command.documents[0];\n        }\n        else if (command === null || command === void 0 ? void 0 : command.cursors) {\n            commandObj = command.cursors;\n        }\n        else {\n            commandObj = command;\n        }\n        this._addAllSpanAttributes(span, ns.db, ns.collection, host, port, commandObj, operation);\n    }\n    /**\n     * Populate span's attributes by fetching related metadata from the context\n     * @param span span to add attributes to\n     * @param ns mongodb namespace\n     * @param topology mongodb internal representation of the network topology\n     * @param command mongodb internal representation of a command\n     */\n    _populateV3Attributes(span, ns, topology, command, operation) {\n        var _a, _b, _c, _d, _e, _f, _g, _h;\n        // add network attributes to determine the remote server\n        let host;\n        let port;\n        if (topology && topology.s) {\n            host = (_b = (_a = topology.s.options) === null || _a === void 0 ? void 0 : _a.host) !== null && _b !== void 0 ? _b : topology.s.host;\n            port = (_e = ((_d = (_c = topology.s.options) === null || _c === void 0 ? void 0 : _c.port) !== null && _d !== void 0 ? _d : topology.s.port)) === null || _e === void 0 ? void 0 : _e.toString();\n            if (host == null || port == null) {\n                const address = (_f = topology.description) === null || _f === void 0 ? void 0 : _f.address;\n                if (address) {\n                    const addressSegments = address.split(':');\n                    host = addressSegments[0];\n                    port = addressSegments[1];\n                }\n            }\n        }\n        // The namespace is a combination of the database name and the name of the\n        // collection or index, like so: [database-name].[collection-or-index-name].\n        // It could be a string or an instance of MongoDBNamespace, as such we\n        // always coerce to a string to extract db and collection.\n        const [dbName, dbCollection] = ns.toString().split('.');\n        // capture parameters within the query as well if enhancedDatabaseReporting is enabled.\n        const commandObj = (_h = (_g = command === null || command === void 0 ? void 0 : command.query) !== null && _g !== void 0 ? _g : command === null || command === void 0 ? void 0 : command.q) !== null && _h !== void 0 ? _h : command;\n        this._addAllSpanAttributes(span, dbName, dbCollection, host, port, commandObj, operation);\n    }\n    _addAllSpanAttributes(span, dbName, dbCollection, host, port, commandObj, operation) {\n        // add database related attributes\n        span.setAttributes({\n            [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MONGODB,\n            [semantic_conventions_1.SEMATTRS_DB_NAME]: dbName,\n            [semantic_conventions_1.SEMATTRS_DB_MONGODB_COLLECTION]: dbCollection,\n            [semantic_conventions_1.SEMATTRS_DB_OPERATION]: operation,\n            [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: `mongodb://${host}:${port}/${dbName}`,\n        });\n        if (host && port) {\n            span.setAttribute(semantic_conventions_1.SEMATTRS_NET_PEER_NAME, host);\n            const portNumber = parseInt(port, 10);\n            if (!isNaN(portNumber)) {\n                span.setAttribute(semantic_conventions_1.SEMATTRS_NET_PEER_PORT, portNumber);\n            }\n        }\n        if (!commandObj)\n            return;\n        const { dbStatementSerializer: configDbStatementSerializer } = this.getConfig();\n        const dbStatementSerializer = typeof configDbStatementSerializer === 'function'\n            ? configDbStatementSerializer\n            : this._defaultDbStatementSerializer.bind(this);\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            const query = dbStatementSerializer(commandObj);\n            span.setAttribute(semantic_conventions_1.SEMATTRS_DB_STATEMENT, query);\n        }, err => {\n            if (err) {\n                this._diag.error('Error running dbStatementSerializer hook', err);\n            }\n        }, true);\n    }\n    _defaultDbStatementSerializer(commandObj) {\n        const { enhancedDatabaseReporting } = this.getConfig();\n        const resultObj = enhancedDatabaseReporting\n            ? commandObj\n            : this._scrubStatement(commandObj);\n        return JSON.stringify(resultObj);\n    }\n    _scrubStatement(value) {\n        if (Array.isArray(value)) {\n            return value.map(element => this._scrubStatement(element));\n        }\n        if (typeof value === 'object' && value !== null) {\n            return Object.fromEntries(Object.entries(value).map(([key, element]) => [\n                key,\n                this._scrubStatement(element),\n            ]));\n        }\n        // A value like string or number, possible contains PII, scrub it\n        return '?';\n    }\n    /**\n     * Triggers the response hook in case it is defined.\n     * @param span The span to add the results to.\n     * @param result The command result\n     */\n    _handleExecutionResult(span, result) {\n        const { responseHook } = this.getConfig();\n        if (typeof responseHook === 'function') {\n            (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                responseHook(span, { data: result });\n            }, err => {\n                if (err) {\n                    this._diag.error('Error running response hook', err);\n                }\n            }, true);\n        }\n    }\n    /**\n     * Ends a created span.\n     * @param span The created span to end.\n     * @param resultHandler A callback function.\n     * @param connectionId: The connection ID of the Command response.\n     */\n    _patchEnd(span, resultHandler, connectionId, commandType) {\n        // mongodb is using \"tick\" when calling a callback, this way the context\n        // in final callback (resultHandler) is lost\n        const activeContext = api_1.context.active();\n        const instrumentation = this;\n        return function patchedEnd(...args) {\n            const error = args[0];\n            if (span) {\n                if (error instanceof Error) {\n                    span === null || span === void 0 ? void 0 : span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: error.message,\n                    });\n                }\n                else {\n                    const result = args[1];\n                    instrumentation._handleExecutionResult(span, result);\n                }\n                span.end();\n            }\n            return api_1.context.with(activeContext, () => {\n                if (commandType === 'endSessions') {\n                    instrumentation._connectionsUsage.add(-1, {\n                        state: 'idle',\n                        'pool.name': instrumentation._poolName,\n                    });\n                }\n                return resultHandler.apply(this, args);\n            });\n        };\n    }\n    setPoolName(options) {\n        var _a, _b;\n        const host = (_a = options.hostAddress) === null || _a === void 0 ? void 0 : _a.host;\n        const port = (_b = options.hostAddress) === null || _b === void 0 ? void 0 : _b.port;\n        const database = options.dbName;\n        const poolName = `mongodb://${host}:${port}/${database}`;\n        this._poolName = poolName;\n    }\n    _checkSkipInstrumentation(currentSpan) {\n        const requireParentSpan = this.getConfig().requireParentSpan;\n        const hasNoParentSpan = currentSpan === undefined;\n        return requireParentSpan === true && hasNoParentSpan;\n    }\n}\nexports.MongoDBInstrumentation = MongoDBInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/internal-types.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/internal-types.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MongodbCommandType = void 0;\nvar MongodbCommandType;\n(function (MongodbCommandType) {\n    MongodbCommandType[\"CREATE_INDEXES\"] = \"createIndexes\";\n    MongodbCommandType[\"FIND_AND_MODIFY\"] = \"findAndModify\";\n    MongodbCommandType[\"IS_MASTER\"] = \"isMaster\";\n    MongodbCommandType[\"COUNT\"] = \"count\";\n    MongodbCommandType[\"AGGREGATE\"] = \"aggregate\";\n    MongodbCommandType[\"UNKNOWN\"] = \"unknown\";\n})(MongodbCommandType = exports.MongodbCommandType || (exports.MongodbCommandType = {}));\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/internal-types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MongodbCommandType = void 0;\nvar MongodbCommandType;\n(function (MongodbCommandType) {\n    MongodbCommandType[\"CREATE_INDEXES\"] = \"createIndexes\";\n    MongodbCommandType[\"FIND_AND_MODIFY\"] = \"findAndModify\";\n    MongodbCommandType[\"IS_MASTER\"] = \"isMaster\";\n    MongodbCommandType[\"COUNT\"] = \"count\";\n    MongodbCommandType[\"UNKNOWN\"] = \"unknown\";\n})(MongodbCommandType = exports.MongodbCommandType || (exports.MongodbCommandType = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.52.0';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mongodb';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_076c8da918723f5babd260d34c776f38/node_modules/@opentelemetry/instrumentation-mongodb/build/src/version.js\n");

/***/ })

};
;