"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/index.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/index.js ***!
  \***********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/instrumentation.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/instrumentation.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/instrumentation.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DataloaderInstrumentation = void 0;\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/version.js\");\nconst MODULE_NAME = 'dataloader';\nclass DataloaderInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition(MODULE_NAME, ['>=2.0.0 <3'], dataloader => {\n                this._patchLoad(dataloader.prototype);\n                this._patchLoadMany(dataloader.prototype);\n                return this._getPatchedConstructor(dataloader);\n            }, dataloader => {\n                if ((0, instrumentation_1.isWrapped)(dataloader.prototype.load)) {\n                    this._unwrap(dataloader.prototype, 'load');\n                }\n                if ((0, instrumentation_1.isWrapped)(dataloader.prototype.loadMany)) {\n                    this._unwrap(dataloader.prototype, 'loadMany');\n                }\n            }),\n        ];\n    }\n    shouldCreateSpans() {\n        const config = this.getConfig();\n        const hasParentSpan = api_1.trace.getSpan(api_1.context.active()) !== undefined;\n        return hasParentSpan || !config.requireParentSpan;\n    }\n    getSpanName(dataloader, operation) {\n        const dataloaderName = dataloader.name;\n        if (dataloaderName === undefined || dataloaderName === null) {\n            return `${MODULE_NAME}.${operation}`;\n        }\n        return `${MODULE_NAME}.${operation} ${dataloaderName}`;\n    }\n    _getPatchedConstructor(constructor) {\n        const prototype = constructor.prototype;\n        const instrumentation = this;\n        function PatchedDataloader(...args) {\n            const inst = new constructor(...args);\n            if (!instrumentation.isEnabled()) {\n                return inst;\n            }\n            if ((0, instrumentation_1.isWrapped)(inst._batchLoadFn)) {\n                instrumentation._unwrap(inst, '_batchLoadFn');\n            }\n            instrumentation._wrap(inst, '_batchLoadFn', original => {\n                return function patchedBatchLoadFn(...args) {\n                    var _a;\n                    if (!instrumentation.isEnabled() ||\n                        !instrumentation.shouldCreateSpans()) {\n                        return original.call(this, ...args);\n                    }\n                    const parent = api_1.context.active();\n                    const span = instrumentation.tracer.startSpan(instrumentation.getSpanName(inst, 'batch'), { links: (_a = this._batch) === null || _a === void 0 ? void 0 : _a.spanLinks }, parent);\n                    return api_1.context.with(api_1.trace.setSpan(parent, span), () => {\n                        return original.apply(this, args)\n                            .then(value => {\n                            span.end();\n                            return value;\n                        })\n                            .catch(err => {\n                            span.recordException(err);\n                            span.setStatus({\n                                code: api_1.SpanStatusCode.ERROR,\n                                message: err.message,\n                            });\n                            span.end();\n                            throw err;\n                        });\n                    });\n                };\n            });\n            return inst;\n        }\n        PatchedDataloader.prototype = prototype;\n        return PatchedDataloader;\n    }\n    _patchLoad(proto) {\n        if ((0, instrumentation_1.isWrapped)(proto.load)) {\n            this._unwrap(proto, 'load');\n        }\n        this._wrap(proto, 'load', this._getPatchedLoad.bind(this));\n    }\n    _getPatchedLoad(original) {\n        const instrumentation = this;\n        return function patchedLoad(...args) {\n            if (!instrumentation.shouldCreateSpans()) {\n                return original.call(this, ...args);\n            }\n            const parent = api_1.context.active();\n            const span = instrumentation.tracer.startSpan(instrumentation.getSpanName(this, 'load'), { kind: api_1.SpanKind.CLIENT }, parent);\n            return api_1.context.with(api_1.trace.setSpan(parent, span), () => {\n                const result = original\n                    .call(this, ...args)\n                    .then(value => {\n                    span.end();\n                    return value;\n                })\n                    .catch(err => {\n                    span.recordException(err);\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: err.message,\n                    });\n                    span.end();\n                    throw err;\n                });\n                const loader = this;\n                if (loader._batch) {\n                    if (!loader._batch.spanLinks) {\n                        loader._batch.spanLinks = [];\n                    }\n                    loader._batch.spanLinks.push({ context: span.spanContext() });\n                }\n                return result;\n            });\n        };\n    }\n    _patchLoadMany(proto) {\n        if ((0, instrumentation_1.isWrapped)(proto.loadMany)) {\n            this._unwrap(proto, 'loadMany');\n        }\n        this._wrap(proto, 'loadMany', this._getPatchedLoadMany.bind(this));\n    }\n    _getPatchedLoadMany(original) {\n        const instrumentation = this;\n        return function patchedLoadMany(...args) {\n            if (!instrumentation.shouldCreateSpans()) {\n                return original.call(this, ...args);\n            }\n            const parent = api_1.context.active();\n            const span = instrumentation.tracer.startSpan(instrumentation.getSpanName(this, 'loadMany'), { kind: api_1.SpanKind.CLIENT }, parent);\n            return api_1.context.with(api_1.trace.setSpan(parent, span), () => {\n                // .loadMany never rejects, as errors from internal .load\n                // calls are caught by dataloader lib\n                return original.call(this, ...args).then(value => {\n                    span.end();\n                    return value;\n                });\n            });\n        };\n    }\n}\nexports.DataloaderInstrumentation = DataloaderInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/types.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/types.js ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfYTVhMDY1ZjU0YjU0NTMzNDVmZmEzN2VjMGM3ZjU3ZmMvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1kYXRhbG9hZGVyL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hNWEwNjVmNTRiNTQ1MzM0NWZmYTM3ZWMwYzdmNTdmY1xcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWRhdGFsb2FkZXJcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/version.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/version.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.16.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-dataloader';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/index.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/index.js ***!
  \***********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/instrumentation.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/instrumentation.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/instrumentation.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DataloaderInstrumentation = void 0;\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/version.js\");\nconst MODULE_NAME = 'dataloader';\nclass DataloaderInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition(MODULE_NAME, ['>=2.0.0 <3'], dataloader => {\n                this._patchLoad(dataloader.prototype);\n                this._patchLoadMany(dataloader.prototype);\n                return this._getPatchedConstructor(dataloader);\n            }, dataloader => {\n                if ((0, instrumentation_1.isWrapped)(dataloader.prototype.load)) {\n                    this._unwrap(dataloader.prototype, 'load');\n                }\n                if ((0, instrumentation_1.isWrapped)(dataloader.prototype.loadMany)) {\n                    this._unwrap(dataloader.prototype, 'loadMany');\n                }\n            }),\n        ];\n    }\n    shouldCreateSpans() {\n        const config = this.getConfig();\n        const hasParentSpan = api_1.trace.getSpan(api_1.context.active()) !== undefined;\n        return hasParentSpan || !config.requireParentSpan;\n    }\n    getSpanName(dataloader, operation) {\n        const dataloaderName = dataloader.name;\n        if (dataloaderName === undefined || dataloaderName === null) {\n            return `${MODULE_NAME}.${operation}`;\n        }\n        return `${MODULE_NAME}.${operation} ${dataloaderName}`;\n    }\n    _getPatchedConstructor(constructor) {\n        const prototype = constructor.prototype;\n        const instrumentation = this;\n        function PatchedDataloader(...args) {\n            const inst = new constructor(...args);\n            if (!instrumentation.isEnabled()) {\n                return inst;\n            }\n            if ((0, instrumentation_1.isWrapped)(inst._batchLoadFn)) {\n                instrumentation._unwrap(inst, '_batchLoadFn');\n            }\n            instrumentation._wrap(inst, '_batchLoadFn', original => {\n                return function patchedBatchLoadFn(...args) {\n                    var _a;\n                    if (!instrumentation.isEnabled() ||\n                        !instrumentation.shouldCreateSpans()) {\n                        return original.call(this, ...args);\n                    }\n                    const parent = api_1.context.active();\n                    const span = instrumentation.tracer.startSpan(instrumentation.getSpanName(inst, 'batch'), { links: (_a = this._batch) === null || _a === void 0 ? void 0 : _a.spanLinks }, parent);\n                    return api_1.context.with(api_1.trace.setSpan(parent, span), () => {\n                        return original.apply(this, args)\n                            .then(value => {\n                            span.end();\n                            return value;\n                        })\n                            .catch(err => {\n                            span.recordException(err);\n                            span.setStatus({\n                                code: api_1.SpanStatusCode.ERROR,\n                                message: err.message,\n                            });\n                            span.end();\n                            throw err;\n                        });\n                    });\n                };\n            });\n            return inst;\n        }\n        PatchedDataloader.prototype = prototype;\n        return PatchedDataloader;\n    }\n    _patchLoad(proto) {\n        if ((0, instrumentation_1.isWrapped)(proto.load)) {\n            this._unwrap(proto, 'load');\n        }\n        this._wrap(proto, 'load', this._getPatchedLoad.bind(this));\n    }\n    _getPatchedLoad(original) {\n        const instrumentation = this;\n        return function patchedLoad(...args) {\n            if (!instrumentation.shouldCreateSpans()) {\n                return original.call(this, ...args);\n            }\n            const parent = api_1.context.active();\n            const span = instrumentation.tracer.startSpan(instrumentation.getSpanName(this, 'load'), { kind: api_1.SpanKind.CLIENT }, parent);\n            return api_1.context.with(api_1.trace.setSpan(parent, span), () => {\n                const result = original\n                    .call(this, ...args)\n                    .then(value => {\n                    span.end();\n                    return value;\n                })\n                    .catch(err => {\n                    span.recordException(err);\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: err.message,\n                    });\n                    span.end();\n                    throw err;\n                });\n                const loader = this;\n                if (loader._batch) {\n                    if (!loader._batch.spanLinks) {\n                        loader._batch.spanLinks = [];\n                    }\n                    loader._batch.spanLinks.push({ context: span.spanContext() });\n                }\n                return result;\n            });\n        };\n    }\n    _patchLoadMany(proto) {\n        if ((0, instrumentation_1.isWrapped)(proto.loadMany)) {\n            this._unwrap(proto, 'loadMany');\n        }\n        this._wrap(proto, 'loadMany', this._getPatchedLoadMany.bind(this));\n    }\n    _getPatchedLoadMany(original) {\n        const instrumentation = this;\n        return function patchedLoadMany(...args) {\n            if (!instrumentation.shouldCreateSpans()) {\n                return original.call(this, ...args);\n            }\n            const parent = api_1.context.active();\n            const span = instrumentation.tracer.startSpan(instrumentation.getSpanName(this, 'loadMany'), { kind: api_1.SpanKind.CLIENT }, parent);\n            return api_1.context.with(api_1.trace.setSpan(parent, span), () => {\n                // .loadMany never rejects, as errors from internal .load\n                // calls are caught by dataloader lib\n                return original.call(this, ...args).then(value => {\n                    span.end();\n                    return value;\n                });\n            });\n        };\n    }\n}\nexports.DataloaderInstrumentation = DataloaderInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/types.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/types.js ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hNWEwNjVmNTRiNTQ1MzM0NWZmYTM3ZWMwYzdmNTdmYy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWRhdGFsb2FkZXIvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2E1YTA2NWY1NGI1NDUzMzQ1ZmZhMzdlYzBjN2Y1N2ZjXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tZGF0YWxvYWRlclxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/version.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/version.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.16.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-dataloader';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/index.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/index.js ***!
  \***********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/instrumentation.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/instrumentation.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/instrumentation.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DataloaderInstrumentation = void 0;\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/version.js\");\nconst MODULE_NAME = 'dataloader';\nclass DataloaderInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition(MODULE_NAME, ['>=2.0.0 <3'], dataloader => {\n                this._patchLoad(dataloader.prototype);\n                this._patchLoadMany(dataloader.prototype);\n                return this._getPatchedConstructor(dataloader);\n            }, dataloader => {\n                if ((0, instrumentation_1.isWrapped)(dataloader.prototype.load)) {\n                    this._unwrap(dataloader.prototype, 'load');\n                }\n                if ((0, instrumentation_1.isWrapped)(dataloader.prototype.loadMany)) {\n                    this._unwrap(dataloader.prototype, 'loadMany');\n                }\n            }),\n        ];\n    }\n    shouldCreateSpans() {\n        const config = this.getConfig();\n        const hasParentSpan = api_1.trace.getSpan(api_1.context.active()) !== undefined;\n        return hasParentSpan || !config.requireParentSpan;\n    }\n    getSpanName(dataloader, operation) {\n        const dataloaderName = dataloader.name;\n        if (dataloaderName === undefined || dataloaderName === null) {\n            return `${MODULE_NAME}.${operation}`;\n        }\n        return `${MODULE_NAME}.${operation} ${dataloaderName}`;\n    }\n    _getPatchedConstructor(constructor) {\n        const prototype = constructor.prototype;\n        const instrumentation = this;\n        function PatchedDataloader(...args) {\n            const inst = new constructor(...args);\n            if (!instrumentation.isEnabled()) {\n                return inst;\n            }\n            if ((0, instrumentation_1.isWrapped)(inst._batchLoadFn)) {\n                instrumentation._unwrap(inst, '_batchLoadFn');\n            }\n            instrumentation._wrap(inst, '_batchLoadFn', original => {\n                return function patchedBatchLoadFn(...args) {\n                    var _a;\n                    if (!instrumentation.isEnabled() ||\n                        !instrumentation.shouldCreateSpans()) {\n                        return original.call(this, ...args);\n                    }\n                    const parent = api_1.context.active();\n                    const span = instrumentation.tracer.startSpan(instrumentation.getSpanName(inst, 'batch'), { links: (_a = this._batch) === null || _a === void 0 ? void 0 : _a.spanLinks }, parent);\n                    return api_1.context.with(api_1.trace.setSpan(parent, span), () => {\n                        return original.apply(this, args)\n                            .then(value => {\n                            span.end();\n                            return value;\n                        })\n                            .catch(err => {\n                            span.recordException(err);\n                            span.setStatus({\n                                code: api_1.SpanStatusCode.ERROR,\n                                message: err.message,\n                            });\n                            span.end();\n                            throw err;\n                        });\n                    });\n                };\n            });\n            return inst;\n        }\n        PatchedDataloader.prototype = prototype;\n        return PatchedDataloader;\n    }\n    _patchLoad(proto) {\n        if ((0, instrumentation_1.isWrapped)(proto.load)) {\n            this._unwrap(proto, 'load');\n        }\n        this._wrap(proto, 'load', this._getPatchedLoad.bind(this));\n    }\n    _getPatchedLoad(original) {\n        const instrumentation = this;\n        return function patchedLoad(...args) {\n            if (!instrumentation.shouldCreateSpans()) {\n                return original.call(this, ...args);\n            }\n            const parent = api_1.context.active();\n            const span = instrumentation.tracer.startSpan(instrumentation.getSpanName(this, 'load'), { kind: api_1.SpanKind.CLIENT }, parent);\n            return api_1.context.with(api_1.trace.setSpan(parent, span), () => {\n                const result = original\n                    .call(this, ...args)\n                    .then(value => {\n                    span.end();\n                    return value;\n                })\n                    .catch(err => {\n                    span.recordException(err);\n                    span.setStatus({\n                        code: api_1.SpanStatusCode.ERROR,\n                        message: err.message,\n                    });\n                    span.end();\n                    throw err;\n                });\n                const loader = this;\n                if (loader._batch) {\n                    if (!loader._batch.spanLinks) {\n                        loader._batch.spanLinks = [];\n                    }\n                    loader._batch.spanLinks.push({ context: span.spanContext() });\n                }\n                return result;\n            });\n        };\n    }\n    _patchLoadMany(proto) {\n        if ((0, instrumentation_1.isWrapped)(proto.loadMany)) {\n            this._unwrap(proto, 'loadMany');\n        }\n        this._wrap(proto, 'loadMany', this._getPatchedLoadMany.bind(this));\n    }\n    _getPatchedLoadMany(original) {\n        const instrumentation = this;\n        return function patchedLoadMany(...args) {\n            if (!instrumentation.shouldCreateSpans()) {\n                return original.call(this, ...args);\n            }\n            const parent = api_1.context.active();\n            const span = instrumentation.tracer.startSpan(instrumentation.getSpanName(this, 'loadMany'), { kind: api_1.SpanKind.CLIENT }, parent);\n            return api_1.context.with(api_1.trace.setSpan(parent, span), () => {\n                // .loadMany never rejects, as errors from internal .load\n                // calls are caught by dataloader lib\n                return original.call(this, ...args).then(value => {\n                    span.end();\n                    return value;\n                });\n            });\n        };\n    }\n}\nexports.DataloaderInstrumentation = DataloaderInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/types.js":
/*!***********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/types.js ***!
  \***********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9hNWEwNjVmNTRiNTQ1MzM0NWZmYTM3ZWMwYzdmNTdmYy9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWRhdGFsb2FkZXIvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2E1YTA2NWY1NGI1NDUzMzQ1ZmZhMzdlYzBjN2Y1N2ZjXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tZGF0YWxvYWRlclxcYnVpbGRcXHNyY1xcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/version.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/version.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.16.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-dataloader';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_a5a065f54b5453345ffa37ec0c7f57fc/node_modules/@opentelemetry/instrumentation-dataloader/build/src/version.js\n");

/***/ })

};
;