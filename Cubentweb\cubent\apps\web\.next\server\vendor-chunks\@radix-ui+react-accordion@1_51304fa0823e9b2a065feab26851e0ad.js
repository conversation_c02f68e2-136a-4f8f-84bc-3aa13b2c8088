"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-accordion@1_51304fa0823e9b2a065feab26851e0ad";
exports.ids = ["vendor-chunks/@radix-ui+react-accordion@1_51304fa0823e9b2a065feab26851e0ad"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-accordion@1_51304fa0823e9b2a065feab26851e0ad/node_modules/@radix-ui/react-accordion/dist/index.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-accordion@1_51304fa0823e9b2a065feab26851e0ad/node_modules/@radix-ui/react-accordion/dist/index.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Accordion: () => (/* binding */ Accordion),\n/* harmony export */   AccordionContent: () => (/* binding */ AccordionContent),\n/* harmony export */   AccordionHeader: () => (/* binding */ AccordionHeader),\n/* harmony export */   AccordionItem: () => (/* binding */ AccordionItem),\n/* harmony export */   AccordionTrigger: () => (/* binding */ AccordionTrigger),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Header: () => (/* binding */ Header),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Trigger: () => (/* binding */ Trigger2),\n/* harmony export */   createAccordionScope: () => (/* binding */ createAccordionScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-context@1.1_efc475efe2315f1e47666d242c3ea3f4/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-collection@_7918ae119f10c4289f30f285e519ea7e/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-compose-ref_3a8f72d8524cae11dbbe71796c2b6a49/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-control_038f968d6df614ae636f20523f1cb043/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-primitive@2_0f3a82528133a7d37b0572d0c112c6a5/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-collapsible */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-collapsible_cecf39ac1375c4a4148ed47a207c8f40/node_modules/@radix-ui/react-collapsible/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@19.1.5_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-direction@1_e32f95a12a0e23976853758865c76117/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Accordion,AccordionContent,AccordionHeader,AccordionItem,AccordionTrigger,Content,Header,Item,Root,Trigger,createAccordionScope auto */ // src/accordion.tsx\n\n\n\n\n\n\n\n\n\n\n\n\nvar ACCORDION_NAME = \"Accordion\";\nvar ACCORDION_KEYS = [\n    \"Home\",\n    \"End\",\n    \"ArrowDown\",\n    \"ArrowUp\",\n    \"ArrowLeft\",\n    \"ArrowRight\"\n];\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(ACCORDION_NAME);\nvar [createAccordionContext, createAccordionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(ACCORDION_NAME, [\n    createCollectionScope,\n    _radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.createCollapsibleScope\n]);\nvar useCollapsibleScope = (0,_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.createCollapsibleScope)();\nvar Accordion = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { type, ...accordionProps } = props;\n    const singleProps = accordionProps;\n    const multipleProps = accordionProps;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeAccordion,\n        children: type === \"multiple\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImplMultiple, {\n            ...multipleProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImplSingle, {\n            ...singleProps,\n            ref: forwardedRef\n        })\n    });\n});\nAccordion.displayName = ACCORDION_NAME;\nvar [AccordionValueProvider, useAccordionValueContext] = createAccordionContext(ACCORDION_NAME);\nvar [AccordionCollapsibleProvider, useAccordionCollapsibleContext] = createAccordionContext(ACCORDION_NAME, {\n    collapsible: false\n});\nvar AccordionImplSingle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value: valueProp, defaultValue, onValueChange = ()=>{}, collapsible = false, ...accordionSingleProps } = props;\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue ?? \"\",\n        onChange: onValueChange,\n        caller: ACCORDION_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionValueProvider, {\n        scope: props.__scopeAccordion,\n        value: react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n            \"AccordionImplSingle.useMemo\": ()=>value ? [\n                    value\n                ] : []\n        }[\"AccordionImplSingle.useMemo\"], [\n            value\n        ]),\n        onItemOpen: setValue,\n        onItemClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"AccordionImplSingle.useCallback\": ()=>collapsible && setValue(\"\")\n        }[\"AccordionImplSingle.useCallback\"], [\n            collapsible,\n            setValue\n        ]),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionCollapsibleProvider, {\n            scope: props.__scopeAccordion,\n            collapsible,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImpl, {\n                ...accordionSingleProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nvar AccordionImplMultiple = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value: valueProp, defaultValue, onValueChange = ()=>{}, ...accordionMultipleProps } = props;\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue ?? [],\n        onChange: onValueChange,\n        caller: ACCORDION_NAME\n    });\n    const handleItemOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"AccordionImplMultiple.useCallback[handleItemOpen]\": (itemValue)=>setValue({\n                \"AccordionImplMultiple.useCallback[handleItemOpen]\": (prevValue = [])=>[\n                        ...prevValue,\n                        itemValue\n                    ]\n            }[\"AccordionImplMultiple.useCallback[handleItemOpen]\"])\n    }[\"AccordionImplMultiple.useCallback[handleItemOpen]\"], [\n        setValue\n    ]);\n    const handleItemClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"AccordionImplMultiple.useCallback[handleItemClose]\": (itemValue)=>setValue({\n                \"AccordionImplMultiple.useCallback[handleItemClose]\": (prevValue = [])=>prevValue.filter({\n                        \"AccordionImplMultiple.useCallback[handleItemClose]\": (value2)=>value2 !== itemValue\n                    }[\"AccordionImplMultiple.useCallback[handleItemClose]\"])\n            }[\"AccordionImplMultiple.useCallback[handleItemClose]\"])\n    }[\"AccordionImplMultiple.useCallback[handleItemClose]\"], [\n        setValue\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionValueProvider, {\n        scope: props.__scopeAccordion,\n        value,\n        onItemOpen: handleItemOpen,\n        onItemClose: handleItemClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionCollapsibleProvider, {\n            scope: props.__scopeAccordion,\n            collapsible: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImpl, {\n                ...accordionMultipleProps,\n                ref: forwardedRef\n            })\n        })\n    });\n});\nvar [AccordionImplProvider, useAccordionContext] = createAccordionContext(ACCORDION_NAME);\nvar AccordionImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, disabled, dir, orientation = \"vertical\", ...accordionProps } = props;\n    const accordionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(accordionRef, forwardedRef);\n    const getItems = useCollection(__scopeAccordion);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__.useDirection)(dir);\n    const isDirectionLTR = direction === \"ltr\";\n    const handleKeyDown = (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onKeyDown, (event)=>{\n        if (!ACCORDION_KEYS.includes(event.key)) return;\n        const target = event.target;\n        const triggerCollection = getItems().filter((item)=>!item.ref.current?.disabled);\n        const triggerIndex = triggerCollection.findIndex((item)=>item.ref.current === target);\n        const triggerCount = triggerCollection.length;\n        if (triggerIndex === -1) return;\n        event.preventDefault();\n        let nextIndex = triggerIndex;\n        const homeIndex = 0;\n        const endIndex = triggerCount - 1;\n        const moveNext = ()=>{\n            nextIndex = triggerIndex + 1;\n            if (nextIndex > endIndex) {\n                nextIndex = homeIndex;\n            }\n        };\n        const movePrev = ()=>{\n            nextIndex = triggerIndex - 1;\n            if (nextIndex < homeIndex) {\n                nextIndex = endIndex;\n            }\n        };\n        switch(event.key){\n            case \"Home\":\n                nextIndex = homeIndex;\n                break;\n            case \"End\":\n                nextIndex = endIndex;\n                break;\n            case \"ArrowRight\":\n                if (orientation === \"horizontal\") {\n                    if (isDirectionLTR) {\n                        moveNext();\n                    } else {\n                        movePrev();\n                    }\n                }\n                break;\n            case \"ArrowDown\":\n                if (orientation === \"vertical\") {\n                    moveNext();\n                }\n                break;\n            case \"ArrowLeft\":\n                if (orientation === \"horizontal\") {\n                    if (isDirectionLTR) {\n                        movePrev();\n                    } else {\n                        moveNext();\n                    }\n                }\n                break;\n            case \"ArrowUp\":\n                if (orientation === \"vertical\") {\n                    movePrev();\n                }\n                break;\n        }\n        const clampedIndex = nextIndex % triggerCount;\n        triggerCollection[clampedIndex].ref.current?.focus();\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionImplProvider, {\n        scope: __scopeAccordion,\n        disabled,\n        direction: dir,\n        orientation,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n            scope: __scopeAccordion,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.div, {\n                ...accordionProps,\n                \"data-orientation\": orientation,\n                ref: composedRefs,\n                onKeyDown: disabled ? void 0 : handleKeyDown\n            })\n        })\n    });\n});\nvar ITEM_NAME = \"AccordionItem\";\nvar [AccordionItemProvider, useAccordionItemContext] = createAccordionContext(ITEM_NAME);\nvar AccordionItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, value, ...accordionItemProps } = props;\n    const accordionContext = useAccordionContext(ITEM_NAME, __scopeAccordion);\n    const valueContext = useAccordionValueContext(ITEM_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    const triggerId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const open = value && valueContext.value.includes(value) || false;\n    const disabled = accordionContext.disabled || props.disabled;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AccordionItemProvider, {\n        scope: __scopeAccordion,\n        open,\n        disabled,\n        triggerId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.Root, {\n            \"data-orientation\": accordionContext.orientation,\n            \"data-state\": getState(open),\n            ...collapsibleScope,\n            ...accordionItemProps,\n            ref: forwardedRef,\n            disabled,\n            open,\n            onOpenChange: (open2)=>{\n                if (open2) {\n                    valueContext.onItemOpen(value);\n                } else {\n                    valueContext.onItemClose(value);\n                }\n            }\n        })\n    });\n});\nAccordionItem.displayName = ITEM_NAME;\nvar HEADER_NAME = \"AccordionHeader\";\nvar AccordionHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, ...headerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(HEADER_NAME, __scopeAccordion);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_9__.Primitive.h3, {\n        \"data-orientation\": accordionContext.orientation,\n        \"data-state\": getState(itemContext.open),\n        \"data-disabled\": itemContext.disabled ? \"\" : void 0,\n        ...headerProps,\n        ref: forwardedRef\n    });\n});\nAccordionHeader.displayName = HEADER_NAME;\nvar TRIGGER_NAME = \"AccordionTrigger\";\nvar AccordionTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, ...triggerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleContext = useAccordionCollapsibleContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeAccordion,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.Trigger, {\n            \"aria-disabled\": itemContext.open && !collapsibleContext.collapsible || void 0,\n            \"data-orientation\": accordionContext.orientation,\n            id: itemContext.triggerId,\n            ...collapsibleScope,\n            ...triggerProps,\n            ref: forwardedRef\n        })\n    });\n});\nAccordionTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"AccordionContent\";\nvar AccordionContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAccordion, ...contentProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(CONTENT_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_collapsible__WEBPACK_IMPORTED_MODULE_4__.Content, {\n        role: \"region\",\n        \"aria-labelledby\": itemContext.triggerId,\n        \"data-orientation\": accordionContext.orientation,\n        ...collapsibleScope,\n        ...contentProps,\n        ref: forwardedRef,\n        style: {\n            [\"--radix-accordion-content-height\"]: \"var(--radix-collapsible-content-height)\",\n            [\"--radix-accordion-content-width\"]: \"var(--radix-collapsible-content-width)\",\n            ...props.style\n        }\n    });\n});\nAccordionContent.displayName = CONTENT_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar Root2 = Accordion;\nvar Item = AccordionItem;\nvar Header = AccordionHeader;\nvar Trigger2 = AccordionTrigger;\nvar Content2 = AccordionContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-accordion@1_51304fa0823e9b2a065feab26851e0ad/node_modules/@radix-ui/react-accordion/dist/index.mjs\n");

/***/ })

};
;