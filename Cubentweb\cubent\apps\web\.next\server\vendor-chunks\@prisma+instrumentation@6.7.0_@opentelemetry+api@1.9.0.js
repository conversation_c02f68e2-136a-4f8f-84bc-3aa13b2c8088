"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@prisma+instrumentation@6.7.0_@opentelemetry+api@1.9.0";
exports.ids = ["vendor-chunks/@prisma+instrumentation@6.7.0_@opentelemetry+api@1.9.0"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@prisma+instrumentation@6.7.0_@opentelemetry+api@1.9.0/node_modules/@prisma/instrumentation/dist/index.js":
/*!******************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@prisma+instrumentation@6.7.0_@opentelemetry+api@1.9.0/node_modules/@prisma/instrumentation/dist/index.js ***!
  \******************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar index_exports = {};\n__export(index_exports, {\n  PrismaInstrumentation: () => PrismaInstrumentation,\n  registerInstrumentations: () => import_instrumentation2.registerInstrumentations\n});\nmodule.exports = __toCommonJS(index_exports);\n\n// src/PrismaInstrumentation.ts\nvar import_api2 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nvar import_instrumentation = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n\n// src/ActiveTracingHelper.ts\nvar import_api = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nvar showAllTraces = process.env.PRISMA_SHOW_ALL_TRACES === \"true\";\nvar nonSampledTraceParent = `00-10-10-00`;\nfunction engineSpanKindToOtelSpanKind(engineSpanKind) {\n  switch (engineSpanKind) {\n    case \"client\":\n      return import_api.SpanKind.CLIENT;\n    case \"internal\":\n    default:\n      return import_api.SpanKind.INTERNAL;\n  }\n}\nvar ActiveTracingHelper = class {\n  traceMiddleware;\n  tracerProvider;\n  ignoreSpanTypes;\n  constructor({ traceMiddleware, tracerProvider, ignoreSpanTypes }) {\n    this.traceMiddleware = traceMiddleware;\n    this.tracerProvider = tracerProvider;\n    this.ignoreSpanTypes = ignoreSpanTypes;\n  }\n  isEnabled() {\n    return true;\n  }\n  getTraceParent(context) {\n    const span = import_api.trace.getSpanContext(context ?? import_api.context.active());\n    if (span) {\n      return `00-${span.traceId}-${span.spanId}-0${span.traceFlags}`;\n    }\n    return nonSampledTraceParent;\n  }\n  dispatchEngineSpans(spans) {\n    const tracer = this.tracerProvider.getTracer(\"prisma\");\n    const linkIds = /* @__PURE__ */ new Map();\n    const roots = spans.filter((span) => span.parentId === null);\n    for (const root of roots) {\n      dispatchEngineSpan(tracer, root, spans, linkIds, this.ignoreSpanTypes);\n    }\n  }\n  getActiveContext() {\n    return import_api.context.active();\n  }\n  runInChildSpan(options, callback) {\n    if (typeof options === \"string\") {\n      options = { name: options };\n    }\n    if (options.internal && !showAllTraces) {\n      return callback();\n    }\n    if (options.middleware && !this.traceMiddleware) {\n      return callback();\n    }\n    const tracer = this.tracerProvider.getTracer(\"prisma\");\n    const context = options.context ?? this.getActiveContext();\n    const name = `prisma:client:${options.name}`;\n    if (shouldIgnoreSpan(name, this.ignoreSpanTypes)) {\n      return callback();\n    }\n    if (options.active === false) {\n      const span = tracer.startSpan(name, options, context);\n      return endSpan(span, callback(span, context));\n    }\n    return tracer.startActiveSpan(name, options, (span) => endSpan(span, callback(span, context)));\n  }\n};\nfunction dispatchEngineSpan(tracer, engineSpan, allSpans, linkIds, ignoreSpanTypes) {\n  if (shouldIgnoreSpan(engineSpan.name, ignoreSpanTypes)) return;\n  const spanOptions = {\n    attributes: engineSpan.attributes,\n    kind: engineSpanKindToOtelSpanKind(engineSpan.kind),\n    startTime: engineSpan.startTime\n  };\n  tracer.startActiveSpan(engineSpan.name, spanOptions, (span) => {\n    linkIds.set(engineSpan.id, span.spanContext().spanId);\n    if (engineSpan.links) {\n      span.addLinks(\n        engineSpan.links.flatMap((link) => {\n          const linkedId = linkIds.get(link);\n          if (!linkedId) {\n            return [];\n          }\n          return {\n            context: {\n              spanId: linkedId,\n              traceId: span.spanContext().traceId,\n              traceFlags: span.spanContext().traceFlags\n            }\n          };\n        })\n      );\n    }\n    const children = allSpans.filter((s) => s.parentId === engineSpan.id);\n    for (const child of children) {\n      dispatchEngineSpan(tracer, child, allSpans, linkIds, ignoreSpanTypes);\n    }\n    span.end(engineSpan.endTime);\n  });\n}\nfunction endSpan(span, result) {\n  if (isPromiseLike(result)) {\n    return result.then(\n      (value) => {\n        span.end();\n        return value;\n      },\n      (reason) => {\n        span.end();\n        throw reason;\n      }\n    );\n  }\n  span.end();\n  return result;\n}\nfunction isPromiseLike(value) {\n  return value != null && typeof value[\"then\"] === \"function\";\n}\nfunction shouldIgnoreSpan(spanName, ignoreSpanTypes) {\n  return ignoreSpanTypes.some(\n    (pattern) => typeof pattern === \"string\" ? pattern === spanName : pattern.test(spanName)\n  );\n}\n\n// package.json\nvar package_default = {\n  name: \"@prisma/instrumentation\",\n  version: \"6.7.0\",\n  description: \"OpenTelemetry compliant instrumentation for Prisma Client\",\n  main: \"dist/index.js\",\n  module: \"dist/index.mjs\",\n  types: \"dist/index.d.ts\",\n  exports: {\n    \".\": {\n      require: {\n        types: \"./dist/index.d.ts\",\n        default: \"./dist/index.js\"\n      },\n      import: {\n        types: \"./dist/index.d.ts\",\n        default: \"./dist/index.mjs\"\n      }\n    }\n  },\n  license: \"Apache-2.0\",\n  homepage: \"https://www.prisma.io\",\n  repository: {\n    type: \"git\",\n    url: \"https://github.com/prisma/prisma.git\",\n    directory: \"packages/instrumentation\"\n  },\n  bugs: \"https://github.com/prisma/prisma/issues\",\n  devDependencies: {\n    \"@prisma/internals\": \"workspace:*\",\n    \"@swc/core\": \"1.11.5\",\n    \"@types/jest\": \"29.5.14\",\n    \"@types/node\": \"18.19.76\",\n    \"@opentelemetry/api\": \"1.9.0\",\n    jest: \"29.7.0\",\n    \"jest-junit\": \"16.0.0\",\n    typescript: \"5.4.5\"\n  },\n  dependencies: {\n    \"@opentelemetry/instrumentation\": \"^0.52.0 || ^0.53.0 || ^0.54.0 || ^0.55.0 || ^0.56.0 || ^0.57.0\"\n  },\n  peerDependencies: {\n    \"@opentelemetry/api\": \"^1.8\"\n  },\n  files: [\n    \"dist\"\n  ],\n  keywords: [\n    \"prisma\",\n    \"instrumentation\",\n    \"opentelemetry\",\n    \"otel\"\n  ],\n  scripts: {\n    dev: \"DEV=true tsx helpers/build.ts\",\n    build: \"tsx helpers/build.ts\",\n    prepublishOnly: \"pnpm run build\",\n    test: \"jest\"\n  },\n  sideEffects: false\n};\n\n// src/constants.ts\nvar VERSION = package_default.version;\nvar majorVersion = VERSION.split(\".\")[0];\nvar GLOBAL_INSTRUMENTATION_ACCESSOR_KEY = \"PRISMA_INSTRUMENTATION\";\nvar GLOBAL_VERSIONED_INSTRUMENTATION_ACCESSOR_KEY = `V${majorVersion}_PRISMA_INSTRUMENTATION`;\nvar NAME = package_default.name;\nvar MODULE_NAME = \"@prisma/client\";\n\n// src/PrismaInstrumentation.ts\nvar PrismaInstrumentation = class extends import_instrumentation.InstrumentationBase {\n  tracerProvider;\n  constructor(config = {}) {\n    super(NAME, VERSION, config);\n  }\n  setTracerProvider(tracerProvider) {\n    this.tracerProvider = tracerProvider;\n  }\n  init() {\n    const module2 = new import_instrumentation.InstrumentationNodeModuleDefinition(MODULE_NAME, [VERSION]);\n    return [module2];\n  }\n  enable() {\n    const config = this._config;\n    const globalValue = {\n      helper: new ActiveTracingHelper({\n        traceMiddleware: config.middleware ?? false,\n        tracerProvider: this.tracerProvider ?? import_api2.trace.getTracerProvider(),\n        ignoreSpanTypes: config.ignoreSpanTypes ?? []\n      })\n    };\n    global[GLOBAL_INSTRUMENTATION_ACCESSOR_KEY] = globalValue;\n    global[GLOBAL_VERSIONED_INSTRUMENTATION_ACCESSOR_KEY] = globalValue;\n  }\n  disable() {\n    delete global[GLOBAL_INSTRUMENTATION_ACCESSOR_KEY];\n    delete global[GLOBAL_VERSIONED_INSTRUMENTATION_ACCESSOR_KEY];\n  }\n  isEnabled() {\n    return Boolean(global[GLOBAL_VERSIONED_INSTRUMENTATION_ACCESSOR_KEY]);\n  }\n};\n\n// src/index.ts\nvar import_instrumentation2 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@prisma+instrumentation@6.7.0_@opentelemetry+api@1.9.0/node_modules/@prisma/instrumentation/dist/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@prisma+instrumentation@6.7.0_@opentelemetry+api@1.9.0/node_modules/@prisma/instrumentation/dist/index.js":
/*!******************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@prisma+instrumentation@6.7.0_@opentelemetry+api@1.9.0/node_modules/@prisma/instrumentation/dist/index.js ***!
  \******************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar index_exports = {};\n__export(index_exports, {\n  PrismaInstrumentation: () => PrismaInstrumentation,\n  registerInstrumentations: () => import_instrumentation2.registerInstrumentations\n});\nmodule.exports = __toCommonJS(index_exports);\n\n// src/PrismaInstrumentation.ts\nvar import_api2 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nvar import_instrumentation = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n\n// src/ActiveTracingHelper.ts\nvar import_api = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nvar showAllTraces = process.env.PRISMA_SHOW_ALL_TRACES === \"true\";\nvar nonSampledTraceParent = `00-10-10-00`;\nfunction engineSpanKindToOtelSpanKind(engineSpanKind) {\n  switch (engineSpanKind) {\n    case \"client\":\n      return import_api.SpanKind.CLIENT;\n    case \"internal\":\n    default:\n      return import_api.SpanKind.INTERNAL;\n  }\n}\nvar ActiveTracingHelper = class {\n  traceMiddleware;\n  tracerProvider;\n  ignoreSpanTypes;\n  constructor({ traceMiddleware, tracerProvider, ignoreSpanTypes }) {\n    this.traceMiddleware = traceMiddleware;\n    this.tracerProvider = tracerProvider;\n    this.ignoreSpanTypes = ignoreSpanTypes;\n  }\n  isEnabled() {\n    return true;\n  }\n  getTraceParent(context) {\n    const span = import_api.trace.getSpanContext(context ?? import_api.context.active());\n    if (span) {\n      return `00-${span.traceId}-${span.spanId}-0${span.traceFlags}`;\n    }\n    return nonSampledTraceParent;\n  }\n  dispatchEngineSpans(spans) {\n    const tracer = this.tracerProvider.getTracer(\"prisma\");\n    const linkIds = /* @__PURE__ */ new Map();\n    const roots = spans.filter((span) => span.parentId === null);\n    for (const root of roots) {\n      dispatchEngineSpan(tracer, root, spans, linkIds, this.ignoreSpanTypes);\n    }\n  }\n  getActiveContext() {\n    return import_api.context.active();\n  }\n  runInChildSpan(options, callback) {\n    if (typeof options === \"string\") {\n      options = { name: options };\n    }\n    if (options.internal && !showAllTraces) {\n      return callback();\n    }\n    if (options.middleware && !this.traceMiddleware) {\n      return callback();\n    }\n    const tracer = this.tracerProvider.getTracer(\"prisma\");\n    const context = options.context ?? this.getActiveContext();\n    const name = `prisma:client:${options.name}`;\n    if (shouldIgnoreSpan(name, this.ignoreSpanTypes)) {\n      return callback();\n    }\n    if (options.active === false) {\n      const span = tracer.startSpan(name, options, context);\n      return endSpan(span, callback(span, context));\n    }\n    return tracer.startActiveSpan(name, options, (span) => endSpan(span, callback(span, context)));\n  }\n};\nfunction dispatchEngineSpan(tracer, engineSpan, allSpans, linkIds, ignoreSpanTypes) {\n  if (shouldIgnoreSpan(engineSpan.name, ignoreSpanTypes)) return;\n  const spanOptions = {\n    attributes: engineSpan.attributes,\n    kind: engineSpanKindToOtelSpanKind(engineSpan.kind),\n    startTime: engineSpan.startTime\n  };\n  tracer.startActiveSpan(engineSpan.name, spanOptions, (span) => {\n    linkIds.set(engineSpan.id, span.spanContext().spanId);\n    if (engineSpan.links) {\n      span.addLinks(\n        engineSpan.links.flatMap((link) => {\n          const linkedId = linkIds.get(link);\n          if (!linkedId) {\n            return [];\n          }\n          return {\n            context: {\n              spanId: linkedId,\n              traceId: span.spanContext().traceId,\n              traceFlags: span.spanContext().traceFlags\n            }\n          };\n        })\n      );\n    }\n    const children = allSpans.filter((s) => s.parentId === engineSpan.id);\n    for (const child of children) {\n      dispatchEngineSpan(tracer, child, allSpans, linkIds, ignoreSpanTypes);\n    }\n    span.end(engineSpan.endTime);\n  });\n}\nfunction endSpan(span, result) {\n  if (isPromiseLike(result)) {\n    return result.then(\n      (value) => {\n        span.end();\n        return value;\n      },\n      (reason) => {\n        span.end();\n        throw reason;\n      }\n    );\n  }\n  span.end();\n  return result;\n}\nfunction isPromiseLike(value) {\n  return value != null && typeof value[\"then\"] === \"function\";\n}\nfunction shouldIgnoreSpan(spanName, ignoreSpanTypes) {\n  return ignoreSpanTypes.some(\n    (pattern) => typeof pattern === \"string\" ? pattern === spanName : pattern.test(spanName)\n  );\n}\n\n// package.json\nvar package_default = {\n  name: \"@prisma/instrumentation\",\n  version: \"6.7.0\",\n  description: \"OpenTelemetry compliant instrumentation for Prisma Client\",\n  main: \"dist/index.js\",\n  module: \"dist/index.mjs\",\n  types: \"dist/index.d.ts\",\n  exports: {\n    \".\": {\n      require: {\n        types: \"./dist/index.d.ts\",\n        default: \"./dist/index.js\"\n      },\n      import: {\n        types: \"./dist/index.d.ts\",\n        default: \"./dist/index.mjs\"\n      }\n    }\n  },\n  license: \"Apache-2.0\",\n  homepage: \"https://www.prisma.io\",\n  repository: {\n    type: \"git\",\n    url: \"https://github.com/prisma/prisma.git\",\n    directory: \"packages/instrumentation\"\n  },\n  bugs: \"https://github.com/prisma/prisma/issues\",\n  devDependencies: {\n    \"@prisma/internals\": \"workspace:*\",\n    \"@swc/core\": \"1.11.5\",\n    \"@types/jest\": \"29.5.14\",\n    \"@types/node\": \"18.19.76\",\n    \"@opentelemetry/api\": \"1.9.0\",\n    jest: \"29.7.0\",\n    \"jest-junit\": \"16.0.0\",\n    typescript: \"5.4.5\"\n  },\n  dependencies: {\n    \"@opentelemetry/instrumentation\": \"^0.52.0 || ^0.53.0 || ^0.54.0 || ^0.55.0 || ^0.56.0 || ^0.57.0\"\n  },\n  peerDependencies: {\n    \"@opentelemetry/api\": \"^1.8\"\n  },\n  files: [\n    \"dist\"\n  ],\n  keywords: [\n    \"prisma\",\n    \"instrumentation\",\n    \"opentelemetry\",\n    \"otel\"\n  ],\n  scripts: {\n    dev: \"DEV=true tsx helpers/build.ts\",\n    build: \"tsx helpers/build.ts\",\n    prepublishOnly: \"pnpm run build\",\n    test: \"jest\"\n  },\n  sideEffects: false\n};\n\n// src/constants.ts\nvar VERSION = package_default.version;\nvar majorVersion = VERSION.split(\".\")[0];\nvar GLOBAL_INSTRUMENTATION_ACCESSOR_KEY = \"PRISMA_INSTRUMENTATION\";\nvar GLOBAL_VERSIONED_INSTRUMENTATION_ACCESSOR_KEY = `V${majorVersion}_PRISMA_INSTRUMENTATION`;\nvar NAME = package_default.name;\nvar MODULE_NAME = \"@prisma/client\";\n\n// src/PrismaInstrumentation.ts\nvar PrismaInstrumentation = class extends import_instrumentation.InstrumentationBase {\n  tracerProvider;\n  constructor(config = {}) {\n    super(NAME, VERSION, config);\n  }\n  setTracerProvider(tracerProvider) {\n    this.tracerProvider = tracerProvider;\n  }\n  init() {\n    const module2 = new import_instrumentation.InstrumentationNodeModuleDefinition(MODULE_NAME, [VERSION]);\n    return [module2];\n  }\n  enable() {\n    const config = this._config;\n    const globalValue = {\n      helper: new ActiveTracingHelper({\n        traceMiddleware: config.middleware ?? false,\n        tracerProvider: this.tracerProvider ?? import_api2.trace.getTracerProvider(),\n        ignoreSpanTypes: config.ignoreSpanTypes ?? []\n      })\n    };\n    global[GLOBAL_INSTRUMENTATION_ACCESSOR_KEY] = globalValue;\n    global[GLOBAL_VERSIONED_INSTRUMENTATION_ACCESSOR_KEY] = globalValue;\n  }\n  disable() {\n    delete global[GLOBAL_INSTRUMENTATION_ACCESSOR_KEY];\n    delete global[GLOBAL_VERSIONED_INSTRUMENTATION_ACCESSOR_KEY];\n  }\n  isEnabled() {\n    return Boolean(global[GLOBAL_VERSIONED_INSTRUMENTATION_ACCESSOR_KEY]);\n  }\n};\n\n// src/index.ts\nvar import_instrumentation2 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@prisma+instrumentation@6.7.0_@opentelemetry+api@1.9.0/node_modules/@prisma/instrumentation/dist/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@prisma+instrumentation@6.7.0_@opentelemetry+api@1.9.0/node_modules/@prisma/instrumentation/dist/index.js":
/*!******************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@prisma+instrumentation@6.7.0_@opentelemetry+api@1.9.0/node_modules/@prisma/instrumentation/dist/index.js ***!
  \******************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar index_exports = {};\n__export(index_exports, {\n  PrismaInstrumentation: () => PrismaInstrumentation,\n  registerInstrumentations: () => import_instrumentation2.registerInstrumentations\n});\nmodule.exports = __toCommonJS(index_exports);\n\n// src/PrismaInstrumentation.ts\nvar import_api2 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nvar import_instrumentation = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n\n// src/ActiveTracingHelper.ts\nvar import_api = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nvar showAllTraces = process.env.PRISMA_SHOW_ALL_TRACES === \"true\";\nvar nonSampledTraceParent = `00-10-10-00`;\nfunction engineSpanKindToOtelSpanKind(engineSpanKind) {\n  switch (engineSpanKind) {\n    case \"client\":\n      return import_api.SpanKind.CLIENT;\n    case \"internal\":\n    default:\n      return import_api.SpanKind.INTERNAL;\n  }\n}\nvar ActiveTracingHelper = class {\n  traceMiddleware;\n  tracerProvider;\n  ignoreSpanTypes;\n  constructor({ traceMiddleware, tracerProvider, ignoreSpanTypes }) {\n    this.traceMiddleware = traceMiddleware;\n    this.tracerProvider = tracerProvider;\n    this.ignoreSpanTypes = ignoreSpanTypes;\n  }\n  isEnabled() {\n    return true;\n  }\n  getTraceParent(context) {\n    const span = import_api.trace.getSpanContext(context ?? import_api.context.active());\n    if (span) {\n      return `00-${span.traceId}-${span.spanId}-0${span.traceFlags}`;\n    }\n    return nonSampledTraceParent;\n  }\n  dispatchEngineSpans(spans) {\n    const tracer = this.tracerProvider.getTracer(\"prisma\");\n    const linkIds = /* @__PURE__ */ new Map();\n    const roots = spans.filter((span) => span.parentId === null);\n    for (const root of roots) {\n      dispatchEngineSpan(tracer, root, spans, linkIds, this.ignoreSpanTypes);\n    }\n  }\n  getActiveContext() {\n    return import_api.context.active();\n  }\n  runInChildSpan(options, callback) {\n    if (typeof options === \"string\") {\n      options = { name: options };\n    }\n    if (options.internal && !showAllTraces) {\n      return callback();\n    }\n    if (options.middleware && !this.traceMiddleware) {\n      return callback();\n    }\n    const tracer = this.tracerProvider.getTracer(\"prisma\");\n    const context = options.context ?? this.getActiveContext();\n    const name = `prisma:client:${options.name}`;\n    if (shouldIgnoreSpan(name, this.ignoreSpanTypes)) {\n      return callback();\n    }\n    if (options.active === false) {\n      const span = tracer.startSpan(name, options, context);\n      return endSpan(span, callback(span, context));\n    }\n    return tracer.startActiveSpan(name, options, (span) => endSpan(span, callback(span, context)));\n  }\n};\nfunction dispatchEngineSpan(tracer, engineSpan, allSpans, linkIds, ignoreSpanTypes) {\n  if (shouldIgnoreSpan(engineSpan.name, ignoreSpanTypes)) return;\n  const spanOptions = {\n    attributes: engineSpan.attributes,\n    kind: engineSpanKindToOtelSpanKind(engineSpan.kind),\n    startTime: engineSpan.startTime\n  };\n  tracer.startActiveSpan(engineSpan.name, spanOptions, (span) => {\n    linkIds.set(engineSpan.id, span.spanContext().spanId);\n    if (engineSpan.links) {\n      span.addLinks(\n        engineSpan.links.flatMap((link) => {\n          const linkedId = linkIds.get(link);\n          if (!linkedId) {\n            return [];\n          }\n          return {\n            context: {\n              spanId: linkedId,\n              traceId: span.spanContext().traceId,\n              traceFlags: span.spanContext().traceFlags\n            }\n          };\n        })\n      );\n    }\n    const children = allSpans.filter((s) => s.parentId === engineSpan.id);\n    for (const child of children) {\n      dispatchEngineSpan(tracer, child, allSpans, linkIds, ignoreSpanTypes);\n    }\n    span.end(engineSpan.endTime);\n  });\n}\nfunction endSpan(span, result) {\n  if (isPromiseLike(result)) {\n    return result.then(\n      (value) => {\n        span.end();\n        return value;\n      },\n      (reason) => {\n        span.end();\n        throw reason;\n      }\n    );\n  }\n  span.end();\n  return result;\n}\nfunction isPromiseLike(value) {\n  return value != null && typeof value[\"then\"] === \"function\";\n}\nfunction shouldIgnoreSpan(spanName, ignoreSpanTypes) {\n  return ignoreSpanTypes.some(\n    (pattern) => typeof pattern === \"string\" ? pattern === spanName : pattern.test(spanName)\n  );\n}\n\n// package.json\nvar package_default = {\n  name: \"@prisma/instrumentation\",\n  version: \"6.7.0\",\n  description: \"OpenTelemetry compliant instrumentation for Prisma Client\",\n  main: \"dist/index.js\",\n  module: \"dist/index.mjs\",\n  types: \"dist/index.d.ts\",\n  exports: {\n    \".\": {\n      require: {\n        types: \"./dist/index.d.ts\",\n        default: \"./dist/index.js\"\n      },\n      import: {\n        types: \"./dist/index.d.ts\",\n        default: \"./dist/index.mjs\"\n      }\n    }\n  },\n  license: \"Apache-2.0\",\n  homepage: \"https://www.prisma.io\",\n  repository: {\n    type: \"git\",\n    url: \"https://github.com/prisma/prisma.git\",\n    directory: \"packages/instrumentation\"\n  },\n  bugs: \"https://github.com/prisma/prisma/issues\",\n  devDependencies: {\n    \"@prisma/internals\": \"workspace:*\",\n    \"@swc/core\": \"1.11.5\",\n    \"@types/jest\": \"29.5.14\",\n    \"@types/node\": \"18.19.76\",\n    \"@opentelemetry/api\": \"1.9.0\",\n    jest: \"29.7.0\",\n    \"jest-junit\": \"16.0.0\",\n    typescript: \"5.4.5\"\n  },\n  dependencies: {\n    \"@opentelemetry/instrumentation\": \"^0.52.0 || ^0.53.0 || ^0.54.0 || ^0.55.0 || ^0.56.0 || ^0.57.0\"\n  },\n  peerDependencies: {\n    \"@opentelemetry/api\": \"^1.8\"\n  },\n  files: [\n    \"dist\"\n  ],\n  keywords: [\n    \"prisma\",\n    \"instrumentation\",\n    \"opentelemetry\",\n    \"otel\"\n  ],\n  scripts: {\n    dev: \"DEV=true tsx helpers/build.ts\",\n    build: \"tsx helpers/build.ts\",\n    prepublishOnly: \"pnpm run build\",\n    test: \"jest\"\n  },\n  sideEffects: false\n};\n\n// src/constants.ts\nvar VERSION = package_default.version;\nvar majorVersion = VERSION.split(\".\")[0];\nvar GLOBAL_INSTRUMENTATION_ACCESSOR_KEY = \"PRISMA_INSTRUMENTATION\";\nvar GLOBAL_VERSIONED_INSTRUMENTATION_ACCESSOR_KEY = `V${majorVersion}_PRISMA_INSTRUMENTATION`;\nvar NAME = package_default.name;\nvar MODULE_NAME = \"@prisma/client\";\n\n// src/PrismaInstrumentation.ts\nvar PrismaInstrumentation = class extends import_instrumentation.InstrumentationBase {\n  tracerProvider;\n  constructor(config = {}) {\n    super(NAME, VERSION, config);\n  }\n  setTracerProvider(tracerProvider) {\n    this.tracerProvider = tracerProvider;\n  }\n  init() {\n    const module2 = new import_instrumentation.InstrumentationNodeModuleDefinition(MODULE_NAME, [VERSION]);\n    return [module2];\n  }\n  enable() {\n    const config = this._config;\n    const globalValue = {\n      helper: new ActiveTracingHelper({\n        traceMiddleware: config.middleware ?? false,\n        tracerProvider: this.tracerProvider ?? import_api2.trace.getTracerProvider(),\n        ignoreSpanTypes: config.ignoreSpanTypes ?? []\n      })\n    };\n    global[GLOBAL_INSTRUMENTATION_ACCESSOR_KEY] = globalValue;\n    global[GLOBAL_VERSIONED_INSTRUMENTATION_ACCESSOR_KEY] = globalValue;\n  }\n  disable() {\n    delete global[GLOBAL_INSTRUMENTATION_ACCESSOR_KEY];\n    delete global[GLOBAL_VERSIONED_INSTRUMENTATION_ACCESSOR_KEY];\n  }\n  isEnabled() {\n    return Boolean(global[GLOBAL_VERSIONED_INSTRUMENTATION_ACCESSOR_KEY]);\n  }\n};\n\n// src/index.ts\nvar import_instrumentation2 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@prisma+instrumentation@6.7.0_@opentelemetry+api@1.9.0/node_modules/@prisma/instrumentation/dist/index.js\n");

/***/ })

};
;