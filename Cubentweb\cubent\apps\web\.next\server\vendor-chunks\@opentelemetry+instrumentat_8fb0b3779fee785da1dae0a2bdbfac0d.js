"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MySQL2Instrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst sql_common_1 = __webpack_require__(/*! @opentelemetry/sql-common */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js\");\nconst supportedVersions = ['>=1.4.2 <4'];\nclass MySQL2Instrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        let format;\n        function setFormatFunction(moduleExports) {\n            if (!format && moduleExports.format) {\n                format = moduleExports.format;\n            }\n        }\n        const patch = (ConnectionPrototype) => {\n            if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.query)) {\n                this._unwrap(ConnectionPrototype, 'query');\n            }\n            this._wrap(ConnectionPrototype, 'query', this._patchQuery(format, false));\n            if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.execute)) {\n                this._unwrap(ConnectionPrototype, 'execute');\n            }\n            this._wrap(ConnectionPrototype, 'execute', this._patchQuery(format, true));\n        };\n        const unpatch = (ConnectionPrototype) => {\n            this._unwrap(ConnectionPrototype, 'query');\n            this._unwrap(ConnectionPrototype, 'execute');\n        };\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mysql2', supportedVersions, (moduleExports) => {\n                setFormatFunction(moduleExports);\n                return moduleExports;\n            }, () => { }, [\n                new instrumentation_1.InstrumentationNodeModuleFile('mysql2/promise.js', supportedVersions, (moduleExports) => {\n                    setFormatFunction(moduleExports);\n                    return moduleExports;\n                }, () => { }),\n                new instrumentation_1.InstrumentationNodeModuleFile('mysql2/lib/connection.js', supportedVersions, (moduleExports) => {\n                    const ConnectionPrototype = (0, utils_1.getConnectionPrototypeToInstrument)(moduleExports);\n                    patch(ConnectionPrototype);\n                    return moduleExports;\n                }, (moduleExports) => {\n                    if (moduleExports === undefined)\n                        return;\n                    const ConnectionPrototype = (0, utils_1.getConnectionPrototypeToInstrument)(moduleExports);\n                    unpatch(ConnectionPrototype);\n                }),\n            ]),\n        ];\n    }\n    _patchQuery(format, isPrepared) {\n        return (originalQuery) => {\n            const thisPlugin = this;\n            return function query(query, _valuesOrCallback, _callback) {\n                let values;\n                if (Array.isArray(_valuesOrCallback)) {\n                    values = _valuesOrCallback;\n                }\n                else if (arguments[2]) {\n                    values = [_valuesOrCallback];\n                }\n                const span = thisPlugin.tracer.startSpan((0, utils_1.getSpanName)(query), {\n                    kind: api.SpanKind.CLIENT,\n                    attributes: Object.assign(Object.assign(Object.assign({}, MySQL2Instrumentation.COMMON_ATTRIBUTES), (0, utils_1.getConnectionAttributes)(this.config)), { [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: (0, utils_1.getDbStatement)(query, format, values) }),\n                });\n                if (!isPrepared &&\n                    thisPlugin.getConfig().addSqlCommenterCommentToQueries) {\n                    arguments[0] = query =\n                        typeof query === 'string'\n                            ? (0, sql_common_1.addSqlCommenterComment)(span, query)\n                            : Object.assign(query, {\n                                sql: (0, sql_common_1.addSqlCommenterComment)(span, query.sql),\n                            });\n                }\n                const endSpan = (0, utils_1.once)((err, results) => {\n                    if (err) {\n                        span.setStatus({\n                            code: api.SpanStatusCode.ERROR,\n                            message: err.message,\n                        });\n                    }\n                    else {\n                        const { responseHook } = thisPlugin.getConfig();\n                        if (typeof responseHook === 'function') {\n                            (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                                responseHook(span, {\n                                    queryResults: results,\n                                });\n                            }, err => {\n                                if (err) {\n                                    thisPlugin._diag.warn('Failed executing responseHook', err);\n                                }\n                            }, true);\n                        }\n                    }\n                    span.end();\n                });\n                if (arguments.length === 1) {\n                    if (typeof query.onResult === 'function') {\n                        thisPlugin._wrap(query, 'onResult', thisPlugin._patchCallbackQuery(endSpan));\n                    }\n                    const streamableQuery = originalQuery.apply(this, arguments);\n                    // `end` in mysql behaves similarly to `result` in mysql2.\n                    streamableQuery\n                        .once('error', err => {\n                        endSpan(err);\n                    })\n                        .once('result', results => {\n                        endSpan(undefined, results);\n                    });\n                    return streamableQuery;\n                }\n                if (typeof arguments[1] === 'function') {\n                    thisPlugin._wrap(arguments, 1, thisPlugin._patchCallbackQuery(endSpan));\n                }\n                else if (typeof arguments[2] === 'function') {\n                    thisPlugin._wrap(arguments, 2, thisPlugin._patchCallbackQuery(endSpan));\n                }\n                return originalQuery.apply(this, arguments);\n            };\n        };\n    }\n    _patchCallbackQuery(endSpan) {\n        return (originalCallback) => {\n            return function (err, results, fields) {\n                endSpan(err, results);\n                return originalCallback(...arguments);\n            };\n        };\n    }\n}\nexports.MySQL2Instrumentation = MySQL2Instrumentation;\nMySQL2Instrumentation.COMMON_ATTRIBUTES = {\n    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MYSQL,\n};\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfOGZiMGIzNzc5ZmVlNzg1ZGExZGFlMGEyYmRiZmFjMGQvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1teXNxbDIvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzhmYjBiMzc3OWZlZTc4NWRhMWRhZTBhMmJkYmZhYzBkXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tbXlzcWwyXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getConnectionPrototypeToInstrument = exports.once = exports.getSpanName = exports.getDbStatement = exports.getConnectionAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\n/**\n * Get an Attributes map from a mysql connection config object\n *\n * @param config ConnectionConfig\n */\nfunction getConnectionAttributes(config) {\n    const { host, port, database, user } = getConfig(config);\n    const portNumber = parseInt(port, 10);\n    if (!isNaN(portNumber)) {\n        return {\n            [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n            [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: portNumber,\n            [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n            [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n            [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n        };\n    }\n    return {\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n        [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n    };\n}\nexports.getConnectionAttributes = getConnectionAttributes;\nfunction getConfig(config) {\n    const { host, port, database, user } = (config && config.connectionConfig) || config || {};\n    return { host, port, database, user };\n}\nfunction getJDBCString(host, port, database) {\n    let jdbcString = `jdbc:mysql://${host || 'localhost'}`;\n    if (typeof port === 'number') {\n        jdbcString += `:${port}`;\n    }\n    if (typeof database === 'string') {\n        jdbcString += `/${database}`;\n    }\n    return jdbcString;\n}\n/**\n * Conjures up the value for the db.statement attribute by formatting a SQL query.\n *\n * @returns the database statement being executed.\n */\nfunction getDbStatement(query, format, values) {\n    if (!format) {\n        return typeof query === 'string' ? query : query.sql;\n    }\n    if (typeof query === 'string') {\n        return values ? format(query, values) : query;\n    }\n    else {\n        // According to https://github.com/mysqljs/mysql#performing-queries\n        // The values argument will override the values in the option object.\n        return values || query.values\n            ? format(query.sql, values || query.values)\n            : query.sql;\n    }\n}\nexports.getDbStatement = getDbStatement;\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns SQL statement without variable arguments or SQL verb\n */\nfunction getSpanName(query) {\n    const rawQuery = typeof query === 'object' ? query.sql : query;\n    // Extract the SQL verb\n    const firstSpace = rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.indexOf(' ');\n    if (typeof firstSpace === 'number' && firstSpace !== -1) {\n        return rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.substring(0, firstSpace);\n    }\n    return rawQuery;\n}\nexports.getSpanName = getSpanName;\nconst once = (fn) => {\n    let called = false;\n    return (...args) => {\n        if (called)\n            return;\n        called = true;\n        return fn(...args);\n    };\n};\nexports.once = once;\nfunction getConnectionPrototypeToInstrument(connection) {\n    const connectionPrototype = connection.prototype;\n    const basePrototype = Object.getPrototypeOf(connectionPrototype);\n    // mysql2@3.11.5 included a refactoring, where most code was moved out of the `Connection` class and into a shared base\n    // so we need to instrument that instead, see https://github.com/sidorares/node-mysql2/pull/3081\n    // This checks if the functions we're instrumenting are there on the base - we cannot use the presence of a base\n    // prototype since EventEmitter is the base for mysql2@<=3.11.4\n    if (typeof (basePrototype === null || basePrototype === void 0 ? void 0 : basePrototype.query) === 'function' &&\n        typeof (basePrototype === null || basePrototype === void 0 ? void 0 : basePrototype.execute) === 'function') {\n        return basePrototype;\n    }\n    // otherwise instrument the connection directly.\n    return connectionPrototype;\n}\nexports.getConnectionPrototypeToInstrument = getConnectionPrototypeToInstrument;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.45.2';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mysql2';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MySQL2Instrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst sql_common_1 = __webpack_require__(/*! @opentelemetry/sql-common */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js\");\nconst supportedVersions = ['>=1.4.2 <4'];\nclass MySQL2Instrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        let format;\n        function setFormatFunction(moduleExports) {\n            if (!format && moduleExports.format) {\n                format = moduleExports.format;\n            }\n        }\n        const patch = (ConnectionPrototype) => {\n            if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.query)) {\n                this._unwrap(ConnectionPrototype, 'query');\n            }\n            this._wrap(ConnectionPrototype, 'query', this._patchQuery(format, false));\n            if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.execute)) {\n                this._unwrap(ConnectionPrototype, 'execute');\n            }\n            this._wrap(ConnectionPrototype, 'execute', this._patchQuery(format, true));\n        };\n        const unpatch = (ConnectionPrototype) => {\n            this._unwrap(ConnectionPrototype, 'query');\n            this._unwrap(ConnectionPrototype, 'execute');\n        };\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mysql2', supportedVersions, (moduleExports) => {\n                setFormatFunction(moduleExports);\n                return moduleExports;\n            }, () => { }, [\n                new instrumentation_1.InstrumentationNodeModuleFile('mysql2/promise.js', supportedVersions, (moduleExports) => {\n                    setFormatFunction(moduleExports);\n                    return moduleExports;\n                }, () => { }),\n                new instrumentation_1.InstrumentationNodeModuleFile('mysql2/lib/connection.js', supportedVersions, (moduleExports) => {\n                    const ConnectionPrototype = (0, utils_1.getConnectionPrototypeToInstrument)(moduleExports);\n                    patch(ConnectionPrototype);\n                    return moduleExports;\n                }, (moduleExports) => {\n                    if (moduleExports === undefined)\n                        return;\n                    const ConnectionPrototype = (0, utils_1.getConnectionPrototypeToInstrument)(moduleExports);\n                    unpatch(ConnectionPrototype);\n                }),\n            ]),\n        ];\n    }\n    _patchQuery(format, isPrepared) {\n        return (originalQuery) => {\n            const thisPlugin = this;\n            return function query(query, _valuesOrCallback, _callback) {\n                let values;\n                if (Array.isArray(_valuesOrCallback)) {\n                    values = _valuesOrCallback;\n                }\n                else if (arguments[2]) {\n                    values = [_valuesOrCallback];\n                }\n                const span = thisPlugin.tracer.startSpan((0, utils_1.getSpanName)(query), {\n                    kind: api.SpanKind.CLIENT,\n                    attributes: Object.assign(Object.assign(Object.assign({}, MySQL2Instrumentation.COMMON_ATTRIBUTES), (0, utils_1.getConnectionAttributes)(this.config)), { [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: (0, utils_1.getDbStatement)(query, format, values) }),\n                });\n                if (!isPrepared &&\n                    thisPlugin.getConfig().addSqlCommenterCommentToQueries) {\n                    arguments[0] = query =\n                        typeof query === 'string'\n                            ? (0, sql_common_1.addSqlCommenterComment)(span, query)\n                            : Object.assign(query, {\n                                sql: (0, sql_common_1.addSqlCommenterComment)(span, query.sql),\n                            });\n                }\n                const endSpan = (0, utils_1.once)((err, results) => {\n                    if (err) {\n                        span.setStatus({\n                            code: api.SpanStatusCode.ERROR,\n                            message: err.message,\n                        });\n                    }\n                    else {\n                        const { responseHook } = thisPlugin.getConfig();\n                        if (typeof responseHook === 'function') {\n                            (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                                responseHook(span, {\n                                    queryResults: results,\n                                });\n                            }, err => {\n                                if (err) {\n                                    thisPlugin._diag.warn('Failed executing responseHook', err);\n                                }\n                            }, true);\n                        }\n                    }\n                    span.end();\n                });\n                if (arguments.length === 1) {\n                    if (typeof query.onResult === 'function') {\n                        thisPlugin._wrap(query, 'onResult', thisPlugin._patchCallbackQuery(endSpan));\n                    }\n                    const streamableQuery = originalQuery.apply(this, arguments);\n                    // `end` in mysql behaves similarly to `result` in mysql2.\n                    streamableQuery\n                        .once('error', err => {\n                        endSpan(err);\n                    })\n                        .once('result', results => {\n                        endSpan(undefined, results);\n                    });\n                    return streamableQuery;\n                }\n                if (typeof arguments[1] === 'function') {\n                    thisPlugin._wrap(arguments, 1, thisPlugin._patchCallbackQuery(endSpan));\n                }\n                else if (typeof arguments[2] === 'function') {\n                    thisPlugin._wrap(arguments, 2, thisPlugin._patchCallbackQuery(endSpan));\n                }\n                return originalQuery.apply(this, arguments);\n            };\n        };\n    }\n    _patchCallbackQuery(endSpan) {\n        return (originalCallback) => {\n            return function (err, results, fields) {\n                endSpan(err, results);\n                return originalCallback(...arguments);\n            };\n        };\n    }\n}\nexports.MySQL2Instrumentation = MySQL2Instrumentation;\nMySQL2Instrumentation.COMMON_ATTRIBUTES = {\n    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MYSQL,\n};\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF84ZmIwYjM3NzlmZWU3ODVkYTFkYWUwYTJiZGJmYWMwZC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLW15c3FsMi9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfOGZiMGIzNzc5ZmVlNzg1ZGExZGFlMGEyYmRiZmFjMGRcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1teXNxbDJcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getConnectionPrototypeToInstrument = exports.once = exports.getSpanName = exports.getDbStatement = exports.getConnectionAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\n/**\n * Get an Attributes map from a mysql connection config object\n *\n * @param config ConnectionConfig\n */\nfunction getConnectionAttributes(config) {\n    const { host, port, database, user } = getConfig(config);\n    const portNumber = parseInt(port, 10);\n    if (!isNaN(portNumber)) {\n        return {\n            [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n            [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: portNumber,\n            [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n            [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n            [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n        };\n    }\n    return {\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n        [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n    };\n}\nexports.getConnectionAttributes = getConnectionAttributes;\nfunction getConfig(config) {\n    const { host, port, database, user } = (config && config.connectionConfig) || config || {};\n    return { host, port, database, user };\n}\nfunction getJDBCString(host, port, database) {\n    let jdbcString = `jdbc:mysql://${host || 'localhost'}`;\n    if (typeof port === 'number') {\n        jdbcString += `:${port}`;\n    }\n    if (typeof database === 'string') {\n        jdbcString += `/${database}`;\n    }\n    return jdbcString;\n}\n/**\n * Conjures up the value for the db.statement attribute by formatting a SQL query.\n *\n * @returns the database statement being executed.\n */\nfunction getDbStatement(query, format, values) {\n    if (!format) {\n        return typeof query === 'string' ? query : query.sql;\n    }\n    if (typeof query === 'string') {\n        return values ? format(query, values) : query;\n    }\n    else {\n        // According to https://github.com/mysqljs/mysql#performing-queries\n        // The values argument will override the values in the option object.\n        return values || query.values\n            ? format(query.sql, values || query.values)\n            : query.sql;\n    }\n}\nexports.getDbStatement = getDbStatement;\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns SQL statement without variable arguments or SQL verb\n */\nfunction getSpanName(query) {\n    const rawQuery = typeof query === 'object' ? query.sql : query;\n    // Extract the SQL verb\n    const firstSpace = rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.indexOf(' ');\n    if (typeof firstSpace === 'number' && firstSpace !== -1) {\n        return rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.substring(0, firstSpace);\n    }\n    return rawQuery;\n}\nexports.getSpanName = getSpanName;\nconst once = (fn) => {\n    let called = false;\n    return (...args) => {\n        if (called)\n            return;\n        called = true;\n        return fn(...args);\n    };\n};\nexports.once = once;\nfunction getConnectionPrototypeToInstrument(connection) {\n    const connectionPrototype = connection.prototype;\n    const basePrototype = Object.getPrototypeOf(connectionPrototype);\n    // mysql2@3.11.5 included a refactoring, where most code was moved out of the `Connection` class and into a shared base\n    // so we need to instrument that instead, see https://github.com/sidorares/node-mysql2/pull/3081\n    // This checks if the functions we're instrumenting are there on the base - we cannot use the presence of a base\n    // prototype since EventEmitter is the base for mysql2@<=3.11.4\n    if (typeof (basePrototype === null || basePrototype === void 0 ? void 0 : basePrototype.query) === 'function' &&\n        typeof (basePrototype === null || basePrototype === void 0 ? void 0 : basePrototype.execute) === 'function') {\n        return basePrototype;\n    }\n    // otherwise instrument the connection directly.\n    return connectionPrototype;\n}\nexports.getConnectionPrototypeToInstrument = getConnectionPrototypeToInstrument;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.45.2';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mysql2';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js":
/*!*****************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js ***!
  \*****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.MySQL2Instrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst sql_common_1 = __webpack_require__(/*! @opentelemetry/sql-common */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+sql-common@0.40.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sql-common/build/src/index.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js\");\nconst supportedVersions = ['>=1.4.2 <4'];\nclass MySQL2Instrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        let format;\n        function setFormatFunction(moduleExports) {\n            if (!format && moduleExports.format) {\n                format = moduleExports.format;\n            }\n        }\n        const patch = (ConnectionPrototype) => {\n            if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.query)) {\n                this._unwrap(ConnectionPrototype, 'query');\n            }\n            this._wrap(ConnectionPrototype, 'query', this._patchQuery(format, false));\n            if ((0, instrumentation_1.isWrapped)(ConnectionPrototype.execute)) {\n                this._unwrap(ConnectionPrototype, 'execute');\n            }\n            this._wrap(ConnectionPrototype, 'execute', this._patchQuery(format, true));\n        };\n        const unpatch = (ConnectionPrototype) => {\n            this._unwrap(ConnectionPrototype, 'query');\n            this._unwrap(ConnectionPrototype, 'execute');\n        };\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition('mysql2', supportedVersions, (moduleExports) => {\n                setFormatFunction(moduleExports);\n                return moduleExports;\n            }, () => { }, [\n                new instrumentation_1.InstrumentationNodeModuleFile('mysql2/promise.js', supportedVersions, (moduleExports) => {\n                    setFormatFunction(moduleExports);\n                    return moduleExports;\n                }, () => { }),\n                new instrumentation_1.InstrumentationNodeModuleFile('mysql2/lib/connection.js', supportedVersions, (moduleExports) => {\n                    const ConnectionPrototype = (0, utils_1.getConnectionPrototypeToInstrument)(moduleExports);\n                    patch(ConnectionPrototype);\n                    return moduleExports;\n                }, (moduleExports) => {\n                    if (moduleExports === undefined)\n                        return;\n                    const ConnectionPrototype = (0, utils_1.getConnectionPrototypeToInstrument)(moduleExports);\n                    unpatch(ConnectionPrototype);\n                }),\n            ]),\n        ];\n    }\n    _patchQuery(format, isPrepared) {\n        return (originalQuery) => {\n            const thisPlugin = this;\n            return function query(query, _valuesOrCallback, _callback) {\n                let values;\n                if (Array.isArray(_valuesOrCallback)) {\n                    values = _valuesOrCallback;\n                }\n                else if (arguments[2]) {\n                    values = [_valuesOrCallback];\n                }\n                const span = thisPlugin.tracer.startSpan((0, utils_1.getSpanName)(query), {\n                    kind: api.SpanKind.CLIENT,\n                    attributes: Object.assign(Object.assign(Object.assign({}, MySQL2Instrumentation.COMMON_ATTRIBUTES), (0, utils_1.getConnectionAttributes)(this.config)), { [semantic_conventions_1.SEMATTRS_DB_STATEMENT]: (0, utils_1.getDbStatement)(query, format, values) }),\n                });\n                if (!isPrepared &&\n                    thisPlugin.getConfig().addSqlCommenterCommentToQueries) {\n                    arguments[0] = query =\n                        typeof query === 'string'\n                            ? (0, sql_common_1.addSqlCommenterComment)(span, query)\n                            : Object.assign(query, {\n                                sql: (0, sql_common_1.addSqlCommenterComment)(span, query.sql),\n                            });\n                }\n                const endSpan = (0, utils_1.once)((err, results) => {\n                    if (err) {\n                        span.setStatus({\n                            code: api.SpanStatusCode.ERROR,\n                            message: err.message,\n                        });\n                    }\n                    else {\n                        const { responseHook } = thisPlugin.getConfig();\n                        if (typeof responseHook === 'function') {\n                            (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n                                responseHook(span, {\n                                    queryResults: results,\n                                });\n                            }, err => {\n                                if (err) {\n                                    thisPlugin._diag.warn('Failed executing responseHook', err);\n                                }\n                            }, true);\n                        }\n                    }\n                    span.end();\n                });\n                if (arguments.length === 1) {\n                    if (typeof query.onResult === 'function') {\n                        thisPlugin._wrap(query, 'onResult', thisPlugin._patchCallbackQuery(endSpan));\n                    }\n                    const streamableQuery = originalQuery.apply(this, arguments);\n                    // `end` in mysql behaves similarly to `result` in mysql2.\n                    streamableQuery\n                        .once('error', err => {\n                        endSpan(err);\n                    })\n                        .once('result', results => {\n                        endSpan(undefined, results);\n                    });\n                    return streamableQuery;\n                }\n                if (typeof arguments[1] === 'function') {\n                    thisPlugin._wrap(arguments, 1, thisPlugin._patchCallbackQuery(endSpan));\n                }\n                else if (typeof arguments[2] === 'function') {\n                    thisPlugin._wrap(arguments, 2, thisPlugin._patchCallbackQuery(endSpan));\n                }\n                return originalQuery.apply(this, arguments);\n            };\n        };\n    }\n    _patchCallbackQuery(endSpan) {\n        return (originalCallback) => {\n            return function (err, results, fields) {\n                endSpan(err, results);\n                return originalCallback(...arguments);\n            };\n        };\n    }\n}\nexports.MySQL2Instrumentation = MySQL2Instrumentation;\nMySQL2Instrumentation.COMMON_ATTRIBUTES = {\n    [semantic_conventions_1.SEMATTRS_DB_SYSTEM]: semantic_conventions_1.DBSYSTEMVALUES_MYSQL,\n};\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF84ZmIwYjM3NzlmZWU3ODVkYTFkYWUwYTJiZGJmYWMwZC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLW15c3FsMi9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfOGZiMGIzNzc5ZmVlNzg1ZGExZGFlMGEyYmRiZmFjMGRcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1teXNxbDJcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getConnectionPrototypeToInstrument = exports.once = exports.getSpanName = exports.getDbStatement = exports.getConnectionAttributes = void 0;\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\n/**\n * Get an Attributes map from a mysql connection config object\n *\n * @param config ConnectionConfig\n */\nfunction getConnectionAttributes(config) {\n    const { host, port, database, user } = getConfig(config);\n    const portNumber = parseInt(port, 10);\n    if (!isNaN(portNumber)) {\n        return {\n            [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n            [semantic_conventions_1.SEMATTRS_NET_PEER_PORT]: portNumber,\n            [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n            [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n            [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n        };\n    }\n    return {\n        [semantic_conventions_1.SEMATTRS_NET_PEER_NAME]: host,\n        [semantic_conventions_1.SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n        [semantic_conventions_1.SEMATTRS_DB_NAME]: database,\n        [semantic_conventions_1.SEMATTRS_DB_USER]: user,\n    };\n}\nexports.getConnectionAttributes = getConnectionAttributes;\nfunction getConfig(config) {\n    const { host, port, database, user } = (config && config.connectionConfig) || config || {};\n    return { host, port, database, user };\n}\nfunction getJDBCString(host, port, database) {\n    let jdbcString = `jdbc:mysql://${host || 'localhost'}`;\n    if (typeof port === 'number') {\n        jdbcString += `:${port}`;\n    }\n    if (typeof database === 'string') {\n        jdbcString += `/${database}`;\n    }\n    return jdbcString;\n}\n/**\n * Conjures up the value for the db.statement attribute by formatting a SQL query.\n *\n * @returns the database statement being executed.\n */\nfunction getDbStatement(query, format, values) {\n    if (!format) {\n        return typeof query === 'string' ? query : query.sql;\n    }\n    if (typeof query === 'string') {\n        return values ? format(query, values) : query;\n    }\n    else {\n        // According to https://github.com/mysqljs/mysql#performing-queries\n        // The values argument will override the values in the option object.\n        return values || query.values\n            ? format(query.sql, values || query.values)\n            : query.sql;\n    }\n}\nexports.getDbStatement = getDbStatement;\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns SQL statement without variable arguments or SQL verb\n */\nfunction getSpanName(query) {\n    const rawQuery = typeof query === 'object' ? query.sql : query;\n    // Extract the SQL verb\n    const firstSpace = rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.indexOf(' ');\n    if (typeof firstSpace === 'number' && firstSpace !== -1) {\n        return rawQuery === null || rawQuery === void 0 ? void 0 : rawQuery.substring(0, firstSpace);\n    }\n    return rawQuery;\n}\nexports.getSpanName = getSpanName;\nconst once = (fn) => {\n    let called = false;\n    return (...args) => {\n        if (called)\n            return;\n        called = true;\n        return fn(...args);\n    };\n};\nexports.once = once;\nfunction getConnectionPrototypeToInstrument(connection) {\n    const connectionPrototype = connection.prototype;\n    const basePrototype = Object.getPrototypeOf(connectionPrototype);\n    // mysql2@3.11.5 included a refactoring, where most code was moved out of the `Connection` class and into a shared base\n    // so we need to instrument that instead, see https://github.com/sidorares/node-mysql2/pull/3081\n    // This checks if the functions we're instrumenting are there on the base - we cannot use the presence of a base\n    // prototype since EventEmitter is the base for mysql2@<=3.11.4\n    if (typeof (basePrototype === null || basePrototype === void 0 ? void 0 : basePrototype.query) === 'function' &&\n        typeof (basePrototype === null || basePrototype === void 0 ? void 0 : basePrototype.execute) === 'function') {\n        return basePrototype;\n    }\n    // otherwise instrument the connection directly.\n    return connectionPrototype;\n}\nexports.getConnectionPrototypeToInstrument = getConnectionPrototypeToInstrument;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.45.2';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-mysql2';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_8fb0b3779fee785da1dae0a2bdbfac0d/node_modules/@opentelemetry/instrumentation-mysql2/build/src/version.js\n");

/***/ })

};
;