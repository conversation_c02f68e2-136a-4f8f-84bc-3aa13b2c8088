"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"KOA_TYPE\"] = \"koa.type\";\n    AttributeNames[\"KOA_NAME\"] = \"koa.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMjVmNzNkY2FhOGE5OGY4NzlkZmRkZjljYzg1N2I1YzUvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1rb2EvYnVpbGQvc3JjL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxvQ0FBb0MsZ0JBQWdCO0FBQ3ZGLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxhQUFhLG1CQUFPLENBQUMsME1BQW1CO0FBQ3hDLGFBQWEsbUJBQU8sQ0FBQyxzTEFBUztBQUM5QixhQUFhLG1CQUFPLENBQUMsb05BQXdCO0FBQzdDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzI1ZjczZGNhYThhOThmODc5ZGZkZGY5Y2M4NTdiNWM1XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24ta29hXFxidWlsZFxcc3JjXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG52YXIgX19jcmVhdGVCaW5kaW5nID0gKHRoaXMgJiYgdGhpcy5fX2NyZWF0ZUJpbmRpbmcpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgazIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfSk7XG59KSA6IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgb1trMl0gPSBtW2tdO1xufSkpO1xudmFyIF9fZXhwb3J0U3RhciA9ICh0aGlzICYmIHRoaXMuX19leHBvcnRTdGFyKSB8fCBmdW5jdGlvbihtLCBleHBvcnRzKSB7XG4gICAgZm9yICh2YXIgcCBpbiBtKSBpZiAocCAhPT0gXCJkZWZhdWx0XCIgJiYgIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChleHBvcnRzLCBwKSkgX19jcmVhdGVCaW5kaW5nKGV4cG9ydHMsIG0sIHApO1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9pbnN0cnVtZW50YXRpb25cIiksIGV4cG9ydHMpO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL3R5cGVzXCIpLCBleHBvcnRzKTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi9lbnVtcy9BdHRyaWJ1dGVOYW1lc1wiKSwgZXhwb3J0cyk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KoaInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst types_1 = __webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js\");\n/** Koa instrumentation for OpenTelemetry */\nclass KoaInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return new instrumentation_1.InstrumentationNodeModuleDefinition('koa', ['>=2.0.0 <3'], (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module'\n                ? module.default // ESM\n                : module; // CommonJS\n            if (moduleExports == null) {\n                return moduleExports;\n            }\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.use)) {\n                this._unwrap(moduleExports.prototype, 'use');\n            }\n            this._wrap(moduleExports.prototype, 'use', this._getKoaUsePatch.bind(this));\n            return module;\n        }, (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module'\n                ? module.default // ESM\n                : module; // CommonJS\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.use)) {\n                this._unwrap(moduleExports.prototype, 'use');\n            }\n        });\n    }\n    /**\n     * Patches the Koa.use function in order to instrument each original\n     * middleware layer which is introduced\n     * @param {KoaMiddleware} middleware - the original middleware function\n     */\n    _getKoaUsePatch(original) {\n        const plugin = this;\n        return function use(middlewareFunction) {\n            let patchedFunction;\n            if (middlewareFunction.router) {\n                patchedFunction = plugin._patchRouterDispatch(middlewareFunction);\n            }\n            else {\n                patchedFunction = plugin._patchLayer(middlewareFunction, false);\n            }\n            return original.apply(this, [patchedFunction]);\n        };\n    }\n    /**\n     * Patches the dispatch function used by @koa/router. This function\n     * goes through each routed middleware and adds instrumentation via a call\n     * to the @function _patchLayer function.\n     * @param {KoaMiddleware} dispatchLayer - the original dispatch function which dispatches\n     * routed middleware\n     */\n    _patchRouterDispatch(dispatchLayer) {\n        var _a;\n        api.diag.debug('Patching @koa/router dispatch');\n        const router = dispatchLayer.router;\n        const routesStack = (_a = router === null || router === void 0 ? void 0 : router.stack) !== null && _a !== void 0 ? _a : [];\n        for (const pathLayer of routesStack) {\n            const path = pathLayer.path;\n            const pathStack = pathLayer.stack;\n            for (let j = 0; j < pathStack.length; j++) {\n                const routedMiddleware = pathStack[j];\n                pathStack[j] = this._patchLayer(routedMiddleware, true, path);\n            }\n        }\n        return dispatchLayer;\n    }\n    /**\n     * Patches each individual @param middlewareLayer function in order to create the\n     * span and propagate context. It does not create spans when there is no parent span.\n     * @param {KoaMiddleware} middlewareLayer - the original middleware function.\n     * @param {boolean} isRouter - tracks whether the original middleware function\n     * was dispatched by the router originally\n     * @param {string?} layerPath - if present, provides additional data from the\n     * router about the routed path which the middleware is attached to\n     */\n    _patchLayer(middlewareLayer, isRouter, layerPath) {\n        const layerType = isRouter ? types_1.KoaLayerType.ROUTER : types_1.KoaLayerType.MIDDLEWARE;\n        // Skip patching layer if its ignored in the config\n        if (middlewareLayer[internal_types_1.kLayerPatched] === true ||\n            (0, utils_1.isLayerIgnored)(layerType, this.getConfig()))\n            return middlewareLayer;\n        if (middlewareLayer.constructor.name === 'GeneratorFunction' ||\n            middlewareLayer.constructor.name === 'AsyncGeneratorFunction') {\n            api.diag.debug('ignoring generator-based Koa middleware layer');\n            return middlewareLayer;\n        }\n        middlewareLayer[internal_types_1.kLayerPatched] = true;\n        api.diag.debug('patching Koa middleware layer');\n        return async (context, next) => {\n            const parent = api.trace.getSpan(api.context.active());\n            if (parent === undefined) {\n                return middlewareLayer(context, next);\n            }\n            const metadata = (0, utils_1.getMiddlewareMetadata)(context, middlewareLayer, isRouter, layerPath);\n            const span = this.tracer.startSpan(metadata.name, {\n                attributes: metadata.attributes,\n            });\n            const rpcMetadata = (0, core_1.getRPCMetadata)(api.context.active());\n            if ((rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP && context._matchedRoute) {\n                rpcMetadata.route = context._matchedRoute.toString();\n            }\n            const { requestHook } = this.getConfig();\n            if (requestHook) {\n                (0, instrumentation_1.safeExecuteInTheMiddle)(() => requestHook(span, {\n                    context,\n                    middlewareLayer,\n                    layerType,\n                }), e => {\n                    if (e) {\n                        api.diag.error('koa instrumentation: request hook failed', e);\n                    }\n                }, true);\n            }\n            const newContext = api.trace.setSpan(api.context.active(), span);\n            return api.context.with(newContext, async () => {\n                try {\n                    return await middlewareLayer(context, next);\n                }\n                catch (err) {\n                    span.recordException(err);\n                    throw err;\n                }\n                finally {\n                    span.end();\n                }\n            });\n        };\n    }\n}\nexports.KoaInstrumentation = KoaInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.kLayerPatched = void 0;\n/**\n * This symbol is used to mark a Koa layer as being already instrumented\n * since its possible to use a given layer multiple times (ex: middlewares)\n */\nexports.kLayerPatched = Symbol('koa-layer-patched');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMjVmNzNkY2FhOGE5OGY4NzlkZmRkZjljYzg1N2I1YzUvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1rb2EvYnVpbGQvc3JjL2ludGVybmFsLXR5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8yNWY3M2RjYWE4YTk4Zjg3OWRmZGRmOWNjODU3YjVjNVxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWtvYVxcYnVpbGRcXHNyY1xcaW50ZXJuYWwtdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmtMYXllclBhdGNoZWQgPSB2b2lkIDA7XG4vKipcbiAqIFRoaXMgc3ltYm9sIGlzIHVzZWQgdG8gbWFyayBhIEtvYSBsYXllciBhcyBiZWluZyBhbHJlYWR5IGluc3RydW1lbnRlZFxuICogc2luY2UgaXRzIHBvc3NpYmxlIHRvIHVzZSBhIGdpdmVuIGxheWVyIG11bHRpcGxlIHRpbWVzIChleDogbWlkZGxld2FyZXMpXG4gKi9cbmV4cG9ydHMua0xheWVyUGF0Y2hlZCA9IFN5bWJvbCgna29hLWxheWVyLXBhdGNoZWQnKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWludGVybmFsLXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KoaLayerType = void 0;\nvar KoaLayerType;\n(function (KoaLayerType) {\n    KoaLayerType[\"ROUTER\"] = \"router\";\n    KoaLayerType[\"MIDDLEWARE\"] = \"middleware\";\n})(KoaLayerType = exports.KoaLayerType || (exports.KoaLayerType = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMjVmNzNkY2FhOGE5OGY4NzlkZmRkZjljYzg1N2I1YzUvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1rb2EvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsMENBQTBDLG9CQUFvQixLQUFLO0FBQ3BFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzI1ZjczZGNhYThhOThmODc5ZGZkZGY5Y2M4NTdiNWM1XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24ta29hXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuS29hTGF5ZXJUeXBlID0gdm9pZCAwO1xudmFyIEtvYUxheWVyVHlwZTtcbihmdW5jdGlvbiAoS29hTGF5ZXJUeXBlKSB7XG4gICAgS29hTGF5ZXJUeXBlW1wiUk9VVEVSXCJdID0gXCJyb3V0ZXJcIjtcbiAgICBLb2FMYXllclR5cGVbXCJNSURETEVXQVJFXCJdID0gXCJtaWRkbGV3YXJlXCI7XG59KShLb2FMYXllclR5cGUgPSBleHBvcnRzLktvYUxheWVyVHlwZSB8fCAoZXhwb3J0cy5Lb2FMYXllclR5cGUgPSB7fSkpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isLayerIgnored = exports.getMiddlewareMetadata = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst types_1 = __webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst getMiddlewareMetadata = (context, layer, isRouter, layerPath) => {\n    var _a;\n    if (isRouter) {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.KOA_NAME]: layerPath === null || layerPath === void 0 ? void 0 : layerPath.toString(),\n                [AttributeNames_1.AttributeNames.KOA_TYPE]: types_1.KoaLayerType.ROUTER,\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: layerPath === null || layerPath === void 0 ? void 0 : layerPath.toString(),\n            },\n            name: context._matchedRouteName || `router - ${layerPath}`,\n        };\n    }\n    else {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.KOA_NAME]: (_a = layer.name) !== null && _a !== void 0 ? _a : 'middleware',\n                [AttributeNames_1.AttributeNames.KOA_TYPE]: types_1.KoaLayerType.MIDDLEWARE,\n            },\n            name: `middleware - ${layer.name}`,\n        };\n    }\n};\nexports.getMiddlewareMetadata = getMiddlewareMetadata;\n/**\n * Check whether the given request is ignored by configuration\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nconst isLayerIgnored = (type, config) => {\n    var _a;\n    return !!(Array.isArray(config === null || config === void 0 ? void 0 : config.ignoreLayersType) &&\n        ((_a = config === null || config === void 0 ? void 0 : config.ignoreLayersType) === null || _a === void 0 ? void 0 : _a.includes(type)));\n};\nexports.isLayerIgnored = isLayerIgnored;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-koa';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"KOA_TYPE\"] = \"koa.type\";\n    AttributeNames[\"KOA_NAME\"] = \"koa.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KoaInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst types_1 = __webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js\");\n/** Koa instrumentation for OpenTelemetry */\nclass KoaInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return new instrumentation_1.InstrumentationNodeModuleDefinition('koa', ['>=2.0.0 <3'], (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module'\n                ? module.default // ESM\n                : module; // CommonJS\n            if (moduleExports == null) {\n                return moduleExports;\n            }\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.use)) {\n                this._unwrap(moduleExports.prototype, 'use');\n            }\n            this._wrap(moduleExports.prototype, 'use', this._getKoaUsePatch.bind(this));\n            return module;\n        }, (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module'\n                ? module.default // ESM\n                : module; // CommonJS\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.use)) {\n                this._unwrap(moduleExports.prototype, 'use');\n            }\n        });\n    }\n    /**\n     * Patches the Koa.use function in order to instrument each original\n     * middleware layer which is introduced\n     * @param {KoaMiddleware} middleware - the original middleware function\n     */\n    _getKoaUsePatch(original) {\n        const plugin = this;\n        return function use(middlewareFunction) {\n            let patchedFunction;\n            if (middlewareFunction.router) {\n                patchedFunction = plugin._patchRouterDispatch(middlewareFunction);\n            }\n            else {\n                patchedFunction = plugin._patchLayer(middlewareFunction, false);\n            }\n            return original.apply(this, [patchedFunction]);\n        };\n    }\n    /**\n     * Patches the dispatch function used by @koa/router. This function\n     * goes through each routed middleware and adds instrumentation via a call\n     * to the @function _patchLayer function.\n     * @param {KoaMiddleware} dispatchLayer - the original dispatch function which dispatches\n     * routed middleware\n     */\n    _patchRouterDispatch(dispatchLayer) {\n        var _a;\n        api.diag.debug('Patching @koa/router dispatch');\n        const router = dispatchLayer.router;\n        const routesStack = (_a = router === null || router === void 0 ? void 0 : router.stack) !== null && _a !== void 0 ? _a : [];\n        for (const pathLayer of routesStack) {\n            const path = pathLayer.path;\n            const pathStack = pathLayer.stack;\n            for (let j = 0; j < pathStack.length; j++) {\n                const routedMiddleware = pathStack[j];\n                pathStack[j] = this._patchLayer(routedMiddleware, true, path);\n            }\n        }\n        return dispatchLayer;\n    }\n    /**\n     * Patches each individual @param middlewareLayer function in order to create the\n     * span and propagate context. It does not create spans when there is no parent span.\n     * @param {KoaMiddleware} middlewareLayer - the original middleware function.\n     * @param {boolean} isRouter - tracks whether the original middleware function\n     * was dispatched by the router originally\n     * @param {string?} layerPath - if present, provides additional data from the\n     * router about the routed path which the middleware is attached to\n     */\n    _patchLayer(middlewareLayer, isRouter, layerPath) {\n        const layerType = isRouter ? types_1.KoaLayerType.ROUTER : types_1.KoaLayerType.MIDDLEWARE;\n        // Skip patching layer if its ignored in the config\n        if (middlewareLayer[internal_types_1.kLayerPatched] === true ||\n            (0, utils_1.isLayerIgnored)(layerType, this.getConfig()))\n            return middlewareLayer;\n        if (middlewareLayer.constructor.name === 'GeneratorFunction' ||\n            middlewareLayer.constructor.name === 'AsyncGeneratorFunction') {\n            api.diag.debug('ignoring generator-based Koa middleware layer');\n            return middlewareLayer;\n        }\n        middlewareLayer[internal_types_1.kLayerPatched] = true;\n        api.diag.debug('patching Koa middleware layer');\n        return async (context, next) => {\n            const parent = api.trace.getSpan(api.context.active());\n            if (parent === undefined) {\n                return middlewareLayer(context, next);\n            }\n            const metadata = (0, utils_1.getMiddlewareMetadata)(context, middlewareLayer, isRouter, layerPath);\n            const span = this.tracer.startSpan(metadata.name, {\n                attributes: metadata.attributes,\n            });\n            const rpcMetadata = (0, core_1.getRPCMetadata)(api.context.active());\n            if ((rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP && context._matchedRoute) {\n                rpcMetadata.route = context._matchedRoute.toString();\n            }\n            const { requestHook } = this.getConfig();\n            if (requestHook) {\n                (0, instrumentation_1.safeExecuteInTheMiddle)(() => requestHook(span, {\n                    context,\n                    middlewareLayer,\n                    layerType,\n                }), e => {\n                    if (e) {\n                        api.diag.error('koa instrumentation: request hook failed', e);\n                    }\n                }, true);\n            }\n            const newContext = api.trace.setSpan(api.context.active(), span);\n            return api.context.with(newContext, async () => {\n                try {\n                    return await middlewareLayer(context, next);\n                }\n                catch (err) {\n                    span.recordException(err);\n                    throw err;\n                }\n                finally {\n                    span.end();\n                }\n            });\n        };\n    }\n}\nexports.KoaInstrumentation = KoaInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8yNWY3M2RjYWE4YTk4Zjg3OWRmZGRmOWNjODU3YjVjNS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWtvYS9idWlsZC9zcmMvaW5zdHJ1bWVudGF0aW9uLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDBCQUEwQjtBQUMxQixZQUFZLG1CQUFPLENBQUMsc0lBQW9CO0FBQ3hDLDBCQUEwQixtQkFBTyxDQUFDLGtNQUFnQztBQUNsRSxnQkFBZ0IsbUJBQU8sQ0FBQywrS0FBUztBQUNqQztBQUNBLGtCQUFrQixtQkFBTyxDQUFDLG1MQUFXO0FBQ3JDLGdCQUFnQixtQkFBTyxDQUFDLCtLQUFTO0FBQ2pDLGVBQWUsbUJBQU8sQ0FBQyxtS0FBcUI7QUFDNUMseUJBQXlCLG1CQUFPLENBQUMsaU1BQWtCO0FBQ25EO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGVBQWU7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxlQUFlO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLHNCQUFzQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGVBQWU7QUFDOUIsZUFBZSxTQUFTO0FBQ3hCO0FBQ0EsZUFBZSxTQUFTO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsY0FBYztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMjVmNzNkY2FhOGE5OGY4NzlkZmRkZjljYzg1N2I1YzVcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1rb2FcXGJ1aWxkXFxzcmNcXGluc3RydW1lbnRhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLktvYUluc3RydW1lbnRhdGlvbiA9IHZvaWQgMDtcbmNvbnN0IGFwaSA9IHJlcXVpcmUoXCJAb3BlbnRlbGVtZXRyeS9hcGlcIik7XG5jb25zdCBpbnN0cnVtZW50YXRpb25fMSA9IHJlcXVpcmUoXCJAb3BlbnRlbGVtZXRyeS9pbnN0cnVtZW50YXRpb25cIik7XG5jb25zdCB0eXBlc18xID0gcmVxdWlyZShcIi4vdHlwZXNcIik7XG4vKiogQGtuaXBpZ25vcmUgKi9cbmNvbnN0IHZlcnNpb25fMSA9IHJlcXVpcmUoXCIuL3ZlcnNpb25cIik7XG5jb25zdCB1dGlsc18xID0gcmVxdWlyZShcIi4vdXRpbHNcIik7XG5jb25zdCBjb3JlXzEgPSByZXF1aXJlKFwiQG9wZW50ZWxlbWV0cnkvY29yZVwiKTtcbmNvbnN0IGludGVybmFsX3R5cGVzXzEgPSByZXF1aXJlKFwiLi9pbnRlcm5hbC10eXBlc1wiKTtcbi8qKiBLb2EgaW5zdHJ1bWVudGF0aW9uIGZvciBPcGVuVGVsZW1ldHJ5ICovXG5jbGFzcyBLb2FJbnN0cnVtZW50YXRpb24gZXh0ZW5kcyBpbnN0cnVtZW50YXRpb25fMS5JbnN0cnVtZW50YXRpb25CYXNlIHtcbiAgICBjb25zdHJ1Y3Rvcihjb25maWcgPSB7fSkge1xuICAgICAgICBzdXBlcih2ZXJzaW9uXzEuUEFDS0FHRV9OQU1FLCB2ZXJzaW9uXzEuUEFDS0FHRV9WRVJTSU9OLCBjb25maWcpO1xuICAgIH1cbiAgICBpbml0KCkge1xuICAgICAgICByZXR1cm4gbmV3IGluc3RydW1lbnRhdGlvbl8xLkluc3RydW1lbnRhdGlvbk5vZGVNb2R1bGVEZWZpbml0aW9uKCdrb2EnLCBbJz49Mi4wLjAgPDMnXSwgKG1vZHVsZSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgbW9kdWxlRXhwb3J0cyA9IG1vZHVsZVtTeW1ib2wudG9TdHJpbmdUYWddID09PSAnTW9kdWxlJ1xuICAgICAgICAgICAgICAgID8gbW9kdWxlLmRlZmF1bHQgLy8gRVNNXG4gICAgICAgICAgICAgICAgOiBtb2R1bGU7IC8vIENvbW1vbkpTXG4gICAgICAgICAgICBpZiAobW9kdWxlRXhwb3J0cyA9PSBudWxsKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG1vZHVsZUV4cG9ydHM7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoKDAsIGluc3RydW1lbnRhdGlvbl8xLmlzV3JhcHBlZCkobW9kdWxlRXhwb3J0cy5wcm90b3R5cGUudXNlKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChtb2R1bGVFeHBvcnRzLnByb3RvdHlwZSwgJ3VzZScpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhpcy5fd3JhcChtb2R1bGVFeHBvcnRzLnByb3RvdHlwZSwgJ3VzZScsIHRoaXMuX2dldEtvYVVzZVBhdGNoLmJpbmQodGhpcykpO1xuICAgICAgICAgICAgcmV0dXJuIG1vZHVsZTtcbiAgICAgICAgfSwgKG1vZHVsZSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgbW9kdWxlRXhwb3J0cyA9IG1vZHVsZVtTeW1ib2wudG9TdHJpbmdUYWddID09PSAnTW9kdWxlJ1xuICAgICAgICAgICAgICAgID8gbW9kdWxlLmRlZmF1bHQgLy8gRVNNXG4gICAgICAgICAgICAgICAgOiBtb2R1bGU7IC8vIENvbW1vbkpTXG4gICAgICAgICAgICBpZiAoKDAsIGluc3RydW1lbnRhdGlvbl8xLmlzV3JhcHBlZCkobW9kdWxlRXhwb3J0cy5wcm90b3R5cGUudXNlKSkge1xuICAgICAgICAgICAgICAgIHRoaXMuX3Vud3JhcChtb2R1bGVFeHBvcnRzLnByb3RvdHlwZSwgJ3VzZScpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUGF0Y2hlcyB0aGUgS29hLnVzZSBmdW5jdGlvbiBpbiBvcmRlciB0byBpbnN0cnVtZW50IGVhY2ggb3JpZ2luYWxcbiAgICAgKiBtaWRkbGV3YXJlIGxheWVyIHdoaWNoIGlzIGludHJvZHVjZWRcbiAgICAgKiBAcGFyYW0ge0tvYU1pZGRsZXdhcmV9IG1pZGRsZXdhcmUgLSB0aGUgb3JpZ2luYWwgbWlkZGxld2FyZSBmdW5jdGlvblxuICAgICAqL1xuICAgIF9nZXRLb2FVc2VQYXRjaChvcmlnaW5hbCkge1xuICAgICAgICBjb25zdCBwbHVnaW4gPSB0aGlzO1xuICAgICAgICByZXR1cm4gZnVuY3Rpb24gdXNlKG1pZGRsZXdhcmVGdW5jdGlvbikge1xuICAgICAgICAgICAgbGV0IHBhdGNoZWRGdW5jdGlvbjtcbiAgICAgICAgICAgIGlmIChtaWRkbGV3YXJlRnVuY3Rpb24ucm91dGVyKSB7XG4gICAgICAgICAgICAgICAgcGF0Y2hlZEZ1bmN0aW9uID0gcGx1Z2luLl9wYXRjaFJvdXRlckRpc3BhdGNoKG1pZGRsZXdhcmVGdW5jdGlvbik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBwYXRjaGVkRnVuY3Rpb24gPSBwbHVnaW4uX3BhdGNoTGF5ZXIobWlkZGxld2FyZUZ1bmN0aW9uLCBmYWxzZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gb3JpZ2luYWwuYXBwbHkodGhpcywgW3BhdGNoZWRGdW5jdGlvbl0pO1xuICAgICAgICB9O1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBQYXRjaGVzIHRoZSBkaXNwYXRjaCBmdW5jdGlvbiB1c2VkIGJ5IEBrb2Evcm91dGVyLiBUaGlzIGZ1bmN0aW9uXG4gICAgICogZ29lcyB0aHJvdWdoIGVhY2ggcm91dGVkIG1pZGRsZXdhcmUgYW5kIGFkZHMgaW5zdHJ1bWVudGF0aW9uIHZpYSBhIGNhbGxcbiAgICAgKiB0byB0aGUgQGZ1bmN0aW9uIF9wYXRjaExheWVyIGZ1bmN0aW9uLlxuICAgICAqIEBwYXJhbSB7S29hTWlkZGxld2FyZX0gZGlzcGF0Y2hMYXllciAtIHRoZSBvcmlnaW5hbCBkaXNwYXRjaCBmdW5jdGlvbiB3aGljaCBkaXNwYXRjaGVzXG4gICAgICogcm91dGVkIG1pZGRsZXdhcmVcbiAgICAgKi9cbiAgICBfcGF0Y2hSb3V0ZXJEaXNwYXRjaChkaXNwYXRjaExheWVyKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgYXBpLmRpYWcuZGVidWcoJ1BhdGNoaW5nIEBrb2Evcm91dGVyIGRpc3BhdGNoJyk7XG4gICAgICAgIGNvbnN0IHJvdXRlciA9IGRpc3BhdGNoTGF5ZXIucm91dGVyO1xuICAgICAgICBjb25zdCByb3V0ZXNTdGFjayA9IChfYSA9IHJvdXRlciA9PT0gbnVsbCB8fCByb3V0ZXIgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHJvdXRlci5zdGFjaykgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogW107XG4gICAgICAgIGZvciAoY29uc3QgcGF0aExheWVyIG9mIHJvdXRlc1N0YWNrKSB7XG4gICAgICAgICAgICBjb25zdCBwYXRoID0gcGF0aExheWVyLnBhdGg7XG4gICAgICAgICAgICBjb25zdCBwYXRoU3RhY2sgPSBwYXRoTGF5ZXIuc3RhY2s7XG4gICAgICAgICAgICBmb3IgKGxldCBqID0gMDsgaiA8IHBhdGhTdGFjay5sZW5ndGg7IGorKykge1xuICAgICAgICAgICAgICAgIGNvbnN0IHJvdXRlZE1pZGRsZXdhcmUgPSBwYXRoU3RhY2tbal07XG4gICAgICAgICAgICAgICAgcGF0aFN0YWNrW2pdID0gdGhpcy5fcGF0Y2hMYXllcihyb3V0ZWRNaWRkbGV3YXJlLCB0cnVlLCBwYXRoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZGlzcGF0Y2hMYXllcjtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUGF0Y2hlcyBlYWNoIGluZGl2aWR1YWwgQHBhcmFtIG1pZGRsZXdhcmVMYXllciBmdW5jdGlvbiBpbiBvcmRlciB0byBjcmVhdGUgdGhlXG4gICAgICogc3BhbiBhbmQgcHJvcGFnYXRlIGNvbnRleHQuIEl0IGRvZXMgbm90IGNyZWF0ZSBzcGFucyB3aGVuIHRoZXJlIGlzIG5vIHBhcmVudCBzcGFuLlxuICAgICAqIEBwYXJhbSB7S29hTWlkZGxld2FyZX0gbWlkZGxld2FyZUxheWVyIC0gdGhlIG9yaWdpbmFsIG1pZGRsZXdhcmUgZnVuY3Rpb24uXG4gICAgICogQHBhcmFtIHtib29sZWFufSBpc1JvdXRlciAtIHRyYWNrcyB3aGV0aGVyIHRoZSBvcmlnaW5hbCBtaWRkbGV3YXJlIGZ1bmN0aW9uXG4gICAgICogd2FzIGRpc3BhdGNoZWQgYnkgdGhlIHJvdXRlciBvcmlnaW5hbGx5XG4gICAgICogQHBhcmFtIHtzdHJpbmc/fSBsYXllclBhdGggLSBpZiBwcmVzZW50LCBwcm92aWRlcyBhZGRpdGlvbmFsIGRhdGEgZnJvbSB0aGVcbiAgICAgKiByb3V0ZXIgYWJvdXQgdGhlIHJvdXRlZCBwYXRoIHdoaWNoIHRoZSBtaWRkbGV3YXJlIGlzIGF0dGFjaGVkIHRvXG4gICAgICovXG4gICAgX3BhdGNoTGF5ZXIobWlkZGxld2FyZUxheWVyLCBpc1JvdXRlciwgbGF5ZXJQYXRoKSB7XG4gICAgICAgIGNvbnN0IGxheWVyVHlwZSA9IGlzUm91dGVyID8gdHlwZXNfMS5Lb2FMYXllclR5cGUuUk9VVEVSIDogdHlwZXNfMS5Lb2FMYXllclR5cGUuTUlERExFV0FSRTtcbiAgICAgICAgLy8gU2tpcCBwYXRjaGluZyBsYXllciBpZiBpdHMgaWdub3JlZCBpbiB0aGUgY29uZmlnXG4gICAgICAgIGlmIChtaWRkbGV3YXJlTGF5ZXJbaW50ZXJuYWxfdHlwZXNfMS5rTGF5ZXJQYXRjaGVkXSA9PT0gdHJ1ZSB8fFxuICAgICAgICAgICAgKDAsIHV0aWxzXzEuaXNMYXllcklnbm9yZWQpKGxheWVyVHlwZSwgdGhpcy5nZXRDb25maWcoKSkpXG4gICAgICAgICAgICByZXR1cm4gbWlkZGxld2FyZUxheWVyO1xuICAgICAgICBpZiAobWlkZGxld2FyZUxheWVyLmNvbnN0cnVjdG9yLm5hbWUgPT09ICdHZW5lcmF0b3JGdW5jdGlvbicgfHxcbiAgICAgICAgICAgIG1pZGRsZXdhcmVMYXllci5jb25zdHJ1Y3Rvci5uYW1lID09PSAnQXN5bmNHZW5lcmF0b3JGdW5jdGlvbicpIHtcbiAgICAgICAgICAgIGFwaS5kaWFnLmRlYnVnKCdpZ25vcmluZyBnZW5lcmF0b3ItYmFzZWQgS29hIG1pZGRsZXdhcmUgbGF5ZXInKTtcbiAgICAgICAgICAgIHJldHVybiBtaWRkbGV3YXJlTGF5ZXI7XG4gICAgICAgIH1cbiAgICAgICAgbWlkZGxld2FyZUxheWVyW2ludGVybmFsX3R5cGVzXzEua0xheWVyUGF0Y2hlZF0gPSB0cnVlO1xuICAgICAgICBhcGkuZGlhZy5kZWJ1ZygncGF0Y2hpbmcgS29hIG1pZGRsZXdhcmUgbGF5ZXInKTtcbiAgICAgICAgcmV0dXJuIGFzeW5jIChjb250ZXh0LCBuZXh0KSA9PiB7XG4gICAgICAgICAgICBjb25zdCBwYXJlbnQgPSBhcGkudHJhY2UuZ2V0U3BhbihhcGkuY29udGV4dC5hY3RpdmUoKSk7XG4gICAgICAgICAgICBpZiAocGFyZW50ID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gbWlkZGxld2FyZUxheWVyKGNvbnRleHQsIG5leHQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgbWV0YWRhdGEgPSAoMCwgdXRpbHNfMS5nZXRNaWRkbGV3YXJlTWV0YWRhdGEpKGNvbnRleHQsIG1pZGRsZXdhcmVMYXllciwgaXNSb3V0ZXIsIGxheWVyUGF0aCk7XG4gICAgICAgICAgICBjb25zdCBzcGFuID0gdGhpcy50cmFjZXIuc3RhcnRTcGFuKG1ldGFkYXRhLm5hbWUsIHtcbiAgICAgICAgICAgICAgICBhdHRyaWJ1dGVzOiBtZXRhZGF0YS5hdHRyaWJ1dGVzLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBjb25zdCBycGNNZXRhZGF0YSA9ICgwLCBjb3JlXzEuZ2V0UlBDTWV0YWRhdGEpKGFwaS5jb250ZXh0LmFjdGl2ZSgpKTtcbiAgICAgICAgICAgIGlmICgocnBjTWV0YWRhdGEgPT09IG51bGwgfHwgcnBjTWV0YWRhdGEgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHJwY01ldGFkYXRhLnR5cGUpID09PSBjb3JlXzEuUlBDVHlwZS5IVFRQICYmIGNvbnRleHQuX21hdGNoZWRSb3V0ZSkge1xuICAgICAgICAgICAgICAgIHJwY01ldGFkYXRhLnJvdXRlID0gY29udGV4dC5fbWF0Y2hlZFJvdXRlLnRvU3RyaW5nKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCB7IHJlcXVlc3RIb29rIH0gPSB0aGlzLmdldENvbmZpZygpO1xuICAgICAgICAgICAgaWYgKHJlcXVlc3RIb29rKSB7XG4gICAgICAgICAgICAgICAgKDAsIGluc3RydW1lbnRhdGlvbl8xLnNhZmVFeGVjdXRlSW5UaGVNaWRkbGUpKCgpID0+IHJlcXVlc3RIb29rKHNwYW4sIHtcbiAgICAgICAgICAgICAgICAgICAgY29udGV4dCxcbiAgICAgICAgICAgICAgICAgICAgbWlkZGxld2FyZUxheWVyLFxuICAgICAgICAgICAgICAgICAgICBsYXllclR5cGUsXG4gICAgICAgICAgICAgICAgfSksIGUgPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAoZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgYXBpLmRpYWcuZXJyb3IoJ2tvYSBpbnN0cnVtZW50YXRpb246IHJlcXVlc3QgaG9vayBmYWlsZWQnLCBlKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0sIHRydWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgbmV3Q29udGV4dCA9IGFwaS50cmFjZS5zZXRTcGFuKGFwaS5jb250ZXh0LmFjdGl2ZSgpLCBzcGFuKTtcbiAgICAgICAgICAgIHJldHVybiBhcGkuY29udGV4dC53aXRoKG5ld0NvbnRleHQsIGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gYXdhaXQgbWlkZGxld2FyZUxheWVyKGNvbnRleHQsIG5leHQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4ucmVjb3JkRXhjZXB0aW9uKGVycik7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IGVycjtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZmluYWxseSB7XG4gICAgICAgICAgICAgICAgICAgIHNwYW4uZW5kKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgfVxufVxuZXhwb3J0cy5Lb2FJbnN0cnVtZW50YXRpb24gPSBLb2FJbnN0cnVtZW50YXRpb247XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbnN0cnVtZW50YXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.kLayerPatched = void 0;\n/**\n * This symbol is used to mark a Koa layer as being already instrumented\n * since its possible to use a given layer multiple times (ex: middlewares)\n */\nexports.kLayerPatched = Symbol('koa-layer-patched');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8yNWY3M2RjYWE4YTk4Zjg3OWRmZGRmOWNjODU3YjVjNS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWtvYS9idWlsZC9zcmMvaW50ZXJuYWwtdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzI1ZjczZGNhYThhOThmODc5ZGZkZGY5Y2M4NTdiNWM1XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24ta29hXFxidWlsZFxcc3JjXFxpbnRlcm5hbC10eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMua0xheWVyUGF0Y2hlZCA9IHZvaWQgMDtcbi8qKlxuICogVGhpcyBzeW1ib2wgaXMgdXNlZCB0byBtYXJrIGEgS29hIGxheWVyIGFzIGJlaW5nIGFscmVhZHkgaW5zdHJ1bWVudGVkXG4gKiBzaW5jZSBpdHMgcG9zc2libGUgdG8gdXNlIGEgZ2l2ZW4gbGF5ZXIgbXVsdGlwbGUgdGltZXMgKGV4OiBtaWRkbGV3YXJlcylcbiAqL1xuZXhwb3J0cy5rTGF5ZXJQYXRjaGVkID0gU3ltYm9sKCdrb2EtbGF5ZXItcGF0Y2hlZCcpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW50ZXJuYWwtdHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KoaLayerType = void 0;\nvar KoaLayerType;\n(function (KoaLayerType) {\n    KoaLayerType[\"ROUTER\"] = \"router\";\n    KoaLayerType[\"MIDDLEWARE\"] = \"middleware\";\n})(KoaLayerType = exports.KoaLayerType || (exports.KoaLayerType = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8yNWY3M2RjYWE4YTk4Zjg3OWRmZGRmOWNjODU3YjVjNS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWtvYS9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwQ0FBMEMsb0JBQW9CLEtBQUs7QUFDcEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMjVmNzNkY2FhOGE5OGY4NzlkZmRkZjljYzg1N2I1YzVcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1rb2FcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5Lb2FMYXllclR5cGUgPSB2b2lkIDA7XG52YXIgS29hTGF5ZXJUeXBlO1xuKGZ1bmN0aW9uIChLb2FMYXllclR5cGUpIHtcbiAgICBLb2FMYXllclR5cGVbXCJST1VURVJcIl0gPSBcInJvdXRlclwiO1xuICAgIEtvYUxheWVyVHlwZVtcIk1JRERMRVdBUkVcIl0gPSBcIm1pZGRsZXdhcmVcIjtcbn0pKEtvYUxheWVyVHlwZSA9IGV4cG9ydHMuS29hTGF5ZXJUeXBlIHx8IChleHBvcnRzLktvYUxheWVyVHlwZSA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isLayerIgnored = exports.getMiddlewareMetadata = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst types_1 = __webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst getMiddlewareMetadata = (context, layer, isRouter, layerPath) => {\n    var _a;\n    if (isRouter) {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.KOA_NAME]: layerPath === null || layerPath === void 0 ? void 0 : layerPath.toString(),\n                [AttributeNames_1.AttributeNames.KOA_TYPE]: types_1.KoaLayerType.ROUTER,\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: layerPath === null || layerPath === void 0 ? void 0 : layerPath.toString(),\n            },\n            name: context._matchedRouteName || `router - ${layerPath}`,\n        };\n    }\n    else {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.KOA_NAME]: (_a = layer.name) !== null && _a !== void 0 ? _a : 'middleware',\n                [AttributeNames_1.AttributeNames.KOA_TYPE]: types_1.KoaLayerType.MIDDLEWARE,\n            },\n            name: `middleware - ${layer.name}`,\n        };\n    }\n};\nexports.getMiddlewareMetadata = getMiddlewareMetadata;\n/**\n * Check whether the given request is ignored by configuration\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nconst isLayerIgnored = (type, config) => {\n    var _a;\n    return !!(Array.isArray(config === null || config === void 0 ? void 0 : config.ignoreLayersType) &&\n        ((_a = config === null || config === void 0 ? void 0 : config.ignoreLayersType) === null || _a === void 0 ? void 0 : _a.includes(type)));\n};\nexports.isLayerIgnored = isLayerIgnored;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-koa';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js ***!
  \*******************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AttributeNames = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar AttributeNames;\n(function (AttributeNames) {\n    AttributeNames[\"KOA_TYPE\"] = \"koa.type\";\n    AttributeNames[\"KOA_NAME\"] = \"koa.name\";\n})(AttributeNames = exports.AttributeNames || (exports.AttributeNames = {}));\n//# sourceMappingURL=AttributeNames.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\"), exports);\n__exportStar(__webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js":
/*!**************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js ***!
  \**************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KoaInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst types_1 = __webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst internal_types_1 = __webpack_require__(/*! ./internal-types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js\");\n/** Koa instrumentation for OpenTelemetry */\nclass KoaInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n    }\n    init() {\n        return new instrumentation_1.InstrumentationNodeModuleDefinition('koa', ['>=2.0.0 <3'], (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module'\n                ? module.default // ESM\n                : module; // CommonJS\n            if (moduleExports == null) {\n                return moduleExports;\n            }\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.use)) {\n                this._unwrap(moduleExports.prototype, 'use');\n            }\n            this._wrap(moduleExports.prototype, 'use', this._getKoaUsePatch.bind(this));\n            return module;\n        }, (module) => {\n            const moduleExports = module[Symbol.toStringTag] === 'Module'\n                ? module.default // ESM\n                : module; // CommonJS\n            if ((0, instrumentation_1.isWrapped)(moduleExports.prototype.use)) {\n                this._unwrap(moduleExports.prototype, 'use');\n            }\n        });\n    }\n    /**\n     * Patches the Koa.use function in order to instrument each original\n     * middleware layer which is introduced\n     * @param {KoaMiddleware} middleware - the original middleware function\n     */\n    _getKoaUsePatch(original) {\n        const plugin = this;\n        return function use(middlewareFunction) {\n            let patchedFunction;\n            if (middlewareFunction.router) {\n                patchedFunction = plugin._patchRouterDispatch(middlewareFunction);\n            }\n            else {\n                patchedFunction = plugin._patchLayer(middlewareFunction, false);\n            }\n            return original.apply(this, [patchedFunction]);\n        };\n    }\n    /**\n     * Patches the dispatch function used by @koa/router. This function\n     * goes through each routed middleware and adds instrumentation via a call\n     * to the @function _patchLayer function.\n     * @param {KoaMiddleware} dispatchLayer - the original dispatch function which dispatches\n     * routed middleware\n     */\n    _patchRouterDispatch(dispatchLayer) {\n        var _a;\n        api.diag.debug('Patching @koa/router dispatch');\n        const router = dispatchLayer.router;\n        const routesStack = (_a = router === null || router === void 0 ? void 0 : router.stack) !== null && _a !== void 0 ? _a : [];\n        for (const pathLayer of routesStack) {\n            const path = pathLayer.path;\n            const pathStack = pathLayer.stack;\n            for (let j = 0; j < pathStack.length; j++) {\n                const routedMiddleware = pathStack[j];\n                pathStack[j] = this._patchLayer(routedMiddleware, true, path);\n            }\n        }\n        return dispatchLayer;\n    }\n    /**\n     * Patches each individual @param middlewareLayer function in order to create the\n     * span and propagate context. It does not create spans when there is no parent span.\n     * @param {KoaMiddleware} middlewareLayer - the original middleware function.\n     * @param {boolean} isRouter - tracks whether the original middleware function\n     * was dispatched by the router originally\n     * @param {string?} layerPath - if present, provides additional data from the\n     * router about the routed path which the middleware is attached to\n     */\n    _patchLayer(middlewareLayer, isRouter, layerPath) {\n        const layerType = isRouter ? types_1.KoaLayerType.ROUTER : types_1.KoaLayerType.MIDDLEWARE;\n        // Skip patching layer if its ignored in the config\n        if (middlewareLayer[internal_types_1.kLayerPatched] === true ||\n            (0, utils_1.isLayerIgnored)(layerType, this.getConfig()))\n            return middlewareLayer;\n        if (middlewareLayer.constructor.name === 'GeneratorFunction' ||\n            middlewareLayer.constructor.name === 'AsyncGeneratorFunction') {\n            api.diag.debug('ignoring generator-based Koa middleware layer');\n            return middlewareLayer;\n        }\n        middlewareLayer[internal_types_1.kLayerPatched] = true;\n        api.diag.debug('patching Koa middleware layer');\n        return async (context, next) => {\n            const parent = api.trace.getSpan(api.context.active());\n            if (parent === undefined) {\n                return middlewareLayer(context, next);\n            }\n            const metadata = (0, utils_1.getMiddlewareMetadata)(context, middlewareLayer, isRouter, layerPath);\n            const span = this.tracer.startSpan(metadata.name, {\n                attributes: metadata.attributes,\n            });\n            const rpcMetadata = (0, core_1.getRPCMetadata)(api.context.active());\n            if ((rpcMetadata === null || rpcMetadata === void 0 ? void 0 : rpcMetadata.type) === core_1.RPCType.HTTP && context._matchedRoute) {\n                rpcMetadata.route = context._matchedRoute.toString();\n            }\n            const { requestHook } = this.getConfig();\n            if (requestHook) {\n                (0, instrumentation_1.safeExecuteInTheMiddle)(() => requestHook(span, {\n                    context,\n                    middlewareLayer,\n                    layerType,\n                }), e => {\n                    if (e) {\n                        api.diag.error('koa instrumentation: request hook failed', e);\n                    }\n                }, true);\n            }\n            const newContext = api.trace.setSpan(api.context.active(), span);\n            return api.context.with(newContext, async () => {\n                try {\n                    return await middlewareLayer(context, next);\n                }\n                catch (err) {\n                    span.recordException(err);\n                    throw err;\n                }\n                finally {\n                    span.end();\n                }\n            });\n        };\n    }\n}\nexports.KoaInstrumentation = KoaInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js ***!
  \*************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.kLayerPatched = void 0;\n/**\n * This symbol is used to mark a Koa layer as being already instrumented\n * since its possible to use a given layer multiple times (ex: middlewares)\n */\nexports.kLayerPatched = Symbol('koa-layer-patched');\n//# sourceMappingURL=internal-types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8yNWY3M2RjYWE4YTk4Zjg3OWRmZGRmOWNjODU3YjVjNS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWtvYS9idWlsZC9zcmMvaW50ZXJuYWwtdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzI1ZjczZGNhYThhOThmODc5ZGZkZGY5Y2M4NTdiNWM1XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24ta29hXFxidWlsZFxcc3JjXFxpbnRlcm5hbC10eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMua0xheWVyUGF0Y2hlZCA9IHZvaWQgMDtcbi8qKlxuICogVGhpcyBzeW1ib2wgaXMgdXNlZCB0byBtYXJrIGEgS29hIGxheWVyIGFzIGJlaW5nIGFscmVhZHkgaW5zdHJ1bWVudGVkXG4gKiBzaW5jZSBpdHMgcG9zc2libGUgdG8gdXNlIGEgZ2l2ZW4gbGF5ZXIgbXVsdGlwbGUgdGltZXMgKGV4OiBtaWRkbGV3YXJlcylcbiAqL1xuZXhwb3J0cy5rTGF5ZXJQYXRjaGVkID0gU3ltYm9sKCdrb2EtbGF5ZXItcGF0Y2hlZCcpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW50ZXJuYWwtdHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/internal-types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.KoaLayerType = void 0;\nvar KoaLayerType;\n(function (KoaLayerType) {\n    KoaLayerType[\"ROUTER\"] = \"router\";\n    KoaLayerType[\"MIDDLEWARE\"] = \"middleware\";\n})(KoaLayerType = exports.KoaLayerType || (exports.KoaLayerType = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF8yNWY3M2RjYWE4YTk4Zjg3OWRmZGRmOWNjODU3YjVjNS9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWtvYS9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQywwQ0FBMEMsb0JBQW9CLEtBQUs7QUFDcEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfMjVmNzNkY2FhOGE5OGY4NzlkZmRkZjljYzg1N2I1YzVcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi1rb2FcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5Lb2FMYXllclR5cGUgPSB2b2lkIDA7XG52YXIgS29hTGF5ZXJUeXBlO1xuKGZ1bmN0aW9uIChLb2FMYXllclR5cGUpIHtcbiAgICBLb2FMYXllclR5cGVbXCJST1VURVJcIl0gPSBcInJvdXRlclwiO1xuICAgIEtvYUxheWVyVHlwZVtcIk1JRERMRVdBUkVcIl0gPSBcIm1pZGRsZXdhcmVcIjtcbn0pKEtvYUxheWVyVHlwZSA9IGV4cG9ydHMuS29hTGF5ZXJUeXBlIHx8IChleHBvcnRzLktvYUxheWVyVHlwZSA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isLayerIgnored = exports.getMiddlewareMetadata = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst types_1 = __webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/types.js\");\nconst AttributeNames_1 = __webpack_require__(/*! ./enums/AttributeNames */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/enums/AttributeNames.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst getMiddlewareMetadata = (context, layer, isRouter, layerPath) => {\n    var _a;\n    if (isRouter) {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.KOA_NAME]: layerPath === null || layerPath === void 0 ? void 0 : layerPath.toString(),\n                [AttributeNames_1.AttributeNames.KOA_TYPE]: types_1.KoaLayerType.ROUTER,\n                [semantic_conventions_1.SEMATTRS_HTTP_ROUTE]: layerPath === null || layerPath === void 0 ? void 0 : layerPath.toString(),\n            },\n            name: context._matchedRouteName || `router - ${layerPath}`,\n        };\n    }\n    else {\n        return {\n            attributes: {\n                [AttributeNames_1.AttributeNames.KOA_NAME]: (_a = layer.name) !== null && _a !== void 0 ? _a : 'middleware',\n                [AttributeNames_1.AttributeNames.KOA_TYPE]: types_1.KoaLayerType.MIDDLEWARE,\n            },\n            name: `middleware - ${layer.name}`,\n        };\n    }\n};\nexports.getMiddlewareMetadata = getMiddlewareMetadata;\n/**\n * Check whether the given request is ignored by configuration\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nconst isLayerIgnored = (type, config) => {\n    var _a;\n    return !!(Array.isArray(config === null || config === void 0 ? void 0 : config.ignoreLayersType) &&\n        ((_a = config === null || config === void 0 ? void 0 : config.ignoreLayersType) === null || _a === void 0 ? void 0 : _a.includes(type)));\n};\nexports.isLayerIgnored = isLayerIgnored;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js":
/*!******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.47.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-koa';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_25f73dcaa8a98f879dfddf9cc857b5c5/node_modules/@opentelemetry/instrumentation-koa/build/src/version.js\n");

/***/ })

};
;