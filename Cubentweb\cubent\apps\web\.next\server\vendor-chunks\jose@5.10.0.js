"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jose@5.10.0";
exports.ids = ["vendor-chunks/jose@5.10.0"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/compact/decrypt.js":
/*!***************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/compact/decrypt.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compactDecrypt: () => (/* binding */ compactDecrypt)\n/* harmony export */ });\n/* harmony import */ var _flattened_decrypt_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../flattened/decrypt.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\n\nasync function compactDecrypt(jwe, key, options) {\n    if (jwe instanceof Uint8Array) {\n        jwe = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.decoder.decode(jwe);\n    }\n    if (typeof jwe !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Compact JWE must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: encryptedKey, 2: iv, 3: ciphertext, 4: tag, length, } = jwe.split('.');\n    if (length !== 5) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Invalid Compact JWE');\n    }\n    const decrypted = await (0,_flattened_decrypt_js__WEBPACK_IMPORTED_MODULE_2__.flattenedDecrypt)({\n        ciphertext,\n        iv: iv || undefined,\n        protected: protectedHeader,\n        tag: tag || undefined,\n        encrypted_key: encryptedKey || undefined,\n    }, key, options);\n    const result = { plaintext: decrypted.plaintext, protectedHeader: decrypted.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/compact/decrypt.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/compact/encrypt.js":
/*!***************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/compact/encrypt.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompactEncrypt: () => (/* binding */ CompactEncrypt)\n/* harmony export */ });\n/* harmony import */ var _flattened_encrypt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../flattened/encrypt.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js\");\n\nclass CompactEncrypt {\n    _flattened;\n    constructor(plaintext) {\n        this._flattened = new _flattened_encrypt_js__WEBPACK_IMPORTED_MODULE_0__.FlattenedEncrypt(plaintext);\n    }\n    setContentEncryptionKey(cek) {\n        this._flattened.setContentEncryptionKey(cek);\n        return this;\n    }\n    setInitializationVector(iv) {\n        this._flattened.setInitializationVector(iv);\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this._flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        this._flattened.setKeyManagementParameters(parameters);\n        return this;\n    }\n    async encrypt(key, options) {\n        const jwe = await this._flattened.encrypt(key, options);\n        return [jwe.protected, jwe.encrypted_key, jwe.iv, jwe.ciphertext, jwe.tag].join('.');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vandlL2NvbXBhY3QvZW5jcnlwdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyRDtBQUNwRDtBQUNQO0FBQ0E7QUFDQSw4QkFBOEIsbUVBQWdCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcam9zZUA1LjEwLjBcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxqd2VcXGNvbXBhY3RcXGVuY3J5cHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgRmxhdHRlbmVkRW5jcnlwdCB9IGZyb20gJy4uL2ZsYXR0ZW5lZC9lbmNyeXB0LmpzJztcbmV4cG9ydCBjbGFzcyBDb21wYWN0RW5jcnlwdCB7XG4gICAgX2ZsYXR0ZW5lZDtcbiAgICBjb25zdHJ1Y3RvcihwbGFpbnRleHQpIHtcbiAgICAgICAgdGhpcy5fZmxhdHRlbmVkID0gbmV3IEZsYXR0ZW5lZEVuY3J5cHQocGxhaW50ZXh0KTtcbiAgICB9XG4gICAgc2V0Q29udGVudEVuY3J5cHRpb25LZXkoY2VrKSB7XG4gICAgICAgIHRoaXMuX2ZsYXR0ZW5lZC5zZXRDb250ZW50RW5jcnlwdGlvbktleShjZWspO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgc2V0SW5pdGlhbGl6YXRpb25WZWN0b3IoaXYpIHtcbiAgICAgICAgdGhpcy5fZmxhdHRlbmVkLnNldEluaXRpYWxpemF0aW9uVmVjdG9yKGl2KTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIHNldFByb3RlY3RlZEhlYWRlcihwcm90ZWN0ZWRIZWFkZXIpIHtcbiAgICAgICAgdGhpcy5fZmxhdHRlbmVkLnNldFByb3RlY3RlZEhlYWRlcihwcm90ZWN0ZWRIZWFkZXIpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgc2V0S2V5TWFuYWdlbWVudFBhcmFtZXRlcnMocGFyYW1ldGVycykge1xuICAgICAgICB0aGlzLl9mbGF0dGVuZWQuc2V0S2V5TWFuYWdlbWVudFBhcmFtZXRlcnMocGFyYW1ldGVycyk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICBhc3luYyBlbmNyeXB0KGtleSwgb3B0aW9ucykge1xuICAgICAgICBjb25zdCBqd2UgPSBhd2FpdCB0aGlzLl9mbGF0dGVuZWQuZW5jcnlwdChrZXksIG9wdGlvbnMpO1xuICAgICAgICByZXR1cm4gW2p3ZS5wcm90ZWN0ZWQsIGp3ZS5lbmNyeXB0ZWRfa2V5LCBqd2UuaXYsIGp3ZS5jaXBoZXJ0ZXh0LCBqd2UudGFnXS5qb2luKCcuJyk7XG4gICAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/compact/encrypt.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flattenedDecrypt: () => (/* binding */ flattenedDecrypt)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../runtime/base64url.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_decrypt_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../runtime/decrypt.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/decrypt.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/is_disjoint.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_disjoint.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/is_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_object.js\");\n/* harmony import */ var _lib_decrypt_key_management_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../lib/decrypt_key_management.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/decrypt_key_management.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _lib_cek_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../lib/cek.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/cek.js\");\n/* harmony import */ var _lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/validate_crit.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/validate_crit.js\");\n/* harmony import */ var _lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../lib/validate_algorithms.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/validate_algorithms.js\");\n\n\n\n\n\n\n\n\n\n\nasync function flattenedDecrypt(jwe, key, options) {\n    if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jwe)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Flattened JWE must be an object');\n    }\n    if (jwe.protected === undefined && jwe.header === undefined && jwe.unprotected === undefined) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JOSE Header missing');\n    }\n    if (jwe.iv !== undefined && typeof jwe.iv !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Initialization Vector incorrect type');\n    }\n    if (typeof jwe.ciphertext !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Ciphertext missing or incorrect type');\n    }\n    if (jwe.tag !== undefined && typeof jwe.tag !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Authentication Tag incorrect type');\n    }\n    if (jwe.protected !== undefined && typeof jwe.protected !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Protected Header incorrect type');\n    }\n    if (jwe.encrypted_key !== undefined && typeof jwe.encrypted_key !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Encrypted Key incorrect type');\n    }\n    if (jwe.aad !== undefined && typeof jwe.aad !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE AAD incorrect type');\n    }\n    if (jwe.header !== undefined && !(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jwe.header)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Shared Unprotected Header incorrect type');\n    }\n    if (jwe.unprotected !== undefined && !(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jwe.unprotected)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Per-Recipient Unprotected Header incorrect type');\n    }\n    let parsedProt;\n    if (jwe.protected) {\n        try {\n            const protectedHeader = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.protected);\n            parsedProt = JSON.parse(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Protected Header is invalid');\n        }\n    }\n    if (!(0,_lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(parsedProt, jwe.header, jwe.unprotected)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jwe.header,\n        ...jwe.unprotected,\n    };\n    (0,_lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid, new Map(), options?.crit, parsedProt, joseHeader);\n    if (joseHeader.zip !== undefined) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n    }\n    const { alg, enc } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('missing JWE Algorithm (alg) in JWE Header');\n    }\n    if (typeof enc !== 'string' || !enc) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('missing JWE Encryption Algorithm (enc) in JWE Header');\n    }\n    const keyManagementAlgorithms = options && (0,_lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('keyManagementAlgorithms', options.keyManagementAlgorithms);\n    const contentEncryptionAlgorithms = options &&\n        (0,_lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('contentEncryptionAlgorithms', options.contentEncryptionAlgorithms);\n    if ((keyManagementAlgorithms && !keyManagementAlgorithms.has(alg)) ||\n        (!keyManagementAlgorithms && alg.startsWith('PBES2'))) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (contentEncryptionAlgorithms && !contentEncryptionAlgorithms.has(enc)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSEAlgNotAllowed('\"enc\" (Encryption Algorithm) Header Parameter value not allowed');\n    }\n    let encryptedKey;\n    if (jwe.encrypted_key !== undefined) {\n        try {\n            encryptedKey = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.encrypted_key);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the encrypted_key');\n        }\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jwe);\n        resolvedKey = true;\n    }\n    let cek;\n    try {\n        cek = await (0,_lib_decrypt_key_management_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(alg, key, encryptedKey, joseHeader, options);\n    }\n    catch (err) {\n        if (err instanceof TypeError || err instanceof _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid || err instanceof _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSENotSupported) {\n            throw err;\n        }\n        cek = (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(enc);\n    }\n    let iv;\n    let tag;\n    if (jwe.iv !== undefined) {\n        try {\n            iv = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.iv);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the iv');\n        }\n    }\n    if (jwe.tag !== undefined) {\n        try {\n            tag = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.tag);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the tag');\n        }\n    }\n    const protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jwe.protected ?? '');\n    let additionalData;\n    if (jwe.aad !== undefined) {\n        additionalData = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.concat)(protectedHeader, _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode('.'), _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jwe.aad));\n    }\n    else {\n        additionalData = protectedHeader;\n    }\n    let ciphertext;\n    try {\n        ciphertext = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.ciphertext);\n    }\n    catch {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the ciphertext');\n    }\n    const plaintext = await (0,_runtime_decrypt_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(enc, cek, ciphertext, iv, tag, additionalData);\n    const result = { plaintext };\n    if (jwe.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jwe.aad !== undefined) {\n        try {\n            result.additionalAuthenticatedData = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwe.aad);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Failed to base64url decode the aad');\n        }\n    }\n    if (jwe.unprotected !== undefined) {\n        result.sharedUnprotectedHeader = jwe.unprotected;\n    }\n    if (jwe.header !== undefined) {\n        result.unprotectedHeader = jwe.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/flattened/decrypt.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlattenedEncrypt: () => (/* binding */ FlattenedEncrypt)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../runtime/base64url.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _lib_private_symbols_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/private_symbols.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/private_symbols.js\");\n/* harmony import */ var _runtime_encrypt_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../runtime/encrypt.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/encrypt.js\");\n/* harmony import */ var _lib_encrypt_key_management_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/encrypt_key_management.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/encrypt_key_management.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/is_disjoint.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_disjoint.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/validate_crit.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/validate_crit.js\");\n\n\n\n\n\n\n\n\nclass FlattenedEncrypt {\n    _plaintext;\n    _protectedHeader;\n    _sharedUnprotectedHeader;\n    _unprotectedHeader;\n    _aad;\n    _cek;\n    _iv;\n    _keyManagementParameters;\n    constructor(plaintext) {\n        if (!(plaintext instanceof Uint8Array)) {\n            throw new TypeError('plaintext must be an instance of Uint8Array');\n        }\n        this._plaintext = plaintext;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this._keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this._keyManagementParameters = parameters;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setSharedUnprotectedHeader(sharedUnprotectedHeader) {\n        if (this._sharedUnprotectedHeader) {\n            throw new TypeError('setSharedUnprotectedHeader can only be called once');\n        }\n        this._sharedUnprotectedHeader = sharedUnprotectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    setAdditionalAuthenticatedData(aad) {\n        this._aad = aad;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this._cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this._cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this._iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this._iv = iv;\n        return this;\n    }\n    async encrypt(key, options) {\n        if (!this._protectedHeader && !this._unprotectedHeader && !this._sharedUnprotectedHeader) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()');\n        }\n        if (!(0,_lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this._protectedHeader, this._unprotectedHeader, this._sharedUnprotectedHeader)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this._protectedHeader,\n            ...this._unprotectedHeader,\n            ...this._sharedUnprotectedHeader,\n        };\n        (0,_lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid, new Map(), options?.crit, this._protectedHeader, joseHeader);\n        if (joseHeader.zip !== undefined) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n        }\n        const { alg, enc } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        if (typeof enc !== 'string' || !enc) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter missing or invalid');\n        }\n        let encryptedKey;\n        if (this._cek && (alg === 'dir' || alg === 'ECDH-ES')) {\n            throw new TypeError(`setContentEncryptionKey cannot be called with JWE \"alg\" (Algorithm) Header ${alg}`);\n        }\n        let cek;\n        {\n            let parameters;\n            ({ cek, encryptedKey, parameters } = await (0,_lib_encrypt_key_management_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(alg, enc, key, this._cek, this._keyManagementParameters));\n            if (parameters) {\n                if (options && _lib_private_symbols_js__WEBPACK_IMPORTED_MODULE_4__.unprotected in options) {\n                    if (!this._unprotectedHeader) {\n                        this.setUnprotectedHeader(parameters);\n                    }\n                    else {\n                        this._unprotectedHeader = { ...this._unprotectedHeader, ...parameters };\n                    }\n                }\n                else if (!this._protectedHeader) {\n                    this.setProtectedHeader(parameters);\n                }\n                else {\n                    this._protectedHeader = { ...this._protectedHeader, ...parameters };\n                }\n            }\n        }\n        let additionalData;\n        let protectedHeader;\n        let aadMember;\n        if (this._protectedHeader) {\n            protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.encoder.encode((0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(JSON.stringify(this._protectedHeader)));\n        }\n        else {\n            protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.encoder.encode('');\n        }\n        if (this._aad) {\n            aadMember = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(this._aad);\n            additionalData = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.concat)(protectedHeader, _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.encoder.encode('.'), _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.encoder.encode(aadMember));\n        }\n        else {\n            additionalData = protectedHeader;\n        }\n        const { ciphertext, tag, iv } = await (0,_runtime_encrypt_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(enc, this._plaintext, cek, this._iv, additionalData);\n        const jwe = {\n            ciphertext: (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(ciphertext),\n        };\n        if (iv) {\n            jwe.iv = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(iv);\n        }\n        if (tag) {\n            jwe.tag = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(tag);\n        }\n        if (encryptedKey) {\n            jwe.encrypted_key = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(encryptedKey);\n        }\n        if (aadMember) {\n            jwe.aad = aadMember;\n        }\n        if (this._protectedHeader) {\n            jwe.protected = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_5__.decoder.decode(protectedHeader);\n        }\n        if (this._sharedUnprotectedHeader) {\n            jwe.unprotected = this._sharedUnprotectedHeader;\n        }\n        if (this._unprotectedHeader) {\n            jwe.header = this._unprotectedHeader;\n        }\n        return jwe;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/flattened/encrypt.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/compact/sign.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/compact/sign.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompactSign: () => (/* binding */ CompactSign)\n/* harmony export */ });\n/* harmony import */ var _flattened_sign_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../flattened/sign.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/flattened/sign.js\");\n\nclass CompactSign {\n    _flattened;\n    constructor(payload) {\n        this._flattened = new _flattened_sign_js__WEBPACK_IMPORTED_MODULE_0__.FlattenedSign(payload);\n    }\n    setProtectedHeader(protectedHeader) {\n        this._flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    async sign(key, options) {\n        const jws = await this._flattened.sign(key, options);\n        if (jws.payload === undefined) {\n            throw new TypeError('use the flattened module for creating JWS with b64: false');\n        }\n        return `${jws.protected}.${jws.payload}.${jws.signature}`;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vandzL2NvbXBhY3Qvc2lnbi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUM5QztBQUNQO0FBQ0E7QUFDQSw4QkFBOEIsNkRBQWE7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsY0FBYyxHQUFHLFlBQVksR0FBRyxjQUFjO0FBQ2hFO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxqb3NlQDUuMTAuMFxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGp3c1xcY29tcGFjdFxcc2lnbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBGbGF0dGVuZWRTaWduIH0gZnJvbSAnLi4vZmxhdHRlbmVkL3NpZ24uanMnO1xuZXhwb3J0IGNsYXNzIENvbXBhY3RTaWduIHtcbiAgICBfZmxhdHRlbmVkO1xuICAgIGNvbnN0cnVjdG9yKHBheWxvYWQpIHtcbiAgICAgICAgdGhpcy5fZmxhdHRlbmVkID0gbmV3IEZsYXR0ZW5lZFNpZ24ocGF5bG9hZCk7XG4gICAgfVxuICAgIHNldFByb3RlY3RlZEhlYWRlcihwcm90ZWN0ZWRIZWFkZXIpIHtcbiAgICAgICAgdGhpcy5fZmxhdHRlbmVkLnNldFByb3RlY3RlZEhlYWRlcihwcm90ZWN0ZWRIZWFkZXIpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgYXN5bmMgc2lnbihrZXksIG9wdGlvbnMpIHtcbiAgICAgICAgY29uc3QgandzID0gYXdhaXQgdGhpcy5fZmxhdHRlbmVkLnNpZ24oa2V5LCBvcHRpb25zKTtcbiAgICAgICAgaWYgKGp3cy5wYXlsb2FkID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ3VzZSB0aGUgZmxhdHRlbmVkIG1vZHVsZSBmb3IgY3JlYXRpbmcgSldTIHdpdGggYjY0OiBmYWxzZScpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBgJHtqd3MucHJvdGVjdGVkfS4ke2p3cy5wYXlsb2FkfS4ke2p3cy5zaWduYXR1cmV9YDtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/compact/sign.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/compact/verify.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/compact/verify.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compactVerify: () => (/* binding */ compactVerify)\n/* harmony export */ });\n/* harmony import */ var _flattened_verify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../flattened/verify.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/flattened/verify.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\n\nasync function compactVerify(jws, key, options) {\n    if (jws instanceof Uint8Array) {\n        jws = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.decoder.decode(jws);\n    }\n    if (typeof jws !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('Compact JWS must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: payload, 2: signature, length } = jws.split('.');\n    if (length !== 3) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('Invalid Compact JWS');\n    }\n    const verified = await (0,_flattened_verify_js__WEBPACK_IMPORTED_MODULE_2__.flattenedVerify)({ payload, protected: protectedHeader, signature }, key, options);\n    const result = { payload: verified.payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/compact/verify.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/flattened/sign.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/flattened/sign.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FlattenedSign: () => (/* binding */ FlattenedSign)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../runtime/base64url.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_sign_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../runtime/sign.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/sign.js\");\n/* harmony import */ var _lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/is_disjoint.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_disjoint.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/check_key_type.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_key_type.js\");\n/* harmony import */ var _lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/validate_crit.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/validate_crit.js\");\n\n\n\n\n\n\n\nclass FlattenedSign {\n    _payload;\n    _protectedHeader;\n    _unprotectedHeader;\n    constructor(payload) {\n        if (!(payload instanceof Uint8Array)) {\n            throw new TypeError('payload must be an instance of Uint8Array');\n        }\n        this._payload = payload;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this._unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this._unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        if (!this._protectedHeader && !this._unprotectedHeader) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWSInvalid('either setProtectedHeader or setUnprotectedHeader must be called before #sign()');\n        }\n        if (!(0,_lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(this._protectedHeader, this._unprotectedHeader)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this._protectedHeader,\n            ...this._unprotectedHeader,\n        };\n        const extensions = (0,_lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWSInvalid, new Map([['b64', true]]), options?.crit, this._protectedHeader, joseHeader);\n        let b64 = true;\n        if (extensions.has('b64')) {\n            b64 = this._protectedHeader.b64;\n            if (typeof b64 !== 'boolean') {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n            }\n        }\n        const { alg } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        (0,_lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_3__.checkKeyTypeWithJwk)(alg, key, 'sign');\n        let payload = this._payload;\n        if (b64) {\n            payload = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.encoder.encode((0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_5__.encode)(payload));\n        }\n        let protectedHeader;\n        if (this._protectedHeader) {\n            protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.encoder.encode((0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_5__.encode)(JSON.stringify(this._protectedHeader)));\n        }\n        else {\n            protectedHeader = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.encoder.encode('');\n        }\n        const data = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.concat)(protectedHeader, _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.encoder.encode('.'), payload);\n        const signature = await (0,_runtime_sign_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(alg, key, data);\n        const jws = {\n            signature: (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_5__.encode)(signature),\n            payload: '',\n        };\n        if (b64) {\n            jws.payload = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.decoder.decode(payload);\n        }\n        if (this._unprotectedHeader) {\n            jws.header = this._unprotectedHeader;\n        }\n        if (this._protectedHeader) {\n            jws.protected = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.decoder.decode(protectedHeader);\n        }\n        return jws;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/flattened/sign.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/flattened/verify.js":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/flattened/verify.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   flattenedVerify: () => (/* binding */ flattenedVerify)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../runtime/base64url.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_verify_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../runtime/verify.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/verify.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../lib/is_disjoint.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_disjoint.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/is_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_object.js\");\n/* harmony import */ var _lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../lib/check_key_type.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_key_type.js\");\n/* harmony import */ var _lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../lib/validate_crit.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/validate_crit.js\");\n/* harmony import */ var _lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../lib/validate_algorithms.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/validate_algorithms.js\");\n/* harmony import */ var _lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../lib/is_jwk.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_jwk.js\");\n/* harmony import */ var _key_import_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../key/import.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/key/import.js\");\n\n\n\n\n\n\n\n\n\n\n\nasync function flattenedVerify(jws, key, options) {\n    if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jws)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('Flattened JWS must be an object');\n    }\n    if (jws.protected === undefined && jws.header === undefined) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('Flattened JWS must have either of the \"protected\" or \"header\" members');\n    }\n    if (jws.protected !== undefined && typeof jws.protected !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Protected Header incorrect type');\n    }\n    if (jws.payload === undefined) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Payload missing');\n    }\n    if (typeof jws.signature !== 'string') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Signature missing or incorrect type');\n    }\n    if (jws.header !== undefined && !(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jws.header)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Unprotected Header incorrect type');\n    }\n    let parsedProt = {};\n    if (jws.protected) {\n        try {\n            const protectedHeader = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jws.protected);\n            parsedProt = JSON.parse(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Protected Header is invalid');\n        }\n    }\n    if (!(0,_lib_is_disjoint_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(parsedProt, jws.header)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jws.header,\n    };\n    const extensions = (0,_lib_validate_crit_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid, new Map([['b64', true]]), options?.crit, parsedProt, joseHeader);\n    let b64 = true;\n    if (extensions.has('b64')) {\n        b64 = parsedProt.b64;\n        if (typeof b64 !== 'boolean') {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n        }\n    }\n    const { alg } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n    }\n    const algorithms = options && (0,_lib_validate_algorithms_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])('algorithms', options.algorithms);\n    if (algorithms && !algorithms.has(alg)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (b64) {\n        if (typeof jws.payload !== 'string') {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Payload must be a string');\n        }\n    }\n    else if (typeof jws.payload !== 'string' && !(jws.payload instanceof Uint8Array)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('JWS Payload must be a string or an Uint8Array instance');\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jws);\n        resolvedKey = true;\n        (0,_lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_7__.checkKeyTypeWithJwk)(alg, key, 'verify');\n        if ((0,_lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_8__.isJWK)(key)) {\n            key = await (0,_key_import_js__WEBPACK_IMPORTED_MODULE_9__.importJWK)(key, alg);\n        }\n    }\n    else {\n        (0,_lib_check_key_type_js__WEBPACK_IMPORTED_MODULE_7__.checkKeyTypeWithJwk)(alg, key, 'verify');\n    }\n    const data = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.concat)(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jws.protected ?? ''), _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode('.'), typeof jws.payload === 'string' ? _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jws.payload) : jws.payload);\n    let signature;\n    try {\n        signature = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jws.signature);\n    }\n    catch {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('Failed to base64url decode the signature');\n    }\n    const verified = await (0,_runtime_verify_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(alg, key, signature, data);\n    if (!verified) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSSignatureVerificationFailed();\n    }\n    let payload;\n    if (b64) {\n        try {\n            payload = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jws.payload);\n        }\n        catch {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWSInvalid('Failed to base64url decode the payload');\n        }\n    }\n    else if (typeof jws.payload === 'string') {\n        payload = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_3__.encoder.encode(jws.payload);\n    }\n    else {\n        payload = jws.payload;\n    }\n    const result = { payload };\n    if (jws.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jws.header !== undefined) {\n        result.unprotectedHeader = jws.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jws/flattened/verify.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwt/decrypt.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwt/decrypt.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   jwtDecrypt: () => (/* binding */ jwtDecrypt)\n/* harmony export */ });\n/* harmony import */ var _jwe_compact_decrypt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jwe/compact/decrypt.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/compact/decrypt.js\");\n/* harmony import */ var _lib_jwt_claims_set_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/jwt_claims_set.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/jwt_claims_set.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n\n\n\nasync function jwtDecrypt(jwt, key, options) {\n    const decrypted = await (0,_jwe_compact_decrypt_js__WEBPACK_IMPORTED_MODULE_0__.compactDecrypt)(jwt, key, options);\n    const payload = (0,_lib_jwt_claims_set_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(decrypted.protectedHeader, decrypted.plaintext, options);\n    const { protectedHeader } = decrypted;\n    if (protectedHeader.iss !== undefined && protectedHeader.iss !== payload.iss) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('replicated \"iss\" claim header parameter mismatch', payload, 'iss', 'mismatch');\n    }\n    if (protectedHeader.sub !== undefined && protectedHeader.sub !== payload.sub) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('replicated \"sub\" claim header parameter mismatch', payload, 'sub', 'mismatch');\n    }\n    if (protectedHeader.aud !== undefined &&\n        JSON.stringify(protectedHeader.aud) !== JSON.stringify(payload.aud)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('replicated \"aud\" claim header parameter mismatch', payload, 'aud', 'mismatch');\n    }\n    const result = { payload, protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwt/decrypt.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwt/encrypt.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwt/encrypt.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EncryptJWT: () => (/* binding */ EncryptJWT)\n/* harmony export */ });\n/* harmony import */ var _jwe_compact_encrypt_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../jwe/compact/encrypt.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwe/compact/encrypt.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _produce_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./produce.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwt/produce.js\");\n\n\n\nclass EncryptJWT extends _produce_js__WEBPACK_IMPORTED_MODULE_0__.ProduceJWT {\n    _cek;\n    _iv;\n    _keyManagementParameters;\n    _protectedHeader;\n    _replicateIssuerAsHeader;\n    _replicateSubjectAsHeader;\n    _replicateAudienceAsHeader;\n    setProtectedHeader(protectedHeader) {\n        if (this._protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this._protectedHeader = protectedHeader;\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this._keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this._keyManagementParameters = parameters;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this._cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this._cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this._iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this._iv = iv;\n        return this;\n    }\n    replicateIssuerAsHeader() {\n        this._replicateIssuerAsHeader = true;\n        return this;\n    }\n    replicateSubjectAsHeader() {\n        this._replicateSubjectAsHeader = true;\n        return this;\n    }\n    replicateAudienceAsHeader() {\n        this._replicateAudienceAsHeader = true;\n        return this;\n    }\n    async encrypt(key, options) {\n        const enc = new _jwe_compact_encrypt_js__WEBPACK_IMPORTED_MODULE_1__.CompactEncrypt(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_2__.encoder.encode(JSON.stringify(this._payload)));\n        if (this._replicateIssuerAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, iss: this._payload.iss };\n        }\n        if (this._replicateSubjectAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, sub: this._payload.sub };\n        }\n        if (this._replicateAudienceAsHeader) {\n            this._protectedHeader = { ...this._protectedHeader, aud: this._payload.aud };\n        }\n        enc.setProtectedHeader(this._protectedHeader);\n        if (this._iv) {\n            enc.setInitializationVector(this._iv);\n        }\n        if (this._cek) {\n            enc.setContentEncryptionKey(this._cek);\n        }\n        if (this._keyManagementParameters) {\n            enc.setKeyManagementParameters(this._keyManagementParameters);\n        }\n        return enc.encrypt(key, options);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwt/encrypt.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwt/produce.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwt/produce.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProduceJWT: () => (/* binding */ ProduceJWT)\n/* harmony export */ });\n/* harmony import */ var _lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/epoch.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/epoch.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/is_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_object.js\");\n/* harmony import */ var _lib_secs_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/secs.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/secs.js\");\n\n\n\nfunction validateInput(label, input) {\n    if (!Number.isFinite(input)) {\n        throw new TypeError(`Invalid ${label} input`);\n    }\n    return input;\n}\nclass ProduceJWT {\n    _payload;\n    constructor(payload = {}) {\n        if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this._payload = payload;\n    }\n    setIssuer(issuer) {\n        this._payload = { ...this._payload, iss: issuer };\n        return this;\n    }\n    setSubject(subject) {\n        this._payload = { ...this._payload, sub: subject };\n        return this;\n    }\n    setAudience(audience) {\n        this._payload = { ...this._payload, aud: audience };\n        return this;\n    }\n    setJti(jwtId) {\n        this._payload = { ...this._payload, jti: jwtId };\n        return this;\n    }\n    setNotBefore(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, nbf: validateInput('setNotBefore', input) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, nbf: validateInput('setNotBefore', (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input)) };\n        }\n        else {\n            this._payload = { ...this._payload, nbf: (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(new Date()) + (0,_lib_secs_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(input) };\n        }\n        return this;\n    }\n    setExpirationTime(input) {\n        if (typeof input === 'number') {\n            this._payload = { ...this._payload, exp: validateInput('setExpirationTime', input) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, exp: validateInput('setExpirationTime', (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input)) };\n        }\n        else {\n            this._payload = { ...this._payload, exp: (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(new Date()) + (0,_lib_secs_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(input) };\n        }\n        return this;\n    }\n    setIssuedAt(input) {\n        if (typeof input === 'undefined') {\n            this._payload = { ...this._payload, iat: (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(new Date()) };\n        }\n        else if (input instanceof Date) {\n            this._payload = { ...this._payload, iat: validateInput('setIssuedAt', (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input)) };\n        }\n        else if (typeof input === 'string') {\n            this._payload = {\n                ...this._payload,\n                iat: validateInput('setIssuedAt', (0,_lib_epoch_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(new Date()) + (0,_lib_secs_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(input)),\n            };\n        }\n        else {\n            this._payload = { ...this._payload, iat: validateInput('setIssuedAt', input) };\n        }\n        return this;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/jwt/produce.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/key/export.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/key/export.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   exportJWK: () => (/* binding */ exportJWK),\n/* harmony export */   exportPKCS8: () => (/* binding */ exportPKCS8),\n/* harmony export */   exportSPKI: () => (/* binding */ exportSPKI)\n/* harmony export */ });\n/* harmony import */ var _runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/asn1.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/asn1.js\");\n/* harmony import */ var _runtime_key_to_jwk_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/key_to_jwk.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/key_to_jwk.js\");\n\n\n\nasync function exportSPKI(key) {\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.toSPKI)(key);\n}\nasync function exportPKCS8(key) {\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.toPKCS8)(key);\n}\nasync function exportJWK(key) {\n    return (0,_runtime_key_to_jwk_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20va2V5L2V4cG9ydC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE0RDtBQUNFO0FBQ2Q7QUFDekM7QUFDUCxXQUFXLHdEQUFZO0FBQ3ZCO0FBQ087QUFDUCxXQUFXLHlEQUFhO0FBQ3hCO0FBQ087QUFDUCxXQUFXLGtFQUFRO0FBQ25CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcam9zZUA1LjEwLjBcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxrZXlcXGV4cG9ydC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0b1NQS0kgYXMgZXhwb3J0UHVibGljIH0gZnJvbSAnLi4vcnVudGltZS9hc24xLmpzJztcbmltcG9ydCB7IHRvUEtDUzggYXMgZXhwb3J0UHJpdmF0ZSB9IGZyb20gJy4uL3J1bnRpbWUvYXNuMS5qcyc7XG5pbXBvcnQga2V5VG9KV0sgZnJvbSAnLi4vcnVudGltZS9rZXlfdG9fandrLmpzJztcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBleHBvcnRTUEtJKGtleSkge1xuICAgIHJldHVybiBleHBvcnRQdWJsaWMoa2V5KTtcbn1cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBleHBvcnRQS0NTOChrZXkpIHtcbiAgICByZXR1cm4gZXhwb3J0UHJpdmF0ZShrZXkpO1xufVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGV4cG9ydEpXSyhrZXkpIHtcbiAgICByZXR1cm4ga2V5VG9KV0soa2V5KTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/key/export.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/key/import.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/key/import.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   importJWK: () => (/* binding */ importJWK),\n/* harmony export */   importPKCS8: () => (/* binding */ importPKCS8),\n/* harmony export */   importSPKI: () => (/* binding */ importSPKI),\n/* harmony export */   importX509: () => (/* binding */ importX509)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/asn1.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/asn1.js\");\n/* harmony import */ var _runtime_jwk_to_key_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../runtime/jwk_to_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/jwk_to_key.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_is_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/is_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_object.js\");\n\n\n\n\n\nasync function importSPKI(spki, alg, options) {\n    if (typeof spki !== 'string' || spki.indexOf('-----BEGIN PUBLIC KEY-----') !== 0) {\n        throw new TypeError('\"spki\" must be SPKI formatted string');\n    }\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.fromSPKI)(spki, alg, options);\n}\nasync function importX509(x509, alg, options) {\n    if (typeof x509 !== 'string' || x509.indexOf('-----BEGIN CERTIFICATE-----') !== 0) {\n        throw new TypeError('\"x509\" must be X.509 formatted string');\n    }\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.fromX509)(x509, alg, options);\n}\nasync function importPKCS8(pkcs8, alg, options) {\n    if (typeof pkcs8 !== 'string' || pkcs8.indexOf('-----BEGIN PRIVATE KEY-----') !== 0) {\n        throw new TypeError('\"pkcs8\" must be PKCS#8 formatted string');\n    }\n    return (0,_runtime_asn1_js__WEBPACK_IMPORTED_MODULE_0__.fromPKCS8)(pkcs8, alg, options);\n}\nasync function importJWK(jwk, alg) {\n    if (!(0,_lib_is_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(jwk)) {\n        throw new TypeError('JWK must be an object');\n    }\n    alg ||= jwk.alg;\n    switch (jwk.kty) {\n        case 'oct':\n            if (typeof jwk.k !== 'string' || !jwk.k) {\n                throw new TypeError('missing \"k\" (Key Value) Parameter value');\n            }\n            return (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_2__.decode)(jwk.k);\n        case 'RSA':\n            if ('oth' in jwk && jwk.oth !== undefined) {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('RSA JWK \"oth\" (Other Primes Info) Parameter value is not supported');\n            }\n        case 'EC':\n        case 'OKP':\n            return (0,_runtime_jwk_to_key_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({ ...jwk, alg });\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Unsupported \"kty\" (Key Type) Parameter value');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/key/import.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/aesgcmkw.js":
/*!********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/aesgcmkw.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _runtime_encrypt_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/encrypt.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/encrypt.js\");\n/* harmony import */ var _runtime_decrypt_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../runtime/decrypt.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/decrypt.js\");\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js\");\n\n\n\nasync function wrap(alg, key, cek, iv) {\n    const jweAlgorithm = alg.slice(0, 7);\n    const wrapped = await (0,_runtime_encrypt_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(jweAlgorithm, cek, key, iv, new Uint8Array(0));\n    return {\n        encryptedKey: wrapped.ciphertext,\n        iv: (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_1__.encode)(wrapped.iv),\n        tag: (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_1__.encode)(wrapped.tag),\n    };\n}\nasync function unwrap(alg, key, encryptedKey, iv, tag) {\n    const jweAlgorithm = alg.slice(0, 7);\n    return (0,_runtime_decrypt_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(jweAlgorithm, key, encryptedKey, iv, tag, new Uint8Array(0));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vbGliL2Flc2djbWt3LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTRDO0FBQ0E7QUFDa0I7QUFDdkQ7QUFDUDtBQUNBLDBCQUEwQiwrREFBTztBQUNqQztBQUNBO0FBQ0EsWUFBWSw2REFBUztBQUNyQixhQUFhLDZEQUFTO0FBQ3RCO0FBQ0E7QUFDTztBQUNQO0FBQ0EsV0FBVywrREFBTztBQUNsQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGpvc2VANS4xMC4wXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcbGliXFxhZXNnY21rdy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZW5jcnlwdCBmcm9tICcuLi9ydW50aW1lL2VuY3J5cHQuanMnO1xuaW1wb3J0IGRlY3J5cHQgZnJvbSAnLi4vcnVudGltZS9kZWNyeXB0LmpzJztcbmltcG9ydCB7IGVuY29kZSBhcyBiYXNlNjR1cmwgfSBmcm9tICcuLi9ydW50aW1lL2Jhc2U2NHVybC5qcyc7XG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gd3JhcChhbGcsIGtleSwgY2VrLCBpdikge1xuICAgIGNvbnN0IGp3ZUFsZ29yaXRobSA9IGFsZy5zbGljZSgwLCA3KTtcbiAgICBjb25zdCB3cmFwcGVkID0gYXdhaXQgZW5jcnlwdChqd2VBbGdvcml0aG0sIGNlaywga2V5LCBpdiwgbmV3IFVpbnQ4QXJyYXkoMCkpO1xuICAgIHJldHVybiB7XG4gICAgICAgIGVuY3J5cHRlZEtleTogd3JhcHBlZC5jaXBoZXJ0ZXh0LFxuICAgICAgICBpdjogYmFzZTY0dXJsKHdyYXBwZWQuaXYpLFxuICAgICAgICB0YWc6IGJhc2U2NHVybCh3cmFwcGVkLnRhZyksXG4gICAgfTtcbn1cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1bndyYXAoYWxnLCBrZXksIGVuY3J5cHRlZEtleSwgaXYsIHRhZykge1xuICAgIGNvbnN0IGp3ZUFsZ29yaXRobSA9IGFsZy5zbGljZSgwLCA3KTtcbiAgICByZXR1cm4gZGVjcnlwdChqd2VBbGdvcml0aG0sIGtleSwgZW5jcnlwdGVkS2V5LCBpdiwgdGFnLCBuZXcgVWludDhBcnJheSgwKSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/aesgcmkw.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   concat: () => (/* binding */ concat),\n/* harmony export */   concatKdf: () => (/* binding */ concatKdf),\n/* harmony export */   decoder: () => (/* binding */ decoder),\n/* harmony export */   encoder: () => (/* binding */ encoder),\n/* harmony export */   lengthAndInput: () => (/* binding */ lengthAndInput),\n/* harmony export */   p2s: () => (/* binding */ p2s),\n/* harmony export */   uint32be: () => (/* binding */ uint32be),\n/* harmony export */   uint64be: () => (/* binding */ uint64be)\n/* harmony export */ });\n/* harmony import */ var _runtime_digest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/digest.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/digest.js\");\n\nconst encoder = new TextEncoder();\nconst decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nfunction concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nfunction p2s(alg, p2sInput) {\n    return concat(encoder.encode(alg), new Uint8Array([0]), p2sInput);\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nfunction uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nfunction uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\nfunction lengthAndInput(input) {\n    return concat(uint32be(input.length), input);\n}\nasync function concatKdf(secret, bits, value) {\n    const iterations = Math.ceil((bits >> 3) / 32);\n    const res = new Uint8Array(iterations * 32);\n    for (let iter = 0; iter < iterations; iter++) {\n        const buf = new Uint8Array(4 + secret.length + value.length);\n        buf.set(uint32be(iter + 1));\n        buf.set(secret, 4);\n        buf.set(value, 4 + secret.length);\n        res.set(await (0,_runtime_digest_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])('sha256', buf), iter * 32);\n    }\n    return res.slice(0, bits >> 3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/cek.js":
/*!***************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/cek.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bitLength: () => (/* binding */ bitLength),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _runtime_random_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/random.js */ \"node:crypto\");\n\n\nfunction bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n            return 128;\n        case 'A192GCM':\n            return 192;\n        case 'A256GCM':\n        case 'A128CBC-HS256':\n            return 256;\n        case 'A192CBC-HS384':\n            return 384;\n        case 'A256CBC-HS512':\n            return 512;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((alg) => (0,_runtime_random_js__WEBPACK_IMPORTED_MODULE_1__.randomFillSync)(new Uint8Array(bitLength(alg) >> 3)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vbGliL2Nlay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXFEO0FBQ1g7QUFDbkM7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2REFBZ0IsK0JBQStCLElBQUk7QUFDekU7QUFDQTtBQUNBLGlFQUFlLFNBQVMsa0VBQU0scUNBQXFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxqb3NlQDUuMTAuMFxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGxpYlxcY2VrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEpPU0VOb3RTdXBwb3J0ZWQgfSBmcm9tICcuLi91dGlsL2Vycm9ycy5qcyc7XG5pbXBvcnQgcmFuZG9tIGZyb20gJy4uL3J1bnRpbWUvcmFuZG9tLmpzJztcbmV4cG9ydCBmdW5jdGlvbiBiaXRMZW5ndGgoYWxnKSB7XG4gICAgc3dpdGNoIChhbGcpIHtcbiAgICAgICAgY2FzZSAnQTEyOEdDTSc6XG4gICAgICAgICAgICByZXR1cm4gMTI4O1xuICAgICAgICBjYXNlICdBMTkyR0NNJzpcbiAgICAgICAgICAgIHJldHVybiAxOTI7XG4gICAgICAgIGNhc2UgJ0EyNTZHQ00nOlxuICAgICAgICBjYXNlICdBMTI4Q0JDLUhTMjU2JzpcbiAgICAgICAgICAgIHJldHVybiAyNTY7XG4gICAgICAgIGNhc2UgJ0ExOTJDQkMtSFMzODQnOlxuICAgICAgICAgICAgcmV0dXJuIDM4NDtcbiAgICAgICAgY2FzZSAnQTI1NkNCQy1IUzUxMic6XG4gICAgICAgICAgICByZXR1cm4gNTEyO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgdGhyb3cgbmV3IEpPU0VOb3RTdXBwb3J0ZWQoYFVuc3VwcG9ydGVkIEpXRSBBbGdvcml0aG06ICR7YWxnfWApO1xuICAgIH1cbn1cbmV4cG9ydCBkZWZhdWx0IChhbGcpID0+IHJhbmRvbShuZXcgVWludDhBcnJheShiaXRMZW5ndGgoYWxnKSA+PiAzKSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/cek.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_iv_length.js":
/*!***************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_iv_length.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _iv_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./iv.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/iv.js\");\n\n\nconst checkIvLength = (enc, iv) => {\n    if (iv.length << 3 !== (0,_iv_js__WEBPACK_IMPORTED_MODULE_0__.bitLength)(enc)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JWEInvalid('Invalid Initialization Vector length');\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (checkIvLength);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vbGliL2NoZWNrX2l2X2xlbmd0aC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0M7QUFDWDtBQUNwQztBQUNBLDJCQUEyQixpREFBUztBQUNwQyxrQkFBa0IsdURBQVU7QUFDNUI7QUFDQTtBQUNBLGlFQUFlLGFBQWEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGpvc2VANS4xMC4wXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcbGliXFxjaGVja19pdl9sZW5ndGguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSldFSW52YWxpZCB9IGZyb20gJy4uL3V0aWwvZXJyb3JzLmpzJztcbmltcG9ydCB7IGJpdExlbmd0aCB9IGZyb20gJy4vaXYuanMnO1xuY29uc3QgY2hlY2tJdkxlbmd0aCA9IChlbmMsIGl2KSA9PiB7XG4gICAgaWYgKGl2Lmxlbmd0aCA8PCAzICE9PSBiaXRMZW5ndGgoZW5jKSkge1xuICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZCgnSW52YWxpZCBJbml0aWFsaXphdGlvbiBWZWN0b3IgbGVuZ3RoJyk7XG4gICAgfVxufTtcbmV4cG9ydCBkZWZhdWx0IGNoZWNrSXZMZW5ndGg7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_iv_length.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_key_type.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_key_type.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkKeyTypeWithJwk: () => (/* binding */ checkKeyTypeWithJwk),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./invalid_key_input.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/is_key_like.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n/* harmony import */ var _is_jwk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_jwk.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_jwk.js\");\n\n\n\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined && key.use !== 'sig') {\n        throw new TypeError('Invalid key for this operation, when present its use must be sig');\n    }\n    if (key.key_ops !== undefined && key.key_ops.includes?.(usage) !== true) {\n        throw new TypeError(`Invalid key for this operation, when present its key_ops must include ${usage}`);\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, when present its alg must be ${alg}`);\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (allowJwk && _is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isJWK(key)) {\n        if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!(0,_runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__.withAlg)(alg, key, ..._runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__.types, 'Uint8Array', allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage, allowJwk) => {\n    if (allowJwk && _is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isJWK(key)) {\n        switch (usage) {\n            case 'sign':\n                if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'verify':\n                if (_is_jwk_js__WEBPACK_IMPORTED_MODULE_0__.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!(0,_runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key)) {\n        throw new TypeError((0,_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_2__.withAlg)(alg, key, ..._runtime_is_key_like_js__WEBPACK_IMPORTED_MODULE_1__.types, allowJwk ? 'JSON Web Key' : null));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (usage === 'sign' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n    }\n    if (usage === 'decrypt' && key.type === 'public') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n    }\n    if (key.algorithm && usage === 'verify' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n    }\n    if (key.algorithm && usage === 'encrypt' && key.type === 'private') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n    }\n};\nfunction checkKeyType(allowJwk, alg, key, usage) {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A\\d{3}(?:GCM)?KW$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage, allowJwk);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (checkKeyType.bind(undefined, false));\nconst checkKeyTypeWithJwk = checkKeyType.bind(undefined, true);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_key_type.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_p2s.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_p2s.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ checkP2s)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n\nfunction checkP2s(p2s) {\n    if (!(p2s instanceof Uint8Array) || p2s.length < 8) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid('PBES2 Salt Input must be 8 or more octets');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vbGliL2NoZWNrX3Aycy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUErQztBQUNoQztBQUNmO0FBQ0Esa0JBQWtCLHVEQUFVO0FBQzVCO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxqb3NlQDUuMTAuMFxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGxpYlxcY2hlY2tfcDJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEpXRUludmFsaWQgfSBmcm9tICcuLi91dGlsL2Vycm9ycy5qcyc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBjaGVja1AycyhwMnMpIHtcbiAgICBpZiAoIShwMnMgaW5zdGFuY2VvZiBVaW50OEFycmF5KSB8fCBwMnMubGVuZ3RoIDwgOCkge1xuICAgICAgICB0aHJvdyBuZXcgSldFSW52YWxpZCgnUEJFUzIgU2FsdCBJbnB1dCBtdXN0IGJlIDggb3IgbW9yZSBvY3RldHMnKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_p2s.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/crypto_key.js":
/*!**********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/crypto_key.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkEncCryptoKey: () => (/* binding */ checkEncCryptoKey),\n/* harmony export */   checkSigCryptoKey: () => (/* binding */ checkSigCryptoKey)\n/* harmony export */ });\nfunction unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usages) {\n    if (usages.length && !usages.some((expected) => key.usages.includes(expected))) {\n        let msg = 'CryptoKey does not support this operation, its usages must include ';\n        if (usages.length > 2) {\n            const last = usages.pop();\n            msg += `one of ${usages.join(', ')}, or ${last}.`;\n        }\n        else if (usages.length === 2) {\n            msg += `one of ${usages[0]} or ${usages[1]}.`;\n        }\n        else {\n            msg += `${usages[0]}.`;\n        }\n        throw new TypeError(msg);\n    }\n}\nfunction checkSigCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'EdDSA': {\n            if (key.algorithm.name !== 'Ed25519' && key.algorithm.name !== 'Ed448') {\n                throw unusable('Ed25519 or Ed448');\n            }\n            break;\n        }\n        case 'Ed25519': {\n            if (!isAlgorithm(key.algorithm, 'Ed25519'))\n                throw unusable('Ed25519');\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\nfunction checkEncCryptoKey(key, alg, ...usages) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                case 'X448':\n                    break;\n                default:\n                    throw unusable('ECDH, X25519, or X448');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usages);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/crypto_key.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/decrypt_key_management.js":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/decrypt_key_management.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../runtime/aeskw.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/aeskw.js\");\n/* harmony import */ var _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../runtime/ecdhes.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/ecdhes.js\");\n/* harmony import */ var _runtime_pbes2kw_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../runtime/pbes2kw.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/pbes2kw.js\");\n/* harmony import */ var _runtime_rsaes_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../runtime/rsaes.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/rsaes.js\");\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_normalize_key_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/normalize_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/normalize_key.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_cek_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/cek.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/cek.js\");\n/* harmony import */ var _key_import_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../key/import.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/key/import.js\");\n/* harmony import */ var _check_key_type_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check_key_type.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_key_type.js\");\n/* harmony import */ var _is_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_object.js\");\n/* harmony import */ var _aesgcmkw_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./aesgcmkw.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/aesgcmkw.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nasync function decryptKeyManagement(alg, key, encryptedKey, joseHeader, options) {\n    (0,_check_key_type_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(alg, key, 'decrypt');\n    key = (await _runtime_normalize_key_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].normalizePrivateKey?.(key, alg)) || key;\n    switch (alg) {\n        case 'dir': {\n            if (encryptedKey !== undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Encountered unexpected JWE Encrypted Key');\n            return key;\n        }\n        case 'ECDH-ES':\n            if (encryptedKey !== undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Encountered unexpected JWE Encrypted Key');\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!(0,_is_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(joseHeader.epk))\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"epk\" (Ephemeral Public Key) missing or invalid`);\n            if (!_runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_4__.ecdhAllowed(key))\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            const epk = await (0,_key_import_js__WEBPACK_IMPORTED_MODULE_5__.importJWK)(joseHeader.epk, alg);\n            let partyUInfo;\n            let partyVInfo;\n            if (joseHeader.apu !== undefined) {\n                if (typeof joseHeader.apu !== 'string')\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"apu\" (Agreement PartyUInfo) invalid`);\n                try {\n                    partyUInfo = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.apu);\n                }\n                catch {\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the apu');\n                }\n            }\n            if (joseHeader.apv !== undefined) {\n                if (typeof joseHeader.apv !== 'string')\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"apv\" (Agreement PartyVInfo) invalid`);\n                try {\n                    partyVInfo = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.apv);\n                }\n                catch {\n                    throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the apv');\n                }\n            }\n            const sharedSecret = await _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_4__.deriveKey(epk, key, alg === 'ECDH-ES' ? joseHeader.enc : alg, alg === 'ECDH-ES' ? (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_7__.bitLength)(joseHeader.enc) : parseInt(alg.slice(-5, -2), 10), partyUInfo, partyVInfo);\n            if (alg === 'ECDH-ES')\n                return sharedSecret;\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            return (0,_runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_8__.unwrap)(alg.slice(-6), sharedSecret, encryptedKey);\n        }\n        case 'RSA1_5':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            return (0,_runtime_rsaes_js__WEBPACK_IMPORTED_MODULE_9__.decrypt)(alg, key, encryptedKey);\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.p2c !== 'number')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) missing or invalid`);\n            const p2cLimit = options?.maxPBES2Count || 10_000;\n            if (joseHeader.p2c > p2cLimit)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) out is of acceptable bounds`);\n            if (typeof joseHeader.p2s !== 'string')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"p2s\" (PBES2 Salt) missing or invalid`);\n            let p2s;\n            try {\n                p2s = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.p2s);\n            }\n            catch {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the p2s');\n            }\n            return (0,_runtime_pbes2kw_js__WEBPACK_IMPORTED_MODULE_10__.decrypt)(alg, key, encryptedKey, joseHeader.p2c, p2s);\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            return (0,_runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_8__.unwrap)(alg, key, encryptedKey);\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            if (encryptedKey === undefined)\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.iv !== 'string')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"iv\" (Initialization Vector) missing or invalid`);\n            if (typeof joseHeader.tag !== 'string')\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid(`JOSE Header \"tag\" (Authentication Tag) missing or invalid`);\n            let iv;\n            try {\n                iv = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.iv);\n            }\n            catch {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the iv');\n            }\n            let tag;\n            try {\n                tag = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.decode)(joseHeader.tag);\n            }\n            catch {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWEInvalid('Failed to base64url decode the tag');\n            }\n            return (0,_aesgcmkw_js__WEBPACK_IMPORTED_MODULE_11__.unwrap)(alg, key, encryptedKey, iv, tag);\n        }\n        default: {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (decryptKeyManagement);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/decrypt_key_management.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/encrypt_key_management.js":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/encrypt_key_management.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../runtime/aeskw.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/aeskw.js\");\n/* harmony import */ var _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../runtime/ecdhes.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/ecdhes.js\");\n/* harmony import */ var _runtime_pbes2kw_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../runtime/pbes2kw.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/pbes2kw.js\");\n/* harmony import */ var _runtime_rsaes_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../runtime/rsaes.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/rsaes.js\");\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _runtime_normalize_key_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/normalize_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/normalize_key.js\");\n/* harmony import */ var _lib_cek_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/cek.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/cek.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _key_export_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../key/export.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/key/export.js\");\n/* harmony import */ var _check_key_type_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./check_key_type.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_key_type.js\");\n/* harmony import */ var _aesgcmkw_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./aesgcmkw.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/aesgcmkw.js\");\n\n\n\n\n\n\n\n\n\n\n\nasync function encryptKeyManagement(alg, enc, key, providedCek, providedParameters = {}) {\n    let encryptedKey;\n    let parameters;\n    let cek;\n    (0,_check_key_type_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(alg, key, 'encrypt');\n    key = (await _runtime_normalize_key_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].normalizePublicKey?.(key, alg)) || key;\n    switch (alg) {\n        case 'dir': {\n            cek = key;\n            break;\n        }\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!_runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_2__.ecdhAllowed(key)) {\n                throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            }\n            const { apu, apv } = providedParameters;\n            let { epk: ephemeralKey } = providedParameters;\n            ephemeralKey ||= (await _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_2__.generateEpk(key)).privateKey;\n            const { x, y, crv, kty } = await (0,_key_export_js__WEBPACK_IMPORTED_MODULE_4__.exportJWK)(ephemeralKey);\n            const sharedSecret = await _runtime_ecdhes_js__WEBPACK_IMPORTED_MODULE_2__.deriveKey(key, ephemeralKey, alg === 'ECDH-ES' ? enc : alg, alg === 'ECDH-ES' ? (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__.bitLength)(enc) : parseInt(alg.slice(-5, -2), 10), apu, apv);\n            parameters = { epk: { x, crv, kty } };\n            if (kty === 'EC')\n                parameters.epk.y = y;\n            if (apu)\n                parameters.apu = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(apu);\n            if (apv)\n                parameters.apv = (0,_runtime_base64url_js__WEBPACK_IMPORTED_MODULE_6__.encode)(apv);\n            if (alg === 'ECDH-ES') {\n                cek = sharedSecret;\n                break;\n            }\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            const kwAlg = alg.slice(-6);\n            encryptedKey = await (0,_runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_7__.wrap)(kwAlg, sharedSecret, cek);\n            break;\n        }\n        case 'RSA1_5':\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            encryptedKey = await (0,_runtime_rsaes_js__WEBPACK_IMPORTED_MODULE_8__.encrypt)(alg, key, cek);\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            const { p2c, p2s } = providedParameters;\n            ({ encryptedKey, ...parameters } = await (0,_runtime_pbes2kw_js__WEBPACK_IMPORTED_MODULE_9__.encrypt)(alg, key, cek, p2c, p2s));\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            encryptedKey = await (0,_runtime_aeskw_js__WEBPACK_IMPORTED_MODULE_7__.wrap)(alg, key, cek);\n            break;\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            cek = providedCek || (0,_lib_cek_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(enc);\n            const { iv } = providedParameters;\n            ({ encryptedKey, ...parameters } = await (0,_aesgcmkw_js__WEBPACK_IMPORTED_MODULE_10__.wrap)(alg, key, cek, iv));\n            break;\n        }\n        default: {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n    return { cek, encryptedKey, parameters };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (encryptKeyManagement);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vbGliL2VuY3J5cHRfa2V5X21hbmFnZW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQW9EO0FBQ1A7QUFDYztBQUNKO0FBQ087QUFDVjtBQUNnQjtBQUNmO0FBQ1I7QUFDRTtBQUNFO0FBQ2pELHVGQUF1RjtBQUN2RjtBQUNBO0FBQ0E7QUFDQSxJQUFJLDhEQUFZO0FBQ2hCLGlCQUFpQixpRUFBUztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsMkRBQWdCO0FBQ2pDLDBCQUEwQiw2REFBZ0I7QUFDMUM7QUFDQSxvQkFBb0IsV0FBVztBQUMvQixrQkFBa0Isb0JBQW9CO0FBQ3RDLG9DQUFvQywyREFBZ0I7QUFDcEQsb0JBQW9CLGlCQUFpQixRQUFRLHlEQUFTO0FBQ3RELHVDQUF1Qyx5REFBYyx1RUFBdUUsc0RBQVM7QUFDckksMkJBQTJCLE9BQU87QUFDbEM7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLDZEQUFTO0FBQzFDO0FBQ0EsaUNBQWlDLDZEQUFTO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLHVEQUFXO0FBQzVDO0FBQ0EsaUNBQWlDLHVEQUFLO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLHVEQUFXO0FBQzVDLGlDQUFpQywwREFBSztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLHVEQUFXO0FBQzVDLG9CQUFvQixXQUFXO0FBQy9CLGVBQWUsOEJBQThCLFFBQVEsNERBQU87QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyx1REFBVztBQUM1QyxpQ0FBaUMsdURBQUs7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyx1REFBVztBQUM1QyxvQkFBb0IsS0FBSztBQUN6QixlQUFlLDhCQUE4QixRQUFRLG1EQUFRO0FBQzdEO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2REFBZ0I7QUFDdEM7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLGlFQUFlLG9CQUFvQixFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcam9zZUA1LjEwLjBcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxsaWJcXGVuY3J5cHRfa2V5X21hbmFnZW1lbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgd3JhcCBhcyBhZXNLdyB9IGZyb20gJy4uL3J1bnRpbWUvYWVza3cuanMnO1xuaW1wb3J0ICogYXMgRUNESCBmcm9tICcuLi9ydW50aW1lL2VjZGhlcy5qcyc7XG5pbXBvcnQgeyBlbmNyeXB0IGFzIHBiZXMyS3cgfSBmcm9tICcuLi9ydW50aW1lL3BiZXMya3cuanMnO1xuaW1wb3J0IHsgZW5jcnlwdCBhcyByc2FFcyB9IGZyb20gJy4uL3J1bnRpbWUvcnNhZXMuanMnO1xuaW1wb3J0IHsgZW5jb2RlIGFzIGJhc2U2NHVybCB9IGZyb20gJy4uL3J1bnRpbWUvYmFzZTY0dXJsLmpzJztcbmltcG9ydCBub3JtYWxpemUgZnJvbSAnLi4vcnVudGltZS9ub3JtYWxpemVfa2V5LmpzJztcbmltcG9ydCBnZW5lcmF0ZUNlaywgeyBiaXRMZW5ndGggYXMgY2VrTGVuZ3RoIH0gZnJvbSAnLi4vbGliL2Nlay5qcyc7XG5pbXBvcnQgeyBKT1NFTm90U3VwcG9ydGVkIH0gZnJvbSAnLi4vdXRpbC9lcnJvcnMuanMnO1xuaW1wb3J0IHsgZXhwb3J0SldLIH0gZnJvbSAnLi4va2V5L2V4cG9ydC5qcyc7XG5pbXBvcnQgY2hlY2tLZXlUeXBlIGZyb20gJy4vY2hlY2tfa2V5X3R5cGUuanMnO1xuaW1wb3J0IHsgd3JhcCBhcyBhZXNHY21LdyB9IGZyb20gJy4vYWVzZ2Nta3cuanMnO1xuYXN5bmMgZnVuY3Rpb24gZW5jcnlwdEtleU1hbmFnZW1lbnQoYWxnLCBlbmMsIGtleSwgcHJvdmlkZWRDZWssIHByb3ZpZGVkUGFyYW1ldGVycyA9IHt9KSB7XG4gICAgbGV0IGVuY3J5cHRlZEtleTtcbiAgICBsZXQgcGFyYW1ldGVycztcbiAgICBsZXQgY2VrO1xuICAgIGNoZWNrS2V5VHlwZShhbGcsIGtleSwgJ2VuY3J5cHQnKTtcbiAgICBrZXkgPSAoYXdhaXQgbm9ybWFsaXplLm5vcm1hbGl6ZVB1YmxpY0tleT8uKGtleSwgYWxnKSkgfHwga2V5O1xuICAgIHN3aXRjaCAoYWxnKSB7XG4gICAgICAgIGNhc2UgJ2Rpcic6IHtcbiAgICAgICAgICAgIGNlayA9IGtleTtcbiAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgJ0VDREgtRVMnOlxuICAgICAgICBjYXNlICdFQ0RILUVTK0ExMjhLVyc6XG4gICAgICAgIGNhc2UgJ0VDREgtRVMrQTE5MktXJzpcbiAgICAgICAgY2FzZSAnRUNESC1FUytBMjU2S1cnOiB7XG4gICAgICAgICAgICBpZiAoIUVDREguZWNkaEFsbG93ZWQoa2V5KSkge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBKT1NFTm90U3VwcG9ydGVkKCdFQ0RIIHdpdGggdGhlIHByb3ZpZGVkIGtleSBpcyBub3QgYWxsb3dlZCBvciBub3Qgc3VwcG9ydGVkIGJ5IHlvdXIgamF2YXNjcmlwdCBydW50aW1lJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCB7IGFwdSwgYXB2IH0gPSBwcm92aWRlZFBhcmFtZXRlcnM7XG4gICAgICAgICAgICBsZXQgeyBlcGs6IGVwaGVtZXJhbEtleSB9ID0gcHJvdmlkZWRQYXJhbWV0ZXJzO1xuICAgICAgICAgICAgZXBoZW1lcmFsS2V5IHx8PSAoYXdhaXQgRUNESC5nZW5lcmF0ZUVwayhrZXkpKS5wcml2YXRlS2V5O1xuICAgICAgICAgICAgY29uc3QgeyB4LCB5LCBjcnYsIGt0eSB9ID0gYXdhaXQgZXhwb3J0SldLKGVwaGVtZXJhbEtleSk7XG4gICAgICAgICAgICBjb25zdCBzaGFyZWRTZWNyZXQgPSBhd2FpdCBFQ0RILmRlcml2ZUtleShrZXksIGVwaGVtZXJhbEtleSwgYWxnID09PSAnRUNESC1FUycgPyBlbmMgOiBhbGcsIGFsZyA9PT0gJ0VDREgtRVMnID8gY2VrTGVuZ3RoKGVuYykgOiBwYXJzZUludChhbGcuc2xpY2UoLTUsIC0yKSwgMTApLCBhcHUsIGFwdik7XG4gICAgICAgICAgICBwYXJhbWV0ZXJzID0geyBlcGs6IHsgeCwgY3J2LCBrdHkgfSB9O1xuICAgICAgICAgICAgaWYgKGt0eSA9PT0gJ0VDJylcbiAgICAgICAgICAgICAgICBwYXJhbWV0ZXJzLmVway55ID0geTtcbiAgICAgICAgICAgIGlmIChhcHUpXG4gICAgICAgICAgICAgICAgcGFyYW1ldGVycy5hcHUgPSBiYXNlNjR1cmwoYXB1KTtcbiAgICAgICAgICAgIGlmIChhcHYpXG4gICAgICAgICAgICAgICAgcGFyYW1ldGVycy5hcHYgPSBiYXNlNjR1cmwoYXB2KTtcbiAgICAgICAgICAgIGlmIChhbGcgPT09ICdFQ0RILUVTJykge1xuICAgICAgICAgICAgICAgIGNlayA9IHNoYXJlZFNlY3JldDtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNlayA9IHByb3ZpZGVkQ2VrIHx8IGdlbmVyYXRlQ2VrKGVuYyk7XG4gICAgICAgICAgICBjb25zdCBrd0FsZyA9IGFsZy5zbGljZSgtNik7XG4gICAgICAgICAgICBlbmNyeXB0ZWRLZXkgPSBhd2FpdCBhZXNLdyhrd0FsZywgc2hhcmVkU2VjcmV0LCBjZWspO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgY2FzZSAnUlNBMV81JzpcbiAgICAgICAgY2FzZSAnUlNBLU9BRVAnOlxuICAgICAgICBjYXNlICdSU0EtT0FFUC0yNTYnOlxuICAgICAgICBjYXNlICdSU0EtT0FFUC0zODQnOlxuICAgICAgICBjYXNlICdSU0EtT0FFUC01MTInOiB7XG4gICAgICAgICAgICBjZWsgPSBwcm92aWRlZENlayB8fCBnZW5lcmF0ZUNlayhlbmMpO1xuICAgICAgICAgICAgZW5jcnlwdGVkS2V5ID0gYXdhaXQgcnNhRXMoYWxnLCBrZXksIGNlayk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICBjYXNlICdQQkVTMi1IUzI1NitBMTI4S1cnOlxuICAgICAgICBjYXNlICdQQkVTMi1IUzM4NCtBMTkyS1cnOlxuICAgICAgICBjYXNlICdQQkVTMi1IUzUxMitBMjU2S1cnOiB7XG4gICAgICAgICAgICBjZWsgPSBwcm92aWRlZENlayB8fCBnZW5lcmF0ZUNlayhlbmMpO1xuICAgICAgICAgICAgY29uc3QgeyBwMmMsIHAycyB9ID0gcHJvdmlkZWRQYXJhbWV0ZXJzO1xuICAgICAgICAgICAgKHsgZW5jcnlwdGVkS2V5LCAuLi5wYXJhbWV0ZXJzIH0gPSBhd2FpdCBwYmVzMkt3KGFsZywga2V5LCBjZWssIHAyYywgcDJzKSk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICBjYXNlICdBMTI4S1cnOlxuICAgICAgICBjYXNlICdBMTkyS1cnOlxuICAgICAgICBjYXNlICdBMjU2S1cnOiB7XG4gICAgICAgICAgICBjZWsgPSBwcm92aWRlZENlayB8fCBnZW5lcmF0ZUNlayhlbmMpO1xuICAgICAgICAgICAgZW5jcnlwdGVkS2V5ID0gYXdhaXQgYWVzS3coYWxnLCBrZXksIGNlayk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICBjYXNlICdBMTI4R0NNS1cnOlxuICAgICAgICBjYXNlICdBMTkyR0NNS1cnOlxuICAgICAgICBjYXNlICdBMjU2R0NNS1cnOiB7XG4gICAgICAgICAgICBjZWsgPSBwcm92aWRlZENlayB8fCBnZW5lcmF0ZUNlayhlbmMpO1xuICAgICAgICAgICAgY29uc3QgeyBpdiB9ID0gcHJvdmlkZWRQYXJhbWV0ZXJzO1xuICAgICAgICAgICAgKHsgZW5jcnlwdGVkS2V5LCAuLi5wYXJhbWV0ZXJzIH0gPSBhd2FpdCBhZXNHY21LdyhhbGcsIGtleSwgY2VrLCBpdikpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgZGVmYXVsdDoge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEpPU0VOb3RTdXBwb3J0ZWQoJ0ludmFsaWQgb3IgdW5zdXBwb3J0ZWQgXCJhbGdcIiAoSldFIEFsZ29yaXRobSkgaGVhZGVyIHZhbHVlJyk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHsgY2VrLCBlbmNyeXB0ZWRLZXksIHBhcmFtZXRlcnMgfTtcbn1cbmV4cG9ydCBkZWZhdWx0IGVuY3J5cHRLZXlNYW5hZ2VtZW50O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/encrypt_key_management.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/epoch.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/epoch.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((date) => Math.floor(date.getTime() / 1000));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vbGliL2Vwb2NoLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSwyQ0FBMkMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGpvc2VANS4xMC4wXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcbGliXFxlcG9jaC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCAoZGF0ZSkgPT4gTWF0aC5mbG9vcihkYXRlLmdldFRpbWUoKSAvIDEwMDApO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/epoch.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   withAlg: () => (/* binding */ withAlg)\n/* harmony export */ });\nfunction message(msg, actual, ...types) {\n    types = types.filter(Boolean);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n});\nfunction withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_disjoint.js":
/*!***********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_disjoint.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst isDisjoint = (...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isDisjoint);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vbGliL2lzX2Rpc2pvaW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBZSxVQUFVLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxqb3NlQDUuMTAuMFxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGxpYlxcaXNfZGlzam9pbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNEaXNqb2ludCA9ICguLi5oZWFkZXJzKSA9PiB7XG4gICAgY29uc3Qgc291cmNlcyA9IGhlYWRlcnMuZmlsdGVyKEJvb2xlYW4pO1xuICAgIGlmIChzb3VyY2VzLmxlbmd0aCA9PT0gMCB8fCBzb3VyY2VzLmxlbmd0aCA9PT0gMSkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgbGV0IGFjYztcbiAgICBmb3IgKGNvbnN0IGhlYWRlciBvZiBzb3VyY2VzKSB7XG4gICAgICAgIGNvbnN0IHBhcmFtZXRlcnMgPSBPYmplY3Qua2V5cyhoZWFkZXIpO1xuICAgICAgICBpZiAoIWFjYyB8fCBhY2Muc2l6ZSA9PT0gMCkge1xuICAgICAgICAgICAgYWNjID0gbmV3IFNldChwYXJhbWV0ZXJzKTtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIGZvciAoY29uc3QgcGFyYW1ldGVyIG9mIHBhcmFtZXRlcnMpIHtcbiAgICAgICAgICAgIGlmIChhY2MuaGFzKHBhcmFtZXRlcikpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBhY2MuYWRkKHBhcmFtZXRlcik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59O1xuZXhwb3J0IGRlZmF1bHQgaXNEaXNqb2ludDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_disjoint.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_jwk.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_jwk.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isJWK: () => (/* binding */ isJWK),\n/* harmony export */   isPrivateJWK: () => (/* binding */ isPrivateJWK),\n/* harmony export */   isPublicJWK: () => (/* binding */ isPublicJWK),\n/* harmony export */   isSecretJWK: () => (/* binding */ isSecretJWK)\n/* harmony export */ });\n/* harmony import */ var _is_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_object.js\");\n\nfunction isJWK(key) {\n    return (0,_is_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(key) && typeof key.kty === 'string';\n}\nfunction isPrivateJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'string';\n}\nfunction isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined';\n}\nfunction isSecretJWK(key) {\n    return isJWK(key) && key.kty === 'oct' && typeof key.k === 'string';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vbGliL2lzX2p3ay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFzQztBQUMvQjtBQUNQLFdBQVcseURBQVE7QUFDbkI7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxqb3NlQDUuMTAuMFxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGxpYlxcaXNfandrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBpc09iamVjdCBmcm9tICcuL2lzX29iamVjdC5qcyc7XG5leHBvcnQgZnVuY3Rpb24gaXNKV0soa2V5KSB7XG4gICAgcmV0dXJuIGlzT2JqZWN0KGtleSkgJiYgdHlwZW9mIGtleS5rdHkgPT09ICdzdHJpbmcnO1xufVxuZXhwb3J0IGZ1bmN0aW9uIGlzUHJpdmF0ZUpXSyhrZXkpIHtcbiAgICByZXR1cm4ga2V5Lmt0eSAhPT0gJ29jdCcgJiYgdHlwZW9mIGtleS5kID09PSAnc3RyaW5nJztcbn1cbmV4cG9ydCBmdW5jdGlvbiBpc1B1YmxpY0pXSyhrZXkpIHtcbiAgICByZXR1cm4ga2V5Lmt0eSAhPT0gJ29jdCcgJiYgdHlwZW9mIGtleS5kID09PSAndW5kZWZpbmVkJztcbn1cbmV4cG9ydCBmdW5jdGlvbiBpc1NlY3JldEpXSyhrZXkpIHtcbiAgICByZXR1cm4gaXNKV0soa2V5KSAmJiBrZXkua3R5ID09PSAnb2N0JyAmJiB0eXBlb2Yga2V5LmsgPT09ICdzdHJpbmcnO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_jwk.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_object.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_object.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ isObject)\n/* harmony export */ });\nfunction isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nfunction isObject(input) {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vbGliL2lzX29iamVjdC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ2U7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxqb3NlQDUuMTAuMFxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGxpYlxcaXNfb2JqZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGlzT2JqZWN0TGlrZSh2YWx1ZSkge1xuICAgIHJldHVybiB0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnICYmIHZhbHVlICE9PSBudWxsO1xufVxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gaXNPYmplY3QoaW5wdXQpIHtcbiAgICBpZiAoIWlzT2JqZWN0TGlrZShpbnB1dCkgfHwgT2JqZWN0LnByb3RvdHlwZS50b1N0cmluZy5jYWxsKGlucHV0KSAhPT0gJ1tvYmplY3QgT2JqZWN0XScpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAoT2JqZWN0LmdldFByb3RvdHlwZU9mKGlucHV0KSA9PT0gbnVsbCkge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgbGV0IHByb3RvID0gaW5wdXQ7XG4gICAgd2hpbGUgKE9iamVjdC5nZXRQcm90b3R5cGVPZihwcm90bykgIT09IG51bGwpIHtcbiAgICAgICAgcHJvdG8gPSBPYmplY3QuZ2V0UHJvdG90eXBlT2YocHJvdG8pO1xuICAgIH1cbiAgICByZXR1cm4gT2JqZWN0LmdldFByb3RvdHlwZU9mKGlucHV0KSA9PT0gcHJvdG87XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_object.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/iv.js":
/*!**************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/iv.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bitLength: () => (/* binding */ bitLength),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _runtime_random_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../runtime/random.js */ \"node:crypto\");\n\n\nfunction bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A128GCMKW':\n        case 'A192GCM':\n        case 'A192GCMKW':\n        case 'A256GCM':\n        case 'A256GCMKW':\n            return 96;\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return 128;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((alg) => (0,_runtime_random_js__WEBPACK_IMPORTED_MODULE_1__.randomFillSync)(new Uint8Array(bitLength(alg) >> 3)));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vbGliL2l2LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBcUQ7QUFDWDtBQUNuQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDZEQUFnQiwrQkFBK0IsSUFBSTtBQUN6RTtBQUNBO0FBQ0EsaUVBQWUsU0FBUyxrRUFBTSxxQ0FBcUMsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGpvc2VANS4xMC4wXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxcbGliXFxpdi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBKT1NFTm90U3VwcG9ydGVkIH0gZnJvbSAnLi4vdXRpbC9lcnJvcnMuanMnO1xuaW1wb3J0IHJhbmRvbSBmcm9tICcuLi9ydW50aW1lL3JhbmRvbS5qcyc7XG5leHBvcnQgZnVuY3Rpb24gYml0TGVuZ3RoKGFsZykge1xuICAgIHN3aXRjaCAoYWxnKSB7XG4gICAgICAgIGNhc2UgJ0ExMjhHQ00nOlxuICAgICAgICBjYXNlICdBMTI4R0NNS1cnOlxuICAgICAgICBjYXNlICdBMTkyR0NNJzpcbiAgICAgICAgY2FzZSAnQTE5MkdDTUtXJzpcbiAgICAgICAgY2FzZSAnQTI1NkdDTSc6XG4gICAgICAgIGNhc2UgJ0EyNTZHQ01LVyc6XG4gICAgICAgICAgICByZXR1cm4gOTY7XG4gICAgICAgIGNhc2UgJ0ExMjhDQkMtSFMyNTYnOlxuICAgICAgICBjYXNlICdBMTkyQ0JDLUhTMzg0JzpcbiAgICAgICAgY2FzZSAnQTI1NkNCQy1IUzUxMic6XG4gICAgICAgICAgICByZXR1cm4gMTI4O1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgdGhyb3cgbmV3IEpPU0VOb3RTdXBwb3J0ZWQoYFVuc3VwcG9ydGVkIEpXRSBBbGdvcml0aG06ICR7YWxnfWApO1xuICAgIH1cbn1cbmV4cG9ydCBkZWZhdWx0IChhbGcpID0+IHJhbmRvbShuZXcgVWludDhBcnJheShiaXRMZW5ndGgoYWxnKSA+PiAzKSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/iv.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/jwt_claims_set.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/jwt_claims_set.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _epoch_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./epoch.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/epoch.js\");\n/* harmony import */ var _secs_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./secs.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/secs.js\");\n/* harmony import */ var _is_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_object.js\");\n\n\n\n\n\nconst normalizeTyp = (value) => value.toLowerCase().replace(/^application\\//, '');\nconst checkAudiencePresence = (audPayload, audOption) => {\n    if (typeof audPayload === 'string') {\n        return audOption.includes(audPayload);\n    }\n    if (Array.isArray(audPayload)) {\n        return audOption.some(Set.prototype.has.bind(new Set(audPayload)));\n    }\n    return false;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((protectedHeader, encodedPayload, options = {}) => {\n    let payload;\n    try {\n        payload = JSON.parse(_buffer_utils_js__WEBPACK_IMPORTED_MODULE_0__.decoder.decode(encodedPayload));\n    }\n    catch {\n    }\n    if (!(0,_is_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(payload)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTInvalid('JWT Claims Set must be a top-level JSON object');\n    }\n    const { typ } = options;\n    if (typ &&\n        (typeof protectedHeader.typ !== 'string' ||\n            normalizeTyp(protectedHeader.typ) !== normalizeTyp(typ))) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"typ\" JWT header value', payload, 'typ', 'check_failed');\n    }\n    const { requiredClaims = [], issuer, subject, audience, maxTokenAge } = options;\n    const presenceCheck = [...requiredClaims];\n    if (maxTokenAge !== undefined)\n        presenceCheck.push('iat');\n    if (audience !== undefined)\n        presenceCheck.push('aud');\n    if (subject !== undefined)\n        presenceCheck.push('sub');\n    if (issuer !== undefined)\n        presenceCheck.push('iss');\n    for (const claim of new Set(presenceCheck.reverse())) {\n        if (!(claim in payload)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed(`missing required \"${claim}\" claim`, payload, claim, 'missing');\n        }\n    }\n    if (issuer &&\n        !(Array.isArray(issuer) ? issuer : [issuer]).includes(payload.iss)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"iss\" claim value', payload, 'iss', 'check_failed');\n    }\n    if (subject && payload.sub !== subject) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"sub\" claim value', payload, 'sub', 'check_failed');\n    }\n    if (audience &&\n        !checkAudiencePresence(payload.aud, typeof audience === 'string' ? [audience] : audience)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('unexpected \"aud\" claim value', payload, 'aud', 'check_failed');\n    }\n    let tolerance;\n    switch (typeof options.clockTolerance) {\n        case 'string':\n            tolerance = (0,_secs_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(options.clockTolerance);\n            break;\n        case 'number':\n            tolerance = options.clockTolerance;\n            break;\n        case 'undefined':\n            tolerance = 0;\n            break;\n        default:\n            throw new TypeError('Invalid clockTolerance option type');\n    }\n    const { currentDate } = options;\n    const now = (0,_epoch_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(currentDate || new Date());\n    if ((payload.iat !== undefined || maxTokenAge) && typeof payload.iat !== 'number') {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"iat\" claim must be a number', payload, 'iat', 'invalid');\n    }\n    if (payload.nbf !== undefined) {\n        if (typeof payload.nbf !== 'number') {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"nbf\" claim must be a number', payload, 'nbf', 'invalid');\n        }\n        if (payload.nbf > now + tolerance) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"nbf\" claim timestamp check failed', payload, 'nbf', 'check_failed');\n        }\n    }\n    if (payload.exp !== undefined) {\n        if (typeof payload.exp !== 'number') {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"exp\" claim must be a number', payload, 'exp', 'invalid');\n        }\n        if (payload.exp <= now - tolerance) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTExpired('\"exp\" claim timestamp check failed', payload, 'exp', 'check_failed');\n        }\n    }\n    if (maxTokenAge) {\n        const age = now - payload.iat;\n        const max = typeof maxTokenAge === 'number' ? maxTokenAge : (0,_secs_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(maxTokenAge);\n        if (age - tolerance > max) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTExpired('\"iat\" claim timestamp check failed (too far in the past)', payload, 'iat', 'check_failed');\n        }\n        if (age < 0 - tolerance) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_2__.JWTClaimValidationFailed('\"iat\" claim timestamp check failed (it should be in the past)', payload, 'iat', 'check_failed');\n        }\n    }\n    return payload;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/jwt_claims_set.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/private_symbols.js":
/*!***************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/private_symbols.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unprotected: () => (/* binding */ unprotected)\n/* harmony export */ });\nconst unprotected = Symbol();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vbGliL3ByaXZhdGVfc3ltYm9scy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxqb3NlQDUuMTAuMFxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXGxpYlxccHJpdmF0ZV9zeW1ib2xzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCB1bnByb3RlY3RlZCA9IFN5bWJvbCgpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/private_symbols.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/secs.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/secs.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/secs.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/validate_algorithms.js":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/validate_algorithms.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst validateAlgorithms = (option, algorithms) => {\n    if (algorithms !== undefined &&\n        (!Array.isArray(algorithms) || algorithms.some((s) => typeof s !== 'string'))) {\n        throw new TypeError(`\"${option}\" option must be an array of strings`);\n    }\n    if (!algorithms) {\n        return undefined;\n    }\n    return new Set(algorithms);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validateAlgorithms);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vbGliL3ZhbGlkYXRlX2FsZ29yaXRobXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxPQUFPO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFlLGtCQUFrQixFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcam9zZUA1LjEwLjBcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxsaWJcXHZhbGlkYXRlX2FsZ29yaXRobXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgdmFsaWRhdGVBbGdvcml0aG1zID0gKG9wdGlvbiwgYWxnb3JpdGhtcykgPT4ge1xuICAgIGlmIChhbGdvcml0aG1zICE9PSB1bmRlZmluZWQgJiZcbiAgICAgICAgKCFBcnJheS5pc0FycmF5KGFsZ29yaXRobXMpIHx8IGFsZ29yaXRobXMuc29tZSgocykgPT4gdHlwZW9mIHMgIT09ICdzdHJpbmcnKSkpIHtcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihgXCIke29wdGlvbn1cIiBvcHRpb24gbXVzdCBiZSBhbiBhcnJheSBvZiBzdHJpbmdzYCk7XG4gICAgfVxuICAgIGlmICghYWxnb3JpdGhtcykge1xuICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIH1cbiAgICByZXR1cm4gbmV3IFNldChhbGdvcml0aG1zKTtcbn07XG5leHBvcnQgZGVmYXVsdCB2YWxpZGF0ZUFsZ29yaXRobXM7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/validate_algorithms.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/validate_crit.js":
/*!*************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/validate_crit.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n\nfunction validateCrit(Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) {\n    if (joseHeader.crit !== undefined && protectedHeader?.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validateCrit);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/validate_crit.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/aeskw.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/aeskw.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var node_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:buffer */ \"node:buffer\");\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _ciphers_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ciphers.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/ciphers.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\nfunction checkKeySize(key, alg) {\n    if (key.symmetricKeySize << 3 !== parseInt(alg.slice(1, 4), 10)) {\n        throw new TypeError(`Invalid key size for alg: ${alg}`);\n    }\n}\nfunction ensureKeyObject(key, alg, usage) {\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key)) {\n        return key;\n    }\n    if (key instanceof Uint8Array) {\n        return (0,node_crypto__WEBPACK_IMPORTED_MODULE_1__.createSecretKey)(key);\n    }\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_3__.isCryptoKey)(key)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__.checkEncCryptoKey)(key, alg, usage);\n        return node_crypto__WEBPACK_IMPORTED_MODULE_1__.KeyObject.from(key);\n    }\n    throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types, 'Uint8Array'));\n}\nconst wrap = (alg, key, cek) => {\n    const size = parseInt(alg.slice(1, 4), 10);\n    const algorithm = `aes${size}-wrap`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_8__.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    const keyObject = ensureKeyObject(key, alg, 'wrapKey');\n    checkKeySize(keyObject, alg);\n    const cipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_1__.createCipheriv)(algorithm, keyObject, node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.alloc(8, 0xa6));\n    return (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_9__.concat)(cipher.update(cek), cipher.final());\n};\nconst unwrap = (alg, key, encryptedKey) => {\n    const size = parseInt(alg.slice(1, 4), 10);\n    const algorithm = `aes${size}-wrap`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_8__.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    const keyObject = ensureKeyObject(key, alg, 'unwrapKey');\n    checkKeySize(keyObject, alg);\n    const cipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_1__.createDecipheriv)(algorithm, keyObject, node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.alloc(8, 0xa6));\n    return (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_9__.concat)(cipher.update(encryptedKey), cipher.final());\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/aeskw.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/asn1.js":
/*!********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/asn1.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromPKCS8: () => (/* binding */ fromPKCS8),\n/* harmony export */   fromSPKI: () => (/* binding */ fromSPKI),\n/* harmony export */   fromX509: () => (/* binding */ fromX509),\n/* harmony export */   toPKCS8: () => (/* binding */ toPKCS8),\n/* harmony export */   toSPKI: () => (/* binding */ toSPKI)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_buffer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:buffer */ \"node:buffer\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\nconst genericExport = (keyType, keyFormat, key) => {\n    let keyObject;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(key)) {\n        if (!key.extractable) {\n            throw new TypeError('CryptoKey is not extractable');\n        }\n        keyObject = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(key);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(key)) {\n        keyObject = key;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_5__.types));\n    }\n    if (keyObject.type !== keyType) {\n        throw new TypeError(`key is not a ${keyType} key`);\n    }\n    return keyObject.export({ format: 'pem', type: keyFormat });\n};\nconst toSPKI = (key) => {\n    return genericExport('public', 'spki', key);\n};\nconst toPKCS8 = (key) => {\n    return genericExport('private', 'pkcs8', key);\n};\nconst fromPKCS8 = (pem) => (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPrivateKey)({\n    key: node_buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(pem.replace(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\\s)/g, ''), 'base64'),\n    type: 'pkcs8',\n    format: 'der',\n});\nconst fromSPKI = (pem) => (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPublicKey)({\n    key: node_buffer__WEBPACK_IMPORTED_MODULE_1__.Buffer.from(pem.replace(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\\s)/g, ''), 'base64'),\n    type: 'spki',\n    format: 'der',\n});\nconst fromX509 = (pem) => (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPublicKey)({\n    key: pem,\n    type: 'spki',\n    format: 'pem',\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/asn1.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js":
/*!*************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   decodeBase64: () => (/* binding */ decodeBase64),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   encodeBase64: () => (/* binding */ encodeBase64)\n/* harmony export */ });\n/* harmony import */ var node_buffer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:buffer */ \"node:buffer\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\nfunction normalize(input) {\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.decoder.decode(encoded);\n    }\n    return encoded;\n}\nconst encode = (input) => node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString('base64url');\nconst decodeBase64 = (input) => new Uint8Array(node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input, 'base64'));\nconst encodeBase64 = (input) => node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(input).toString('base64');\n\nconst decode = (input) => new Uint8Array(node_buffer__WEBPACK_IMPORTED_MODULE_0__.Buffer.from(normalize(input), 'base64url'));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS9iYXNlNjR1cmwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXFDO0FBQ1k7QUFDakQ7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHlEQUFPO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQiwrQ0FBTTtBQUN6QiwrQ0FBK0MsK0NBQU07QUFDckQsZ0NBQWdDLCtDQUFNO0FBQzNCO0FBQ1gseUNBQXlDLCtDQUFNIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcam9zZUA1LjEwLjBcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxydW50aW1lXFxiYXNlNjR1cmwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQnVmZmVyIH0gZnJvbSAnbm9kZTpidWZmZXInO1xuaW1wb3J0IHsgZGVjb2RlciB9IGZyb20gJy4uL2xpYi9idWZmZXJfdXRpbHMuanMnO1xuZnVuY3Rpb24gbm9ybWFsaXplKGlucHV0KSB7XG4gICAgbGV0IGVuY29kZWQgPSBpbnB1dDtcbiAgICBpZiAoZW5jb2RlZCBpbnN0YW5jZW9mIFVpbnQ4QXJyYXkpIHtcbiAgICAgICAgZW5jb2RlZCA9IGRlY29kZXIuZGVjb2RlKGVuY29kZWQpO1xuICAgIH1cbiAgICByZXR1cm4gZW5jb2RlZDtcbn1cbmNvbnN0IGVuY29kZSA9IChpbnB1dCkgPT4gQnVmZmVyLmZyb20oaW5wdXQpLnRvU3RyaW5nKCdiYXNlNjR1cmwnKTtcbmV4cG9ydCBjb25zdCBkZWNvZGVCYXNlNjQgPSAoaW5wdXQpID0+IG5ldyBVaW50OEFycmF5KEJ1ZmZlci5mcm9tKGlucHV0LCAnYmFzZTY0JykpO1xuZXhwb3J0IGNvbnN0IGVuY29kZUJhc2U2NCA9IChpbnB1dCkgPT4gQnVmZmVyLmZyb20oaW5wdXQpLnRvU3RyaW5nKCdiYXNlNjQnKTtcbmV4cG9ydCB7IGVuY29kZSB9O1xuZXhwb3J0IGNvbnN0IGRlY29kZSA9IChpbnB1dCkgPT4gbmV3IFVpbnQ4QXJyYXkoQnVmZmVyLmZyb20obm9ybWFsaXplKGlucHV0KSwgJ2Jhc2U2NHVybCcpKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/cbc_tag.js":
/*!***********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/cbc_tag.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ cbcTag)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n\n\nfunction cbcTag(aad, iv, ciphertext, macSize, macKey, keySize) {\n    const macData = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.concat)(aad, iv, ciphertext, (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_1__.uint64be)(aad.length << 3));\n    const hmac = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createHmac)(`sha${macSize}`, macKey);\n    hmac.update(macData);\n    return hmac.digest().slice(0, keySize >> 3);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS9jYmNfdGFnLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF5QztBQUNpQjtBQUMzQztBQUNmLG9CQUFvQiw0REFBTSxzQkFBc0IsOERBQVE7QUFDeEQsaUJBQWlCLHVEQUFVLE9BQU8sUUFBUTtBQUMxQztBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxqb3NlQDUuMTAuMFxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXGNiY190YWcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlSG1hYyB9IGZyb20gJ25vZGU6Y3J5cHRvJztcbmltcG9ydCB7IGNvbmNhdCwgdWludDY0YmUgfSBmcm9tICcuLi9saWIvYnVmZmVyX3V0aWxzLmpzJztcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNiY1RhZyhhYWQsIGl2LCBjaXBoZXJ0ZXh0LCBtYWNTaXplLCBtYWNLZXksIGtleVNpemUpIHtcbiAgICBjb25zdCBtYWNEYXRhID0gY29uY2F0KGFhZCwgaXYsIGNpcGhlcnRleHQsIHVpbnQ2NGJlKGFhZC5sZW5ndGggPDwgMykpO1xuICAgIGNvbnN0IGhtYWMgPSBjcmVhdGVIbWFjKGBzaGEke21hY1NpemV9YCwgbWFjS2V5KTtcbiAgICBobWFjLnVwZGF0ZShtYWNEYXRhKTtcbiAgICByZXR1cm4gaG1hYy5kaWdlc3QoKS5zbGljZSgwLCBrZXlTaXplID4+IDMpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/cbc_tag.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/check_cek_length.js":
/*!********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/check_cek_length.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n\n\nconst checkCekLength = (enc, cek) => {\n    let expected;\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            expected = parseInt(enc.slice(-3), 10);\n            break;\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            expected = parseInt(enc.slice(1, 4), 10);\n            break;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`Content Encryption Algorithm ${enc} is not supported either by JOSE or your javascript runtime`);\n    }\n    if (cek instanceof Uint8Array) {\n        const actual = cek.byteLength << 3;\n        if (actual !== expected) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n        }\n        return;\n    }\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek) && cek.type === 'secret') {\n        const actual = cek.symmetricKeySize << 3;\n        if (actual !== expected) {\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n        }\n        return;\n    }\n    throw new TypeError('Invalid Content Encryption Key type');\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (checkCekLength);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/check_cek_length.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/check_key_length.js":
/*!********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/check_key_length.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((key, alg) => {\n    let modulusLength;\n    try {\n        if (key instanceof node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject) {\n            modulusLength = key.asymmetricKeyDetails?.modulusLength;\n        }\n        else {\n            modulusLength = Buffer.from(key.n, 'base64url').byteLength << 3;\n        }\n    }\n    catch { }\n    if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n        throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS9jaGVja19rZXlfbGVuZ3RoLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXdDO0FBQ3hDLGlFQUFlO0FBQ2Y7QUFDQTtBQUNBLDJCQUEyQixrREFBUztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLEtBQUs7QUFDcEM7QUFDQSxDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxqb3NlQDUuMTAuMFxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXGNoZWNrX2tleV9sZW5ndGguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgS2V5T2JqZWN0IH0gZnJvbSAnbm9kZTpjcnlwdG8nO1xuZXhwb3J0IGRlZmF1bHQgKGtleSwgYWxnKSA9PiB7XG4gICAgbGV0IG1vZHVsdXNMZW5ndGg7XG4gICAgdHJ5IHtcbiAgICAgICAgaWYgKGtleSBpbnN0YW5jZW9mIEtleU9iamVjdCkge1xuICAgICAgICAgICAgbW9kdWx1c0xlbmd0aCA9IGtleS5hc3ltbWV0cmljS2V5RGV0YWlscz8ubW9kdWx1c0xlbmd0aDtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIG1vZHVsdXNMZW5ndGggPSBCdWZmZXIuZnJvbShrZXkubiwgJ2Jhc2U2NHVybCcpLmJ5dGVMZW5ndGggPDwgMztcbiAgICAgICAgfVxuICAgIH1cbiAgICBjYXRjaCB7IH1cbiAgICBpZiAodHlwZW9mIG1vZHVsdXNMZW5ndGggIT09ICdudW1iZXInIHx8IG1vZHVsdXNMZW5ndGggPCAyMDQ4KSB7XG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYCR7YWxnfSByZXF1aXJlcyBrZXkgbW9kdWx1c0xlbmd0aCB0byBiZSAyMDQ4IGJpdHMgb3IgbGFyZ2VyYCk7XG4gICAgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/check_key_length.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/ciphers.js":
/*!***********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/ciphers.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\nlet ciphers;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((algorithm) => {\n    ciphers ||= new Set((0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.getCiphers)());\n    return ciphers.has(algorithm);\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS9jaXBoZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXlDO0FBQ3pDO0FBQ0EsaUVBQWU7QUFDZix3QkFBd0IsdURBQVU7QUFDbEM7QUFDQSxDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxqb3NlQDUuMTAuMFxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXGNpcGhlcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0Q2lwaGVycyB9IGZyb20gJ25vZGU6Y3J5cHRvJztcbmxldCBjaXBoZXJzO1xuZXhwb3J0IGRlZmF1bHQgKGFsZ29yaXRobSkgPT4ge1xuICAgIGNpcGhlcnMgfHw9IG5ldyBTZXQoZ2V0Q2lwaGVycygpKTtcbiAgICByZXR1cm4gY2lwaGVycy5oYXMoYWxnb3JpdGhtKTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/ciphers.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/decrypt.js":
/*!***********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/decrypt.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _lib_check_iv_length_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../lib/check_iv_length.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_iv_length.js\");\n/* harmony import */ var _check_cek_length_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./check_cek_length.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/check_cek_length.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _timing_safe_equal_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./timing_safe_equal.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js\");\n/* harmony import */ var _cbc_tag_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./cbc_tag.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/cbc_tag.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _ciphers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ciphers.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/ciphers.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction cbcDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek)) {\n        cek = cek.export();\n    }\n    const encKey = cek.subarray(keySize >> 3);\n    const macKey = cek.subarray(0, keySize >> 3);\n    const macSize = parseInt(enc.slice(-3), 10);\n    const algorithm = `aes-${keySize}-cbc`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const expectedTag = (0,_cbc_tag_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(aad, iv, ciphertext, macSize, macKey, keySize);\n    let macCheckPassed;\n    try {\n        macCheckPassed = (0,_timing_safe_equal_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(tag, expectedTag);\n    }\n    catch {\n    }\n    if (!macCheckPassed) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEDecryptionFailed();\n    }\n    let plaintext;\n    try {\n        const decipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createDecipheriv)(algorithm, encKey, iv);\n        plaintext = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_6__.concat)(decipher.update(ciphertext), decipher.final());\n    }\n    catch {\n    }\n    if (!plaintext) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEDecryptionFailed();\n    }\n    return plaintext;\n}\nfunction gcmDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const algorithm = `aes-${keySize}-gcm`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    try {\n        const decipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createDecipheriv)(algorithm, cek, iv, { authTagLength: 16 });\n        decipher.setAuthTag(tag);\n        if (aad.byteLength) {\n            decipher.setAAD(aad, { plaintextLength: ciphertext.length });\n        }\n        const plaintext = decipher.update(ciphertext);\n        decipher.final();\n        return plaintext;\n    }\n    catch {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEDecryptionFailed();\n    }\n}\nconst decrypt = (enc, cek, ciphertext, iv, tag, aad) => {\n    let key;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_7__.isCryptoKey)(cek)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_8__.checkEncCryptoKey)(cek, enc, 'decrypt');\n        key = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(cek);\n    }\n    else if (cek instanceof Uint8Array || (0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek)) {\n        key = cek;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(cek, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_10__.types, 'Uint8Array'));\n    }\n    if (!iv) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEInvalid('JWE Initialization Vector missing');\n    }\n    if (!tag) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JWEInvalid('JWE Authentication Tag missing');\n    }\n    (0,_check_cek_length_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(enc, key);\n    (0,_lib_check_iv_length_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(enc, iv);\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return cbcDecrypt(enc, key, ciphertext, iv, tag, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            return gcmDecrypt(enc, key, ciphertext, iv, tag, aad);\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (decrypt);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/decrypt.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/digest.js":
/*!**********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/digest.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\nconst digest = (algorithm, data) => (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createHash)(algorithm).update(data).digest();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (digest);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS9kaWdlc3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUM7QUFDekMsb0NBQW9DLHVEQUFVO0FBQzlDLGlFQUFlLE1BQU0sRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGpvc2VANS4xMC4wXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcZGlnZXN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUhhc2ggfSBmcm9tICdub2RlOmNyeXB0byc7XG5jb25zdCBkaWdlc3QgPSAoYWxnb3JpdGhtLCBkYXRhKSA9PiBjcmVhdGVIYXNoKGFsZ29yaXRobSkudXBkYXRlKGRhdGEpLmRpZ2VzdCgpO1xuZXhwb3J0IGRlZmF1bHQgZGlnZXN0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/digest.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/dsa_digest.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/dsa_digest.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ dsaDigest)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n\nfunction dsaDigest(alg) {\n    switch (alg) {\n        case 'PS256':\n        case 'RS256':\n        case 'ES256':\n        case 'ES256K':\n            return 'sha256';\n        case 'PS384':\n        case 'RS384':\n        case 'ES384':\n            return 'sha384';\n        case 'PS512':\n        case 'RS512':\n        case 'ES512':\n            return 'sha512';\n        case 'Ed25519':\n        case 'EdDSA':\n            return undefined;\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS9kc2FfZGlnZXN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFEO0FBQ3RDO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDZEQUFnQixRQUFRLEtBQUs7QUFDbkQ7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGpvc2VANS4xMC4wXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcZHNhX2RpZ2VzdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBKT1NFTm90U3VwcG9ydGVkIH0gZnJvbSAnLi4vdXRpbC9lcnJvcnMuanMnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZHNhRGlnZXN0KGFsZykge1xuICAgIHN3aXRjaCAoYWxnKSB7XG4gICAgICAgIGNhc2UgJ1BTMjU2JzpcbiAgICAgICAgY2FzZSAnUlMyNTYnOlxuICAgICAgICBjYXNlICdFUzI1Nic6XG4gICAgICAgIGNhc2UgJ0VTMjU2Syc6XG4gICAgICAgICAgICByZXR1cm4gJ3NoYTI1Nic7XG4gICAgICAgIGNhc2UgJ1BTMzg0JzpcbiAgICAgICAgY2FzZSAnUlMzODQnOlxuICAgICAgICBjYXNlICdFUzM4NCc6XG4gICAgICAgICAgICByZXR1cm4gJ3NoYTM4NCc7XG4gICAgICAgIGNhc2UgJ1BTNTEyJzpcbiAgICAgICAgY2FzZSAnUlM1MTInOlxuICAgICAgICBjYXNlICdFUzUxMic6XG4gICAgICAgICAgICByZXR1cm4gJ3NoYTUxMic7XG4gICAgICAgIGNhc2UgJ0VkMjU1MTknOlxuICAgICAgICBjYXNlICdFZERTQSc6XG4gICAgICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgdGhyb3cgbmV3IEpPU0VOb3RTdXBwb3J0ZWQoYGFsZyAke2FsZ30gaXMgbm90IHN1cHBvcnRlZCBlaXRoZXIgYnkgSk9TRSBvciB5b3VyIGphdmFzY3JpcHQgcnVudGltZWApO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/dsa_digest.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/ecdhes.js":
/*!**********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/ecdhes.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deriveKey: () => (/* binding */ deriveKey),\n/* harmony export */   ecdhAllowed: () => (/* binding */ ecdhAllowed),\n/* harmony export */   generateEpk: () => (/* binding */ generateEpk)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var _get_named_curve_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./get_named_curve.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/get_named_curve.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\nconst generateKeyPair = (0,node_util__WEBPACK_IMPORTED_MODULE_1__.promisify)(node_crypto__WEBPACK_IMPORTED_MODULE_0__.generateKeyPair);\nasync function deriveKey(publicKee, privateKee, algorithm, keyLength, apu = new Uint8Array(0), apv = new Uint8Array(0)) {\n    let publicKey;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(publicKee)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_3__.checkEncCryptoKey)(publicKee, 'ECDH');\n        publicKey = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(publicKee);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(publicKee)) {\n        publicKey = publicKee;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(publicKee, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types));\n    }\n    let privateKey;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(privateKee)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_3__.checkEncCryptoKey)(privateKee, 'ECDH', 'deriveBits');\n        privateKey = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(privateKee);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(privateKee)) {\n        privateKey = privateKee;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(privateKee, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types));\n    }\n    const value = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.concat)((0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.lengthAndInput)(_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.encoder.encode(algorithm)), (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.lengthAndInput)(apu), (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.lengthAndInput)(apv), (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.uint32be)(keyLength));\n    const sharedSecret = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.diffieHellman)({ privateKey, publicKey });\n    return (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_7__.concatKdf)(sharedSecret, keyLength, value);\n}\nasync function generateEpk(kee) {\n    let key;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(kee)) {\n        key = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(kee);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(kee)) {\n        key = kee;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(kee, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types));\n    }\n    switch (key.asymmetricKeyType) {\n        case 'x25519':\n            return generateKeyPair('x25519');\n        case 'x448': {\n            return generateKeyPair('x448');\n        }\n        case 'ec': {\n            const namedCurve = (0,_get_named_curve_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(key);\n            return generateKeyPair('ec', { namedCurve });\n        }\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_9__.JOSENotSupported('Invalid or unsupported EPK');\n    }\n}\nconst ecdhAllowed = (key) => ['P-256', 'P-384', 'P-521', 'X25519', 'X448'].includes((0,_get_named_curve_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(key));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/ecdhes.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/encrypt.js":
/*!***********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/encrypt.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _lib_check_iv_length_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../lib/check_iv_length.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_iv_length.js\");\n/* harmony import */ var _check_cek_length_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./check_cek_length.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/check_cek_length.js\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _cbc_tag_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./cbc_tag.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/cbc_tag.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _lib_iv_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../lib/iv.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/iv.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _ciphers_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ciphers.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/ciphers.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction cbcEncrypt(enc, plaintext, cek, iv, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek)) {\n        cek = cek.export();\n    }\n    const encKey = cek.subarray(keySize >> 3);\n    const macKey = cek.subarray(0, keySize >> 3);\n    const algorithm = `aes-${keySize}-cbc`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const cipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createCipheriv)(algorithm, encKey, iv);\n    const ciphertext = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_4__.concat)(cipher.update(plaintext), cipher.final());\n    const macSize = parseInt(enc.slice(-3), 10);\n    const tag = (0,_cbc_tag_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(aad, iv, ciphertext, macSize, macKey, keySize);\n    return { ciphertext, tag, iv };\n}\nfunction gcmEncrypt(enc, plaintext, cek, iv, aad) {\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const algorithm = `aes-${keySize}-gcm`;\n    if (!(0,_ciphers_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(algorithm)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${enc} is not supported by your javascript runtime`);\n    }\n    const cipher = (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createCipheriv)(algorithm, cek, iv, { authTagLength: 16 });\n    if (aad.byteLength) {\n        cipher.setAAD(aad, { plaintextLength: plaintext.length });\n    }\n    const ciphertext = cipher.update(plaintext);\n    cipher.final();\n    const tag = cipher.getAuthTag();\n    return { ciphertext, tag, iv };\n}\nconst encrypt = (enc, plaintext, cek, iv, aad) => {\n    let key;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_6__.isCryptoKey)(cek)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_7__.checkEncCryptoKey)(cek, enc, 'encrypt');\n        key = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(cek);\n    }\n    else if (cek instanceof Uint8Array || (0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cek)) {\n        key = cek;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(cek, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_9__.types, 'Uint8Array'));\n    }\n    (0,_check_cek_length_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(enc, key);\n    if (iv) {\n        (0,_lib_check_iv_length_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(enc, iv);\n    }\n    else {\n        iv = (0,_lib_iv_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(enc);\n    }\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return cbcEncrypt(enc, plaintext, key, iv, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            return gcmEncrypt(enc, plaintext, key, iv, aad);\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (encrypt);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/encrypt.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/get_named_curve.js":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/get_named_curve.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   weakMap: () => (/* binding */ weakMap)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n/* harmony import */ var _lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/is_jwk.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_jwk.js\");\n\n\n\n\n\n\n\nconst weakMap = new WeakMap();\nconst namedCurveToJOSE = (namedCurve) => {\n    switch (namedCurve) {\n        case 'prime256v1':\n            return 'P-256';\n        case 'secp384r1':\n            return 'P-384';\n        case 'secp521r1':\n            return 'P-521';\n        case 'secp256k1':\n            return 'secp256k1';\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_1__.JOSENotSupported('Unsupported key curve for this operation');\n    }\n};\nconst getNamedCurve = (kee, raw) => {\n    let key;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_2__.isCryptoKey)(kee)) {\n        key = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(kee);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(kee)) {\n        key = kee;\n    }\n    else if ((0,_lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_4__.isJWK)(kee)) {\n        return kee.crv;\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(kee, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError('only \"private\" or \"public\" type keys can be used for this operation');\n    }\n    switch (key.asymmetricKeyType) {\n        case 'ed25519':\n        case 'ed448':\n            return `Ed${key.asymmetricKeyType.slice(2)}`;\n        case 'x25519':\n        case 'x448':\n            return `X${key.asymmetricKeyType.slice(1)}`;\n        case 'ec': {\n            const namedCurve = key.asymmetricKeyDetails.namedCurve;\n            if (raw) {\n                return namedCurve;\n            }\n            return namedCurveToJOSE(namedCurve);\n        }\n        default:\n            throw new TypeError('Invalid asymmetric key type for this operation');\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (getNamedCurve);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/get_named_curve.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/get_sign_verify_key.js":
/*!***********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/get_sign_verify_key.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getSignVerifyKey)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n/* harmony import */ var _lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/is_jwk.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/is_jwk.js\");\n\n\n\n\n\n\nfunction getSignVerifyKey(alg, key, usage) {\n    if (key instanceof Uint8Array) {\n        if (!alg.startsWith('HS')) {\n            throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_2__.types));\n        }\n        return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createSecretKey)(key);\n    }\n    if (key instanceof node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject) {\n        return key;\n    }\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_3__.isCryptoKey)(key)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__.checkSigCryptoKey)(key, alg, usage);\n        return node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(key);\n    }\n    if (_lib_is_jwk_js__WEBPACK_IMPORTED_MODULE_5__.isJWK(key)) {\n        if (alg.startsWith('HS')) {\n            return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createSecretKey)(Buffer.from(key.k, 'base64url'));\n        }\n        return key;\n    }\n    throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_2__.types, 'Uint8Array', 'JSON Web Key'));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/get_sign_verify_key.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/hmac_digest.js":
/*!***************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/hmac_digest.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ hmacDigest)\n/* harmony export */ });\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n\nfunction hmacDigest(alg) {\n    switch (alg) {\n        case 'HS256':\n            return 'sha256';\n        case 'HS384':\n            return 'sha384';\n        case 'HS512':\n            return 'sha512';\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_0__.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS9obWFjX2RpZ2VzdC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUN0QztBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsNkRBQWdCLFFBQVEsS0FBSztBQUNuRDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcam9zZUA1LjEwLjBcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxydW50aW1lXFxobWFjX2RpZ2VzdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBKT1NFTm90U3VwcG9ydGVkIH0gZnJvbSAnLi4vdXRpbC9lcnJvcnMuanMnO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gaG1hY0RpZ2VzdChhbGcpIHtcbiAgICBzd2l0Y2ggKGFsZykge1xuICAgICAgICBjYXNlICdIUzI1Nic6XG4gICAgICAgICAgICByZXR1cm4gJ3NoYTI1Nic7XG4gICAgICAgIGNhc2UgJ0hTMzg0JzpcbiAgICAgICAgICAgIHJldHVybiAnc2hhMzg0JztcbiAgICAgICAgY2FzZSAnSFM1MTInOlxuICAgICAgICAgICAgcmV0dXJuICdzaGE1MTInO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgdGhyb3cgbmV3IEpPU0VOb3RTdXBwb3J0ZWQoYGFsZyAke2FsZ30gaXMgbm90IHN1cHBvcnRlZCBlaXRoZXIgYnkgSk9TRSBvciB5b3VyIGphdmFzY3JpcHQgcnVudGltZWApO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/hmac_digest.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js":
/*!***************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((key) => (0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(key) || (0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_1__.isCryptoKey)(key));\nconst types = ['KeyObject'];\nif (globalThis.CryptoKey || _webcrypto_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]?.CryptoKey) {\n    types.push('CryptoKey');\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS9pc19rZXlfbGlrZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXdEO0FBQ1g7QUFDN0MsaUVBQWUsU0FBUyw2REFBVyxTQUFTLDBEQUFXLEtBQUssRUFBQztBQUM3RDtBQUNBLDRCQUE0QixxREFBUztBQUNyQztBQUNBO0FBQ2lCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcam9zZUA1LjEwLjBcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxydW50aW1lXFxpc19rZXlfbGlrZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgd2ViY3J5cHRvLCB7IGlzQ3J5cHRvS2V5IH0gZnJvbSAnLi93ZWJjcnlwdG8uanMnO1xuaW1wb3J0IGlzS2V5T2JqZWN0IGZyb20gJy4vaXNfa2V5X29iamVjdC5qcyc7XG5leHBvcnQgZGVmYXVsdCAoa2V5KSA9PiBpc0tleU9iamVjdChrZXkpIHx8IGlzQ3J5cHRvS2V5KGtleSk7XG5jb25zdCB0eXBlcyA9IFsnS2V5T2JqZWN0J107XG5pZiAoZ2xvYmFsVGhpcy5DcnlwdG9LZXkgfHwgd2ViY3J5cHRvPy5DcnlwdG9LZXkpIHtcbiAgICB0eXBlcy5wdXNoKCdDcnlwdG9LZXknKTtcbn1cbmV4cG9ydCB7IHR5cGVzIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:util */ \"node:util\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((obj) => node_util__WEBPACK_IMPORTED_MODULE_0__.types.isKeyObject(obj));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS9pc19rZXlfb2JqZWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWtDO0FBQ2xDLGlFQUFlLFNBQVMsNENBQVUsaUJBQWlCLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxqb3NlQDUuMTAuMFxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXGlzX2tleV9vYmplY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgdXRpbCBmcm9tICdub2RlOnV0aWwnO1xuZXhwb3J0IGRlZmF1bHQgKG9iaikgPT4gdXRpbC50eXBlcy5pc0tleU9iamVjdChvYmopO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/jwk_to_key.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/jwk_to_key.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\nconst parse = (key) => {\n    if (key.d) {\n        return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPrivateKey)({ format: 'jwk', key });\n    }\n    return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.createPublicKey)({ format: 'jwk', key });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (parse);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS9qd2tfdG9fa2V5LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdFO0FBQ2hFO0FBQ0E7QUFDQSxlQUFlLDZEQUFnQixHQUFHLG9CQUFvQjtBQUN0RDtBQUNBLFdBQVcsNERBQWUsR0FBRyxvQkFBb0I7QUFDakQ7QUFDQSxpRUFBZSxLQUFLLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxqb3NlQDUuMTAuMFxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXGp3a190b19rZXkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlUHJpdmF0ZUtleSwgY3JlYXRlUHVibGljS2V5IH0gZnJvbSAnbm9kZTpjcnlwdG8nO1xuY29uc3QgcGFyc2UgPSAoa2V5KSA9PiB7XG4gICAgaWYgKGtleS5kKSB7XG4gICAgICAgIHJldHVybiBjcmVhdGVQcml2YXRlS2V5KHsgZm9ybWF0OiAnandrJywga2V5IH0pO1xuICAgIH1cbiAgICByZXR1cm4gY3JlYXRlUHVibGljS2V5KHsgZm9ybWF0OiAnandrJywga2V5IH0pO1xufTtcbmV4cG9ydCBkZWZhdWx0IHBhcnNlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/jwk_to_key.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/key_to_jwk.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/key_to_jwk.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _base64url_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base64url.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\nconst keyToJWK = (key) => {\n    let keyObject;\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_1__.isCryptoKey)(key)) {\n        if (!key.extractable) {\n            throw new TypeError('CryptoKey is not extractable');\n        }\n        keyObject = node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(key);\n    }\n    else if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key)) {\n        keyObject = key;\n    }\n    else if (key instanceof Uint8Array) {\n        return {\n            kty: 'oct',\n            k: (0,_base64url_js__WEBPACK_IMPORTED_MODULE_3__.encode)(key),\n        };\n    }\n    else {\n        throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_5__.types, 'Uint8Array'));\n    }\n    if (keyObject.type !== 'secret' &&\n        !['rsa', 'ec', 'ed25519', 'x25519', 'ed448', 'x448'].includes(keyObject.asymmetricKeyType)) {\n        throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_6__.JOSENotSupported('Unsupported key asymmetricKeyType');\n    }\n    return keyObject.export({ format: 'jwk' });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (keyToJWK);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/key_to_jwk.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/node_key.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/node_key.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ keyForCrypto)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var _get_named_curve_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./get_named_curve.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/get_named_curve.js\");\n/* harmony import */ var _util_errors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../util/errors.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\");\n/* harmony import */ var _check_key_length_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./check_key_length.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/check_key_length.js\");\n\n\n\n\nconst ecCurveAlgMap = new Map([\n    ['ES256', 'P-256'],\n    ['ES256K', 'secp256k1'],\n    ['ES384', 'P-384'],\n    ['ES512', 'P-521'],\n]);\nfunction keyForCrypto(alg, key) {\n    let asymmetricKeyType;\n    let asymmetricKeyDetails;\n    let isJWK;\n    if (key instanceof node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject) {\n        asymmetricKeyType = key.asymmetricKeyType;\n        asymmetricKeyDetails = key.asymmetricKeyDetails;\n    }\n    else {\n        isJWK = true;\n        switch (key.kty) {\n            case 'RSA':\n                asymmetricKeyType = 'rsa';\n                break;\n            case 'EC':\n                asymmetricKeyType = 'ec';\n                break;\n            case 'OKP': {\n                if (key.crv === 'Ed25519') {\n                    asymmetricKeyType = 'ed25519';\n                    break;\n                }\n                if (key.crv === 'Ed448') {\n                    asymmetricKeyType = 'ed448';\n                    break;\n                }\n                throw new TypeError('Invalid key for this operation, its crv must be Ed25519 or Ed448');\n            }\n            default:\n                throw new TypeError('Invalid key for this operation, its kty must be RSA, OKP, or EC');\n        }\n    }\n    let options;\n    switch (alg) {\n        case 'Ed25519':\n            if (asymmetricKeyType !== 'ed25519') {\n                throw new TypeError(`Invalid key for this operation, its asymmetricKeyType must be ed25519`);\n            }\n            break;\n        case 'EdDSA':\n            if (!['ed25519', 'ed448'].includes(asymmetricKeyType)) {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be ed25519 or ed448');\n            }\n            break;\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            if (asymmetricKeyType !== 'rsa') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa');\n            }\n            (0,_check_key_length_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key, alg);\n            break;\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            if (asymmetricKeyType === 'rsa-pss') {\n                const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = asymmetricKeyDetails;\n                const length = parseInt(alg.slice(-3), 10);\n                if (hashAlgorithm !== undefined &&\n                    (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm)) {\n                    throw new TypeError(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${alg}`);\n                }\n                if (saltLength !== undefined && saltLength > length >> 3) {\n                    throw new TypeError(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${alg}`);\n                }\n            }\n            else if (asymmetricKeyType !== 'rsa') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa or rsa-pss');\n            }\n            (0,_check_key_length_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(key, alg);\n            options = {\n                padding: node_crypto__WEBPACK_IMPORTED_MODULE_0__.constants.RSA_PKCS1_PSS_PADDING,\n                saltLength: node_crypto__WEBPACK_IMPORTED_MODULE_0__.constants.RSA_PSS_SALTLEN_DIGEST,\n            };\n            break;\n        case 'ES256':\n        case 'ES256K':\n        case 'ES384':\n        case 'ES512': {\n            if (asymmetricKeyType !== 'ec') {\n                throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be ec');\n            }\n            const actual = (0,_get_named_curve_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key);\n            const expected = ecCurveAlgMap.get(alg);\n            if (actual !== expected) {\n                throw new TypeError(`Invalid key curve for the algorithm, its curve must be ${expected}, got ${actual}`);\n            }\n            options = { dsaEncoding: 'ieee-p1363' };\n            break;\n        }\n        default:\n            throw new _util_errors_js__WEBPACK_IMPORTED_MODULE_3__.JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n    if (isJWK) {\n        return { format: 'jwk', key, ...options };\n    }\n    return options ? { ...options, key } : key;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/node_key.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/normalize_key.js":
/*!*****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/normalize_key.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS9ub3JtYWxpemVfa2V5LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxFQUFFLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxqb3NlQDUuMTAuMFxcbm9kZV9tb2R1bGVzXFxqb3NlXFxkaXN0XFxub2RlXFxlc21cXHJ1bnRpbWVcXG5vcm1hbGl6ZV9rZXkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge307XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/normalize_key.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/pbes2kw.js":
/*!***********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/pbes2kw.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./random.js */ \"node:crypto\");\n/* harmony import */ var _lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../lib/buffer_utils.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/buffer_utils.js\");\n/* harmony import */ var _base64url_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./base64url.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js\");\n/* harmony import */ var _aeskw_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./aeskw.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/aeskw.js\");\n/* harmony import */ var _lib_check_p2s_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../lib/check_p2s.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/check_p2s.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst pbkdf2 = (0,node_util__WEBPACK_IMPORTED_MODULE_0__.promisify)(node_crypto__WEBPACK_IMPORTED_MODULE_1__.pbkdf2);\nfunction getPassword(key, alg) {\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key)) {\n        return key.export();\n    }\n    if (key instanceof Uint8Array) {\n        return key;\n    }\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_3__.isCryptoKey)(key)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_4__.checkEncCryptoKey)(key, alg, 'deriveBits', 'deriveKey');\n        return node_crypto__WEBPACK_IMPORTED_MODULE_1__.KeyObject.from(key).export();\n    }\n    throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_6__.types, 'Uint8Array'));\n}\nconst encrypt = async (alg, key, cek, p2c = 2048, p2s = (0,node_crypto__WEBPACK_IMPORTED_MODULE_1__.randomFillSync)(new Uint8Array(16))) => {\n    (0,_lib_check_p2s_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(p2s);\n    const salt = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_8__.p2s)(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10) >> 3;\n    const password = getPassword(key, alg);\n    const derivedKey = await pbkdf2(password, salt, p2c, keylen, `sha${alg.slice(8, 11)}`);\n    const encryptedKey = await (0,_aeskw_js__WEBPACK_IMPORTED_MODULE_9__.wrap)(alg.slice(-6), derivedKey, cek);\n    return { encryptedKey, p2c, p2s: (0,_base64url_js__WEBPACK_IMPORTED_MODULE_10__.encode)(p2s) };\n};\nconst decrypt = async (alg, key, encryptedKey, p2c, p2s) => {\n    (0,_lib_check_p2s_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(p2s);\n    const salt = (0,_lib_buffer_utils_js__WEBPACK_IMPORTED_MODULE_8__.p2s)(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10) >> 3;\n    const password = getPassword(key, alg);\n    const derivedKey = await pbkdf2(password, salt, p2c, keylen, `sha${alg.slice(8, 11)}`);\n    return (0,_aeskw_js__WEBPACK_IMPORTED_MODULE_9__.unwrap)(alg.slice(-6), derivedKey, encryptedKey);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/pbes2kw.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/rsaes.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/rsaes.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var _check_key_length_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./check_key_length.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/check_key_length.js\");\n/* harmony import */ var _webcrypto_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./webcrypto.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js\");\n/* harmony import */ var _lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../lib/crypto_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/crypto_key.js\");\n/* harmony import */ var _is_key_object_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./is_key_object.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_object.js\");\n/* harmony import */ var _lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../lib/invalid_key_input.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/lib/invalid_key_input.js\");\n/* harmony import */ var _is_key_like_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./is_key_like.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/is_key_like.js\");\n\n\n\n\n\n\n\n\nconst checkKey = (key, alg) => {\n    if (key.asymmetricKeyType !== 'rsa') {\n        throw new TypeError('Invalid key for this operation, its asymmetricKeyType must be rsa');\n    }\n    (0,_check_key_length_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(key, alg);\n};\nconst RSA1_5 = (0,node_util__WEBPACK_IMPORTED_MODULE_1__.deprecate)(() => node_crypto__WEBPACK_IMPORTED_MODULE_0__.constants.RSA_PKCS1_PADDING, 'The RSA1_5 \"alg\" (JWE Algorithm) is deprecated and will be removed in the next major revision.');\nconst resolvePadding = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            return node_crypto__WEBPACK_IMPORTED_MODULE_0__.constants.RSA_PKCS1_OAEP_PADDING;\n        case 'RSA1_5':\n            return RSA1_5();\n        default:\n            return undefined;\n    }\n};\nconst resolveOaepHash = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n            return 'sha1';\n        case 'RSA-OAEP-256':\n            return 'sha256';\n        case 'RSA-OAEP-384':\n            return 'sha384';\n        case 'RSA-OAEP-512':\n            return 'sha512';\n        default:\n            return undefined;\n    }\n};\nfunction ensureKeyObject(key, alg, ...usages) {\n    if ((0,_is_key_object_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(key)) {\n        return key;\n    }\n    if ((0,_webcrypto_js__WEBPACK_IMPORTED_MODULE_4__.isCryptoKey)(key)) {\n        (0,_lib_crypto_key_js__WEBPACK_IMPORTED_MODULE_5__.checkEncCryptoKey)(key, alg, ...usages);\n        return node_crypto__WEBPACK_IMPORTED_MODULE_0__.KeyObject.from(key);\n    }\n    throw new TypeError((0,_lib_invalid_key_input_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(key, ..._is_key_like_js__WEBPACK_IMPORTED_MODULE_7__.types));\n}\nconst encrypt = (alg, key, cek) => {\n    const padding = resolvePadding(alg);\n    const oaepHash = resolveOaepHash(alg);\n    const keyObject = ensureKeyObject(key, alg, 'wrapKey', 'encrypt');\n    checkKey(keyObject, alg);\n    return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.publicEncrypt)({ key: keyObject, oaepHash, padding }, cek);\n};\nconst decrypt = (alg, key, encryptedKey) => {\n    const padding = resolvePadding(alg);\n    const oaepHash = resolveOaepHash(alg);\n    const keyObject = ensureKeyObject(key, alg, 'unwrapKey', 'decrypt');\n    checkKey(keyObject, alg);\n    return (0,node_crypto__WEBPACK_IMPORTED_MODULE_0__.privateDecrypt)({ key: keyObject, oaepHash, padding }, encryptedKey);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/rsaes.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/sign.js":
/*!********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/sign.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var _dsa_digest_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dsa_digest.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/dsa_digest.js\");\n/* harmony import */ var _hmac_digest_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hmac_digest.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/hmac_digest.js\");\n/* harmony import */ var _node_key_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/node_key.js\");\n/* harmony import */ var _get_sign_verify_key_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./get_sign_verify_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/get_sign_verify_key.js\");\n\n\n\n\n\n\nconst oneShotSign = (0,node_util__WEBPACK_IMPORTED_MODULE_1__.promisify)(node_crypto__WEBPACK_IMPORTED_MODULE_0__.sign);\nconst sign = async (alg, key, data) => {\n    const k = (0,_get_sign_verify_key_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(alg, key, 'sign');\n    if (alg.startsWith('HS')) {\n        const hmac = node_crypto__WEBPACK_IMPORTED_MODULE_0__.createHmac((0,_hmac_digest_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(alg), k);\n        hmac.update(data);\n        return hmac.digest();\n    }\n    return oneShotSign((0,_dsa_digest_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(alg), data, (0,_node_key_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(alg, k));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sign);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS9zaWduLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBc0M7QUFDQTtBQUNHO0FBQ0M7QUFDTjtBQUNjO0FBQ2xELG9CQUFvQixvREFBUyxDQUFDLDZDQUFXO0FBQ3pDO0FBQ0EsY0FBYyxtRUFBVTtBQUN4QjtBQUNBLHFCQUFxQixtREFBaUIsQ0FBQywyREFBVTtBQUNqRDtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsMERBQVUsYUFBYSx3REFBTztBQUNyRDtBQUNBLGlFQUFlLElBQUksRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGpvc2VANS4xMC4wXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcc2lnbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBjcnlwdG8gZnJvbSAnbm9kZTpjcnlwdG8nO1xuaW1wb3J0IHsgcHJvbWlzaWZ5IH0gZnJvbSAnbm9kZTp1dGlsJztcbmltcG9ydCBub2RlRGlnZXN0IGZyb20gJy4vZHNhX2RpZ2VzdC5qcyc7XG5pbXBvcnQgaG1hY0RpZ2VzdCBmcm9tICcuL2htYWNfZGlnZXN0LmpzJztcbmltcG9ydCBub2RlS2V5IGZyb20gJy4vbm9kZV9rZXkuanMnO1xuaW1wb3J0IGdldFNpZ25LZXkgZnJvbSAnLi9nZXRfc2lnbl92ZXJpZnlfa2V5LmpzJztcbmNvbnN0IG9uZVNob3RTaWduID0gcHJvbWlzaWZ5KGNyeXB0by5zaWduKTtcbmNvbnN0IHNpZ24gPSBhc3luYyAoYWxnLCBrZXksIGRhdGEpID0+IHtcbiAgICBjb25zdCBrID0gZ2V0U2lnbktleShhbGcsIGtleSwgJ3NpZ24nKTtcbiAgICBpZiAoYWxnLnN0YXJ0c1dpdGgoJ0hTJykpIHtcbiAgICAgICAgY29uc3QgaG1hYyA9IGNyeXB0by5jcmVhdGVIbWFjKGhtYWNEaWdlc3QoYWxnKSwgayk7XG4gICAgICAgIGhtYWMudXBkYXRlKGRhdGEpO1xuICAgICAgICByZXR1cm4gaG1hYy5kaWdlc3QoKTtcbiAgICB9XG4gICAgcmV0dXJuIG9uZVNob3RTaWduKG5vZGVEaWdlc3QoYWxnKSwgZGF0YSwgbm9kZUtleShhbGcsIGspKTtcbn07XG5leHBvcnQgZGVmYXVsdCBzaWduO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/sign.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js":
/*!*********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\nconst timingSafeEqual = node_crypto__WEBPACK_IMPORTED_MODULE_0__.timingSafeEqual;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (timingSafeEqual);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS90aW1pbmdfc2FmZV9lcXVhbC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRDtBQUN0RCx3QkFBd0Isd0RBQUk7QUFDNUIsaUVBQWUsZUFBZSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcam9zZUA1LjEwLjBcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFxydW50aW1lXFx0aW1pbmdfc2FmZV9lcXVhbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0aW1pbmdTYWZlRXF1YWwgYXMgaW1wbCB9IGZyb20gJ25vZGU6Y3J5cHRvJztcbmNvbnN0IHRpbWluZ1NhZmVFcXVhbCA9IGltcGw7XG5leHBvcnQgZGVmYXVsdCB0aW1pbmdTYWZlRXF1YWw7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/timing_safe_equal.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/verify.js":
/*!**********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/verify.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:util */ \"node:util\");\n/* harmony import */ var _dsa_digest_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./dsa_digest.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/dsa_digest.js\");\n/* harmony import */ var _node_key_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/node_key.js\");\n/* harmony import */ var _sign_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sign.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/sign.js\");\n/* harmony import */ var _get_sign_verify_key_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./get_sign_verify_key.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/get_sign_verify_key.js\");\n\n\n\n\n\n\nconst oneShotVerify = (0,node_util__WEBPACK_IMPORTED_MODULE_1__.promisify)(node_crypto__WEBPACK_IMPORTED_MODULE_0__.verify);\nconst verify = async (alg, key, signature, data) => {\n    const k = (0,_get_sign_verify_key_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(alg, key, 'verify');\n    if (alg.startsWith('HS')) {\n        const expected = await (0,_sign_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(alg, k, data);\n        const actual = signature;\n        try {\n            return node_crypto__WEBPACK_IMPORTED_MODULE_0__.timingSafeEqual(actual, expected);\n        }\n        catch {\n            return false;\n        }\n    }\n    const algorithm = (0,_dsa_digest_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(alg);\n    const keyInput = (0,_node_key_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(alg, k);\n    try {\n        return await oneShotVerify(algorithm, data, keyInput, signature);\n    }\n    catch {\n        return false;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (verify);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/verify.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js":
/*!*************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isCryptoKey: () => (/* binding */ isCryptoKey)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:util */ \"node:util\");\n\n\nconst webcrypto = node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (webcrypto);\nconst isCryptoKey = (key) => node_util__WEBPACK_IMPORTED_MODULE_1__.types.isCryptoKey(key);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vcnVudGltZS93ZWJjcnlwdG8uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFzQztBQUNKO0FBQ2xDLGtCQUFrQixrREFBZ0I7QUFDbEMsaUVBQWUsU0FBUyxFQUFDO0FBQ2xCLDZCQUE2Qiw0Q0FBVSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGpvc2VANS4xMC4wXFxub2RlX21vZHVsZXNcXGpvc2VcXGRpc3RcXG5vZGVcXGVzbVxccnVudGltZVxcd2ViY3J5cHRvLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIGNyeXB0byBmcm9tICdub2RlOmNyeXB0byc7XG5pbXBvcnQgKiBhcyB1dGlsIGZyb20gJ25vZGU6dXRpbCc7XG5jb25zdCB3ZWJjcnlwdG8gPSBjcnlwdG8ud2ViY3J5cHRvO1xuZXhwb3J0IGRlZmF1bHQgd2ViY3J5cHRvO1xuZXhwb3J0IGNvbnN0IGlzQ3J5cHRvS2V5ID0gKGtleSkgPT4gdXRpbC50eXBlcy5pc0NyeXB0b0tleShrZXkpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/webcrypto.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/base64url.js":
/*!**********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/base64url.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\n/* harmony import */ var _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../runtime/base64url.js */ \"(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/runtime/base64url.js\");\n\nconst encode = _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__.encode;\nconst decode = _runtime_base64url_js__WEBPACK_IMPORTED_MODULE_0__.decode;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvc2VANS4xMC4wL25vZGVfbW9kdWxlcy9qb3NlL2Rpc3Qvbm9kZS9lc20vdXRpbC9iYXNlNjR1cmwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXFEO0FBQzlDLGVBQWUseURBQWdCO0FBQy9CLGVBQWUseURBQWdCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcam9zZUA1LjEwLjBcXG5vZGVfbW9kdWxlc1xcam9zZVxcZGlzdFxcbm9kZVxcZXNtXFx1dGlsXFxiYXNlNjR1cmwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgYmFzZTY0dXJsIGZyb20gJy4uL3J1bnRpbWUvYmFzZTY0dXJsLmpzJztcbmV4cG9ydCBjb25zdCBlbmNvZGUgPSBiYXNlNjR1cmwuZW5jb2RlO1xuZXhwb3J0IGNvbnN0IGRlY29kZSA9IGJhc2U2NHVybC5kZWNvZGU7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/base64url.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JOSEAlgNotAllowed: () => (/* binding */ JOSEAlgNotAllowed),\n/* harmony export */   JOSEError: () => (/* binding */ JOSEError),\n/* harmony export */   JOSENotSupported: () => (/* binding */ JOSENotSupported),\n/* harmony export */   JWEDecryptionFailed: () => (/* binding */ JWEDecryptionFailed),\n/* harmony export */   JWEInvalid: () => (/* binding */ JWEInvalid),\n/* harmony export */   JWKInvalid: () => (/* binding */ JWKInvalid),\n/* harmony export */   JWKSInvalid: () => (/* binding */ JWKSInvalid),\n/* harmony export */   JWKSMultipleMatchingKeys: () => (/* binding */ JWKSMultipleMatchingKeys),\n/* harmony export */   JWKSNoMatchingKey: () => (/* binding */ JWKSNoMatchingKey),\n/* harmony export */   JWKSTimeout: () => (/* binding */ JWKSTimeout),\n/* harmony export */   JWSInvalid: () => (/* binding */ JWSInvalid),\n/* harmony export */   JWSSignatureVerificationFailed: () => (/* binding */ JWSSignatureVerificationFailed),\n/* harmony export */   JWTClaimValidationFailed: () => (/* binding */ JWTClaimValidationFailed),\n/* harmony export */   JWTExpired: () => (/* binding */ JWTExpired),\n/* harmony export */   JWTInvalid: () => (/* binding */ JWTInvalid)\n/* harmony export */ });\nclass JOSEError extends Error {\n    static code = 'ERR_JOSE_GENERIC';\n    code = 'ERR_JOSE_GENERIC';\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nclass JWTClaimValidationFailed extends JOSEError {\n    static code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nclass JWTExpired extends JOSEError {\n    static code = 'ERR_JWT_EXPIRED';\n    code = 'ERR_JWT_EXPIRED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nclass JOSEAlgNotAllowed extends JOSEError {\n    static code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n}\nclass JOSENotSupported extends JOSEError {\n    static code = 'ERR_JOSE_NOT_SUPPORTED';\n    code = 'ERR_JOSE_NOT_SUPPORTED';\n}\nclass JWEDecryptionFailed extends JOSEError {\n    static code = 'ERR_JWE_DECRYPTION_FAILED';\n    code = 'ERR_JWE_DECRYPTION_FAILED';\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n    }\n}\nclass JWEInvalid extends JOSEError {\n    static code = 'ERR_JWE_INVALID';\n    code = 'ERR_JWE_INVALID';\n}\nclass JWSInvalid extends JOSEError {\n    static code = 'ERR_JWS_INVALID';\n    code = 'ERR_JWS_INVALID';\n}\nclass JWTInvalid extends JOSEError {\n    static code = 'ERR_JWT_INVALID';\n    code = 'ERR_JWT_INVALID';\n}\nclass JWKInvalid extends JOSEError {\n    static code = 'ERR_JWK_INVALID';\n    code = 'ERR_JWK_INVALID';\n}\nclass JWKSInvalid extends JOSEError {\n    static code = 'ERR_JWKS_INVALID';\n    code = 'ERR_JWKS_INVALID';\n}\nclass JWKSNoMatchingKey extends JOSEError {\n    static code = 'ERR_JWKS_NO_MATCHING_KEY';\n    code = 'ERR_JWKS_NO_MATCHING_KEY';\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nclass JWKSMultipleMatchingKeys extends JOSEError {\n    [Symbol.asyncIterator];\n    static code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nclass JWKSTimeout extends JOSEError {\n    static code = 'ERR_JWKS_TIMEOUT';\n    code = 'ERR_JWKS_TIMEOUT';\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n    }\n}\nclass JWSSignatureVerificationFailed extends JOSEError {\n    static code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/jose@5.10.0/node_modules/jose/dist/node/esm/util/errors.js\n");

/***/ })

};
;