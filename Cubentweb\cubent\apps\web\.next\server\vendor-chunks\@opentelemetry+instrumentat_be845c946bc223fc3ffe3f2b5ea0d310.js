"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/amqplib.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/amqplib.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AmqplibInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst types_1 = __webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/version.js\");\nconst supportedVersions = ['>=0.5.5 <1'];\nclass AmqplibInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, types_1.DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, types_1.DEFAULT_CONFIG), config));\n    }\n    init() {\n        const channelModelModuleFile = new instrumentation_1.InstrumentationNodeModuleFile('amqplib/lib/channel_model.js', supportedVersions, this.patchChannelModel.bind(this), this.unpatchChannelModel.bind(this));\n        const callbackModelModuleFile = new instrumentation_1.InstrumentationNodeModuleFile('amqplib/lib/callback_model.js', supportedVersions, this.patchChannelModel.bind(this), this.unpatchChannelModel.bind(this));\n        const connectModuleFile = new instrumentation_1.InstrumentationNodeModuleFile('amqplib/lib/connect.js', supportedVersions, this.patchConnect.bind(this), this.unpatchConnect.bind(this));\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition('amqplib', supportedVersions, undefined, undefined, [channelModelModuleFile, connectModuleFile, callbackModelModuleFile]);\n        return module;\n    }\n    patchConnect(moduleExports) {\n        moduleExports = this.unpatchConnect(moduleExports);\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.connect)) {\n            this._wrap(moduleExports, 'connect', this.getConnectPatch.bind(this));\n        }\n        return moduleExports;\n    }\n    unpatchConnect(moduleExports) {\n        if ((0, instrumentation_1.isWrapped)(moduleExports.connect)) {\n            this._unwrap(moduleExports, 'connect');\n        }\n        return moduleExports;\n    }\n    patchChannelModel(moduleExports, moduleVersion) {\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.publish)) {\n            this._wrap(moduleExports.Channel.prototype, 'publish', this.getPublishPatch.bind(this, moduleVersion));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.consume)) {\n            this._wrap(moduleExports.Channel.prototype, 'consume', this.getConsumePatch.bind(this, moduleVersion));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.ack)) {\n            this._wrap(moduleExports.Channel.prototype, 'ack', this.getAckPatch.bind(this, false, types_1.EndOperation.Ack));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.nack)) {\n            this._wrap(moduleExports.Channel.prototype, 'nack', this.getAckPatch.bind(this, true, types_1.EndOperation.Nack));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.reject)) {\n            this._wrap(moduleExports.Channel.prototype, 'reject', this.getAckPatch.bind(this, true, types_1.EndOperation.Reject));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.ackAll)) {\n            this._wrap(moduleExports.Channel.prototype, 'ackAll', this.getAckAllPatch.bind(this, false, types_1.EndOperation.AckAll));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.nackAll)) {\n            this._wrap(moduleExports.Channel.prototype, 'nackAll', this.getAckAllPatch.bind(this, true, types_1.EndOperation.NackAll));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.emit)) {\n            this._wrap(moduleExports.Channel.prototype, 'emit', this.getChannelEmitPatch.bind(this));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.ConfirmChannel.prototype.publish)) {\n            this._wrap(moduleExports.ConfirmChannel.prototype, 'publish', this.getConfirmedPublishPatch.bind(this, moduleVersion));\n        }\n        return moduleExports;\n    }\n    unpatchChannelModel(moduleExports) {\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.publish)) {\n            this._unwrap(moduleExports.Channel.prototype, 'publish');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.consume)) {\n            this._unwrap(moduleExports.Channel.prototype, 'consume');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.ack)) {\n            this._unwrap(moduleExports.Channel.prototype, 'ack');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.nack)) {\n            this._unwrap(moduleExports.Channel.prototype, 'nack');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.reject)) {\n            this._unwrap(moduleExports.Channel.prototype, 'reject');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.ackAll)) {\n            this._unwrap(moduleExports.Channel.prototype, 'ackAll');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.nackAll)) {\n            this._unwrap(moduleExports.Channel.prototype, 'nackAll');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.emit)) {\n            this._unwrap(moduleExports.Channel.prototype, 'emit');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.ConfirmChannel.prototype.publish)) {\n            this._unwrap(moduleExports.ConfirmChannel.prototype, 'publish');\n        }\n        return moduleExports;\n    }\n    getConnectPatch(original) {\n        return function patchedConnect(url, socketOptions, openCallback) {\n            return original.call(this, url, socketOptions, function (err, conn) {\n                if (err == null) {\n                    const urlAttributes = (0, utils_1.getConnectionAttributesFromUrl)(url);\n                    // the type of conn in @types/amqplib is amqp.Connection, but in practice the library send the\n                    // `serverProperties` on the `conn` and not in a property `connection`.\n                    // I don't have capacity to debug it currently but it should probably be fixed in @types or\n                    // in the package itself\n                    // currently setting as any to calm typescript\n                    const serverAttributes = (0, utils_1.getConnectionAttributesFromServer)(conn);\n                    conn[utils_1.CONNECTION_ATTRIBUTES] = Object.assign(Object.assign({}, urlAttributes), serverAttributes);\n                }\n                openCallback.apply(this, arguments);\n            });\n        };\n    }\n    getChannelEmitPatch(original) {\n        const self = this;\n        return function emit(eventName) {\n            if (eventName === 'close') {\n                self.endAllSpansOnChannel(this, true, types_1.EndOperation.ChannelClosed, undefined);\n                const activeTimer = this[utils_1.CHANNEL_CONSUME_TIMEOUT_TIMER];\n                if (activeTimer) {\n                    clearInterval(activeTimer);\n                }\n                this[utils_1.CHANNEL_CONSUME_TIMEOUT_TIMER] = undefined;\n            }\n            else if (eventName === 'error') {\n                self.endAllSpansOnChannel(this, true, types_1.EndOperation.ChannelError, undefined);\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    getAckAllPatch(isRejected, endOperation, original) {\n        const self = this;\n        return function ackAll(requeueOrEmpty) {\n            self.endAllSpansOnChannel(this, isRejected, endOperation, requeueOrEmpty);\n            return original.apply(this, arguments);\n        };\n    }\n    getAckPatch(isRejected, endOperation, original) {\n        const self = this;\n        return function ack(message, allUpToOrRequeue, requeue) {\n            var _a;\n            const channel = this;\n            // we use this patch in reject function as well, but it has different signature\n            const requeueResolved = endOperation === types_1.EndOperation.Reject ? allUpToOrRequeue : requeue;\n            const spansNotEnded = (_a = channel[utils_1.CHANNEL_SPANS_NOT_ENDED]) !== null && _a !== void 0 ? _a : [];\n            const msgIndex = spansNotEnded.findIndex(msgDetails => msgDetails.msg === message);\n            if (msgIndex < 0) {\n                // should not happen in happy flow\n                // but possible if user is calling the api function ack twice with same message\n                self.endConsumerSpan(message, isRejected, endOperation, requeueResolved);\n            }\n            else if (endOperation !== types_1.EndOperation.Reject && allUpToOrRequeue) {\n                for (let i = 0; i <= msgIndex; i++) {\n                    self.endConsumerSpan(spansNotEnded[i].msg, isRejected, endOperation, requeueResolved);\n                }\n                spansNotEnded.splice(0, msgIndex + 1);\n            }\n            else {\n                self.endConsumerSpan(message, isRejected, endOperation, requeueResolved);\n                spansNotEnded.splice(msgIndex, 1);\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    getConsumePatch(moduleVersion, original) {\n        const self = this;\n        return function consume(queue, onMessage, options) {\n            const channel = this;\n            if (!Object.prototype.hasOwnProperty.call(channel, utils_1.CHANNEL_SPANS_NOT_ENDED)) {\n                const { consumeTimeoutMs } = self.getConfig();\n                if (consumeTimeoutMs) {\n                    const timer = setInterval(() => {\n                        self.checkConsumeTimeoutOnChannel(channel);\n                    }, consumeTimeoutMs);\n                    timer.unref();\n                    channel[utils_1.CHANNEL_CONSUME_TIMEOUT_TIMER] = timer;\n                }\n                channel[utils_1.CHANNEL_SPANS_NOT_ENDED] = [];\n            }\n            const patchedOnMessage = function (msg) {\n                var _a, _b, _c, _d, _e;\n                // msg is expected to be null for signaling consumer cancel notification\n                // https://www.rabbitmq.com/consumer-cancel.html\n                // in this case, we do not start a span, as this is not a real message.\n                if (!msg) {\n                    return onMessage.call(this, msg);\n                }\n                const headers = (_a = msg.properties.headers) !== null && _a !== void 0 ? _a : {};\n                let parentContext = api_1.propagation.extract(api_1.ROOT_CONTEXT, headers);\n                const exchange = (_b = msg.fields) === null || _b === void 0 ? void 0 : _b.exchange;\n                let links;\n                if (self._config.useLinksForConsume) {\n                    const parentSpanContext = parentContext\n                        ? (_c = api_1.trace.getSpan(parentContext)) === null || _c === void 0 ? void 0 : _c.spanContext()\n                        : undefined;\n                    parentContext = undefined;\n                    if (parentSpanContext) {\n                        links = [\n                            {\n                                context: parentSpanContext,\n                            },\n                        ];\n                    }\n                }\n                const span = self.tracer.startSpan(`${queue} process`, {\n                    kind: api_1.SpanKind.CONSUMER,\n                    attributes: Object.assign(Object.assign({}, (_d = channel === null || channel === void 0 ? void 0 : channel.connection) === null || _d === void 0 ? void 0 : _d[utils_1.CONNECTION_ATTRIBUTES]), { [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION]: exchange, [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION_KIND]: semantic_conventions_1.MESSAGINGDESTINATIONKINDVALUES_TOPIC, [semantic_conventions_1.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY]: (_e = msg.fields) === null || _e === void 0 ? void 0 : _e.routingKey, [semantic_conventions_1.SEMATTRS_MESSAGING_OPERATION]: semantic_conventions_1.MESSAGINGOPERATIONVALUES_PROCESS, [semantic_conventions_1.SEMATTRS_MESSAGING_MESSAGE_ID]: msg === null || msg === void 0 ? void 0 : msg.properties.messageId, [semantic_conventions_1.SEMATTRS_MESSAGING_CONVERSATION_ID]: msg === null || msg === void 0 ? void 0 : msg.properties.correlationId }),\n                    links,\n                }, parentContext);\n                const { consumeHook } = self.getConfig();\n                if (consumeHook) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => consumeHook(span, { moduleVersion, msg }), e => {\n                        if (e) {\n                            api_1.diag.error('amqplib instrumentation: consumerHook error', e);\n                        }\n                    }, true);\n                }\n                if (!(options === null || options === void 0 ? void 0 : options.noAck)) {\n                    // store the message on the channel so we can close the span on ackAll etc\n                    channel[utils_1.CHANNEL_SPANS_NOT_ENDED].push({\n                        msg,\n                        timeOfConsume: (0, core_1.hrTime)(),\n                    });\n                    // store the span on the message, so we can end it when user call 'ack' on it\n                    msg[utils_1.MESSAGE_STORED_SPAN] = span;\n                }\n                const setContext = parentContext\n                    ? parentContext\n                    : api_1.ROOT_CONTEXT;\n                api_1.context.with(api_1.trace.setSpan(setContext, span), () => {\n                    onMessage.call(this, msg);\n                });\n                if (options === null || options === void 0 ? void 0 : options.noAck) {\n                    self.callConsumeEndHook(span, msg, false, types_1.EndOperation.AutoAck);\n                    span.end();\n                }\n            };\n            arguments[1] = patchedOnMessage;\n            return original.apply(this, arguments);\n        };\n    }\n    getConfirmedPublishPatch(moduleVersion, original) {\n        const self = this;\n        return function confirmedPublish(exchange, routingKey, content, options, callback) {\n            const channel = this;\n            const { span, modifiedOptions } = self.createPublishSpan(self, exchange, routingKey, channel, options);\n            const { publishHook } = self.getConfig();\n            if (publishHook) {\n                (0, instrumentation_1.safeExecuteInTheMiddle)(() => publishHook(span, {\n                    moduleVersion,\n                    exchange,\n                    routingKey,\n                    content,\n                    options: modifiedOptions,\n                    isConfirmChannel: true,\n                }), e => {\n                    if (e) {\n                        api_1.diag.error('amqplib instrumentation: publishHook error', e);\n                    }\n                }, true);\n            }\n            const patchedOnConfirm = function (err, ok) {\n                try {\n                    callback === null || callback === void 0 ? void 0 : callback.call(this, err, ok);\n                }\n                finally {\n                    const { publishConfirmHook } = self.getConfig();\n                    if (publishConfirmHook) {\n                        (0, instrumentation_1.safeExecuteInTheMiddle)(() => publishConfirmHook(span, {\n                            moduleVersion,\n                            exchange,\n                            routingKey,\n                            content,\n                            options,\n                            isConfirmChannel: true,\n                            confirmError: err,\n                        }), e => {\n                            if (e) {\n                                api_1.diag.error('amqplib instrumentation: publishConfirmHook error', e);\n                            }\n                        }, true);\n                    }\n                    if (err) {\n                        span.setStatus({\n                            code: api_1.SpanStatusCode.ERROR,\n                            message: \"message confirmation has been nack'ed\",\n                        });\n                    }\n                    span.end();\n                }\n            };\n            // calling confirm channel publish function is storing the message in queue and registering the callback for broker confirm.\n            // span ends in the patched callback.\n            const markedContext = (0, utils_1.markConfirmChannelTracing)(api_1.context.active());\n            const argumentsCopy = [...arguments];\n            argumentsCopy[3] = modifiedOptions;\n            argumentsCopy[4] = api_1.context.bind((0, utils_1.unmarkConfirmChannelTracing)(api_1.trace.setSpan(markedContext, span)), patchedOnConfirm);\n            return api_1.context.with(markedContext, original.bind(this, ...argumentsCopy));\n        };\n    }\n    getPublishPatch(moduleVersion, original) {\n        const self = this;\n        return function publish(exchange, routingKey, content, options) {\n            if ((0, utils_1.isConfirmChannelTracing)(api_1.context.active())) {\n                // work already done\n                return original.apply(this, arguments);\n            }\n            else {\n                const channel = this;\n                const { span, modifiedOptions } = self.createPublishSpan(self, exchange, routingKey, channel, options);\n                const { publishHook } = self.getConfig();\n                if (publishHook) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => publishHook(span, {\n                        moduleVersion,\n                        exchange,\n                        routingKey,\n                        content,\n                        options: modifiedOptions,\n                        isConfirmChannel: false,\n                    }), e => {\n                        if (e) {\n                            api_1.diag.error('amqplib instrumentation: publishHook error', e);\n                        }\n                    }, true);\n                }\n                // calling normal channel publish function is only storing the message in queue.\n                // it does not send it and waits for an ack, so the span duration is expected to be very short.\n                const argumentsCopy = [...arguments];\n                argumentsCopy[3] = modifiedOptions;\n                const originalRes = original.apply(this, argumentsCopy);\n                span.end();\n                return originalRes;\n            }\n        };\n    }\n    createPublishSpan(self, exchange, routingKey, channel, options) {\n        var _a;\n        const normalizedExchange = (0, utils_1.normalizeExchange)(exchange);\n        const span = self.tracer.startSpan(`publish ${normalizedExchange}`, {\n            kind: api_1.SpanKind.PRODUCER,\n            attributes: Object.assign(Object.assign({}, channel.connection[utils_1.CONNECTION_ATTRIBUTES]), { [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION]: exchange, [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION_KIND]: semantic_conventions_1.MESSAGINGDESTINATIONKINDVALUES_TOPIC, [semantic_conventions_1.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY]: routingKey, [semantic_conventions_1.SEMATTRS_MESSAGING_MESSAGE_ID]: options === null || options === void 0 ? void 0 : options.messageId, [semantic_conventions_1.SEMATTRS_MESSAGING_CONVERSATION_ID]: options === null || options === void 0 ? void 0 : options.correlationId }),\n        });\n        const modifiedOptions = options !== null && options !== void 0 ? options : {};\n        modifiedOptions.headers = (_a = modifiedOptions.headers) !== null && _a !== void 0 ? _a : {};\n        api_1.propagation.inject(api_1.trace.setSpan(api_1.context.active(), span), modifiedOptions.headers);\n        return { span, modifiedOptions };\n    }\n    endConsumerSpan(message, isRejected, operation, requeue) {\n        const storedSpan = message[utils_1.MESSAGE_STORED_SPAN];\n        if (!storedSpan)\n            return;\n        if (isRejected !== false) {\n            storedSpan.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: operation !== types_1.EndOperation.ChannelClosed &&\n                    operation !== types_1.EndOperation.ChannelError\n                    ? `${operation} called on message${requeue === true\n                        ? ' with requeue'\n                        : requeue === false\n                            ? ' without requeue'\n                            : ''}`\n                    : operation,\n            });\n        }\n        this.callConsumeEndHook(storedSpan, message, isRejected, operation);\n        storedSpan.end();\n        message[utils_1.MESSAGE_STORED_SPAN] = undefined;\n    }\n    endAllSpansOnChannel(channel, isRejected, operation, requeue) {\n        var _a;\n        const spansNotEnded = (_a = channel[utils_1.CHANNEL_SPANS_NOT_ENDED]) !== null && _a !== void 0 ? _a : [];\n        spansNotEnded.forEach(msgDetails => {\n            this.endConsumerSpan(msgDetails.msg, isRejected, operation, requeue);\n        });\n        channel[utils_1.CHANNEL_SPANS_NOT_ENDED] = [];\n    }\n    callConsumeEndHook(span, msg, rejected, endOperation) {\n        const { consumeEndHook } = this.getConfig();\n        if (!consumeEndHook)\n            return;\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => consumeEndHook(span, { msg, rejected, endOperation }), e => {\n            if (e) {\n                api_1.diag.error('amqplib instrumentation: consumerEndHook error', e);\n            }\n        }, true);\n    }\n    checkConsumeTimeoutOnChannel(channel) {\n        var _a;\n        const currentTime = (0, core_1.hrTime)();\n        const spansNotEnded = (_a = channel[utils_1.CHANNEL_SPANS_NOT_ENDED]) !== null && _a !== void 0 ? _a : [];\n        let i;\n        const { consumeTimeoutMs } = this.getConfig();\n        for (i = 0; i < spansNotEnded.length; i++) {\n            const currMessage = spansNotEnded[i];\n            const timeFromConsume = (0, core_1.hrTimeDuration)(currMessage.timeOfConsume, currentTime);\n            if ((0, core_1.hrTimeToMilliseconds)(timeFromConsume) < consumeTimeoutMs) {\n                break;\n            }\n            this.endConsumerSpan(currMessage.msg, null, types_1.EndOperation.InstrumentationTimeout, true);\n        }\n        spansNotEnded.splice(0, i);\n    }\n}\nexports.AmqplibInstrumentation = AmqplibInstrumentation;\n//# sourceMappingURL=amqplib.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/amqplib.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n__exportStar(__webpack_require__(/*! ./amqplib */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/amqplib.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_CONFIG = exports.EndOperation = void 0;\nvar EndOperation;\n(function (EndOperation) {\n    EndOperation[\"AutoAck\"] = \"auto ack\";\n    EndOperation[\"Ack\"] = \"ack\";\n    EndOperation[\"AckAll\"] = \"ackAll\";\n    EndOperation[\"Reject\"] = \"reject\";\n    EndOperation[\"Nack\"] = \"nack\";\n    EndOperation[\"NackAll\"] = \"nackAll\";\n    EndOperation[\"ChannelClosed\"] = \"channel closed\";\n    EndOperation[\"ChannelError\"] = \"channel error\";\n    EndOperation[\"InstrumentationTimeout\"] = \"instrumentation timeout\";\n})(EndOperation = exports.EndOperation || (exports.EndOperation = {}));\nexports.DEFAULT_CONFIG = {\n    consumeTimeoutMs: 1000 * 60,\n    useLinksForConsume: false,\n};\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isConfirmChannelTracing = exports.unmarkConfirmChannelTracing = exports.markConfirmChannelTracing = exports.getConnectionAttributesFromUrl = exports.getConnectionAttributesFromServer = exports.normalizeExchange = exports.CONNECTION_ATTRIBUTES = exports.CHANNEL_CONSUME_TIMEOUT_TIMER = exports.CHANNEL_SPANS_NOT_ENDED = exports.MESSAGE_STORED_SPAN = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nexports.MESSAGE_STORED_SPAN = Symbol('opentelemetry.amqplib.message.stored-span');\nexports.CHANNEL_SPANS_NOT_ENDED = Symbol('opentelemetry.amqplib.channel.spans-not-ended');\nexports.CHANNEL_CONSUME_TIMEOUT_TIMER = Symbol('opentelemetry.amqplib.channel.consumer-timeout-timer');\nexports.CONNECTION_ATTRIBUTES = Symbol('opentelemetry.amqplib.connection.attributes');\nconst IS_CONFIRM_CHANNEL_CONTEXT_KEY = (0, api_1.createContextKey)('opentelemetry.amqplib.channel.is-confirm-channel');\nconst normalizeExchange = (exchangeName) => exchangeName !== '' ? exchangeName : '<default>';\nexports.normalizeExchange = normalizeExchange;\nconst censorPassword = (url) => {\n    return url.replace(/:[^:@/]*@/, ':***@');\n};\nconst getPort = (portFromUrl, resolvedProtocol) => {\n    // we are using the resolved protocol which is upper case\n    // this code mimic the behavior of the amqplib which is used to set connection params\n    return portFromUrl || (resolvedProtocol === 'AMQP' ? 5672 : 5671);\n};\nconst getProtocol = (protocolFromUrl) => {\n    const resolvedProtocol = protocolFromUrl || 'amqp';\n    // the substring removed the ':' part of the protocol ('amqp:' -> 'amqp')\n    const noEndingColon = resolvedProtocol.endsWith(':')\n        ? resolvedProtocol.substring(0, resolvedProtocol.length - 1)\n        : resolvedProtocol;\n    // upper cases to match spec\n    return noEndingColon.toUpperCase();\n};\nconst getHostname = (hostnameFromUrl) => {\n    // if user supplies empty hostname, it gets forwarded to 'net' package which default it to localhost.\n    // https://nodejs.org/docs/latest-v12.x/api/net.html#net_socket_connect_options_connectlistener\n    return hostnameFromUrl || 'localhost';\n};\nconst extractConnectionAttributeOrLog = (url, attributeKey, attributeValue, nameForLog) => {\n    if (attributeValue) {\n        return { [attributeKey]: attributeValue };\n    }\n    else {\n        api_1.diag.error(`amqplib instrumentation: could not extract connection attribute ${nameForLog} from user supplied url`, {\n            url,\n        });\n        return {};\n    }\n};\nconst getConnectionAttributesFromServer = (conn) => {\n    var _a, _b;\n    const product = (_b = (_a = conn.serverProperties.product) === null || _a === void 0 ? void 0 : _a.toLowerCase) === null || _b === void 0 ? void 0 : _b.call(_a);\n    if (product) {\n        return {\n            [semantic_conventions_1.SEMATTRS_MESSAGING_SYSTEM]: product,\n        };\n    }\n    else {\n        return {};\n    }\n};\nexports.getConnectionAttributesFromServer = getConnectionAttributesFromServer;\nconst getConnectionAttributesFromUrl = (url) => {\n    const attributes = {\n        [semantic_conventions_1.SEMATTRS_MESSAGING_PROTOCOL_VERSION]: '0.9.1', // this is the only protocol supported by the instrumented library\n    };\n    url = url || 'amqp://localhost';\n    if (typeof url === 'object') {\n        const connectOptions = url;\n        const protocol = getProtocol(connectOptions === null || connectOptions === void 0 ? void 0 : connectOptions.protocol);\n        Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(url, semantic_conventions_1.SEMATTRS_MESSAGING_PROTOCOL, protocol, 'protocol')));\n        const hostname = getHostname(connectOptions === null || connectOptions === void 0 ? void 0 : connectOptions.hostname);\n        Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(url, semantic_conventions_1.SEMATTRS_NET_PEER_NAME, hostname, 'hostname')));\n        const port = getPort(connectOptions.port, protocol);\n        Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(url, semantic_conventions_1.SEMATTRS_NET_PEER_PORT, port, 'port')));\n    }\n    else {\n        const censoredUrl = censorPassword(url);\n        attributes[semantic_conventions_1.SEMATTRS_MESSAGING_URL] = censoredUrl;\n        try {\n            const urlParts = new URL(censoredUrl);\n            const protocol = getProtocol(urlParts.protocol);\n            Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(censoredUrl, semantic_conventions_1.SEMATTRS_MESSAGING_PROTOCOL, protocol, 'protocol')));\n            const hostname = getHostname(urlParts.hostname);\n            Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(censoredUrl, semantic_conventions_1.SEMATTRS_NET_PEER_NAME, hostname, 'hostname')));\n            const port = getPort(urlParts.port ? parseInt(urlParts.port) : undefined, protocol);\n            Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(censoredUrl, semantic_conventions_1.SEMATTRS_NET_PEER_PORT, port, 'port')));\n        }\n        catch (err) {\n            api_1.diag.error('amqplib instrumentation: error while extracting connection details from connection url', {\n                censoredUrl,\n                err,\n            });\n        }\n    }\n    return attributes;\n};\nexports.getConnectionAttributesFromUrl = getConnectionAttributesFromUrl;\nconst markConfirmChannelTracing = (context) => {\n    return context.setValue(IS_CONFIRM_CHANNEL_CONTEXT_KEY, true);\n};\nexports.markConfirmChannelTracing = markConfirmChannelTracing;\nconst unmarkConfirmChannelTracing = (context) => {\n    return context.deleteValue(IS_CONFIRM_CHANNEL_CONTEXT_KEY);\n};\nexports.unmarkConfirmChannelTracing = unmarkConfirmChannelTracing;\nconst isConfirmChannelTracing = (context) => {\n    return context.getValue(IS_CONFIRM_CHANNEL_CONTEXT_KEY) === true;\n};\nexports.isConfirmChannelTracing = isConfirmChannelTracing;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/utils.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.46.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-amqplib';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/amqplib.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/amqplib.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AmqplibInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst types_1 = __webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/version.js\");\nconst supportedVersions = ['>=0.5.5 <1'];\nclass AmqplibInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, types_1.DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, types_1.DEFAULT_CONFIG), config));\n    }\n    init() {\n        const channelModelModuleFile = new instrumentation_1.InstrumentationNodeModuleFile('amqplib/lib/channel_model.js', supportedVersions, this.patchChannelModel.bind(this), this.unpatchChannelModel.bind(this));\n        const callbackModelModuleFile = new instrumentation_1.InstrumentationNodeModuleFile('amqplib/lib/callback_model.js', supportedVersions, this.patchChannelModel.bind(this), this.unpatchChannelModel.bind(this));\n        const connectModuleFile = new instrumentation_1.InstrumentationNodeModuleFile('amqplib/lib/connect.js', supportedVersions, this.patchConnect.bind(this), this.unpatchConnect.bind(this));\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition('amqplib', supportedVersions, undefined, undefined, [channelModelModuleFile, connectModuleFile, callbackModelModuleFile]);\n        return module;\n    }\n    patchConnect(moduleExports) {\n        moduleExports = this.unpatchConnect(moduleExports);\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.connect)) {\n            this._wrap(moduleExports, 'connect', this.getConnectPatch.bind(this));\n        }\n        return moduleExports;\n    }\n    unpatchConnect(moduleExports) {\n        if ((0, instrumentation_1.isWrapped)(moduleExports.connect)) {\n            this._unwrap(moduleExports, 'connect');\n        }\n        return moduleExports;\n    }\n    patchChannelModel(moduleExports, moduleVersion) {\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.publish)) {\n            this._wrap(moduleExports.Channel.prototype, 'publish', this.getPublishPatch.bind(this, moduleVersion));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.consume)) {\n            this._wrap(moduleExports.Channel.prototype, 'consume', this.getConsumePatch.bind(this, moduleVersion));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.ack)) {\n            this._wrap(moduleExports.Channel.prototype, 'ack', this.getAckPatch.bind(this, false, types_1.EndOperation.Ack));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.nack)) {\n            this._wrap(moduleExports.Channel.prototype, 'nack', this.getAckPatch.bind(this, true, types_1.EndOperation.Nack));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.reject)) {\n            this._wrap(moduleExports.Channel.prototype, 'reject', this.getAckPatch.bind(this, true, types_1.EndOperation.Reject));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.ackAll)) {\n            this._wrap(moduleExports.Channel.prototype, 'ackAll', this.getAckAllPatch.bind(this, false, types_1.EndOperation.AckAll));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.nackAll)) {\n            this._wrap(moduleExports.Channel.prototype, 'nackAll', this.getAckAllPatch.bind(this, true, types_1.EndOperation.NackAll));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.emit)) {\n            this._wrap(moduleExports.Channel.prototype, 'emit', this.getChannelEmitPatch.bind(this));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.ConfirmChannel.prototype.publish)) {\n            this._wrap(moduleExports.ConfirmChannel.prototype, 'publish', this.getConfirmedPublishPatch.bind(this, moduleVersion));\n        }\n        return moduleExports;\n    }\n    unpatchChannelModel(moduleExports) {\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.publish)) {\n            this._unwrap(moduleExports.Channel.prototype, 'publish');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.consume)) {\n            this._unwrap(moduleExports.Channel.prototype, 'consume');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.ack)) {\n            this._unwrap(moduleExports.Channel.prototype, 'ack');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.nack)) {\n            this._unwrap(moduleExports.Channel.prototype, 'nack');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.reject)) {\n            this._unwrap(moduleExports.Channel.prototype, 'reject');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.ackAll)) {\n            this._unwrap(moduleExports.Channel.prototype, 'ackAll');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.nackAll)) {\n            this._unwrap(moduleExports.Channel.prototype, 'nackAll');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.emit)) {\n            this._unwrap(moduleExports.Channel.prototype, 'emit');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.ConfirmChannel.prototype.publish)) {\n            this._unwrap(moduleExports.ConfirmChannel.prototype, 'publish');\n        }\n        return moduleExports;\n    }\n    getConnectPatch(original) {\n        return function patchedConnect(url, socketOptions, openCallback) {\n            return original.call(this, url, socketOptions, function (err, conn) {\n                if (err == null) {\n                    const urlAttributes = (0, utils_1.getConnectionAttributesFromUrl)(url);\n                    // the type of conn in @types/amqplib is amqp.Connection, but in practice the library send the\n                    // `serverProperties` on the `conn` and not in a property `connection`.\n                    // I don't have capacity to debug it currently but it should probably be fixed in @types or\n                    // in the package itself\n                    // currently setting as any to calm typescript\n                    const serverAttributes = (0, utils_1.getConnectionAttributesFromServer)(conn);\n                    conn[utils_1.CONNECTION_ATTRIBUTES] = Object.assign(Object.assign({}, urlAttributes), serverAttributes);\n                }\n                openCallback.apply(this, arguments);\n            });\n        };\n    }\n    getChannelEmitPatch(original) {\n        const self = this;\n        return function emit(eventName) {\n            if (eventName === 'close') {\n                self.endAllSpansOnChannel(this, true, types_1.EndOperation.ChannelClosed, undefined);\n                const activeTimer = this[utils_1.CHANNEL_CONSUME_TIMEOUT_TIMER];\n                if (activeTimer) {\n                    clearInterval(activeTimer);\n                }\n                this[utils_1.CHANNEL_CONSUME_TIMEOUT_TIMER] = undefined;\n            }\n            else if (eventName === 'error') {\n                self.endAllSpansOnChannel(this, true, types_1.EndOperation.ChannelError, undefined);\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    getAckAllPatch(isRejected, endOperation, original) {\n        const self = this;\n        return function ackAll(requeueOrEmpty) {\n            self.endAllSpansOnChannel(this, isRejected, endOperation, requeueOrEmpty);\n            return original.apply(this, arguments);\n        };\n    }\n    getAckPatch(isRejected, endOperation, original) {\n        const self = this;\n        return function ack(message, allUpToOrRequeue, requeue) {\n            var _a;\n            const channel = this;\n            // we use this patch in reject function as well, but it has different signature\n            const requeueResolved = endOperation === types_1.EndOperation.Reject ? allUpToOrRequeue : requeue;\n            const spansNotEnded = (_a = channel[utils_1.CHANNEL_SPANS_NOT_ENDED]) !== null && _a !== void 0 ? _a : [];\n            const msgIndex = spansNotEnded.findIndex(msgDetails => msgDetails.msg === message);\n            if (msgIndex < 0) {\n                // should not happen in happy flow\n                // but possible if user is calling the api function ack twice with same message\n                self.endConsumerSpan(message, isRejected, endOperation, requeueResolved);\n            }\n            else if (endOperation !== types_1.EndOperation.Reject && allUpToOrRequeue) {\n                for (let i = 0; i <= msgIndex; i++) {\n                    self.endConsumerSpan(spansNotEnded[i].msg, isRejected, endOperation, requeueResolved);\n                }\n                spansNotEnded.splice(0, msgIndex + 1);\n            }\n            else {\n                self.endConsumerSpan(message, isRejected, endOperation, requeueResolved);\n                spansNotEnded.splice(msgIndex, 1);\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    getConsumePatch(moduleVersion, original) {\n        const self = this;\n        return function consume(queue, onMessage, options) {\n            const channel = this;\n            if (!Object.prototype.hasOwnProperty.call(channel, utils_1.CHANNEL_SPANS_NOT_ENDED)) {\n                const { consumeTimeoutMs } = self.getConfig();\n                if (consumeTimeoutMs) {\n                    const timer = setInterval(() => {\n                        self.checkConsumeTimeoutOnChannel(channel);\n                    }, consumeTimeoutMs);\n                    timer.unref();\n                    channel[utils_1.CHANNEL_CONSUME_TIMEOUT_TIMER] = timer;\n                }\n                channel[utils_1.CHANNEL_SPANS_NOT_ENDED] = [];\n            }\n            const patchedOnMessage = function (msg) {\n                var _a, _b, _c, _d, _e;\n                // msg is expected to be null for signaling consumer cancel notification\n                // https://www.rabbitmq.com/consumer-cancel.html\n                // in this case, we do not start a span, as this is not a real message.\n                if (!msg) {\n                    return onMessage.call(this, msg);\n                }\n                const headers = (_a = msg.properties.headers) !== null && _a !== void 0 ? _a : {};\n                let parentContext = api_1.propagation.extract(api_1.ROOT_CONTEXT, headers);\n                const exchange = (_b = msg.fields) === null || _b === void 0 ? void 0 : _b.exchange;\n                let links;\n                if (self._config.useLinksForConsume) {\n                    const parentSpanContext = parentContext\n                        ? (_c = api_1.trace.getSpan(parentContext)) === null || _c === void 0 ? void 0 : _c.spanContext()\n                        : undefined;\n                    parentContext = undefined;\n                    if (parentSpanContext) {\n                        links = [\n                            {\n                                context: parentSpanContext,\n                            },\n                        ];\n                    }\n                }\n                const span = self.tracer.startSpan(`${queue} process`, {\n                    kind: api_1.SpanKind.CONSUMER,\n                    attributes: Object.assign(Object.assign({}, (_d = channel === null || channel === void 0 ? void 0 : channel.connection) === null || _d === void 0 ? void 0 : _d[utils_1.CONNECTION_ATTRIBUTES]), { [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION]: exchange, [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION_KIND]: semantic_conventions_1.MESSAGINGDESTINATIONKINDVALUES_TOPIC, [semantic_conventions_1.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY]: (_e = msg.fields) === null || _e === void 0 ? void 0 : _e.routingKey, [semantic_conventions_1.SEMATTRS_MESSAGING_OPERATION]: semantic_conventions_1.MESSAGINGOPERATIONVALUES_PROCESS, [semantic_conventions_1.SEMATTRS_MESSAGING_MESSAGE_ID]: msg === null || msg === void 0 ? void 0 : msg.properties.messageId, [semantic_conventions_1.SEMATTRS_MESSAGING_CONVERSATION_ID]: msg === null || msg === void 0 ? void 0 : msg.properties.correlationId }),\n                    links,\n                }, parentContext);\n                const { consumeHook } = self.getConfig();\n                if (consumeHook) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => consumeHook(span, { moduleVersion, msg }), e => {\n                        if (e) {\n                            api_1.diag.error('amqplib instrumentation: consumerHook error', e);\n                        }\n                    }, true);\n                }\n                if (!(options === null || options === void 0 ? void 0 : options.noAck)) {\n                    // store the message on the channel so we can close the span on ackAll etc\n                    channel[utils_1.CHANNEL_SPANS_NOT_ENDED].push({\n                        msg,\n                        timeOfConsume: (0, core_1.hrTime)(),\n                    });\n                    // store the span on the message, so we can end it when user call 'ack' on it\n                    msg[utils_1.MESSAGE_STORED_SPAN] = span;\n                }\n                const setContext = parentContext\n                    ? parentContext\n                    : api_1.ROOT_CONTEXT;\n                api_1.context.with(api_1.trace.setSpan(setContext, span), () => {\n                    onMessage.call(this, msg);\n                });\n                if (options === null || options === void 0 ? void 0 : options.noAck) {\n                    self.callConsumeEndHook(span, msg, false, types_1.EndOperation.AutoAck);\n                    span.end();\n                }\n            };\n            arguments[1] = patchedOnMessage;\n            return original.apply(this, arguments);\n        };\n    }\n    getConfirmedPublishPatch(moduleVersion, original) {\n        const self = this;\n        return function confirmedPublish(exchange, routingKey, content, options, callback) {\n            const channel = this;\n            const { span, modifiedOptions } = self.createPublishSpan(self, exchange, routingKey, channel, options);\n            const { publishHook } = self.getConfig();\n            if (publishHook) {\n                (0, instrumentation_1.safeExecuteInTheMiddle)(() => publishHook(span, {\n                    moduleVersion,\n                    exchange,\n                    routingKey,\n                    content,\n                    options: modifiedOptions,\n                    isConfirmChannel: true,\n                }), e => {\n                    if (e) {\n                        api_1.diag.error('amqplib instrumentation: publishHook error', e);\n                    }\n                }, true);\n            }\n            const patchedOnConfirm = function (err, ok) {\n                try {\n                    callback === null || callback === void 0 ? void 0 : callback.call(this, err, ok);\n                }\n                finally {\n                    const { publishConfirmHook } = self.getConfig();\n                    if (publishConfirmHook) {\n                        (0, instrumentation_1.safeExecuteInTheMiddle)(() => publishConfirmHook(span, {\n                            moduleVersion,\n                            exchange,\n                            routingKey,\n                            content,\n                            options,\n                            isConfirmChannel: true,\n                            confirmError: err,\n                        }), e => {\n                            if (e) {\n                                api_1.diag.error('amqplib instrumentation: publishConfirmHook error', e);\n                            }\n                        }, true);\n                    }\n                    if (err) {\n                        span.setStatus({\n                            code: api_1.SpanStatusCode.ERROR,\n                            message: \"message confirmation has been nack'ed\",\n                        });\n                    }\n                    span.end();\n                }\n            };\n            // calling confirm channel publish function is storing the message in queue and registering the callback for broker confirm.\n            // span ends in the patched callback.\n            const markedContext = (0, utils_1.markConfirmChannelTracing)(api_1.context.active());\n            const argumentsCopy = [...arguments];\n            argumentsCopy[3] = modifiedOptions;\n            argumentsCopy[4] = api_1.context.bind((0, utils_1.unmarkConfirmChannelTracing)(api_1.trace.setSpan(markedContext, span)), patchedOnConfirm);\n            return api_1.context.with(markedContext, original.bind(this, ...argumentsCopy));\n        };\n    }\n    getPublishPatch(moduleVersion, original) {\n        const self = this;\n        return function publish(exchange, routingKey, content, options) {\n            if ((0, utils_1.isConfirmChannelTracing)(api_1.context.active())) {\n                // work already done\n                return original.apply(this, arguments);\n            }\n            else {\n                const channel = this;\n                const { span, modifiedOptions } = self.createPublishSpan(self, exchange, routingKey, channel, options);\n                const { publishHook } = self.getConfig();\n                if (publishHook) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => publishHook(span, {\n                        moduleVersion,\n                        exchange,\n                        routingKey,\n                        content,\n                        options: modifiedOptions,\n                        isConfirmChannel: false,\n                    }), e => {\n                        if (e) {\n                            api_1.diag.error('amqplib instrumentation: publishHook error', e);\n                        }\n                    }, true);\n                }\n                // calling normal channel publish function is only storing the message in queue.\n                // it does not send it and waits for an ack, so the span duration is expected to be very short.\n                const argumentsCopy = [...arguments];\n                argumentsCopy[3] = modifiedOptions;\n                const originalRes = original.apply(this, argumentsCopy);\n                span.end();\n                return originalRes;\n            }\n        };\n    }\n    createPublishSpan(self, exchange, routingKey, channel, options) {\n        var _a;\n        const normalizedExchange = (0, utils_1.normalizeExchange)(exchange);\n        const span = self.tracer.startSpan(`publish ${normalizedExchange}`, {\n            kind: api_1.SpanKind.PRODUCER,\n            attributes: Object.assign(Object.assign({}, channel.connection[utils_1.CONNECTION_ATTRIBUTES]), { [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION]: exchange, [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION_KIND]: semantic_conventions_1.MESSAGINGDESTINATIONKINDVALUES_TOPIC, [semantic_conventions_1.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY]: routingKey, [semantic_conventions_1.SEMATTRS_MESSAGING_MESSAGE_ID]: options === null || options === void 0 ? void 0 : options.messageId, [semantic_conventions_1.SEMATTRS_MESSAGING_CONVERSATION_ID]: options === null || options === void 0 ? void 0 : options.correlationId }),\n        });\n        const modifiedOptions = options !== null && options !== void 0 ? options : {};\n        modifiedOptions.headers = (_a = modifiedOptions.headers) !== null && _a !== void 0 ? _a : {};\n        api_1.propagation.inject(api_1.trace.setSpan(api_1.context.active(), span), modifiedOptions.headers);\n        return { span, modifiedOptions };\n    }\n    endConsumerSpan(message, isRejected, operation, requeue) {\n        const storedSpan = message[utils_1.MESSAGE_STORED_SPAN];\n        if (!storedSpan)\n            return;\n        if (isRejected !== false) {\n            storedSpan.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: operation !== types_1.EndOperation.ChannelClosed &&\n                    operation !== types_1.EndOperation.ChannelError\n                    ? `${operation} called on message${requeue === true\n                        ? ' with requeue'\n                        : requeue === false\n                            ? ' without requeue'\n                            : ''}`\n                    : operation,\n            });\n        }\n        this.callConsumeEndHook(storedSpan, message, isRejected, operation);\n        storedSpan.end();\n        message[utils_1.MESSAGE_STORED_SPAN] = undefined;\n    }\n    endAllSpansOnChannel(channel, isRejected, operation, requeue) {\n        var _a;\n        const spansNotEnded = (_a = channel[utils_1.CHANNEL_SPANS_NOT_ENDED]) !== null && _a !== void 0 ? _a : [];\n        spansNotEnded.forEach(msgDetails => {\n            this.endConsumerSpan(msgDetails.msg, isRejected, operation, requeue);\n        });\n        channel[utils_1.CHANNEL_SPANS_NOT_ENDED] = [];\n    }\n    callConsumeEndHook(span, msg, rejected, endOperation) {\n        const { consumeEndHook } = this.getConfig();\n        if (!consumeEndHook)\n            return;\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => consumeEndHook(span, { msg, rejected, endOperation }), e => {\n            if (e) {\n                api_1.diag.error('amqplib instrumentation: consumerEndHook error', e);\n            }\n        }, true);\n    }\n    checkConsumeTimeoutOnChannel(channel) {\n        var _a;\n        const currentTime = (0, core_1.hrTime)();\n        const spansNotEnded = (_a = channel[utils_1.CHANNEL_SPANS_NOT_ENDED]) !== null && _a !== void 0 ? _a : [];\n        let i;\n        const { consumeTimeoutMs } = this.getConfig();\n        for (i = 0; i < spansNotEnded.length; i++) {\n            const currMessage = spansNotEnded[i];\n            const timeFromConsume = (0, core_1.hrTimeDuration)(currMessage.timeOfConsume, currentTime);\n            if ((0, core_1.hrTimeToMilliseconds)(timeFromConsume) < consumeTimeoutMs) {\n                break;\n            }\n            this.endConsumerSpan(currMessage.msg, null, types_1.EndOperation.InstrumentationTimeout, true);\n        }\n        spansNotEnded.splice(0, i);\n    }\n}\nexports.AmqplibInstrumentation = AmqplibInstrumentation;\n//# sourceMappingURL=amqplib.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/amqplib.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n__exportStar(__webpack_require__(/*! ./amqplib */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/amqplib.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_CONFIG = exports.EndOperation = void 0;\nvar EndOperation;\n(function (EndOperation) {\n    EndOperation[\"AutoAck\"] = \"auto ack\";\n    EndOperation[\"Ack\"] = \"ack\";\n    EndOperation[\"AckAll\"] = \"ackAll\";\n    EndOperation[\"Reject\"] = \"reject\";\n    EndOperation[\"Nack\"] = \"nack\";\n    EndOperation[\"NackAll\"] = \"nackAll\";\n    EndOperation[\"ChannelClosed\"] = \"channel closed\";\n    EndOperation[\"ChannelError\"] = \"channel error\";\n    EndOperation[\"InstrumentationTimeout\"] = \"instrumentation timeout\";\n})(EndOperation = exports.EndOperation || (exports.EndOperation = {}));\nexports.DEFAULT_CONFIG = {\n    consumeTimeoutMs: 1000 * 60,\n    useLinksForConsume: false,\n};\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isConfirmChannelTracing = exports.unmarkConfirmChannelTracing = exports.markConfirmChannelTracing = exports.getConnectionAttributesFromUrl = exports.getConnectionAttributesFromServer = exports.normalizeExchange = exports.CONNECTION_ATTRIBUTES = exports.CHANNEL_CONSUME_TIMEOUT_TIMER = exports.CHANNEL_SPANS_NOT_ENDED = exports.MESSAGE_STORED_SPAN = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nexports.MESSAGE_STORED_SPAN = Symbol('opentelemetry.amqplib.message.stored-span');\nexports.CHANNEL_SPANS_NOT_ENDED = Symbol('opentelemetry.amqplib.channel.spans-not-ended');\nexports.CHANNEL_CONSUME_TIMEOUT_TIMER = Symbol('opentelemetry.amqplib.channel.consumer-timeout-timer');\nexports.CONNECTION_ATTRIBUTES = Symbol('opentelemetry.amqplib.connection.attributes');\nconst IS_CONFIRM_CHANNEL_CONTEXT_KEY = (0, api_1.createContextKey)('opentelemetry.amqplib.channel.is-confirm-channel');\nconst normalizeExchange = (exchangeName) => exchangeName !== '' ? exchangeName : '<default>';\nexports.normalizeExchange = normalizeExchange;\nconst censorPassword = (url) => {\n    return url.replace(/:[^:@/]*@/, ':***@');\n};\nconst getPort = (portFromUrl, resolvedProtocol) => {\n    // we are using the resolved protocol which is upper case\n    // this code mimic the behavior of the amqplib which is used to set connection params\n    return portFromUrl || (resolvedProtocol === 'AMQP' ? 5672 : 5671);\n};\nconst getProtocol = (protocolFromUrl) => {\n    const resolvedProtocol = protocolFromUrl || 'amqp';\n    // the substring removed the ':' part of the protocol ('amqp:' -> 'amqp')\n    const noEndingColon = resolvedProtocol.endsWith(':')\n        ? resolvedProtocol.substring(0, resolvedProtocol.length - 1)\n        : resolvedProtocol;\n    // upper cases to match spec\n    return noEndingColon.toUpperCase();\n};\nconst getHostname = (hostnameFromUrl) => {\n    // if user supplies empty hostname, it gets forwarded to 'net' package which default it to localhost.\n    // https://nodejs.org/docs/latest-v12.x/api/net.html#net_socket_connect_options_connectlistener\n    return hostnameFromUrl || 'localhost';\n};\nconst extractConnectionAttributeOrLog = (url, attributeKey, attributeValue, nameForLog) => {\n    if (attributeValue) {\n        return { [attributeKey]: attributeValue };\n    }\n    else {\n        api_1.diag.error(`amqplib instrumentation: could not extract connection attribute ${nameForLog} from user supplied url`, {\n            url,\n        });\n        return {};\n    }\n};\nconst getConnectionAttributesFromServer = (conn) => {\n    var _a, _b;\n    const product = (_b = (_a = conn.serverProperties.product) === null || _a === void 0 ? void 0 : _a.toLowerCase) === null || _b === void 0 ? void 0 : _b.call(_a);\n    if (product) {\n        return {\n            [semantic_conventions_1.SEMATTRS_MESSAGING_SYSTEM]: product,\n        };\n    }\n    else {\n        return {};\n    }\n};\nexports.getConnectionAttributesFromServer = getConnectionAttributesFromServer;\nconst getConnectionAttributesFromUrl = (url) => {\n    const attributes = {\n        [semantic_conventions_1.SEMATTRS_MESSAGING_PROTOCOL_VERSION]: '0.9.1', // this is the only protocol supported by the instrumented library\n    };\n    url = url || 'amqp://localhost';\n    if (typeof url === 'object') {\n        const connectOptions = url;\n        const protocol = getProtocol(connectOptions === null || connectOptions === void 0 ? void 0 : connectOptions.protocol);\n        Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(url, semantic_conventions_1.SEMATTRS_MESSAGING_PROTOCOL, protocol, 'protocol')));\n        const hostname = getHostname(connectOptions === null || connectOptions === void 0 ? void 0 : connectOptions.hostname);\n        Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(url, semantic_conventions_1.SEMATTRS_NET_PEER_NAME, hostname, 'hostname')));\n        const port = getPort(connectOptions.port, protocol);\n        Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(url, semantic_conventions_1.SEMATTRS_NET_PEER_PORT, port, 'port')));\n    }\n    else {\n        const censoredUrl = censorPassword(url);\n        attributes[semantic_conventions_1.SEMATTRS_MESSAGING_URL] = censoredUrl;\n        try {\n            const urlParts = new URL(censoredUrl);\n            const protocol = getProtocol(urlParts.protocol);\n            Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(censoredUrl, semantic_conventions_1.SEMATTRS_MESSAGING_PROTOCOL, protocol, 'protocol')));\n            const hostname = getHostname(urlParts.hostname);\n            Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(censoredUrl, semantic_conventions_1.SEMATTRS_NET_PEER_NAME, hostname, 'hostname')));\n            const port = getPort(urlParts.port ? parseInt(urlParts.port) : undefined, protocol);\n            Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(censoredUrl, semantic_conventions_1.SEMATTRS_NET_PEER_PORT, port, 'port')));\n        }\n        catch (err) {\n            api_1.diag.error('amqplib instrumentation: error while extracting connection details from connection url', {\n                censoredUrl,\n                err,\n            });\n        }\n    }\n    return attributes;\n};\nexports.getConnectionAttributesFromUrl = getConnectionAttributesFromUrl;\nconst markConfirmChannelTracing = (context) => {\n    return context.setValue(IS_CONFIRM_CHANNEL_CONTEXT_KEY, true);\n};\nexports.markConfirmChannelTracing = markConfirmChannelTracing;\nconst unmarkConfirmChannelTracing = (context) => {\n    return context.deleteValue(IS_CONFIRM_CHANNEL_CONTEXT_KEY);\n};\nexports.unmarkConfirmChannelTracing = unmarkConfirmChannelTracing;\nconst isConfirmChannelTracing = (context) => {\n    return context.getValue(IS_CONFIRM_CHANNEL_CONTEXT_KEY) === true;\n};\nexports.isConfirmChannelTracing = isConfirmChannelTracing;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/utils.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.46.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-amqplib';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/amqplib.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/amqplib.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AmqplibInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nconst types_1 = __webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/utils.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/version.js\");\nconst supportedVersions = ['>=0.5.5 <1'];\nclass AmqplibInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, Object.assign(Object.assign({}, types_1.DEFAULT_CONFIG), config));\n    }\n    setConfig(config = {}) {\n        super.setConfig(Object.assign(Object.assign({}, types_1.DEFAULT_CONFIG), config));\n    }\n    init() {\n        const channelModelModuleFile = new instrumentation_1.InstrumentationNodeModuleFile('amqplib/lib/channel_model.js', supportedVersions, this.patchChannelModel.bind(this), this.unpatchChannelModel.bind(this));\n        const callbackModelModuleFile = new instrumentation_1.InstrumentationNodeModuleFile('amqplib/lib/callback_model.js', supportedVersions, this.patchChannelModel.bind(this), this.unpatchChannelModel.bind(this));\n        const connectModuleFile = new instrumentation_1.InstrumentationNodeModuleFile('amqplib/lib/connect.js', supportedVersions, this.patchConnect.bind(this), this.unpatchConnect.bind(this));\n        const module = new instrumentation_1.InstrumentationNodeModuleDefinition('amqplib', supportedVersions, undefined, undefined, [channelModelModuleFile, connectModuleFile, callbackModelModuleFile]);\n        return module;\n    }\n    patchConnect(moduleExports) {\n        moduleExports = this.unpatchConnect(moduleExports);\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.connect)) {\n            this._wrap(moduleExports, 'connect', this.getConnectPatch.bind(this));\n        }\n        return moduleExports;\n    }\n    unpatchConnect(moduleExports) {\n        if ((0, instrumentation_1.isWrapped)(moduleExports.connect)) {\n            this._unwrap(moduleExports, 'connect');\n        }\n        return moduleExports;\n    }\n    patchChannelModel(moduleExports, moduleVersion) {\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.publish)) {\n            this._wrap(moduleExports.Channel.prototype, 'publish', this.getPublishPatch.bind(this, moduleVersion));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.consume)) {\n            this._wrap(moduleExports.Channel.prototype, 'consume', this.getConsumePatch.bind(this, moduleVersion));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.ack)) {\n            this._wrap(moduleExports.Channel.prototype, 'ack', this.getAckPatch.bind(this, false, types_1.EndOperation.Ack));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.nack)) {\n            this._wrap(moduleExports.Channel.prototype, 'nack', this.getAckPatch.bind(this, true, types_1.EndOperation.Nack));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.reject)) {\n            this._wrap(moduleExports.Channel.prototype, 'reject', this.getAckPatch.bind(this, true, types_1.EndOperation.Reject));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.ackAll)) {\n            this._wrap(moduleExports.Channel.prototype, 'ackAll', this.getAckAllPatch.bind(this, false, types_1.EndOperation.AckAll));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.nackAll)) {\n            this._wrap(moduleExports.Channel.prototype, 'nackAll', this.getAckAllPatch.bind(this, true, types_1.EndOperation.NackAll));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.emit)) {\n            this._wrap(moduleExports.Channel.prototype, 'emit', this.getChannelEmitPatch.bind(this));\n        }\n        if (!(0, instrumentation_1.isWrapped)(moduleExports.ConfirmChannel.prototype.publish)) {\n            this._wrap(moduleExports.ConfirmChannel.prototype, 'publish', this.getConfirmedPublishPatch.bind(this, moduleVersion));\n        }\n        return moduleExports;\n    }\n    unpatchChannelModel(moduleExports) {\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.publish)) {\n            this._unwrap(moduleExports.Channel.prototype, 'publish');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.consume)) {\n            this._unwrap(moduleExports.Channel.prototype, 'consume');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.ack)) {\n            this._unwrap(moduleExports.Channel.prototype, 'ack');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.nack)) {\n            this._unwrap(moduleExports.Channel.prototype, 'nack');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.reject)) {\n            this._unwrap(moduleExports.Channel.prototype, 'reject');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.ackAll)) {\n            this._unwrap(moduleExports.Channel.prototype, 'ackAll');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.nackAll)) {\n            this._unwrap(moduleExports.Channel.prototype, 'nackAll');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.Channel.prototype.emit)) {\n            this._unwrap(moduleExports.Channel.prototype, 'emit');\n        }\n        if ((0, instrumentation_1.isWrapped)(moduleExports.ConfirmChannel.prototype.publish)) {\n            this._unwrap(moduleExports.ConfirmChannel.prototype, 'publish');\n        }\n        return moduleExports;\n    }\n    getConnectPatch(original) {\n        return function patchedConnect(url, socketOptions, openCallback) {\n            return original.call(this, url, socketOptions, function (err, conn) {\n                if (err == null) {\n                    const urlAttributes = (0, utils_1.getConnectionAttributesFromUrl)(url);\n                    // the type of conn in @types/amqplib is amqp.Connection, but in practice the library send the\n                    // `serverProperties` on the `conn` and not in a property `connection`.\n                    // I don't have capacity to debug it currently but it should probably be fixed in @types or\n                    // in the package itself\n                    // currently setting as any to calm typescript\n                    const serverAttributes = (0, utils_1.getConnectionAttributesFromServer)(conn);\n                    conn[utils_1.CONNECTION_ATTRIBUTES] = Object.assign(Object.assign({}, urlAttributes), serverAttributes);\n                }\n                openCallback.apply(this, arguments);\n            });\n        };\n    }\n    getChannelEmitPatch(original) {\n        const self = this;\n        return function emit(eventName) {\n            if (eventName === 'close') {\n                self.endAllSpansOnChannel(this, true, types_1.EndOperation.ChannelClosed, undefined);\n                const activeTimer = this[utils_1.CHANNEL_CONSUME_TIMEOUT_TIMER];\n                if (activeTimer) {\n                    clearInterval(activeTimer);\n                }\n                this[utils_1.CHANNEL_CONSUME_TIMEOUT_TIMER] = undefined;\n            }\n            else if (eventName === 'error') {\n                self.endAllSpansOnChannel(this, true, types_1.EndOperation.ChannelError, undefined);\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    getAckAllPatch(isRejected, endOperation, original) {\n        const self = this;\n        return function ackAll(requeueOrEmpty) {\n            self.endAllSpansOnChannel(this, isRejected, endOperation, requeueOrEmpty);\n            return original.apply(this, arguments);\n        };\n    }\n    getAckPatch(isRejected, endOperation, original) {\n        const self = this;\n        return function ack(message, allUpToOrRequeue, requeue) {\n            var _a;\n            const channel = this;\n            // we use this patch in reject function as well, but it has different signature\n            const requeueResolved = endOperation === types_1.EndOperation.Reject ? allUpToOrRequeue : requeue;\n            const spansNotEnded = (_a = channel[utils_1.CHANNEL_SPANS_NOT_ENDED]) !== null && _a !== void 0 ? _a : [];\n            const msgIndex = spansNotEnded.findIndex(msgDetails => msgDetails.msg === message);\n            if (msgIndex < 0) {\n                // should not happen in happy flow\n                // but possible if user is calling the api function ack twice with same message\n                self.endConsumerSpan(message, isRejected, endOperation, requeueResolved);\n            }\n            else if (endOperation !== types_1.EndOperation.Reject && allUpToOrRequeue) {\n                for (let i = 0; i <= msgIndex; i++) {\n                    self.endConsumerSpan(spansNotEnded[i].msg, isRejected, endOperation, requeueResolved);\n                }\n                spansNotEnded.splice(0, msgIndex + 1);\n            }\n            else {\n                self.endConsumerSpan(message, isRejected, endOperation, requeueResolved);\n                spansNotEnded.splice(msgIndex, 1);\n            }\n            return original.apply(this, arguments);\n        };\n    }\n    getConsumePatch(moduleVersion, original) {\n        const self = this;\n        return function consume(queue, onMessage, options) {\n            const channel = this;\n            if (!Object.prototype.hasOwnProperty.call(channel, utils_1.CHANNEL_SPANS_NOT_ENDED)) {\n                const { consumeTimeoutMs } = self.getConfig();\n                if (consumeTimeoutMs) {\n                    const timer = setInterval(() => {\n                        self.checkConsumeTimeoutOnChannel(channel);\n                    }, consumeTimeoutMs);\n                    timer.unref();\n                    channel[utils_1.CHANNEL_CONSUME_TIMEOUT_TIMER] = timer;\n                }\n                channel[utils_1.CHANNEL_SPANS_NOT_ENDED] = [];\n            }\n            const patchedOnMessage = function (msg) {\n                var _a, _b, _c, _d, _e;\n                // msg is expected to be null for signaling consumer cancel notification\n                // https://www.rabbitmq.com/consumer-cancel.html\n                // in this case, we do not start a span, as this is not a real message.\n                if (!msg) {\n                    return onMessage.call(this, msg);\n                }\n                const headers = (_a = msg.properties.headers) !== null && _a !== void 0 ? _a : {};\n                let parentContext = api_1.propagation.extract(api_1.ROOT_CONTEXT, headers);\n                const exchange = (_b = msg.fields) === null || _b === void 0 ? void 0 : _b.exchange;\n                let links;\n                if (self._config.useLinksForConsume) {\n                    const parentSpanContext = parentContext\n                        ? (_c = api_1.trace.getSpan(parentContext)) === null || _c === void 0 ? void 0 : _c.spanContext()\n                        : undefined;\n                    parentContext = undefined;\n                    if (parentSpanContext) {\n                        links = [\n                            {\n                                context: parentSpanContext,\n                            },\n                        ];\n                    }\n                }\n                const span = self.tracer.startSpan(`${queue} process`, {\n                    kind: api_1.SpanKind.CONSUMER,\n                    attributes: Object.assign(Object.assign({}, (_d = channel === null || channel === void 0 ? void 0 : channel.connection) === null || _d === void 0 ? void 0 : _d[utils_1.CONNECTION_ATTRIBUTES]), { [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION]: exchange, [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION_KIND]: semantic_conventions_1.MESSAGINGDESTINATIONKINDVALUES_TOPIC, [semantic_conventions_1.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY]: (_e = msg.fields) === null || _e === void 0 ? void 0 : _e.routingKey, [semantic_conventions_1.SEMATTRS_MESSAGING_OPERATION]: semantic_conventions_1.MESSAGINGOPERATIONVALUES_PROCESS, [semantic_conventions_1.SEMATTRS_MESSAGING_MESSAGE_ID]: msg === null || msg === void 0 ? void 0 : msg.properties.messageId, [semantic_conventions_1.SEMATTRS_MESSAGING_CONVERSATION_ID]: msg === null || msg === void 0 ? void 0 : msg.properties.correlationId }),\n                    links,\n                }, parentContext);\n                const { consumeHook } = self.getConfig();\n                if (consumeHook) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => consumeHook(span, { moduleVersion, msg }), e => {\n                        if (e) {\n                            api_1.diag.error('amqplib instrumentation: consumerHook error', e);\n                        }\n                    }, true);\n                }\n                if (!(options === null || options === void 0 ? void 0 : options.noAck)) {\n                    // store the message on the channel so we can close the span on ackAll etc\n                    channel[utils_1.CHANNEL_SPANS_NOT_ENDED].push({\n                        msg,\n                        timeOfConsume: (0, core_1.hrTime)(),\n                    });\n                    // store the span on the message, so we can end it when user call 'ack' on it\n                    msg[utils_1.MESSAGE_STORED_SPAN] = span;\n                }\n                const setContext = parentContext\n                    ? parentContext\n                    : api_1.ROOT_CONTEXT;\n                api_1.context.with(api_1.trace.setSpan(setContext, span), () => {\n                    onMessage.call(this, msg);\n                });\n                if (options === null || options === void 0 ? void 0 : options.noAck) {\n                    self.callConsumeEndHook(span, msg, false, types_1.EndOperation.AutoAck);\n                    span.end();\n                }\n            };\n            arguments[1] = patchedOnMessage;\n            return original.apply(this, arguments);\n        };\n    }\n    getConfirmedPublishPatch(moduleVersion, original) {\n        const self = this;\n        return function confirmedPublish(exchange, routingKey, content, options, callback) {\n            const channel = this;\n            const { span, modifiedOptions } = self.createPublishSpan(self, exchange, routingKey, channel, options);\n            const { publishHook } = self.getConfig();\n            if (publishHook) {\n                (0, instrumentation_1.safeExecuteInTheMiddle)(() => publishHook(span, {\n                    moduleVersion,\n                    exchange,\n                    routingKey,\n                    content,\n                    options: modifiedOptions,\n                    isConfirmChannel: true,\n                }), e => {\n                    if (e) {\n                        api_1.diag.error('amqplib instrumentation: publishHook error', e);\n                    }\n                }, true);\n            }\n            const patchedOnConfirm = function (err, ok) {\n                try {\n                    callback === null || callback === void 0 ? void 0 : callback.call(this, err, ok);\n                }\n                finally {\n                    const { publishConfirmHook } = self.getConfig();\n                    if (publishConfirmHook) {\n                        (0, instrumentation_1.safeExecuteInTheMiddle)(() => publishConfirmHook(span, {\n                            moduleVersion,\n                            exchange,\n                            routingKey,\n                            content,\n                            options,\n                            isConfirmChannel: true,\n                            confirmError: err,\n                        }), e => {\n                            if (e) {\n                                api_1.diag.error('amqplib instrumentation: publishConfirmHook error', e);\n                            }\n                        }, true);\n                    }\n                    if (err) {\n                        span.setStatus({\n                            code: api_1.SpanStatusCode.ERROR,\n                            message: \"message confirmation has been nack'ed\",\n                        });\n                    }\n                    span.end();\n                }\n            };\n            // calling confirm channel publish function is storing the message in queue and registering the callback for broker confirm.\n            // span ends in the patched callback.\n            const markedContext = (0, utils_1.markConfirmChannelTracing)(api_1.context.active());\n            const argumentsCopy = [...arguments];\n            argumentsCopy[3] = modifiedOptions;\n            argumentsCopy[4] = api_1.context.bind((0, utils_1.unmarkConfirmChannelTracing)(api_1.trace.setSpan(markedContext, span)), patchedOnConfirm);\n            return api_1.context.with(markedContext, original.bind(this, ...argumentsCopy));\n        };\n    }\n    getPublishPatch(moduleVersion, original) {\n        const self = this;\n        return function publish(exchange, routingKey, content, options) {\n            if ((0, utils_1.isConfirmChannelTracing)(api_1.context.active())) {\n                // work already done\n                return original.apply(this, arguments);\n            }\n            else {\n                const channel = this;\n                const { span, modifiedOptions } = self.createPublishSpan(self, exchange, routingKey, channel, options);\n                const { publishHook } = self.getConfig();\n                if (publishHook) {\n                    (0, instrumentation_1.safeExecuteInTheMiddle)(() => publishHook(span, {\n                        moduleVersion,\n                        exchange,\n                        routingKey,\n                        content,\n                        options: modifiedOptions,\n                        isConfirmChannel: false,\n                    }), e => {\n                        if (e) {\n                            api_1.diag.error('amqplib instrumentation: publishHook error', e);\n                        }\n                    }, true);\n                }\n                // calling normal channel publish function is only storing the message in queue.\n                // it does not send it and waits for an ack, so the span duration is expected to be very short.\n                const argumentsCopy = [...arguments];\n                argumentsCopy[3] = modifiedOptions;\n                const originalRes = original.apply(this, argumentsCopy);\n                span.end();\n                return originalRes;\n            }\n        };\n    }\n    createPublishSpan(self, exchange, routingKey, channel, options) {\n        var _a;\n        const normalizedExchange = (0, utils_1.normalizeExchange)(exchange);\n        const span = self.tracer.startSpan(`publish ${normalizedExchange}`, {\n            kind: api_1.SpanKind.PRODUCER,\n            attributes: Object.assign(Object.assign({}, channel.connection[utils_1.CONNECTION_ATTRIBUTES]), { [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION]: exchange, [semantic_conventions_1.SEMATTRS_MESSAGING_DESTINATION_KIND]: semantic_conventions_1.MESSAGINGDESTINATIONKINDVALUES_TOPIC, [semantic_conventions_1.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY]: routingKey, [semantic_conventions_1.SEMATTRS_MESSAGING_MESSAGE_ID]: options === null || options === void 0 ? void 0 : options.messageId, [semantic_conventions_1.SEMATTRS_MESSAGING_CONVERSATION_ID]: options === null || options === void 0 ? void 0 : options.correlationId }),\n        });\n        const modifiedOptions = options !== null && options !== void 0 ? options : {};\n        modifiedOptions.headers = (_a = modifiedOptions.headers) !== null && _a !== void 0 ? _a : {};\n        api_1.propagation.inject(api_1.trace.setSpan(api_1.context.active(), span), modifiedOptions.headers);\n        return { span, modifiedOptions };\n    }\n    endConsumerSpan(message, isRejected, operation, requeue) {\n        const storedSpan = message[utils_1.MESSAGE_STORED_SPAN];\n        if (!storedSpan)\n            return;\n        if (isRejected !== false) {\n            storedSpan.setStatus({\n                code: api_1.SpanStatusCode.ERROR,\n                message: operation !== types_1.EndOperation.ChannelClosed &&\n                    operation !== types_1.EndOperation.ChannelError\n                    ? `${operation} called on message${requeue === true\n                        ? ' with requeue'\n                        : requeue === false\n                            ? ' without requeue'\n                            : ''}`\n                    : operation,\n            });\n        }\n        this.callConsumeEndHook(storedSpan, message, isRejected, operation);\n        storedSpan.end();\n        message[utils_1.MESSAGE_STORED_SPAN] = undefined;\n    }\n    endAllSpansOnChannel(channel, isRejected, operation, requeue) {\n        var _a;\n        const spansNotEnded = (_a = channel[utils_1.CHANNEL_SPANS_NOT_ENDED]) !== null && _a !== void 0 ? _a : [];\n        spansNotEnded.forEach(msgDetails => {\n            this.endConsumerSpan(msgDetails.msg, isRejected, operation, requeue);\n        });\n        channel[utils_1.CHANNEL_SPANS_NOT_ENDED] = [];\n    }\n    callConsumeEndHook(span, msg, rejected, endOperation) {\n        const { consumeEndHook } = this.getConfig();\n        if (!consumeEndHook)\n            return;\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => consumeEndHook(span, { msg, rejected, endOperation }), e => {\n            if (e) {\n                api_1.diag.error('amqplib instrumentation: consumerEndHook error', e);\n            }\n        }, true);\n    }\n    checkConsumeTimeoutOnChannel(channel) {\n        var _a;\n        const currentTime = (0, core_1.hrTime)();\n        const spansNotEnded = (_a = channel[utils_1.CHANNEL_SPANS_NOT_ENDED]) !== null && _a !== void 0 ? _a : [];\n        let i;\n        const { consumeTimeoutMs } = this.getConfig();\n        for (i = 0; i < spansNotEnded.length; i++) {\n            const currMessage = spansNotEnded[i];\n            const timeFromConsume = (0, core_1.hrTimeDuration)(currMessage.timeOfConsume, currentTime);\n            if ((0, core_1.hrTimeToMilliseconds)(timeFromConsume) < consumeTimeoutMs) {\n                break;\n            }\n            this.endConsumerSpan(currMessage.msg, null, types_1.EndOperation.InstrumentationTimeout, true);\n        }\n        spansNotEnded.splice(0, i);\n    }\n}\nexports.AmqplibInstrumentation = AmqplibInstrumentation;\n//# sourceMappingURL=amqplib.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/amqplib.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n__exportStar(__webpack_require__(/*! ./amqplib */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/amqplib.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_CONFIG = exports.EndOperation = void 0;\nvar EndOperation;\n(function (EndOperation) {\n    EndOperation[\"AutoAck\"] = \"auto ack\";\n    EndOperation[\"Ack\"] = \"ack\";\n    EndOperation[\"AckAll\"] = \"ackAll\";\n    EndOperation[\"Reject\"] = \"reject\";\n    EndOperation[\"Nack\"] = \"nack\";\n    EndOperation[\"NackAll\"] = \"nackAll\";\n    EndOperation[\"ChannelClosed\"] = \"channel closed\";\n    EndOperation[\"ChannelError\"] = \"channel error\";\n    EndOperation[\"InstrumentationTimeout\"] = \"instrumentation timeout\";\n})(EndOperation = exports.EndOperation || (exports.EndOperation = {}));\nexports.DEFAULT_CONFIG = {\n    consumeTimeoutMs: 1000 * 60,\n    useLinksForConsume: false,\n};\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/utils.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/utils.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isConfirmChannelTracing = exports.unmarkConfirmChannelTracing = exports.markConfirmChannelTracing = exports.getConnectionAttributesFromUrl = exports.getConnectionAttributesFromServer = exports.normalizeExchange = exports.CONNECTION_ATTRIBUTES = exports.CHANNEL_CONSUME_TIMEOUT_TIMER = exports.CHANNEL_SPANS_NOT_ENDED = exports.MESSAGE_STORED_SPAN = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst semantic_conventions_1 = __webpack_require__(/*! @opentelemetry/semantic-conventions */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+semantic-conventions@1.30.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js\");\nexports.MESSAGE_STORED_SPAN = Symbol('opentelemetry.amqplib.message.stored-span');\nexports.CHANNEL_SPANS_NOT_ENDED = Symbol('opentelemetry.amqplib.channel.spans-not-ended');\nexports.CHANNEL_CONSUME_TIMEOUT_TIMER = Symbol('opentelemetry.amqplib.channel.consumer-timeout-timer');\nexports.CONNECTION_ATTRIBUTES = Symbol('opentelemetry.amqplib.connection.attributes');\nconst IS_CONFIRM_CHANNEL_CONTEXT_KEY = (0, api_1.createContextKey)('opentelemetry.amqplib.channel.is-confirm-channel');\nconst normalizeExchange = (exchangeName) => exchangeName !== '' ? exchangeName : '<default>';\nexports.normalizeExchange = normalizeExchange;\nconst censorPassword = (url) => {\n    return url.replace(/:[^:@/]*@/, ':***@');\n};\nconst getPort = (portFromUrl, resolvedProtocol) => {\n    // we are using the resolved protocol which is upper case\n    // this code mimic the behavior of the amqplib which is used to set connection params\n    return portFromUrl || (resolvedProtocol === 'AMQP' ? 5672 : 5671);\n};\nconst getProtocol = (protocolFromUrl) => {\n    const resolvedProtocol = protocolFromUrl || 'amqp';\n    // the substring removed the ':' part of the protocol ('amqp:' -> 'amqp')\n    const noEndingColon = resolvedProtocol.endsWith(':')\n        ? resolvedProtocol.substring(0, resolvedProtocol.length - 1)\n        : resolvedProtocol;\n    // upper cases to match spec\n    return noEndingColon.toUpperCase();\n};\nconst getHostname = (hostnameFromUrl) => {\n    // if user supplies empty hostname, it gets forwarded to 'net' package which default it to localhost.\n    // https://nodejs.org/docs/latest-v12.x/api/net.html#net_socket_connect_options_connectlistener\n    return hostnameFromUrl || 'localhost';\n};\nconst extractConnectionAttributeOrLog = (url, attributeKey, attributeValue, nameForLog) => {\n    if (attributeValue) {\n        return { [attributeKey]: attributeValue };\n    }\n    else {\n        api_1.diag.error(`amqplib instrumentation: could not extract connection attribute ${nameForLog} from user supplied url`, {\n            url,\n        });\n        return {};\n    }\n};\nconst getConnectionAttributesFromServer = (conn) => {\n    var _a, _b;\n    const product = (_b = (_a = conn.serverProperties.product) === null || _a === void 0 ? void 0 : _a.toLowerCase) === null || _b === void 0 ? void 0 : _b.call(_a);\n    if (product) {\n        return {\n            [semantic_conventions_1.SEMATTRS_MESSAGING_SYSTEM]: product,\n        };\n    }\n    else {\n        return {};\n    }\n};\nexports.getConnectionAttributesFromServer = getConnectionAttributesFromServer;\nconst getConnectionAttributesFromUrl = (url) => {\n    const attributes = {\n        [semantic_conventions_1.SEMATTRS_MESSAGING_PROTOCOL_VERSION]: '0.9.1', // this is the only protocol supported by the instrumented library\n    };\n    url = url || 'amqp://localhost';\n    if (typeof url === 'object') {\n        const connectOptions = url;\n        const protocol = getProtocol(connectOptions === null || connectOptions === void 0 ? void 0 : connectOptions.protocol);\n        Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(url, semantic_conventions_1.SEMATTRS_MESSAGING_PROTOCOL, protocol, 'protocol')));\n        const hostname = getHostname(connectOptions === null || connectOptions === void 0 ? void 0 : connectOptions.hostname);\n        Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(url, semantic_conventions_1.SEMATTRS_NET_PEER_NAME, hostname, 'hostname')));\n        const port = getPort(connectOptions.port, protocol);\n        Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(url, semantic_conventions_1.SEMATTRS_NET_PEER_PORT, port, 'port')));\n    }\n    else {\n        const censoredUrl = censorPassword(url);\n        attributes[semantic_conventions_1.SEMATTRS_MESSAGING_URL] = censoredUrl;\n        try {\n            const urlParts = new URL(censoredUrl);\n            const protocol = getProtocol(urlParts.protocol);\n            Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(censoredUrl, semantic_conventions_1.SEMATTRS_MESSAGING_PROTOCOL, protocol, 'protocol')));\n            const hostname = getHostname(urlParts.hostname);\n            Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(censoredUrl, semantic_conventions_1.SEMATTRS_NET_PEER_NAME, hostname, 'hostname')));\n            const port = getPort(urlParts.port ? parseInt(urlParts.port) : undefined, protocol);\n            Object.assign(attributes, Object.assign({}, extractConnectionAttributeOrLog(censoredUrl, semantic_conventions_1.SEMATTRS_NET_PEER_PORT, port, 'port')));\n        }\n        catch (err) {\n            api_1.diag.error('amqplib instrumentation: error while extracting connection details from connection url', {\n                censoredUrl,\n                err,\n            });\n        }\n    }\n    return attributes;\n};\nexports.getConnectionAttributesFromUrl = getConnectionAttributesFromUrl;\nconst markConfirmChannelTracing = (context) => {\n    return context.setValue(IS_CONFIRM_CHANNEL_CONTEXT_KEY, true);\n};\nexports.markConfirmChannelTracing = markConfirmChannelTracing;\nconst unmarkConfirmChannelTracing = (context) => {\n    return context.deleteValue(IS_CONFIRM_CHANNEL_CONTEXT_KEY);\n};\nexports.unmarkConfirmChannelTracing = unmarkConfirmChannelTracing;\nconst isConfirmChannelTracing = (context) => {\n    return context.getValue(IS_CONFIRM_CHANNEL_CONTEXT_KEY) === true;\n};\nexports.isConfirmChannelTracing = isConfirmChannelTracing;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/utils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/version.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/version.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.46.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-amqplib';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iZTg0NWM5NDZiYzIyM2ZjM2ZmZTNmMmI1ZWEwZDMxMC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWFtcXBsaWIvYnVpbGQvc3JjL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CLEdBQUcsdUJBQXVCO0FBQzlDO0FBQ0EsdUJBQXVCO0FBQ3ZCLG9CQUFvQjtBQUNwQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9iZTg0NWM5NDZiYzIyM2ZjM2ZmZTNmMmI1ZWEwZDMxMFxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWFtcXBsaWJcXGJ1aWxkXFxzcmNcXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKlxuICogQ29weXJpZ2h0IFRoZSBPcGVuVGVsZW1ldHJ5IEF1dGhvcnNcbiAqXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogICAgICBodHRwczovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqL1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5QQUNLQUdFX05BTUUgPSBleHBvcnRzLlBBQ0tBR0VfVkVSU0lPTiA9IHZvaWQgMDtcbi8vIHRoaXMgaXMgYXV0b2dlbmVyYXRlZCBmaWxlLCBzZWUgc2NyaXB0cy92ZXJzaW9uLXVwZGF0ZS5qc1xuZXhwb3J0cy5QQUNLQUdFX1ZFUlNJT04gPSAnMC40Ni4xJztcbmV4cG9ydHMuUEFDS0FHRV9OQU1FID0gJ0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1hbXFwbGliJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_be845c946bc223fc3ffe3f2b5ea0d310/node_modules/@opentelemetry/instrumentation-amqplib/build/src/version.js\n");

/***/ })

};
;