"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/index.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/index.js ***!
  \*************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/instrumentation.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/instrumentation.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/instrumentation.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GenericPoolInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/version.js\");\nconst MODULE_NAME = 'generic-pool';\nclass GenericPoolInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        // only used for v2 - v2.3)\n        this._isDisabled = false;\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition(MODULE_NAME, ['>=3.0.0 <4'], moduleExports => {\n                const Pool = moduleExports.Pool;\n                if ((0, instrumentation_1.isWrapped)(Pool.prototype.acquire)) {\n                    this._unwrap(Pool.prototype, 'acquire');\n                }\n                this._wrap(Pool.prototype, 'acquire', this._acquirePatcher.bind(this));\n                return moduleExports;\n            }, moduleExports => {\n                const Pool = moduleExports.Pool;\n                this._unwrap(Pool.prototype, 'acquire');\n                return moduleExports;\n            }),\n            new instrumentation_1.InstrumentationNodeModuleDefinition(MODULE_NAME, ['>=2.4.0 <3'], moduleExports => {\n                const Pool = moduleExports.Pool;\n                if ((0, instrumentation_1.isWrapped)(Pool.prototype.acquire)) {\n                    this._unwrap(Pool.prototype, 'acquire');\n                }\n                this._wrap(Pool.prototype, 'acquire', this._acquireWithCallbacksPatcher.bind(this));\n                return moduleExports;\n            }, moduleExports => {\n                const Pool = moduleExports.Pool;\n                this._unwrap(Pool.prototype, 'acquire');\n                return moduleExports;\n            }),\n            new instrumentation_1.InstrumentationNodeModuleDefinition(MODULE_NAME, ['>=2.0.0 <2.4'], moduleExports => {\n                this._isDisabled = false;\n                if ((0, instrumentation_1.isWrapped)(moduleExports.Pool)) {\n                    this._unwrap(moduleExports, 'Pool');\n                }\n                this._wrap(moduleExports, 'Pool', this._poolWrapper.bind(this));\n                return moduleExports;\n            }, moduleExports => {\n                // since the object is created on the fly every time, we need to use\n                // a boolean switch here to disable the instrumentation\n                this._isDisabled = true;\n                return moduleExports;\n            }),\n        ];\n    }\n    _acquirePatcher(original) {\n        const instrumentation = this;\n        return function wrapped_acquire(...args) {\n            const parent = api.context.active();\n            const span = instrumentation.tracer.startSpan('generic-pool.acquire', {}, parent);\n            return api.context.with(api.trace.setSpan(parent, span), () => {\n                return original.call(this, ...args).then(value => {\n                    span.end();\n                    return value;\n                }, err => {\n                    span.recordException(err);\n                    span.end();\n                    throw err;\n                });\n            });\n        };\n    }\n    _poolWrapper(original) {\n        const instrumentation = this;\n        return function wrapped_pool() {\n            const pool = original.apply(this, arguments);\n            instrumentation._wrap(pool, 'acquire', instrumentation._acquireWithCallbacksPatcher.bind(instrumentation));\n            return pool;\n        };\n    }\n    _acquireWithCallbacksPatcher(original) {\n        const instrumentation = this;\n        return function wrapped_acquire(cb, priority) {\n            // only used for v2 - v2.3\n            if (instrumentation._isDisabled) {\n                return original.call(this, cb, priority);\n            }\n            const parent = api.context.active();\n            const span = instrumentation.tracer.startSpan('generic-pool.acquire', {}, parent);\n            return api.context.with(api.trace.setSpan(parent, span), () => {\n                original.call(this, (err, client) => {\n                    span.end();\n                    // Not checking whether cb is a function because\n                    // the original code doesn't do that either.\n                    if (cb) {\n                        return cb(err, client);\n                    }\n                }, priority);\n            });\n        };\n    }\n}\nexports.GenericPoolInstrumentation = GenericPoolInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/instrumentation.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/version.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/version.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.43.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-generic-pool';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfNjhjNTgyZjc3Yzk5NjNiYjViZDVkYzY1N2Y0ZGYwN2Ivbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi1nZW5lcmljLXBvb2wvYnVpbGQvc3JjL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CLEdBQUcsdUJBQXVCO0FBQzlDO0FBQ0EsdUJBQXVCO0FBQ3ZCLG9CQUFvQjtBQUNwQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF82OGM1ODJmNzdjOTk2M2JiNWJkNWRjNjU3ZjRkZjA3Ylxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLWdlbmVyaWMtcG9vbFxcYnVpbGRcXHNyY1xcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlBBQ0tBR0VfTkFNRSA9IGV4cG9ydHMuUEFDS0FHRV9WRVJTSU9OID0gdm9pZCAwO1xuLy8gdGhpcyBpcyBhdXRvZ2VuZXJhdGVkIGZpbGUsIHNlZSBzY3JpcHRzL3ZlcnNpb24tdXBkYXRlLmpzXG5leHBvcnRzLlBBQ0tBR0VfVkVSU0lPTiA9ICcwLjQzLjEnO1xuZXhwb3J0cy5QQUNLQUdFX05BTUUgPSAnQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWdlbmVyaWMtcG9vbCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/index.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/index.js ***!
  \*************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/instrumentation.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/instrumentation.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/instrumentation.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GenericPoolInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/version.js\");\nconst MODULE_NAME = 'generic-pool';\nclass GenericPoolInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        // only used for v2 - v2.3)\n        this._isDisabled = false;\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition(MODULE_NAME, ['>=3.0.0 <4'], moduleExports => {\n                const Pool = moduleExports.Pool;\n                if ((0, instrumentation_1.isWrapped)(Pool.prototype.acquire)) {\n                    this._unwrap(Pool.prototype, 'acquire');\n                }\n                this._wrap(Pool.prototype, 'acquire', this._acquirePatcher.bind(this));\n                return moduleExports;\n            }, moduleExports => {\n                const Pool = moduleExports.Pool;\n                this._unwrap(Pool.prototype, 'acquire');\n                return moduleExports;\n            }),\n            new instrumentation_1.InstrumentationNodeModuleDefinition(MODULE_NAME, ['>=2.4.0 <3'], moduleExports => {\n                const Pool = moduleExports.Pool;\n                if ((0, instrumentation_1.isWrapped)(Pool.prototype.acquire)) {\n                    this._unwrap(Pool.prototype, 'acquire');\n                }\n                this._wrap(Pool.prototype, 'acquire', this._acquireWithCallbacksPatcher.bind(this));\n                return moduleExports;\n            }, moduleExports => {\n                const Pool = moduleExports.Pool;\n                this._unwrap(Pool.prototype, 'acquire');\n                return moduleExports;\n            }),\n            new instrumentation_1.InstrumentationNodeModuleDefinition(MODULE_NAME, ['>=2.0.0 <2.4'], moduleExports => {\n                this._isDisabled = false;\n                if ((0, instrumentation_1.isWrapped)(moduleExports.Pool)) {\n                    this._unwrap(moduleExports, 'Pool');\n                }\n                this._wrap(moduleExports, 'Pool', this._poolWrapper.bind(this));\n                return moduleExports;\n            }, moduleExports => {\n                // since the object is created on the fly every time, we need to use\n                // a boolean switch here to disable the instrumentation\n                this._isDisabled = true;\n                return moduleExports;\n            }),\n        ];\n    }\n    _acquirePatcher(original) {\n        const instrumentation = this;\n        return function wrapped_acquire(...args) {\n            const parent = api.context.active();\n            const span = instrumentation.tracer.startSpan('generic-pool.acquire', {}, parent);\n            return api.context.with(api.trace.setSpan(parent, span), () => {\n                return original.call(this, ...args).then(value => {\n                    span.end();\n                    return value;\n                }, err => {\n                    span.recordException(err);\n                    span.end();\n                    throw err;\n                });\n            });\n        };\n    }\n    _poolWrapper(original) {\n        const instrumentation = this;\n        return function wrapped_pool() {\n            const pool = original.apply(this, arguments);\n            instrumentation._wrap(pool, 'acquire', instrumentation._acquireWithCallbacksPatcher.bind(instrumentation));\n            return pool;\n        };\n    }\n    _acquireWithCallbacksPatcher(original) {\n        const instrumentation = this;\n        return function wrapped_acquire(cb, priority) {\n            // only used for v2 - v2.3\n            if (instrumentation._isDisabled) {\n                return original.call(this, cb, priority);\n            }\n            const parent = api.context.active();\n            const span = instrumentation.tracer.startSpan('generic-pool.acquire', {}, parent);\n            return api.context.with(api.trace.setSpan(parent, span), () => {\n                original.call(this, (err, client) => {\n                    span.end();\n                    // Not checking whether cb is a function because\n                    // the original code doesn't do that either.\n                    if (cb) {\n                        return cb(err, client);\n                    }\n                }, priority);\n            });\n        };\n    }\n}\nexports.GenericPoolInstrumentation = GenericPoolInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/instrumentation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/version.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/version.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.43.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-generic-pool';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/index.js":
/*!*************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/index.js ***!
  \*************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/instrumentation.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/instrumentation.js":
/*!***********************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/instrumentation.js ***!
  \***********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GenericPoolInstrumentation = void 0;\nconst api = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/version.js\");\nconst MODULE_NAME = 'generic-pool';\nclass GenericPoolInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        // only used for v2 - v2.3)\n        this._isDisabled = false;\n    }\n    init() {\n        return [\n            new instrumentation_1.InstrumentationNodeModuleDefinition(MODULE_NAME, ['>=3.0.0 <4'], moduleExports => {\n                const Pool = moduleExports.Pool;\n                if ((0, instrumentation_1.isWrapped)(Pool.prototype.acquire)) {\n                    this._unwrap(Pool.prototype, 'acquire');\n                }\n                this._wrap(Pool.prototype, 'acquire', this._acquirePatcher.bind(this));\n                return moduleExports;\n            }, moduleExports => {\n                const Pool = moduleExports.Pool;\n                this._unwrap(Pool.prototype, 'acquire');\n                return moduleExports;\n            }),\n            new instrumentation_1.InstrumentationNodeModuleDefinition(MODULE_NAME, ['>=2.4.0 <3'], moduleExports => {\n                const Pool = moduleExports.Pool;\n                if ((0, instrumentation_1.isWrapped)(Pool.prototype.acquire)) {\n                    this._unwrap(Pool.prototype, 'acquire');\n                }\n                this._wrap(Pool.prototype, 'acquire', this._acquireWithCallbacksPatcher.bind(this));\n                return moduleExports;\n            }, moduleExports => {\n                const Pool = moduleExports.Pool;\n                this._unwrap(Pool.prototype, 'acquire');\n                return moduleExports;\n            }),\n            new instrumentation_1.InstrumentationNodeModuleDefinition(MODULE_NAME, ['>=2.0.0 <2.4'], moduleExports => {\n                this._isDisabled = false;\n                if ((0, instrumentation_1.isWrapped)(moduleExports.Pool)) {\n                    this._unwrap(moduleExports, 'Pool');\n                }\n                this._wrap(moduleExports, 'Pool', this._poolWrapper.bind(this));\n                return moduleExports;\n            }, moduleExports => {\n                // since the object is created on the fly every time, we need to use\n                // a boolean switch here to disable the instrumentation\n                this._isDisabled = true;\n                return moduleExports;\n            }),\n        ];\n    }\n    _acquirePatcher(original) {\n        const instrumentation = this;\n        return function wrapped_acquire(...args) {\n            const parent = api.context.active();\n            const span = instrumentation.tracer.startSpan('generic-pool.acquire', {}, parent);\n            return api.context.with(api.trace.setSpan(parent, span), () => {\n                return original.call(this, ...args).then(value => {\n                    span.end();\n                    return value;\n                }, err => {\n                    span.recordException(err);\n                    span.end();\n                    throw err;\n                });\n            });\n        };\n    }\n    _poolWrapper(original) {\n        const instrumentation = this;\n        return function wrapped_pool() {\n            const pool = original.apply(this, arguments);\n            instrumentation._wrap(pool, 'acquire', instrumentation._acquireWithCallbacksPatcher.bind(instrumentation));\n            return pool;\n        };\n    }\n    _acquireWithCallbacksPatcher(original) {\n        const instrumentation = this;\n        return function wrapped_acquire(cb, priority) {\n            // only used for v2 - v2.3\n            if (instrumentation._isDisabled) {\n                return original.call(this, cb, priority);\n            }\n            const parent = api.context.active();\n            const span = instrumentation.tracer.startSpan('generic-pool.acquire', {}, parent);\n            return api.context.with(api.trace.setSpan(parent, span), () => {\n                original.call(this, (err, client) => {\n                    span.end();\n                    // Not checking whether cb is a function because\n                    // the original code doesn't do that either.\n                    if (cb) {\n                        return cb(err, client);\n                    }\n                }, priority);\n            });\n        };\n    }\n}\nexports.GenericPoolInstrumentation = GenericPoolInstrumentation;\n//# sourceMappingURL=instrumentation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/instrumentation.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/version.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/version.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.43.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-generic-pool';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF82OGM1ODJmNzdjOTk2M2JiNWJkNWRjNjU3ZjRkZjA3Yi9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLWdlbmVyaWMtcG9vbC9idWlsZC9zcmMvdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxvQkFBb0IsR0FBRyx1QkFBdUI7QUFDOUM7QUFDQSx1QkFBdUI7QUFDdkIsb0JBQW9CO0FBQ3BCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0XzY4YzU4MmY3N2M5OTYzYmI1YmQ1ZGM2NTdmNGRmMDdiXFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tZ2VuZXJpYy1wb29sXFxidWlsZFxcc3JjXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLypcbiAqIENvcHlyaWdodCBUaGUgT3BlblRlbGVtZXRyeSBBdXRob3JzXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICAgaHR0cHM6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi9cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuUEFDS0FHRV9OQU1FID0gZXhwb3J0cy5QQUNLQUdFX1ZFUlNJT04gPSB2b2lkIDA7XG4vLyB0aGlzIGlzIGF1dG9nZW5lcmF0ZWQgZmlsZSwgc2VlIHNjcmlwdHMvdmVyc2lvbi11cGRhdGUuanNcbmV4cG9ydHMuUEFDS0FHRV9WRVJTSU9OID0gJzAuNDMuMSc7XG5leHBvcnRzLlBBQ0tBR0VfTkFNRSA9ICdAb3BlbnRlbGVtZXRyeS9pbnN0cnVtZW50YXRpb24tZ2VuZXJpYy1wb29sJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_68c582f77c9963bb5bd5dc657f4df07b/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/version.js\n");

/***/ })

};
;