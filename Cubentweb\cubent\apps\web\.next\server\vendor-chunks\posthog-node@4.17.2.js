"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/posthog-node@4.17.2";
exports.ids = ["vendor-chunks/posthog-node@4.17.2"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/posthog-node@4.17.2/node_modules/posthog-node/lib/node/index.mjs":
/*!*************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/posthog-node@4.17.2/node_modules/posthog-node/lib/node/index.mjs ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostHog: () => (/* binding */ PostHog),\n/* harmony export */   PostHogSentryIntegration: () => (/* binding */ PostHogSentryIntegration),\n/* harmony export */   createEventProcessor: () => (/* binding */ createEventProcessor),\n/* harmony export */   sentryIntegration: () => (/* binding */ sentryIntegration),\n/* harmony export */   setupExpressErrorHandler: () => (/* binding */ setupExpressErrorHandler)\n/* harmony export */ });\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var node_readline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node:readline */ \"node:readline\");\n\n\n\n\n/**\r\n * @file Adapted from [posthog-js](https://github.com/PostHog/posthog-js/blob/8157df935a4d0e71d2fefef7127aa85ee51c82d1/src/extensions/sentry-integration.ts) with modifications for the Node SDK.\r\n */\n/**\r\n * Integrate Sentry with PostHog. This will add a direct link to the person in Sentry, and an $exception event in PostHog.\r\n *\r\n * ### Usage\r\n *\r\n *     Sentry.init({\r\n *          dsn: 'https://example',\r\n *          integrations: [\r\n *              new PostHogSentryIntegration(posthog)\r\n *          ]\r\n *     })\r\n *\r\n *     Sentry.setTag(PostHogSentryIntegration.POSTHOG_ID_TAG, 'some distinct id');\r\n *\r\n * @param {Object} [posthog] The posthog object\r\n * @param {string} [organization] Optional: The Sentry organization, used to send a direct link from PostHog to Sentry\r\n * @param {Number} [projectId] Optional: The Sentry project id, used to send a direct link from PostHog to Sentry\r\n * @param {string} [prefix] Optional: Url of a self-hosted sentry instance (default: https://sentry.io/organizations/)\r\n * @param {SeverityLevel[] | '*'} [severityAllowList] Optional: send events matching the provided levels. Use '*' to send all events (default: ['error'])\r\n */\nconst NAME = 'posthog-node';\nfunction createEventProcessor(_posthog, {\n  organization,\n  projectId,\n  prefix,\n  severityAllowList = ['error']\n} = {}) {\n  return event => {\n    const shouldProcessLevel = severityAllowList === '*' || severityAllowList.includes(event.level);\n    if (!shouldProcessLevel) {\n      return event;\n    }\n    if (!event.tags) {\n      event.tags = {};\n    }\n    // Get the PostHog user ID from a specific tag, which users can set on their Sentry scope as they need.\n    const userId = event.tags[PostHogSentryIntegration.POSTHOG_ID_TAG];\n    if (userId === undefined) {\n      // If we can't find a user ID, don't bother linking the event. We won't be able to send anything meaningful to PostHog without it.\n      return event;\n    }\n    const uiHost = _posthog.options.host ?? 'https://us.i.posthog.com';\n    const personUrl = new URL(`/project/${_posthog.apiKey}/person/${userId}`, uiHost).toString();\n    event.tags['PostHog Person URL'] = personUrl;\n    const exceptions = event.exception?.values || [];\n    const exceptionList = exceptions.map(exception => ({\n      ...exception,\n      stacktrace: exception.stacktrace ? {\n        ...exception.stacktrace,\n        type: 'raw',\n        frames: (exception.stacktrace.frames || []).map(frame => {\n          return {\n            ...frame,\n            platform: 'node:javascript'\n          };\n        })\n      } : undefined\n    }));\n    const properties = {\n      // PostHog Exception Properties,\n      $exception_message: exceptions[0]?.value || event.message,\n      $exception_type: exceptions[0]?.type,\n      $exception_personURL: personUrl,\n      $exception_level: event.level,\n      $exception_list: exceptionList,\n      // Sentry Exception Properties\n      $sentry_event_id: event.event_id,\n      $sentry_exception: event.exception,\n      $sentry_exception_message: exceptions[0]?.value || event.message,\n      $sentry_exception_type: exceptions[0]?.type,\n      $sentry_tags: event.tags\n    };\n    if (organization && projectId) {\n      properties['$sentry_url'] = (prefix || 'https://sentry.io/organizations/') + organization + '/issues/?project=' + projectId + '&query=' + event.event_id;\n    }\n    _posthog.capture({\n      event: '$exception',\n      distinctId: userId,\n      properties\n    });\n    return event;\n  };\n}\n// V8 integration - function based\nfunction sentryIntegration(_posthog, options) {\n  const processor = createEventProcessor(_posthog, options);\n  return {\n    name: NAME,\n    processEvent(event) {\n      return processor(event);\n    }\n  };\n}\n// V7 integration - class based\nclass PostHogSentryIntegration {\n  constructor(_posthog, organization, prefix, severityAllowList) {\n    this.name = NAME;\n    // setupOnce gets called by Sentry when it intializes the plugin\n    this.name = NAME;\n    this.setupOnce = function (addGlobalEventProcessor, getCurrentHub) {\n      const projectId = getCurrentHub()?.getClient()?.getDsn()?.projectId;\n      addGlobalEventProcessor(createEventProcessor(_posthog, {\n        organization,\n        projectId,\n        prefix,\n        severityAllowList\n      }));\n    };\n  }\n}\nPostHogSentryIntegration.POSTHOG_ID_TAG = 'posthog_distinct_id';\n\n// vendor from: https://github.com/LiosK/uuidv7/blob/f30b7a7faff73afbce0b27a46c638310f96912ba/src/index.ts\r\n// https://github.com/LiosK/uuidv7#license\r\n/**\r\n * uuidv7: An experimental implementation of the proposed UUID Version 7\r\n *\r\n * @license Apache-2.0\r\n * @copyright 2021-2023 LiosK\r\n * @packageDocumentation\r\n */\r\nconst DIGITS = \"0123456789abcdef\";\r\n/** Represents a UUID as a 16-byte byte array. */\r\nclass UUID {\r\n    /** @param bytes - The 16-byte byte array representation. */\r\n    constructor(bytes) {\r\n        this.bytes = bytes;\r\n    }\r\n    /**\r\n     * Creates an object from the internal representation, a 16-byte byte array\r\n     * containing the binary UUID representation in the big-endian byte order.\r\n     *\r\n     * This method does NOT shallow-copy the argument, and thus the created object\r\n     * holds the reference to the underlying buffer.\r\n     *\r\n     * @throws TypeError if the length of the argument is not 16.\r\n     */\r\n    static ofInner(bytes) {\r\n        if (bytes.length !== 16) {\r\n            throw new TypeError(\"not 128-bit length\");\r\n        }\r\n        else {\r\n            return new UUID(bytes);\r\n        }\r\n    }\r\n    /**\r\n     * Builds a byte array from UUIDv7 field values.\r\n     *\r\n     * @param unixTsMs - A 48-bit `unix_ts_ms` field value.\r\n     * @param randA - A 12-bit `rand_a` field value.\r\n     * @param randBHi - The higher 30 bits of 62-bit `rand_b` field value.\r\n     * @param randBLo - The lower 32 bits of 62-bit `rand_b` field value.\r\n     * @throws RangeError if any field value is out of the specified range.\r\n     */\r\n    static fromFieldsV7(unixTsMs, randA, randBHi, randBLo) {\r\n        if (!Number.isInteger(unixTsMs) ||\r\n            !Number.isInteger(randA) ||\r\n            !Number.isInteger(randBHi) ||\r\n            !Number.isInteger(randBLo) ||\r\n            unixTsMs < 0 ||\r\n            randA < 0 ||\r\n            randBHi < 0 ||\r\n            randBLo < 0 ||\r\n            unixTsMs > 281474976710655 ||\r\n            randA > 0xfff ||\r\n            randBHi > 1073741823 ||\r\n            randBLo > 4294967295) {\r\n            throw new RangeError(\"invalid field value\");\r\n        }\r\n        const bytes = new Uint8Array(16);\r\n        bytes[0] = unixTsMs / 2 ** 40;\r\n        bytes[1] = unixTsMs / 2 ** 32;\r\n        bytes[2] = unixTsMs / 2 ** 24;\r\n        bytes[3] = unixTsMs / 2 ** 16;\r\n        bytes[4] = unixTsMs / 2 ** 8;\r\n        bytes[5] = unixTsMs;\r\n        bytes[6] = 0x70 | (randA >>> 8);\r\n        bytes[7] = randA;\r\n        bytes[8] = 0x80 | (randBHi >>> 24);\r\n        bytes[9] = randBHi >>> 16;\r\n        bytes[10] = randBHi >>> 8;\r\n        bytes[11] = randBHi;\r\n        bytes[12] = randBLo >>> 24;\r\n        bytes[13] = randBLo >>> 16;\r\n        bytes[14] = randBLo >>> 8;\r\n        bytes[15] = randBLo;\r\n        return new UUID(bytes);\r\n    }\r\n    /**\r\n     * Builds a byte array from a string representation.\r\n     *\r\n     * This method accepts the following formats:\r\n     *\r\n     * - 32-digit hexadecimal format without hyphens: `0189dcd553117d408db09496a2eef37b`\r\n     * - 8-4-4-4-12 hyphenated format: `0189dcd5-5311-7d40-8db0-9496a2eef37b`\r\n     * - Hyphenated format with surrounding braces: `{0189dcd5-5311-7d40-8db0-9496a2eef37b}`\r\n     * - RFC 4122 URN format: `urn:uuid:0189dcd5-5311-7d40-8db0-9496a2eef37b`\r\n     *\r\n     * Leading and trailing whitespaces represents an error.\r\n     *\r\n     * @throws SyntaxError if the argument could not parse as a valid UUID string.\r\n     */\r\n    static parse(uuid) {\r\n        let hex = undefined;\r\n        switch (uuid.length) {\r\n            case 32:\r\n                hex = /^[0-9a-f]{32}$/i.exec(uuid)?.[0];\r\n                break;\r\n            case 36:\r\n                hex =\r\n                    /^([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i\r\n                        .exec(uuid)\r\n                        ?.slice(1, 6)\r\n                        .join(\"\");\r\n                break;\r\n            case 38:\r\n                hex =\r\n                    /^\\{([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})\\}$/i\r\n                        .exec(uuid)\r\n                        ?.slice(1, 6)\r\n                        .join(\"\");\r\n                break;\r\n            case 45:\r\n                hex =\r\n                    /^urn:uuid:([0-9a-f]{8})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{4})-([0-9a-f]{12})$/i\r\n                        .exec(uuid)\r\n                        ?.slice(1, 6)\r\n                        .join(\"\");\r\n                break;\r\n        }\r\n        if (hex) {\r\n            const inner = new Uint8Array(16);\r\n            for (let i = 0; i < 16; i += 4) {\r\n                const n = parseInt(hex.substring(2 * i, 2 * i + 8), 16);\r\n                inner[i + 0] = n >>> 24;\r\n                inner[i + 1] = n >>> 16;\r\n                inner[i + 2] = n >>> 8;\r\n                inner[i + 3] = n;\r\n            }\r\n            return new UUID(inner);\r\n        }\r\n        else {\r\n            throw new SyntaxError(\"could not parse UUID string\");\r\n        }\r\n    }\r\n    /**\r\n     * @returns The 8-4-4-4-12 canonical hexadecimal string representation\r\n     * (`0189dcd5-5311-7d40-8db0-9496a2eef37b`).\r\n     */\r\n    toString() {\r\n        let text = \"\";\r\n        for (let i = 0; i < this.bytes.length; i++) {\r\n            text += DIGITS.charAt(this.bytes[i] >>> 4);\r\n            text += DIGITS.charAt(this.bytes[i] & 0xf);\r\n            if (i === 3 || i === 5 || i === 7 || i === 9) {\r\n                text += \"-\";\r\n            }\r\n        }\r\n        return text;\r\n    }\r\n    /**\r\n     * @returns The 32-digit hexadecimal representation without hyphens\r\n     * (`0189dcd553117d408db09496a2eef37b`).\r\n     */\r\n    toHex() {\r\n        let text = \"\";\r\n        for (let i = 0; i < this.bytes.length; i++) {\r\n            text += DIGITS.charAt(this.bytes[i] >>> 4);\r\n            text += DIGITS.charAt(this.bytes[i] & 0xf);\r\n        }\r\n        return text;\r\n    }\r\n    /** @returns The 8-4-4-4-12 canonical hexadecimal string representation. */\r\n    toJSON() {\r\n        return this.toString();\r\n    }\r\n    /**\r\n     * Reports the variant field value of the UUID or, if appropriate, \"NIL\" or\r\n     * \"MAX\".\r\n     *\r\n     * For convenience, this method reports \"NIL\" or \"MAX\" if `this` represents\r\n     * the Nil or Max UUID, although the Nil and Max UUIDs are technically\r\n     * subsumed under the variants `0b0` and `0b111`, respectively.\r\n     */\r\n    getVariant() {\r\n        const n = this.bytes[8] >>> 4;\r\n        if (n < 0) {\r\n            throw new Error(\"unreachable\");\r\n        }\r\n        else if (n <= 0b0111) {\r\n            return this.bytes.every((e) => e === 0) ? \"NIL\" : \"VAR_0\";\r\n        }\r\n        else if (n <= 0b1011) {\r\n            return \"VAR_10\";\r\n        }\r\n        else if (n <= 0b1101) {\r\n            return \"VAR_110\";\r\n        }\r\n        else if (n <= 0b1111) {\r\n            return this.bytes.every((e) => e === 0xff) ? \"MAX\" : \"VAR_RESERVED\";\r\n        }\r\n        else {\r\n            throw new Error(\"unreachable\");\r\n        }\r\n    }\r\n    /**\r\n     * Returns the version field value of the UUID or `undefined` if the UUID does\r\n     * not have the variant field value of `0b10`.\r\n     */\r\n    getVersion() {\r\n        return this.getVariant() === \"VAR_10\" ? this.bytes[6] >>> 4 : undefined;\r\n    }\r\n    /** Creates an object from `this`. */\r\n    clone() {\r\n        return new UUID(this.bytes.slice(0));\r\n    }\r\n    /** Returns true if `this` is equivalent to `other`. */\r\n    equals(other) {\r\n        return this.compareTo(other) === 0;\r\n    }\r\n    /**\r\n     * Returns a negative integer, zero, or positive integer if `this` is less\r\n     * than, equal to, or greater than `other`, respectively.\r\n     */\r\n    compareTo(other) {\r\n        for (let i = 0; i < 16; i++) {\r\n            const diff = this.bytes[i] - other.bytes[i];\r\n            if (diff !== 0) {\r\n                return Math.sign(diff);\r\n            }\r\n        }\r\n        return 0;\r\n    }\r\n}\r\n/**\r\n * Encapsulates the monotonic counter state.\r\n *\r\n * This class provides APIs to utilize a separate counter state from that of the\r\n * global generator used by {@link uuidv7} and {@link uuidv7obj}. In addition to\r\n * the default {@link generate} method, this class has {@link generateOrAbort}\r\n * that is useful to absolutely guarantee the monotonically increasing order of\r\n * generated UUIDs. See their respective documentation for details.\r\n */\r\nclass V7Generator {\r\n    /**\r\n     * Creates a generator object with the default random number generator, or\r\n     * with the specified one if passed as an argument. The specified random\r\n     * number generator should be cryptographically strong and securely seeded.\r\n     */\r\n    constructor(randomNumberGenerator) {\r\n        this.timestamp = 0;\r\n        this.counter = 0;\r\n        this.random = randomNumberGenerator ?? getDefaultRandom();\r\n    }\r\n    /**\r\n     * Generates a new UUIDv7 object from the current timestamp, or resets the\r\n     * generator upon significant timestamp rollback.\r\n     *\r\n     * This method returns a monotonically increasing UUID by reusing the previous\r\n     * timestamp even if the up-to-date timestamp is smaller than the immediately\r\n     * preceding UUID's. However, when such a clock rollback is considered\r\n     * significant (i.e., by more than ten seconds), this method resets the\r\n     * generator and returns a new UUID based on the given timestamp, breaking the\r\n     * increasing order of UUIDs.\r\n     *\r\n     * See {@link generateOrAbort} for the other mode of generation and\r\n     * {@link generateOrResetCore} for the low-level primitive.\r\n     */\r\n    generate() {\r\n        return this.generateOrResetCore(Date.now(), 10000);\r\n    }\r\n    /**\r\n     * Generates a new UUIDv7 object from the current timestamp, or returns\r\n     * `undefined` upon significant timestamp rollback.\r\n     *\r\n     * This method returns a monotonically increasing UUID by reusing the previous\r\n     * timestamp even if the up-to-date timestamp is smaller than the immediately\r\n     * preceding UUID's. However, when such a clock rollback is considered\r\n     * significant (i.e., by more than ten seconds), this method aborts and\r\n     * returns `undefined` immediately.\r\n     *\r\n     * See {@link generate} for the other mode of generation and\r\n     * {@link generateOrAbortCore} for the low-level primitive.\r\n     */\r\n    generateOrAbort() {\r\n        return this.generateOrAbortCore(Date.now(), 10000);\r\n    }\r\n    /**\r\n     * Generates a new UUIDv7 object from the `unixTsMs` passed, or resets the\r\n     * generator upon significant timestamp rollback.\r\n     *\r\n     * This method is equivalent to {@link generate} except that it takes a custom\r\n     * timestamp and clock rollback allowance.\r\n     *\r\n     * @param rollbackAllowance - The amount of `unixTsMs` rollback that is\r\n     * considered significant. A suggested value is `10_000` (milliseconds).\r\n     * @throws RangeError if `unixTsMs` is not a 48-bit positive integer.\r\n     */\r\n    generateOrResetCore(unixTsMs, rollbackAllowance) {\r\n        let value = this.generateOrAbortCore(unixTsMs, rollbackAllowance);\r\n        if (value === undefined) {\r\n            // reset state and resume\r\n            this.timestamp = 0;\r\n            value = this.generateOrAbortCore(unixTsMs, rollbackAllowance);\r\n        }\r\n        return value;\r\n    }\r\n    /**\r\n     * Generates a new UUIDv7 object from the `unixTsMs` passed, or returns\r\n     * `undefined` upon significant timestamp rollback.\r\n     *\r\n     * This method is equivalent to {@link generateOrAbort} except that it takes a\r\n     * custom timestamp and clock rollback allowance.\r\n     *\r\n     * @param rollbackAllowance - The amount of `unixTsMs` rollback that is\r\n     * considered significant. A suggested value is `10_000` (milliseconds).\r\n     * @throws RangeError if `unixTsMs` is not a 48-bit positive integer.\r\n     */\r\n    generateOrAbortCore(unixTsMs, rollbackAllowance) {\r\n        const MAX_COUNTER = 4398046511103;\r\n        if (!Number.isInteger(unixTsMs) ||\r\n            unixTsMs < 1 ||\r\n            unixTsMs > 281474976710655) {\r\n            throw new RangeError(\"`unixTsMs` must be a 48-bit positive integer\");\r\n        }\r\n        else if (rollbackAllowance < 0 || rollbackAllowance > 281474976710655) {\r\n            throw new RangeError(\"`rollbackAllowance` out of reasonable range\");\r\n        }\r\n        if (unixTsMs > this.timestamp) {\r\n            this.timestamp = unixTsMs;\r\n            this.resetCounter();\r\n        }\r\n        else if (unixTsMs + rollbackAllowance >= this.timestamp) {\r\n            // go on with previous timestamp if new one is not much smaller\r\n            this.counter++;\r\n            if (this.counter > MAX_COUNTER) {\r\n                // increment timestamp at counter overflow\r\n                this.timestamp++;\r\n                this.resetCounter();\r\n            }\r\n        }\r\n        else {\r\n            // abort if clock went backwards to unbearable extent\r\n            return undefined;\r\n        }\r\n        return UUID.fromFieldsV7(this.timestamp, Math.trunc(this.counter / 2 ** 30), this.counter & (2 ** 30 - 1), this.random.nextUint32());\r\n    }\r\n    /** Initializes the counter at a 42-bit random integer. */\r\n    resetCounter() {\r\n        this.counter =\r\n            this.random.nextUint32() * 0x400 + (this.random.nextUint32() & 0x3ff);\r\n    }\r\n    /**\r\n     * Generates a new UUIDv4 object utilizing the random number generator inside.\r\n     *\r\n     * @internal\r\n     */\r\n    generateV4() {\r\n        const bytes = new Uint8Array(Uint32Array.of(this.random.nextUint32(), this.random.nextUint32(), this.random.nextUint32(), this.random.nextUint32()).buffer);\r\n        bytes[6] = 0x40 | (bytes[6] >>> 4);\r\n        bytes[8] = 0x80 | (bytes[8] >>> 2);\r\n        return UUID.ofInner(bytes);\r\n    }\r\n}\r\n/** A global flag to force use of cryptographically strong RNG. */\r\n// declare const UUIDV7_DENY_WEAK_RNG: boolean;\r\n/** Returns the default random number generator available in the environment. */\r\nconst getDefaultRandom = () => {\r\n    // fix: crypto isn't available in react-native, always use Math.random\r\n    //   // detect Web Crypto API\r\n    //   if (\r\n    //     typeof crypto !== \"undefined\" &&\r\n    //     typeof crypto.getRandomValues !== \"undefined\"\r\n    //   ) {\r\n    //     return new BufferedCryptoRandom();\r\n    //   } else {\r\n    //     // fall back on Math.random() unless the flag is set to true\r\n    //     if (typeof UUIDV7_DENY_WEAK_RNG !== \"undefined\" && UUIDV7_DENY_WEAK_RNG) {\r\n    //       throw new Error(\"no cryptographically strong RNG available\");\r\n    //     }\r\n    //     return {\r\n    //       nextUint32: (): number =>\r\n    //         Math.trunc(Math.random() * 0x1_0000) * 0x1_0000 +\r\n    //         Math.trunc(Math.random() * 0x1_0000),\r\n    //     };\r\n    //   }\r\n    return {\r\n        nextUint32: () => Math.trunc(Math.random() * 65536) * 65536 +\r\n            Math.trunc(Math.random() * 65536),\r\n    };\r\n};\r\n// /**\r\n//  * Wraps `crypto.getRandomValues()` to enable buffering; this uses a small\r\n//  * buffer by default to avoid both unbearable throughput decline in some\r\n//  * environments and the waste of time and space for unused values.\r\n//  */\r\n// class BufferedCryptoRandom {\r\n//   private readonly buffer = new Uint32Array(8);\r\n//   private cursor = 0xffff;\r\n//   nextUint32(): number {\r\n//     if (this.cursor >= this.buffer.length) {\r\n//       crypto.getRandomValues(this.buffer);\r\n//       this.cursor = 0;\r\n//     }\r\n//     return this.buffer[this.cursor++];\r\n//   }\r\n// }\r\nlet defaultGenerator;\r\n/**\r\n * Generates a UUIDv7 string.\r\n *\r\n * @returns The 8-4-4-4-12 canonical hexadecimal string representation\r\n * (\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\").\r\n */\r\nconst uuidv7 = () => uuidv7obj().toString();\r\n/** Generates a UUIDv7 object. */\r\nconst uuidv7obj = () => (defaultGenerator || (defaultGenerator = new V7Generator())).generate();\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\nfunction makeUncaughtExceptionHandler(captureFn, onFatalFn) {\n  let calledFatalError = false;\n  return Object.assign(error => {\n    // Attaching a listener to `uncaughtException` will prevent the node process from exiting. We generally do not\n    // want to alter this behaviour so we check for other listeners that users may have attached themselves and adjust\n    // exit behaviour of the SDK accordingly:\n    // - If other listeners are attached, do not exit.\n    // - If the only listener attached is ours, exit.\n    const userProvidedListenersCount = global.process.listeners('uncaughtException').filter(listener => {\n      // There are 2 listeners we ignore:\n      return (\n        // as soon as we're using domains this listener is attached by node itself\n        listener.name !== 'domainUncaughtExceptionClear' &&\n        // the handler we register in this integration\n        listener._posthogErrorHandler !== true\n      );\n    }).length;\n    const processWouldExit = userProvidedListenersCount === 0;\n    captureFn(error, {\n      mechanism: {\n        type: 'onuncaughtexception',\n        handled: false\n      }\n    });\n    if (!calledFatalError && processWouldExit) {\n      calledFatalError = true;\n      onFatalFn();\n    }\n  }, {\n    _posthogErrorHandler: true\n  });\n}\nfunction addUncaughtExceptionListener(captureFn, onFatalFn) {\n  global.process.on('uncaughtException', makeUncaughtExceptionHandler(captureFn, onFatalFn));\n}\nfunction addUnhandledRejectionListener(captureFn) {\n  global.process.on('unhandledRejection', reason => {\n    captureFn(reason, {\n      mechanism: {\n        type: 'onunhandledrejection',\n        handled: false\n      }\n    });\n  });\n}\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\nfunction isEvent(candidate) {\n  return typeof Event !== 'undefined' && isInstanceOf(candidate, Event);\n}\nfunction isPlainObject(candidate) {\n  return isBuiltin(candidate, 'Object');\n}\nfunction isError(candidate) {\n  switch (Object.prototype.toString.call(candidate)) {\n    case '[object Error]':\n    case '[object Exception]':\n    case '[object DOMException]':\n    case '[object WebAssembly.Exception]':\n      return true;\n    default:\n      return isInstanceOf(candidate, Error);\n  }\n}\nfunction isInstanceOf(candidate, base) {\n  try {\n    return candidate instanceof base;\n  } catch {\n    return false;\n  }\n}\nfunction isErrorEvent(event) {\n  return isBuiltin(event, 'ErrorEvent');\n}\nfunction isBuiltin(candidate, className) {\n  return Object.prototype.toString.call(candidate) === `[object ${className}]`;\n}\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\nasync function propertiesFromUnknownInput(stackParser, frameModifiers, input, hint) {\n  const providedMechanism = hint && hint.mechanism;\n  const mechanism = providedMechanism || {\n    handled: true,\n    type: 'generic'\n  };\n  const errorList = getErrorList(mechanism, input, hint);\n  const exceptionList = await Promise.all(errorList.map(async error => {\n    const exception = await exceptionFromError(stackParser, frameModifiers, error);\n    exception.value = exception.value || '';\n    exception.type = exception.type || 'Error';\n    exception.mechanism = mechanism;\n    return exception;\n  }));\n  const properties = {\n    $exception_list: exceptionList\n  };\n  return properties;\n}\n// Flatten error causes into a list of errors\n// See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error/cause\nfunction getErrorList(mechanism, input, hint) {\n  const error = getError(mechanism, input, hint);\n  if (error.cause) {\n    return [error, ...getErrorList(mechanism, error.cause, hint)];\n  }\n  return [error];\n}\nfunction getError(mechanism, exception, hint) {\n  if (isError(exception)) {\n    return exception;\n  }\n  mechanism.synthetic = true;\n  if (isPlainObject(exception)) {\n    const errorFromProp = getErrorPropertyFromObject(exception);\n    if (errorFromProp) {\n      return errorFromProp;\n    }\n    const message = getMessageForObject(exception);\n    const ex = hint?.syntheticException || new Error(message);\n    ex.message = message;\n    return ex;\n  }\n  // This handles when someone does: `throw \"something awesome\";`\n  // We use synthesized Error here so we can extract a (rough) stack trace.\n  const ex = hint?.syntheticException || new Error(exception);\n  ex.message = `${exception}`;\n  return ex;\n}\n/** If a plain object has a property that is an `Error`, return this error. */\nfunction getErrorPropertyFromObject(obj) {\n  for (const prop in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, prop)) {\n      const value = obj[prop];\n      if (isError(value)) {\n        return value;\n      }\n    }\n  }\n  return undefined;\n}\nfunction getMessageForObject(exception) {\n  if ('name' in exception && typeof exception.name === 'string') {\n    let message = `'${exception.name}' captured as exception`;\n    if ('message' in exception && typeof exception.message === 'string') {\n      message += ` with message '${exception.message}'`;\n    }\n    return message;\n  } else if ('message' in exception && typeof exception.message === 'string') {\n    return exception.message;\n  }\n  const keys = extractExceptionKeysForMessage(exception);\n  // Some ErrorEvent instances do not have an `error` property, which is why they are not handled before\n  // We still want to try to get a decent message for these cases\n  if (isErrorEvent(exception)) {\n    return `Event \\`ErrorEvent\\` captured as exception with message \\`${exception.message}\\``;\n  }\n  const className = getObjectClassName(exception);\n  return `${className && className !== 'Object' ? `'${className}'` : 'Object'} captured as exception with keys: ${keys}`;\n}\nfunction getObjectClassName(obj) {\n  try {\n    const prototype = Object.getPrototypeOf(obj);\n    return prototype ? prototype.constructor.name : undefined;\n  } catch (e) {\n    // ignore errors here\n  }\n}\n/**\r\n * Given any captured exception, extract its keys and create a sorted\r\n * and truncated list that will be used inside the event message.\r\n * eg. `Non-error exception captured with keys: foo, bar, baz`\r\n */\nfunction extractExceptionKeysForMessage(exception, maxLength = 40) {\n  const keys = Object.keys(convertToPlainObject(exception));\n  keys.sort();\n  const firstKey = keys[0];\n  if (!firstKey) {\n    return '[object has no keys]';\n  }\n  if (firstKey.length >= maxLength) {\n    return truncate(firstKey, maxLength);\n  }\n  for (let includedKeys = keys.length; includedKeys > 0; includedKeys--) {\n    const serialized = keys.slice(0, includedKeys).join(', ');\n    if (serialized.length > maxLength) {\n      continue;\n    }\n    if (includedKeys === keys.length) {\n      return serialized;\n    }\n    return truncate(serialized, maxLength);\n  }\n  return '';\n}\nfunction truncate(str, max = 0) {\n  if (typeof str !== 'string' || max === 0) {\n    return str;\n  }\n  return str.length <= max ? str : `${str.slice(0, max)}...`;\n}\n/**\r\n * Transforms any `Error` or `Event` into a plain object with all of their enumerable properties, and some of their\r\n * non-enumerable properties attached.\r\n *\r\n * @param value Initial source that we have to transform in order for it to be usable by the serializer\r\n * @returns An Event or Error turned into an object - or the value argument itself, when value is neither an Event nor\r\n *  an Error.\r\n */\nfunction convertToPlainObject(value) {\n  if (isError(value)) {\n    return {\n      message: value.message,\n      name: value.name,\n      stack: value.stack,\n      ...getOwnProperties(value)\n    };\n  } else if (isEvent(value)) {\n    const newObj = {\n      type: value.type,\n      target: serializeEventTarget(value.target),\n      currentTarget: serializeEventTarget(value.currentTarget),\n      ...getOwnProperties(value)\n    };\n    // TODO: figure out why this fails typing (I think CustomEvent is only supported in Node 19 onwards)\n    // if (typeof CustomEvent !== 'undefined' && isInstanceOf(value, CustomEvent)) {\n    //   newObj.detail = (value as unknown as CustomEvent).detail\n    // }\n    return newObj;\n  } else {\n    return value;\n  }\n}\n/** Filters out all but an object's own properties */\nfunction getOwnProperties(obj) {\n  if (typeof obj === 'object' && obj !== null) {\n    const extractedProps = {};\n    for (const property in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, property)) {\n        extractedProps[property] = obj[property];\n      }\n    }\n    return extractedProps;\n  } else {\n    return {};\n  }\n}\n/** Creates a string representation of the target of an `Event` object */\nfunction serializeEventTarget(target) {\n  try {\n    return Object.prototype.toString.call(target);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n/**\r\n * Extracts stack frames from the error and builds an Exception\r\n */\nasync function exceptionFromError(stackParser, frameModifiers, error) {\n  const exception = {\n    type: error.name || error.constructor.name,\n    value: error.message\n  };\n  let frames = parseStackFrames(stackParser, error);\n  for (const modifier of frameModifiers) {\n    frames = await modifier(frames);\n  }\n  if (frames.length) {\n    exception.stacktrace = {\n      frames,\n      type: 'raw'\n    };\n  }\n  return exception;\n}\n/**\r\n * Extracts stack frames from the error.stack string\r\n */\nfunction parseStackFrames(stackParser, error) {\n  return stackParser(error.stack || '', 1);\n}\n\nconst SHUTDOWN_TIMEOUT = 2000;\nclass ErrorTracking {\n  static async captureException(client, error, hint, distinctId, additionalProperties) {\n    const properties = {\n      ...additionalProperties\n    };\n    // Given stateless nature of Node SDK we capture exceptions using personless processing when no\n    // user can be determined because a distinct_id is not provided e.g. exception autocapture\n    if (!distinctId) {\n      properties.$process_person_profile = false;\n    }\n    const exceptionProperties = await propertiesFromUnknownInput(this.stackParser, this.frameModifiers, error, hint);\n    client.capture({\n      event: '$exception',\n      distinctId: distinctId || uuidv7(),\n      properties: {\n        ...exceptionProperties,\n        ...properties\n      }\n    });\n  }\n  constructor(client, options) {\n    this.client = client;\n    this._exceptionAutocaptureEnabled = options.enableExceptionAutocapture || false;\n    this.startAutocaptureIfEnabled();\n  }\n  startAutocaptureIfEnabled() {\n    if (this.isEnabled()) {\n      addUncaughtExceptionListener(this.onException.bind(this), this.onFatalError.bind(this));\n      addUnhandledRejectionListener(this.onException.bind(this));\n    }\n  }\n  onException(exception, hint) {\n    ErrorTracking.captureException(this.client, exception, hint);\n  }\n  async onFatalError() {\n    await this.client.shutdown(SHUTDOWN_TIMEOUT);\n  }\n  isEnabled() {\n    return !this.client.isDisabled && this._exceptionAutocaptureEnabled;\n  }\n}\n\nfunction setupExpressErrorHandler(_posthog, app) {\n  app.use((error, _, __, next) => {\n    const hint = {\n      mechanism: {\n        type: 'middleware',\n        handled: false\n      }\n    };\n    // Given stateless nature of Node SDK we capture exceptions using personless processing\n    // when no user can be determined e.g. in the case of exception autocapture\n    ErrorTracking.captureException(_posthog, error, hint, uuidv7(), {\n      $process_person_profile: false\n    });\n    next(error);\n  });\n}\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n/** Creates a function that gets the module name from a filename */\nfunction createGetModuleFromFilename(basePath = process.argv[1] ? (0,path__WEBPACK_IMPORTED_MODULE_0__.dirname)(process.argv[1]) : process.cwd(), isWindows = path__WEBPACK_IMPORTED_MODULE_0__.sep === '\\\\') {\n  const normalizedBase = isWindows ? normalizeWindowsPath(basePath) : basePath;\n  return filename => {\n    if (!filename) {\n      return;\n    }\n    const normalizedFilename = isWindows ? normalizeWindowsPath(filename) : filename;\n    // eslint-disable-next-line prefer-const\n    let {\n      dir,\n      base: file,\n      ext\n    } = path__WEBPACK_IMPORTED_MODULE_0__.posix.parse(normalizedFilename);\n    if (ext === '.js' || ext === '.mjs' || ext === '.cjs') {\n      file = file.slice(0, ext.length * -1);\n    }\n    // The file name might be URI-encoded which we want to decode to\n    // the original file name.\n    const decodedFile = decodeURIComponent(file);\n    if (!dir) {\n      // No dirname whatsoever\n      dir = '.';\n    }\n    const n = dir.lastIndexOf('/node_modules');\n    if (n > -1) {\n      return `${dir.slice(n + 14).replace(/\\//g, '.')}:${decodedFile}`;\n    }\n    // Let's see if it's a part of the main module\n    // To be a part of main module, it has to share the same base\n    if (dir.startsWith(normalizedBase)) {\n      const moduleName = dir.slice(normalizedBase.length + 1).replace(/\\//g, '.');\n      return moduleName ? `${moduleName}:${decodedFile}` : decodedFile;\n    }\n    return decodedFile;\n  };\n}\n/** normalizes Windows paths */\nfunction normalizeWindowsPath(path) {\n  return path.replace(/^[A-Z]:/, '') // remove Windows-style prefix\n  .replace(/\\\\/g, '/'); // replace all `\\` instances with `/`\n}\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\n/** A simple Least Recently Used map */\nclass ReduceableCache {\n  constructor(_maxSize) {\n    this._maxSize = _maxSize;\n    this._cache = new Map();\n  }\n  /** Get an entry or undefined if it was not in the cache. Re-inserts to update the recently used order */\n  get(key) {\n    const value = this._cache.get(key);\n    if (value === undefined) {\n      return undefined;\n    }\n    // Remove and re-insert to update the order\n    this._cache.delete(key);\n    this._cache.set(key, value);\n    return value;\n  }\n  /** Insert an entry and evict an older entry if we've reached maxSize */\n  set(key, value) {\n    this._cache.set(key, value);\n  }\n  /** Remove an entry and return the entry if it was in the cache */\n  reduce() {\n    while (this._cache.size >= this._maxSize) {\n      const value = this._cache.keys().next().value;\n      if (value) {\n        // keys() returns an iterator in insertion order so keys().next() gives us the oldest key\n        this._cache.delete(value);\n      }\n    }\n  }\n}\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\nconst LRU_FILE_CONTENTS_CACHE = new ReduceableCache(25);\nconst LRU_FILE_CONTENTS_FS_READ_FAILED = new ReduceableCache(20);\nconst DEFAULT_LINES_OF_CONTEXT = 7;\n// Determines the upper bound of lineno/colno that we will attempt to read. Large colno values are likely to be\n// minified code while large lineno values are likely to be bundled code.\n// Exported for testing purposes.\nconst MAX_CONTEXTLINES_COLNO = 1000;\nconst MAX_CONTEXTLINES_LINENO = 10000;\nasync function addSourceContext(frames) {\n  // keep a lookup map of which files we've already enqueued to read,\n  // so we don't enqueue the same file multiple times which would cause multiple i/o reads\n  const filesToLines = {};\n  // Maps preserve insertion order, so we iterate in reverse, starting at the\n  // outermost frame and closer to where the exception has occurred (poor mans priority)\n  for (let i = frames.length - 1; i >= 0; i--) {\n    const frame = frames[i];\n    const filename = frame?.filename;\n    if (!frame || typeof filename !== 'string' || typeof frame.lineno !== 'number' || shouldSkipContextLinesForFile(filename) || shouldSkipContextLinesForFrame(frame)) {\n      continue;\n    }\n    const filesToLinesOutput = filesToLines[filename];\n    if (!filesToLinesOutput) {\n      filesToLines[filename] = [];\n    }\n    filesToLines[filename].push(frame.lineno);\n  }\n  const files = Object.keys(filesToLines);\n  if (files.length == 0) {\n    return frames;\n  }\n  const readlinePromises = [];\n  for (const file of files) {\n    // If we failed to read this before, dont try reading it again.\n    if (LRU_FILE_CONTENTS_FS_READ_FAILED.get(file)) {\n      continue;\n    }\n    const filesToLineRanges = filesToLines[file];\n    if (!filesToLineRanges) {\n      continue;\n    }\n    // Sort ranges so that they are sorted by line increasing order and match how the file is read.\n    filesToLineRanges.sort((a, b) => a - b);\n    // Check if the contents are already in the cache and if we can avoid reading the file again.\n    const ranges = makeLineReaderRanges(filesToLineRanges);\n    if (ranges.every(r => rangeExistsInContentCache(file, r))) {\n      continue;\n    }\n    const cache = emplace(LRU_FILE_CONTENTS_CACHE, file, {});\n    readlinePromises.push(getContextLinesFromFile(file, ranges, cache));\n  }\n  // The promise rejections are caught in order to prevent them from short circuiting Promise.all\n  await Promise.all(readlinePromises).catch(() => {});\n  // Perform the same loop as above, but this time we can assume all files are in the cache\n  // and attempt to add source context to frames.\n  if (frames && frames.length > 0) {\n    addSourceContextToFrames(frames, LRU_FILE_CONTENTS_CACHE);\n  }\n  // Once we're finished processing an exception reduce the files held in the cache\n  // so that we don't indefinetly increase the size of this map\n  LRU_FILE_CONTENTS_CACHE.reduce();\n  return frames;\n}\n/**\r\n * Extracts lines from a file and stores them in a cache.\r\n */\nfunction getContextLinesFromFile(path, ranges, output) {\n  return new Promise(resolve => {\n    // It is important *not* to have any async code between createInterface and the 'line' event listener\n    // as it will cause the 'line' event to\n    // be emitted before the listener is attached.\n    const stream = (0,node_fs__WEBPACK_IMPORTED_MODULE_1__.createReadStream)(path);\n    const lineReaded = (0,node_readline__WEBPACK_IMPORTED_MODULE_2__.createInterface)({\n      input: stream\n    });\n    // We need to explicitly destroy the stream to prevent memory leaks,\n    // removing the listeners on the readline interface is not enough.\n    // See: https://github.com/nodejs/node/issues/9002 and https://github.com/getsentry/sentry-javascript/issues/14892\n    function destroyStreamAndResolve() {\n      stream.destroy();\n      resolve();\n    }\n    // Init at zero and increment at the start of the loop because lines are 1 indexed.\n    let lineNumber = 0;\n    let currentRangeIndex = 0;\n    const range = ranges[currentRangeIndex];\n    if (range === undefined) {\n      // We should never reach this point, but if we do, we should resolve the promise to prevent it from hanging.\n      destroyStreamAndResolve();\n      return;\n    }\n    let rangeStart = range[0];\n    let rangeEnd = range[1];\n    // We use this inside Promise.all, so we need to resolve the promise even if there is an error\n    // to prevent Promise.all from short circuiting the rest.\n    function onStreamError() {\n      // Mark file path as failed to read and prevent multiple read attempts.\n      LRU_FILE_CONTENTS_FS_READ_FAILED.set(path, 1);\n      lineReaded.close();\n      lineReaded.removeAllListeners();\n      destroyStreamAndResolve();\n    }\n    // We need to handle the error event to prevent the process from crashing in < Node 16\n    // https://github.com/nodejs/node/pull/31603\n    stream.on('error', onStreamError);\n    lineReaded.on('error', onStreamError);\n    lineReaded.on('close', destroyStreamAndResolve);\n    lineReaded.on('line', line => {\n      lineNumber++;\n      if (lineNumber < rangeStart) {\n        return;\n      }\n      // !Warning: This mutates the cache by storing the snipped line into the cache.\n      output[lineNumber] = snipLine(line, 0);\n      if (lineNumber >= rangeEnd) {\n        if (currentRangeIndex === ranges.length - 1) {\n          // We need to close the file stream and remove listeners, else the reader will continue to run our listener;\n          lineReaded.close();\n          lineReaded.removeAllListeners();\n          return;\n        }\n        currentRangeIndex++;\n        const range = ranges[currentRangeIndex];\n        if (range === undefined) {\n          // This should never happen as it means we have a bug in the context.\n          lineReaded.close();\n          lineReaded.removeAllListeners();\n          return;\n        }\n        rangeStart = range[0];\n        rangeEnd = range[1];\n      }\n    });\n  });\n}\n/** Adds context lines to frames */\nfunction addSourceContextToFrames(frames, cache) {\n  for (const frame of frames) {\n    // Only add context if we have a filename and it hasn't already been added\n    if (frame.filename && frame.context_line === undefined && typeof frame.lineno === 'number') {\n      const contents = cache.get(frame.filename);\n      if (contents === undefined) {\n        continue;\n      }\n      addContextToFrame(frame.lineno, frame, contents);\n    }\n  }\n}\n/**\r\n * Resolves context lines before and after the given line number and appends them to the frame;\r\n */\nfunction addContextToFrame(lineno, frame, contents) {\n  // When there is no line number in the frame, attaching context is nonsensical and will even break grouping.\n  // We already check for lineno before calling this, but since StackFrame lineno is optional, we check it again.\n  if (frame.lineno === undefined || contents === undefined) {\n    return;\n  }\n  frame.pre_context = [];\n  for (let i = makeRangeStart(lineno); i < lineno; i++) {\n    // We always expect the start context as line numbers cannot be negative. If we dont find a line, then\n    // something went wrong somewhere. Clear the context and return without adding any linecontext.\n    const line = contents[i];\n    if (line === undefined) {\n      clearLineContext(frame);\n      return;\n    }\n    frame.pre_context.push(line);\n  }\n  // We should always have the context line. If we dont, something went wrong, so we clear the context and return\n  // without adding any linecontext.\n  if (contents[lineno] === undefined) {\n    clearLineContext(frame);\n    return;\n  }\n  frame.context_line = contents[lineno];\n  const end = makeRangeEnd(lineno);\n  frame.post_context = [];\n  for (let i = lineno + 1; i <= end; i++) {\n    // Since we dont track when the file ends, we cant clear the context if we dont find a line as it could\n    // just be that we reached the end of the file.\n    const line = contents[i];\n    if (line === undefined) {\n      break;\n    }\n    frame.post_context.push(line);\n  }\n}\n/**\r\n * Clears the context lines from a frame, used to reset a frame to its original state\r\n * if we fail to resolve all context lines for it.\r\n */\nfunction clearLineContext(frame) {\n  delete frame.pre_context;\n  delete frame.context_line;\n  delete frame.post_context;\n}\n/**\r\n * Determines if context lines should be skipped for a file.\r\n * - .min.(mjs|cjs|js) files are and not useful since they dont point to the original source\r\n * - node: prefixed modules are part of the runtime and cannot be resolved to a file\r\n * - data: skip json, wasm and inline js https://nodejs.org/api/esm.html#data-imports\r\n */\nfunction shouldSkipContextLinesForFile(path) {\n  // Test the most common prefix and extension first. These are the ones we\n  // are most likely to see in user applications and are the ones we can break out of first.\n  return path.startsWith('node:') || path.endsWith('.min.js') || path.endsWith('.min.cjs') || path.endsWith('.min.mjs') || path.startsWith('data:');\n}\n/**\r\n * Determines if we should skip contextlines based off the max lineno and colno values.\r\n */\nfunction shouldSkipContextLinesForFrame(frame) {\n  if (frame.lineno !== undefined && frame.lineno > MAX_CONTEXTLINES_LINENO) {\n    return true;\n  }\n  if (frame.colno !== undefined && frame.colno > MAX_CONTEXTLINES_COLNO) {\n    return true;\n  }\n  return false;\n}\n/**\r\n * Checks if we have all the contents that we need in the cache.\r\n */\nfunction rangeExistsInContentCache(file, range) {\n  const contents = LRU_FILE_CONTENTS_CACHE.get(file);\n  if (contents === undefined) {\n    return false;\n  }\n  for (let i = range[0]; i <= range[1]; i++) {\n    if (contents[i] === undefined) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\r\n * Creates contiguous ranges of lines to read from a file. In the case where context lines overlap,\r\n * the ranges are merged to create a single range.\r\n */\nfunction makeLineReaderRanges(lines) {\n  if (!lines.length) {\n    return [];\n  }\n  let i = 0;\n  const line = lines[0];\n  if (typeof line !== 'number') {\n    return [];\n  }\n  let current = makeContextRange(line);\n  const out = [];\n  while (true) {\n    if (i === lines.length - 1) {\n      out.push(current);\n      break;\n    }\n    // If the next line falls into the current range, extend the current range to lineno + linecontext.\n    const next = lines[i + 1];\n    if (typeof next !== 'number') {\n      break;\n    }\n    if (next <= current[1]) {\n      current[1] = next + DEFAULT_LINES_OF_CONTEXT;\n    } else {\n      out.push(current);\n      current = makeContextRange(next);\n    }\n    i++;\n  }\n  return out;\n}\n// Determine start and end indices for context range (inclusive);\nfunction makeContextRange(line) {\n  return [makeRangeStart(line), makeRangeEnd(line)];\n}\n// Compute inclusive end context range\nfunction makeRangeStart(line) {\n  return Math.max(1, line - DEFAULT_LINES_OF_CONTEXT);\n}\n// Compute inclusive start context range\nfunction makeRangeEnd(line) {\n  return line + DEFAULT_LINES_OF_CONTEXT;\n}\n/**\r\n * Get or init map value\r\n */\nfunction emplace(map, key, contents) {\n  const value = map.get(key);\n  if (value === undefined) {\n    map.set(key, contents);\n    return contents;\n  }\n  return value;\n}\nfunction snipLine(line, colno) {\n  let newLine = line;\n  const lineLength = newLine.length;\n  if (lineLength <= 150) {\n    return newLine;\n  }\n  if (colno > lineLength) {\n    colno = lineLength;\n  }\n  let start = Math.max(colno - 60, 0);\n  if (start < 5) {\n    start = 0;\n  }\n  let end = Math.min(start + 140, lineLength);\n  if (end > lineLength - 5) {\n    end = lineLength;\n  }\n  if (end === lineLength) {\n    start = Math.max(end - 140, 0);\n  }\n  newLine = newLine.slice(start, end);\n  if (start > 0) {\n    newLine = `...${newLine}`;\n  }\n  if (end < lineLength) {\n    newLine += '...';\n  }\n  return newLine;\n}\n\nvar version = \"4.17.2\";\n\nvar PostHogPersistedProperty;\r\n(function (PostHogPersistedProperty) {\r\n    PostHogPersistedProperty[\"AnonymousId\"] = \"anonymous_id\";\r\n    PostHogPersistedProperty[\"DistinctId\"] = \"distinct_id\";\r\n    PostHogPersistedProperty[\"Props\"] = \"props\";\r\n    PostHogPersistedProperty[\"FeatureFlagDetails\"] = \"feature_flag_details\";\r\n    PostHogPersistedProperty[\"FeatureFlags\"] = \"feature_flags\";\r\n    PostHogPersistedProperty[\"FeatureFlagPayloads\"] = \"feature_flag_payloads\";\r\n    PostHogPersistedProperty[\"BootstrapFeatureFlagDetails\"] = \"bootstrap_feature_flag_details\";\r\n    PostHogPersistedProperty[\"BootstrapFeatureFlags\"] = \"bootstrap_feature_flags\";\r\n    PostHogPersistedProperty[\"BootstrapFeatureFlagPayloads\"] = \"bootstrap_feature_flag_payloads\";\r\n    PostHogPersistedProperty[\"OverrideFeatureFlags\"] = \"override_feature_flags\";\r\n    PostHogPersistedProperty[\"Queue\"] = \"queue\";\r\n    PostHogPersistedProperty[\"OptedOut\"] = \"opted_out\";\r\n    PostHogPersistedProperty[\"SessionId\"] = \"session_id\";\r\n    PostHogPersistedProperty[\"SessionLastTimestamp\"] = \"session_timestamp\";\r\n    PostHogPersistedProperty[\"PersonProperties\"] = \"person_properties\";\r\n    PostHogPersistedProperty[\"GroupProperties\"] = \"group_properties\";\r\n    PostHogPersistedProperty[\"InstalledAppBuild\"] = \"installed_app_build\";\r\n    PostHogPersistedProperty[\"InstalledAppVersion\"] = \"installed_app_version\";\r\n    PostHogPersistedProperty[\"SessionReplay\"] = \"session_replay\";\r\n    PostHogPersistedProperty[\"DecideEndpointWasHit\"] = \"decide_endpoint_was_hit\";\r\n    PostHogPersistedProperty[\"SurveyLastSeenDate\"] = \"survey_last_seen_date\";\r\n    PostHogPersistedProperty[\"SurveysSeen\"] = \"surveys_seen\";\r\n    PostHogPersistedProperty[\"Surveys\"] = \"surveys\";\r\n    PostHogPersistedProperty[\"RemoteConfig\"] = \"remote_config\";\r\n})(PostHogPersistedProperty || (PostHogPersistedProperty = {}));\r\nvar SurveyPosition;\r\n(function (SurveyPosition) {\r\n    SurveyPosition[\"Left\"] = \"left\";\r\n    SurveyPosition[\"Right\"] = \"right\";\r\n    SurveyPosition[\"Center\"] = \"center\";\r\n})(SurveyPosition || (SurveyPosition = {}));\r\nvar SurveyWidgetType;\r\n(function (SurveyWidgetType) {\r\n    SurveyWidgetType[\"Button\"] = \"button\";\r\n    SurveyWidgetType[\"Tab\"] = \"tab\";\r\n    SurveyWidgetType[\"Selector\"] = \"selector\";\r\n})(SurveyWidgetType || (SurveyWidgetType = {}));\r\nvar SurveyType;\r\n(function (SurveyType) {\r\n    SurveyType[\"Popover\"] = \"popover\";\r\n    SurveyType[\"API\"] = \"api\";\r\n    SurveyType[\"Widget\"] = \"widget\";\r\n})(SurveyType || (SurveyType = {}));\r\nvar SurveyQuestionDescriptionContentType;\r\n(function (SurveyQuestionDescriptionContentType) {\r\n    SurveyQuestionDescriptionContentType[\"Html\"] = \"html\";\r\n    SurveyQuestionDescriptionContentType[\"Text\"] = \"text\";\r\n})(SurveyQuestionDescriptionContentType || (SurveyQuestionDescriptionContentType = {}));\r\nvar SurveyRatingDisplay;\r\n(function (SurveyRatingDisplay) {\r\n    SurveyRatingDisplay[\"Number\"] = \"number\";\r\n    SurveyRatingDisplay[\"Emoji\"] = \"emoji\";\r\n})(SurveyRatingDisplay || (SurveyRatingDisplay = {}));\r\nvar SurveyQuestionType;\r\n(function (SurveyQuestionType) {\r\n    SurveyQuestionType[\"Open\"] = \"open\";\r\n    SurveyQuestionType[\"MultipleChoice\"] = \"multiple_choice\";\r\n    SurveyQuestionType[\"SingleChoice\"] = \"single_choice\";\r\n    SurveyQuestionType[\"Rating\"] = \"rating\";\r\n    SurveyQuestionType[\"Link\"] = \"link\";\r\n})(SurveyQuestionType || (SurveyQuestionType = {}));\r\nvar SurveyQuestionBranchingType;\r\n(function (SurveyQuestionBranchingType) {\r\n    SurveyQuestionBranchingType[\"NextQuestion\"] = \"next_question\";\r\n    SurveyQuestionBranchingType[\"End\"] = \"end\";\r\n    SurveyQuestionBranchingType[\"ResponseBased\"] = \"response_based\";\r\n    SurveyQuestionBranchingType[\"SpecificQuestion\"] = \"specific_question\";\r\n})(SurveyQuestionBranchingType || (SurveyQuestionBranchingType = {}));\r\nvar SurveyMatchType;\r\n(function (SurveyMatchType) {\r\n    SurveyMatchType[\"Regex\"] = \"regex\";\r\n    SurveyMatchType[\"NotRegex\"] = \"not_regex\";\r\n    SurveyMatchType[\"Exact\"] = \"exact\";\r\n    SurveyMatchType[\"IsNot\"] = \"is_not\";\r\n    SurveyMatchType[\"Icontains\"] = \"icontains\";\r\n    SurveyMatchType[\"NotIcontains\"] = \"not_icontains\";\r\n})(SurveyMatchType || (SurveyMatchType = {}));\r\n/** Sync with plugin-server/src/types.ts */\r\nvar ActionStepStringMatching;\r\n(function (ActionStepStringMatching) {\r\n    ActionStepStringMatching[\"Contains\"] = \"contains\";\r\n    ActionStepStringMatching[\"Exact\"] = \"exact\";\r\n    ActionStepStringMatching[\"Regex\"] = \"regex\";\r\n})(ActionStepStringMatching || (ActionStepStringMatching = {}));\n\nconst normalizeDecideResponse = (decideResponse) => {\r\n    if ('flags' in decideResponse) {\r\n        // Convert v4 format to v3 format\r\n        const featureFlags = getFlagValuesFromFlags(decideResponse.flags);\r\n        const featureFlagPayloads = getPayloadsFromFlags(decideResponse.flags);\r\n        return {\r\n            ...decideResponse,\r\n            featureFlags,\r\n            featureFlagPayloads,\r\n        };\r\n    }\r\n    else {\r\n        // Convert v3 format to v4 format\r\n        const featureFlags = decideResponse.featureFlags ?? {};\r\n        const featureFlagPayloads = Object.fromEntries(Object.entries(decideResponse.featureFlagPayloads || {}).map(([k, v]) => [k, parsePayload(v)]));\r\n        const flags = Object.fromEntries(Object.entries(featureFlags).map(([key, value]) => [\r\n            key,\r\n            getFlagDetailFromFlagAndPayload(key, value, featureFlagPayloads[key]),\r\n        ]));\r\n        return {\r\n            ...decideResponse,\r\n            featureFlags,\r\n            featureFlagPayloads,\r\n            flags,\r\n        };\r\n    }\r\n};\r\nfunction getFlagDetailFromFlagAndPayload(key, value, payload) {\r\n    return {\r\n        key: key,\r\n        enabled: typeof value === 'string' ? true : value,\r\n        variant: typeof value === 'string' ? value : undefined,\r\n        reason: undefined,\r\n        metadata: {\r\n            id: undefined,\r\n            version: undefined,\r\n            payload: payload ? JSON.stringify(payload) : undefined,\r\n            description: undefined,\r\n        },\r\n    };\r\n}\r\n/**\r\n * Get the flag values from the flags v4 response.\r\n * @param flags - The flags\r\n * @returns The flag values\r\n */\r\nconst getFlagValuesFromFlags = (flags) => {\r\n    return Object.fromEntries(Object.entries(flags ?? {})\r\n        .map(([key, detail]) => [key, getFeatureFlagValue(detail)])\r\n        .filter(([, value]) => value !== undefined));\r\n};\r\n/**\r\n * Get the payloads from the flags v4 response.\r\n * @param flags - The flags\r\n * @returns The payloads\r\n */\r\nconst getPayloadsFromFlags = (flags) => {\r\n    const safeFlags = flags ?? {};\r\n    return Object.fromEntries(Object.keys(safeFlags)\r\n        .filter((flag) => {\r\n        const details = safeFlags[flag];\r\n        return details.enabled && details.metadata && details.metadata.payload !== undefined;\r\n    })\r\n        .map((flag) => {\r\n        const payload = safeFlags[flag].metadata?.payload;\r\n        return [flag, payload ? parsePayload(payload) : undefined];\r\n    }));\r\n};\r\nconst getFeatureFlagValue = (detail) => {\r\n    return detail === undefined ? undefined : detail.variant ?? detail.enabled;\r\n};\r\nconst parsePayload = (response) => {\r\n    if (typeof response !== 'string') {\r\n        return response;\r\n    }\r\n    try {\r\n        return JSON.parse(response);\r\n    }\r\n    catch {\r\n        return response;\r\n    }\r\n};\n\n// Rollout constants\r\nconst NEW_FLAGS_ROLLOUT_PERCENTAGE = 1;\r\n// The fnv1a hashes of the tokens that are explicitly excluded from the rollout\r\n// see https://github.com/PostHog/posthog-js-lite/blob/main/posthog-core/src/utils.ts#L84\r\n// are hashed API tokens from our top 10 for each category supported by this SDK.\r\nconst NEW_FLAGS_EXCLUDED_HASHES = new Set([\r\n    // Node\r\n    '61be3dd8',\r\n    '96f6df5f',\r\n    '8cfdba9b',\r\n    'bf027177',\r\n    'e59430a8',\r\n    '7fa5500b',\r\n    '569798e9',\r\n    '04809ff7',\r\n    '0ebc61a5',\r\n    '32de7f98',\r\n    '3beeb69a',\r\n    '12d34ad9',\r\n    '733853ec',\r\n    '0645bb64',\r\n    '5dcbee21',\r\n    'b1f95fa3',\r\n    '2189e408',\r\n    '82b460c2',\r\n    '3a8cc979',\r\n    '29ef8843',\r\n    '2cdbf767',\r\n    '38084b54',\r\n    // React Native\r\n    '50f9f8de',\r\n    '41d0df91',\r\n    '5c236689',\r\n    'c11aedd3',\r\n    'ada46672',\r\n    'f4331ee1',\r\n    '42fed62a',\r\n    'c957462c',\r\n    'd62f705a',\r\n    // Web (lots of teams per org, hence lots of API tokens)\r\n    'e0162666',\r\n    '01b3e5cf',\r\n    '441cef7f',\r\n    'bb9cafee',\r\n    '8f348eb0',\r\n    'b2553f3a',\r\n    '97469d7d',\r\n    '39f21a76',\r\n    '03706dcc',\r\n    '27d50569',\r\n    '307584a7',\r\n    '6433e92e',\r\n    '150c7fbb',\r\n    '49f57f22',\r\n    '3772f65b',\r\n    '01eb8256',\r\n    '3c9e9234',\r\n    'f853c7f7',\r\n    'c0ac4b67',\r\n    'cd609d40',\r\n    '10ca9b1a',\r\n    '8a87f11b',\r\n    '8e8e5216',\r\n    '1f6b63b3',\r\n    'db7943dd',\r\n    '79b7164c',\r\n    '07f78e33',\r\n    '2d21b6fd',\r\n    '952db5ee',\r\n    'a7d3b43f',\r\n    '1924dd9c',\r\n    '84e1b8f6',\r\n    'dff631b6',\r\n    'c5aa8a79',\r\n    'fa133a95',\r\n    '498a4508',\r\n    '24748755',\r\n    '98f3d658',\r\n    '21bbda67',\r\n    '7dbfed69',\r\n    'be3ec24c',\r\n    'fc80b8e2',\r\n    '75cc0998',\r\n]);\r\nconst STRING_FORMAT = 'utf8';\r\nfunction assert(truthyValue, message) {\r\n    if (!truthyValue || typeof truthyValue !== 'string' || isEmpty(truthyValue)) {\r\n        throw new Error(message);\r\n    }\r\n}\r\nfunction isEmpty(truthyValue) {\r\n    if (truthyValue.trim().length === 0) {\r\n        return true;\r\n    }\r\n    return false;\r\n}\r\nfunction removeTrailingSlash(url) {\r\n    return url?.replace(/\\/+$/, '');\r\n}\r\nasync function retriable(fn, props) {\r\n    let lastError = null;\r\n    for (let i = 0; i < props.retryCount + 1; i++) {\r\n        if (i > 0) {\r\n            // don't wait when it's the last try\r\n            await new Promise((r) => setTimeout(r, props.retryDelay));\r\n        }\r\n        try {\r\n            const res = await fn();\r\n            return res;\r\n        }\r\n        catch (e) {\r\n            lastError = e;\r\n            if (!props.retryCheck(e)) {\r\n                throw e;\r\n            }\r\n        }\r\n    }\r\n    throw lastError;\r\n}\r\nfunction currentTimestamp() {\r\n    return new Date().getTime();\r\n}\r\nfunction currentISOTime() {\r\n    return new Date().toISOString();\r\n}\r\nfunction safeSetTimeout(fn, timeout) {\r\n    // NOTE: we use this so rarely that it is totally fine to do `safeSetTimeout(fn, 0)``\r\n    // rather than setImmediate.\r\n    const t = setTimeout(fn, timeout);\r\n    // We unref if available to prevent Node.js hanging on exit\r\n    t?.unref && t?.unref();\r\n    return t;\r\n}\r\nfunction getFetch() {\r\n    return typeof fetch !== 'undefined' ? fetch : typeof globalThis.fetch !== 'undefined' ? globalThis.fetch : undefined;\r\n}\r\n// FNV-1a hash function\r\n// https://en.wikipedia.org/wiki/Fowler%E2%80%93Noll%E2%80%93Vo_hash_function\r\n// I know, I know, I'm rolling my own hash function, but I didn't want to take on\r\n// a crypto dependency and this is just temporary anyway\r\nfunction fnv1a(str) {\r\n    let hash = 0x811c9dc5; // FNV offset basis\r\n    for (let i = 0; i < str.length; i++) {\r\n        hash ^= str.charCodeAt(i);\r\n        hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\r\n    }\r\n    // Convert to hex string, padding to 8 chars\r\n    return (hash >>> 0).toString(16).padStart(8, '0');\r\n}\r\nfunction isTokenInRollout(token, percentage = 0, excludedHashes) {\r\n    const tokenHash = fnv1a(token);\r\n    // Check excluded hashes (we're explicitly including these tokens from the rollout)\r\n    if (excludedHashes?.has(tokenHash)) {\r\n        return false;\r\n    }\r\n    // Convert hash to int and divide by max value to get number between 0-1\r\n    const hashInt = parseInt(tokenHash, 16);\r\n    const hashFloat = hashInt / 0xffffffff;\r\n    return hashFloat < percentage;\r\n}\r\nfunction allSettled(promises) {\r\n    return Promise.all(promises.map((p) => (p ?? Promise.resolve()).then((value) => ({ status: 'fulfilled', value }), (reason) => ({ status: 'rejected', reason }))));\r\n}\n\n// Copyright (c) 2013 Pieroxy <<EMAIL>>\r\n// This work is free. You can redistribute it and/or modify it\r\n// under the terms of the WTFPL, Version 2\r\n// For more information see LICENSE.txt or http://www.wtfpl.net/\r\n//\r\n// For more information, the home page:\r\n// http://pieroxy.net/blog/pages/lz-string/testing.html\r\n//\r\n// LZ-based compression algorithm, version 1.4.4\r\n// private property\r\nconst f = String.fromCharCode;\r\nconst keyStrBase64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\r\nconst baseReverseDic = {};\r\nfunction getBaseValue(alphabet, character) {\r\n    if (!baseReverseDic[alphabet]) {\r\n        baseReverseDic[alphabet] = {};\r\n        for (let i = 0; i < alphabet.length; i++) {\r\n            baseReverseDic[alphabet][alphabet.charAt(i)] = i;\r\n        }\r\n    }\r\n    return baseReverseDic[alphabet][character];\r\n}\r\nconst LZString = {\r\n    compressToBase64: function (input) {\r\n        if (input == null) {\r\n            return '';\r\n        }\r\n        const res = LZString._compress(input, 6, function (a) {\r\n            return keyStrBase64.charAt(a);\r\n        });\r\n        switch (res.length % 4 // To produce valid Base64\r\n        ) {\r\n            default: // When could this happen ?\r\n            case 0:\r\n                return res;\r\n            case 1:\r\n                return res + '===';\r\n            case 2:\r\n                return res + '==';\r\n            case 3:\r\n                return res + '=';\r\n        }\r\n    },\r\n    decompressFromBase64: function (input) {\r\n        if (input == null) {\r\n            return '';\r\n        }\r\n        if (input == '') {\r\n            return null;\r\n        }\r\n        return LZString._decompress(input.length, 32, function (index) {\r\n            return getBaseValue(keyStrBase64, input.charAt(index));\r\n        });\r\n    },\r\n    compress: function (uncompressed) {\r\n        return LZString._compress(uncompressed, 16, function (a) {\r\n            return f(a);\r\n        });\r\n    },\r\n    _compress: function (uncompressed, bitsPerChar, getCharFromInt) {\r\n        if (uncompressed == null) {\r\n            return '';\r\n        }\r\n        const context_dictionary = {}, context_dictionaryToCreate = {}, context_data = [];\r\n        let i, value, context_c = '', context_wc = '', context_w = '', context_enlargeIn = 2, // Compensate for the first entry which should not count\r\n        context_dictSize = 3, context_numBits = 2, context_data_val = 0, context_data_position = 0, ii;\r\n        for (ii = 0; ii < uncompressed.length; ii += 1) {\r\n            context_c = uncompressed.charAt(ii);\r\n            if (!Object.prototype.hasOwnProperty.call(context_dictionary, context_c)) {\r\n                context_dictionary[context_c] = context_dictSize++;\r\n                context_dictionaryToCreate[context_c] = true;\r\n            }\r\n            context_wc = context_w + context_c;\r\n            if (Object.prototype.hasOwnProperty.call(context_dictionary, context_wc)) {\r\n                context_w = context_wc;\r\n            }\r\n            else {\r\n                if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate, context_w)) {\r\n                    if (context_w.charCodeAt(0) < 256) {\r\n                        for (i = 0; i < context_numBits; i++) {\r\n                            context_data_val = context_data_val << 1;\r\n                            if (context_data_position == bitsPerChar - 1) {\r\n                                context_data_position = 0;\r\n                                context_data.push(getCharFromInt(context_data_val));\r\n                                context_data_val = 0;\r\n                            }\r\n                            else {\r\n                                context_data_position++;\r\n                            }\r\n                        }\r\n                        value = context_w.charCodeAt(0);\r\n                        for (i = 0; i < 8; i++) {\r\n                            context_data_val = (context_data_val << 1) | (value & 1);\r\n                            if (context_data_position == bitsPerChar - 1) {\r\n                                context_data_position = 0;\r\n                                context_data.push(getCharFromInt(context_data_val));\r\n                                context_data_val = 0;\r\n                            }\r\n                            else {\r\n                                context_data_position++;\r\n                            }\r\n                            value = value >> 1;\r\n                        }\r\n                    }\r\n                    else {\r\n                        value = 1;\r\n                        for (i = 0; i < context_numBits; i++) {\r\n                            context_data_val = (context_data_val << 1) | value;\r\n                            if (context_data_position == bitsPerChar - 1) {\r\n                                context_data_position = 0;\r\n                                context_data.push(getCharFromInt(context_data_val));\r\n                                context_data_val = 0;\r\n                            }\r\n                            else {\r\n                                context_data_position++;\r\n                            }\r\n                            value = 0;\r\n                        }\r\n                        value = context_w.charCodeAt(0);\r\n                        for (i = 0; i < 16; i++) {\r\n                            context_data_val = (context_data_val << 1) | (value & 1);\r\n                            if (context_data_position == bitsPerChar - 1) {\r\n                                context_data_position = 0;\r\n                                context_data.push(getCharFromInt(context_data_val));\r\n                                context_data_val = 0;\r\n                            }\r\n                            else {\r\n                                context_data_position++;\r\n                            }\r\n                            value = value >> 1;\r\n                        }\r\n                    }\r\n                    context_enlargeIn--;\r\n                    if (context_enlargeIn == 0) {\r\n                        context_enlargeIn = Math.pow(2, context_numBits);\r\n                        context_numBits++;\r\n                    }\r\n                    delete context_dictionaryToCreate[context_w];\r\n                }\r\n                else {\r\n                    value = context_dictionary[context_w];\r\n                    for (i = 0; i < context_numBits; i++) {\r\n                        context_data_val = (context_data_val << 1) | (value & 1);\r\n                        if (context_data_position == bitsPerChar - 1) {\r\n                            context_data_position = 0;\r\n                            context_data.push(getCharFromInt(context_data_val));\r\n                            context_data_val = 0;\r\n                        }\r\n                        else {\r\n                            context_data_position++;\r\n                        }\r\n                        value = value >> 1;\r\n                    }\r\n                }\r\n                context_enlargeIn--;\r\n                if (context_enlargeIn == 0) {\r\n                    context_enlargeIn = Math.pow(2, context_numBits);\r\n                    context_numBits++;\r\n                }\r\n                // Add wc to the dictionary.\r\n                context_dictionary[context_wc] = context_dictSize++;\r\n                context_w = String(context_c);\r\n            }\r\n        }\r\n        // Output the code for w.\r\n        if (context_w !== '') {\r\n            if (Object.prototype.hasOwnProperty.call(context_dictionaryToCreate, context_w)) {\r\n                if (context_w.charCodeAt(0) < 256) {\r\n                    for (i = 0; i < context_numBits; i++) {\r\n                        context_data_val = context_data_val << 1;\r\n                        if (context_data_position == bitsPerChar - 1) {\r\n                            context_data_position = 0;\r\n                            context_data.push(getCharFromInt(context_data_val));\r\n                            context_data_val = 0;\r\n                        }\r\n                        else {\r\n                            context_data_position++;\r\n                        }\r\n                    }\r\n                    value = context_w.charCodeAt(0);\r\n                    for (i = 0; i < 8; i++) {\r\n                        context_data_val = (context_data_val << 1) | (value & 1);\r\n                        if (context_data_position == bitsPerChar - 1) {\r\n                            context_data_position = 0;\r\n                            context_data.push(getCharFromInt(context_data_val));\r\n                            context_data_val = 0;\r\n                        }\r\n                        else {\r\n                            context_data_position++;\r\n                        }\r\n                        value = value >> 1;\r\n                    }\r\n                }\r\n                else {\r\n                    value = 1;\r\n                    for (i = 0; i < context_numBits; i++) {\r\n                        context_data_val = (context_data_val << 1) | value;\r\n                        if (context_data_position == bitsPerChar - 1) {\r\n                            context_data_position = 0;\r\n                            context_data.push(getCharFromInt(context_data_val));\r\n                            context_data_val = 0;\r\n                        }\r\n                        else {\r\n                            context_data_position++;\r\n                        }\r\n                        value = 0;\r\n                    }\r\n                    value = context_w.charCodeAt(0);\r\n                    for (i = 0; i < 16; i++) {\r\n                        context_data_val = (context_data_val << 1) | (value & 1);\r\n                        if (context_data_position == bitsPerChar - 1) {\r\n                            context_data_position = 0;\r\n                            context_data.push(getCharFromInt(context_data_val));\r\n                            context_data_val = 0;\r\n                        }\r\n                        else {\r\n                            context_data_position++;\r\n                        }\r\n                        value = value >> 1;\r\n                    }\r\n                }\r\n                context_enlargeIn--;\r\n                if (context_enlargeIn == 0) {\r\n                    context_enlargeIn = Math.pow(2, context_numBits);\r\n                    context_numBits++;\r\n                }\r\n                delete context_dictionaryToCreate[context_w];\r\n            }\r\n            else {\r\n                value = context_dictionary[context_w];\r\n                for (i = 0; i < context_numBits; i++) {\r\n                    context_data_val = (context_data_val << 1) | (value & 1);\r\n                    if (context_data_position == bitsPerChar - 1) {\r\n                        context_data_position = 0;\r\n                        context_data.push(getCharFromInt(context_data_val));\r\n                        context_data_val = 0;\r\n                    }\r\n                    else {\r\n                        context_data_position++;\r\n                    }\r\n                    value = value >> 1;\r\n                }\r\n            }\r\n            context_enlargeIn--;\r\n            if (context_enlargeIn == 0) {\r\n                context_enlargeIn = Math.pow(2, context_numBits);\r\n                context_numBits++;\r\n            }\r\n        }\r\n        // Mark the end of the stream\r\n        value = 2;\r\n        for (i = 0; i < context_numBits; i++) {\r\n            context_data_val = (context_data_val << 1) | (value & 1);\r\n            if (context_data_position == bitsPerChar - 1) {\r\n                context_data_position = 0;\r\n                context_data.push(getCharFromInt(context_data_val));\r\n                context_data_val = 0;\r\n            }\r\n            else {\r\n                context_data_position++;\r\n            }\r\n            value = value >> 1;\r\n        }\r\n        // Flush the last char\r\n        while (true) {\r\n            context_data_val = context_data_val << 1;\r\n            if (context_data_position == bitsPerChar - 1) {\r\n                context_data.push(getCharFromInt(context_data_val));\r\n                break;\r\n            }\r\n            else {\r\n                context_data_position++;\r\n            }\r\n        }\r\n        return context_data.join('');\r\n    },\r\n    decompress: function (compressed) {\r\n        if (compressed == null) {\r\n            return '';\r\n        }\r\n        if (compressed == '') {\r\n            return null;\r\n        }\r\n        return LZString._decompress(compressed.length, 32768, function (index) {\r\n            return compressed.charCodeAt(index);\r\n        });\r\n    },\r\n    _decompress: function (length, resetValue, getNextValue) {\r\n        const dictionary = [], result = [], data = { val: getNextValue(0), position: resetValue, index: 1 };\r\n        let enlargeIn = 4, dictSize = 4, numBits = 3, entry = '', i, w, bits, resb, maxpower, power, c;\r\n        for (i = 0; i < 3; i += 1) {\r\n            dictionary[i] = i;\r\n        }\r\n        bits = 0;\r\n        maxpower = Math.pow(2, 2);\r\n        power = 1;\r\n        while (power != maxpower) {\r\n            resb = data.val & data.position;\r\n            data.position >>= 1;\r\n            if (data.position == 0) {\r\n                data.position = resetValue;\r\n                data.val = getNextValue(data.index++);\r\n            }\r\n            bits |= (resb > 0 ? 1 : 0) * power;\r\n            power <<= 1;\r\n        }\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        switch ((bits)) {\r\n            case 0:\r\n                bits = 0;\r\n                maxpower = Math.pow(2, 8);\r\n                power = 1;\r\n                while (power != maxpower) {\r\n                    resb = data.val & data.position;\r\n                    data.position >>= 1;\r\n                    if (data.position == 0) {\r\n                        data.position = resetValue;\r\n                        data.val = getNextValue(data.index++);\r\n                    }\r\n                    bits |= (resb > 0 ? 1 : 0) * power;\r\n                    power <<= 1;\r\n                }\r\n                c = f(bits);\r\n                break;\r\n            case 1:\r\n                bits = 0;\r\n                maxpower = Math.pow(2, 16);\r\n                power = 1;\r\n                while (power != maxpower) {\r\n                    resb = data.val & data.position;\r\n                    data.position >>= 1;\r\n                    if (data.position == 0) {\r\n                        data.position = resetValue;\r\n                        data.val = getNextValue(data.index++);\r\n                    }\r\n                    bits |= (resb > 0 ? 1 : 0) * power;\r\n                    power <<= 1;\r\n                }\r\n                c = f(bits);\r\n                break;\r\n            case 2:\r\n                return '';\r\n        }\r\n        dictionary[3] = c;\r\n        w = c;\r\n        result.push(c);\r\n        while (true) {\r\n            if (data.index > length) {\r\n                return '';\r\n            }\r\n            bits = 0;\r\n            maxpower = Math.pow(2, numBits);\r\n            power = 1;\r\n            while (power != maxpower) {\r\n                resb = data.val & data.position;\r\n                data.position >>= 1;\r\n                if (data.position == 0) {\r\n                    data.position = resetValue;\r\n                    data.val = getNextValue(data.index++);\r\n                }\r\n                bits |= (resb > 0 ? 1 : 0) * power;\r\n                power <<= 1;\r\n            }\r\n            switch ((c = bits)) {\r\n                case 0:\r\n                    bits = 0;\r\n                    maxpower = Math.pow(2, 8);\r\n                    power = 1;\r\n                    while (power != maxpower) {\r\n                        resb = data.val & data.position;\r\n                        data.position >>= 1;\r\n                        if (data.position == 0) {\r\n                            data.position = resetValue;\r\n                            data.val = getNextValue(data.index++);\r\n                        }\r\n                        bits |= (resb > 0 ? 1 : 0) * power;\r\n                        power <<= 1;\r\n                    }\r\n                    dictionary[dictSize++] = f(bits);\r\n                    c = dictSize - 1;\r\n                    enlargeIn--;\r\n                    break;\r\n                case 1:\r\n                    bits = 0;\r\n                    maxpower = Math.pow(2, 16);\r\n                    power = 1;\r\n                    while (power != maxpower) {\r\n                        resb = data.val & data.position;\r\n                        data.position >>= 1;\r\n                        if (data.position == 0) {\r\n                            data.position = resetValue;\r\n                            data.val = getNextValue(data.index++);\r\n                        }\r\n                        bits |= (resb > 0 ? 1 : 0) * power;\r\n                        power <<= 1;\r\n                    }\r\n                    dictionary[dictSize++] = f(bits);\r\n                    c = dictSize - 1;\r\n                    enlargeIn--;\r\n                    break;\r\n                case 2:\r\n                    return result.join('');\r\n            }\r\n            if (enlargeIn == 0) {\r\n                enlargeIn = Math.pow(2, numBits);\r\n                numBits++;\r\n            }\r\n            if (dictionary[c]) {\r\n                entry = dictionary[c];\r\n            }\r\n            else {\r\n                if (c === dictSize) {\r\n                    entry = w + w.charAt(0);\r\n                }\r\n                else {\r\n                    return null;\r\n                }\r\n            }\r\n            result.push(entry);\r\n            // Add w+entry[0] to the dictionary.\r\n            dictionary[dictSize++] = w + entry.charAt(0);\r\n            enlargeIn--;\r\n            w = entry;\r\n            if (enlargeIn == 0) {\r\n                enlargeIn = Math.pow(2, numBits);\r\n                numBits++;\r\n            }\r\n        }\r\n    },\r\n};\n\nclass SimpleEventEmitter {\r\n    constructor() {\r\n        this.events = {};\r\n        this.events = {};\r\n    }\r\n    on(event, listener) {\r\n        if (!this.events[event]) {\r\n            this.events[event] = [];\r\n        }\r\n        this.events[event].push(listener);\r\n        return () => {\r\n            this.events[event] = this.events[event].filter((x) => x !== listener);\r\n        };\r\n    }\r\n    emit(event, payload) {\r\n        for (const listener of this.events[event] || []) {\r\n            listener(payload);\r\n        }\r\n        for (const listener of this.events['*'] || []) {\r\n            listener(event, payload);\r\n        }\r\n    }\r\n}\n\nclass PostHogFetchHttpError extends Error {\r\n    constructor(response, reqByteLength) {\r\n        super('HTTP error while fetching PostHog: status=' + response.status + ', reqByteLength=' + reqByteLength);\r\n        this.response = response;\r\n        this.reqByteLength = reqByteLength;\r\n        this.name = 'PostHogFetchHttpError';\r\n    }\r\n    get status() {\r\n        return this.response.status;\r\n    }\r\n    get text() {\r\n        return this.response.text();\r\n    }\r\n    get json() {\r\n        return this.response.json();\r\n    }\r\n}\r\nclass PostHogFetchNetworkError extends Error {\r\n    constructor(error) {\r\n        // TRICKY: \"cause\" is a newer property but is just ignored otherwise. Cast to any to ignore the type issue.\r\n        // eslint-disable-next-line @typescript-eslint/prefer-ts-expect-error\r\n        // @ts-ignore\r\n        super('Network error while fetching PostHog', error instanceof Error ? { cause: error } : {});\r\n        this.error = error;\r\n        this.name = 'PostHogFetchNetworkError';\r\n    }\r\n}\r\nasync function logFlushError(err) {\r\n    if (err instanceof PostHogFetchHttpError) {\r\n        let text = '';\r\n        try {\r\n            text = await err.text;\r\n        }\r\n        catch { }\r\n        console.error(`Error while flushing PostHog: message=${err.message}, response body=${text}`, err);\r\n    }\r\n    else {\r\n        console.error('Error while flushing PostHog', err);\r\n    }\r\n    return Promise.resolve();\r\n}\r\nfunction isPostHogFetchError(err) {\r\n    return typeof err === 'object' && (err instanceof PostHogFetchHttpError || err instanceof PostHogFetchNetworkError);\r\n}\r\nfunction isPostHogFetchContentTooLargeError(err) {\r\n    return typeof err === 'object' && err instanceof PostHogFetchHttpError && err.status === 413;\r\n}\r\nvar QuotaLimitedFeature;\r\n(function (QuotaLimitedFeature) {\r\n    QuotaLimitedFeature[\"FeatureFlags\"] = \"feature_flags\";\r\n    QuotaLimitedFeature[\"Recordings\"] = \"recordings\";\r\n})(QuotaLimitedFeature || (QuotaLimitedFeature = {}));\r\nclass PostHogCoreStateless {\r\n    constructor(apiKey, options) {\r\n        this.flushPromise = null;\r\n        this.shutdownPromise = null;\r\n        this.pendingPromises = {};\r\n        // internal\r\n        this._events = new SimpleEventEmitter();\r\n        this._isInitialized = false;\r\n        assert(apiKey, \"You must pass your PostHog project's api key.\");\r\n        this.apiKey = apiKey;\r\n        this.host = removeTrailingSlash(options?.host || 'https://us.i.posthog.com');\r\n        this.flushAt = options?.flushAt ? Math.max(options?.flushAt, 1) : 20;\r\n        this.maxBatchSize = Math.max(this.flushAt, options?.maxBatchSize ?? 100);\r\n        this.maxQueueSize = Math.max(this.flushAt, options?.maxQueueSize ?? 1000);\r\n        this.flushInterval = options?.flushInterval ?? 10000;\r\n        this.captureMode = options?.captureMode || 'json';\r\n        this.preloadFeatureFlags = options?.preloadFeatureFlags ?? true;\r\n        // If enable is explicitly set to false we override the optout\r\n        this.defaultOptIn = options?.defaultOptIn ?? true;\r\n        this.disableSurveys = options?.disableSurveys ?? false;\r\n        this._retryOptions = {\r\n            retryCount: options?.fetchRetryCount ?? 3,\r\n            retryDelay: options?.fetchRetryDelay ?? 3000,\r\n            retryCheck: isPostHogFetchError,\r\n        };\r\n        this.requestTimeout = options?.requestTimeout ?? 10000; // 10 seconds\r\n        this.featureFlagsRequestTimeoutMs = options?.featureFlagsRequestTimeoutMs ?? 3000; // 3 seconds\r\n        this.remoteConfigRequestTimeoutMs = options?.remoteConfigRequestTimeoutMs ?? 3000; // 3 seconds\r\n        this.disableGeoip = options?.disableGeoip ?? true;\r\n        this.disabled = options?.disabled ?? false;\r\n        this.historicalMigration = options?.historicalMigration ?? false;\r\n        // Init promise allows the derived class to block calls until it is ready\r\n        this._initPromise = Promise.resolve();\r\n        this._isInitialized = true;\r\n    }\r\n    logMsgIfDebug(fn) {\r\n        if (this.isDebug) {\r\n            fn();\r\n        }\r\n    }\r\n    wrap(fn) {\r\n        if (this.disabled) {\r\n            this.logMsgIfDebug(() => console.warn('[PostHog] The client is disabled'));\r\n            return;\r\n        }\r\n        if (this._isInitialized) {\r\n            // NOTE: We could also check for the \"opt in\" status here...\r\n            return fn();\r\n        }\r\n        this._initPromise.then(() => fn());\r\n    }\r\n    getCommonEventProperties() {\r\n        return {\r\n            $lib: this.getLibraryId(),\r\n            $lib_version: this.getLibraryVersion(),\r\n        };\r\n    }\r\n    get optedOut() {\r\n        return this.getPersistedProperty(PostHogPersistedProperty.OptedOut) ?? !this.defaultOptIn;\r\n    }\r\n    async optIn() {\r\n        this.wrap(() => {\r\n            this.setPersistedProperty(PostHogPersistedProperty.OptedOut, false);\r\n        });\r\n    }\r\n    async optOut() {\r\n        this.wrap(() => {\r\n            this.setPersistedProperty(PostHogPersistedProperty.OptedOut, true);\r\n        });\r\n    }\r\n    on(event, cb) {\r\n        return this._events.on(event, cb);\r\n    }\r\n    debug(enabled = true) {\r\n        this.removeDebugCallback?.();\r\n        if (enabled) {\r\n            const removeDebugCallback = this.on('*', (event, payload) => console.log('PostHog Debug', event, payload));\r\n            this.removeDebugCallback = () => {\r\n                removeDebugCallback();\r\n                this.removeDebugCallback = undefined;\r\n            };\r\n        }\r\n    }\r\n    get isDebug() {\r\n        return !!this.removeDebugCallback;\r\n    }\r\n    get isDisabled() {\r\n        return this.disabled;\r\n    }\r\n    buildPayload(payload) {\r\n        return {\r\n            distinct_id: payload.distinct_id,\r\n            event: payload.event,\r\n            properties: {\r\n                ...(payload.properties || {}),\r\n                ...this.getCommonEventProperties(), // Common PH props\r\n            },\r\n        };\r\n    }\r\n    addPendingPromise(promise) {\r\n        const promiseUUID = uuidv7();\r\n        this.pendingPromises[promiseUUID] = promise;\r\n        promise\r\n            .catch(() => { })\r\n            .finally(() => {\r\n            delete this.pendingPromises[promiseUUID];\r\n        });\r\n        return promise;\r\n    }\r\n    /***\r\n     *** TRACKING\r\n     ***/\r\n    identifyStateless(distinctId, properties, options) {\r\n        this.wrap(() => {\r\n            // The properties passed to identifyStateless are event properties.\r\n            // To add person properties, pass in all person properties to the `$set` and `$set_once` keys.\r\n            const payload = {\r\n                ...this.buildPayload({\r\n                    distinct_id: distinctId,\r\n                    event: '$identify',\r\n                    properties,\r\n                }),\r\n            };\r\n            this.enqueue('identify', payload, options);\r\n        });\r\n    }\r\n    async identifyStatelessImmediate(distinctId, properties, options) {\r\n        const payload = {\r\n            ...this.buildPayload({\r\n                distinct_id: distinctId,\r\n                event: '$identify',\r\n                properties,\r\n            }),\r\n        };\r\n        await this.sendImmediate('identify', payload, options);\r\n    }\r\n    captureStateless(distinctId, event, properties, options) {\r\n        this.wrap(() => {\r\n            const payload = this.buildPayload({ distinct_id: distinctId, event, properties });\r\n            this.enqueue('capture', payload, options);\r\n        });\r\n    }\r\n    async captureStatelessImmediate(distinctId, event, properties, options) {\r\n        const payload = this.buildPayload({ distinct_id: distinctId, event, properties });\r\n        await this.sendImmediate('capture', payload, options);\r\n    }\r\n    aliasStateless(alias, distinctId, properties, options) {\r\n        this.wrap(() => {\r\n            const payload = this.buildPayload({\r\n                event: '$create_alias',\r\n                distinct_id: distinctId,\r\n                properties: {\r\n                    ...(properties || {}),\r\n                    distinct_id: distinctId,\r\n                    alias,\r\n                },\r\n            });\r\n            this.enqueue('alias', payload, options);\r\n        });\r\n    }\r\n    async aliasStatelessImmediate(alias, distinctId, properties, options) {\r\n        const payload = this.buildPayload({\r\n            event: '$create_alias',\r\n            distinct_id: distinctId,\r\n            properties: {\r\n                ...(properties || {}),\r\n                distinct_id: distinctId,\r\n                alias,\r\n            },\r\n        });\r\n        await this.sendImmediate('alias', payload, options);\r\n    }\r\n    /***\r\n     *** GROUPS\r\n     ***/\r\n    groupIdentifyStateless(groupType, groupKey, groupProperties, options, distinctId, eventProperties) {\r\n        this.wrap(() => {\r\n            const payload = this.buildPayload({\r\n                distinct_id: distinctId || `$${groupType}_${groupKey}`,\r\n                event: '$groupidentify',\r\n                properties: {\r\n                    $group_type: groupType,\r\n                    $group_key: groupKey,\r\n                    $group_set: groupProperties || {},\r\n                    ...(eventProperties || {}),\r\n                },\r\n            });\r\n            this.enqueue('capture', payload, options);\r\n        });\r\n    }\r\n    async getRemoteConfig() {\r\n        await this._initPromise;\r\n        let host = this.host;\r\n        if (host === 'https://us.i.posthog.com') {\r\n            host = 'https://us-assets.i.posthog.com';\r\n        }\r\n        else if (host === 'https://eu.i.posthog.com') {\r\n            host = 'https://eu-assets.i.posthog.com';\r\n        }\r\n        const url = `${host}/array/${this.apiKey}/config`;\r\n        const fetchOptions = {\r\n            method: 'GET',\r\n            headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\r\n        };\r\n        // Don't retry remote config API calls\r\n        return this.fetchWithRetry(url, fetchOptions, { retryCount: 0 }, this.remoteConfigRequestTimeoutMs)\r\n            .then((response) => response.json())\r\n            .catch((error) => {\r\n            this.logMsgIfDebug(() => console.error('Remote config could not be loaded', error));\r\n            this._events.emit('error', error);\r\n            return undefined;\r\n        });\r\n    }\r\n    /***\r\n     *** FEATURE FLAGS\r\n     ***/\r\n    async getDecide(distinctId, groups = {}, personProperties = {}, groupProperties = {}, extraPayload = {}) {\r\n        await this._initPromise;\r\n        // Check if the API token is in the new flags rollout\r\n        // This is a temporary measure to ensure that we can still use the old flags API\r\n        // while we migrate to the new flags API\r\n        const useFlags = isTokenInRollout(this.apiKey, NEW_FLAGS_ROLLOUT_PERCENTAGE, NEW_FLAGS_EXCLUDED_HASHES);\r\n        const url = useFlags ? `${this.host}/flags/?v=2` : `${this.host}/decide/?v=4`;\r\n        const fetchOptions = {\r\n            method: 'POST',\r\n            headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\r\n            body: JSON.stringify({\r\n                token: this.apiKey,\r\n                distinct_id: distinctId,\r\n                groups,\r\n                person_properties: personProperties,\r\n                group_properties: groupProperties,\r\n                ...extraPayload,\r\n            }),\r\n        };\r\n        this.logMsgIfDebug(() => console.log('PostHog Debug', 'Decide URL', url));\r\n        // Don't retry /decide API calls\r\n        return this.fetchWithRetry(url, fetchOptions, { retryCount: 0 }, this.featureFlagsRequestTimeoutMs)\r\n            .then((response) => response.json())\r\n            .then((response) => normalizeDecideResponse(response))\r\n            .catch((error) => {\r\n            this._events.emit('error', error);\r\n            return undefined;\r\n        });\r\n    }\r\n    async getFeatureFlagStateless(key, distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip) {\r\n        await this._initPromise;\r\n        const flagDetailResponse = await this.getFeatureFlagDetailStateless(key, distinctId, groups, personProperties, groupProperties, disableGeoip);\r\n        if (flagDetailResponse === undefined) {\r\n            // If we haven't loaded flags yet, or errored out, we respond with undefined\r\n            return {\r\n                response: undefined,\r\n                requestId: undefined,\r\n            };\r\n        }\r\n        let response = getFeatureFlagValue(flagDetailResponse.response);\r\n        if (response === undefined) {\r\n            // For cases where the flag is unknown, return false\r\n            response = false;\r\n        }\r\n        // If we have flags we either return the value (true or string) or false\r\n        return {\r\n            response,\r\n            requestId: flagDetailResponse.requestId,\r\n        };\r\n    }\r\n    async getFeatureFlagDetailStateless(key, distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip) {\r\n        await this._initPromise;\r\n        const decideResponse = await this.getFeatureFlagDetailsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip, [key]);\r\n        if (decideResponse === undefined) {\r\n            return undefined;\r\n        }\r\n        const featureFlags = decideResponse.flags;\r\n        const flagDetail = featureFlags[key];\r\n        return {\r\n            response: flagDetail,\r\n            requestId: decideResponse.requestId,\r\n        };\r\n    }\r\n    async getFeatureFlagPayloadStateless(key, distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip) {\r\n        await this._initPromise;\r\n        const payloads = await this.getFeatureFlagPayloadsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip, [key]);\r\n        if (!payloads) {\r\n            return undefined;\r\n        }\r\n        const response = payloads[key];\r\n        // Undefined means a loading or missing data issue. Null means evaluation happened and there was no match\r\n        if (response === undefined) {\r\n            return null;\r\n        }\r\n        return response;\r\n    }\r\n    async getFeatureFlagPayloadsStateless(distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip, flagKeysToEvaluate) {\r\n        await this._initPromise;\r\n        const payloads = (await this.getFeatureFlagsAndPayloadsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip, flagKeysToEvaluate)).payloads;\r\n        return payloads;\r\n    }\r\n    async getFeatureFlagsStateless(distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip, flagKeysToEvaluate) {\r\n        await this._initPromise;\r\n        return await this.getFeatureFlagsAndPayloadsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip, flagKeysToEvaluate);\r\n    }\r\n    async getFeatureFlagsAndPayloadsStateless(distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip, flagKeysToEvaluate) {\r\n        await this._initPromise;\r\n        const featureFlagDetails = await this.getFeatureFlagDetailsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip, flagKeysToEvaluate);\r\n        if (!featureFlagDetails) {\r\n            return {\r\n                flags: undefined,\r\n                payloads: undefined,\r\n                requestId: undefined,\r\n            };\r\n        }\r\n        return {\r\n            flags: featureFlagDetails.featureFlags,\r\n            payloads: featureFlagDetails.featureFlagPayloads,\r\n            requestId: featureFlagDetails.requestId,\r\n        };\r\n    }\r\n    async getFeatureFlagDetailsStateless(distinctId, groups = {}, personProperties = {}, groupProperties = {}, disableGeoip, flagKeysToEvaluate) {\r\n        await this._initPromise;\r\n        const extraPayload = {};\r\n        if (disableGeoip ?? this.disableGeoip) {\r\n            extraPayload['geoip_disable'] = true;\r\n        }\r\n        if (flagKeysToEvaluate) {\r\n            extraPayload['flag_keys_to_evaluate'] = flagKeysToEvaluate;\r\n        }\r\n        const decideResponse = await this.getDecide(distinctId, groups, personProperties, groupProperties, extraPayload);\r\n        if (decideResponse === undefined) {\r\n            // We probably errored out, so return undefined\r\n            return undefined;\r\n        }\r\n        // if there's an error on the decideResponse, log a console error, but don't throw an error\r\n        if (decideResponse.errorsWhileComputingFlags) {\r\n            console.error('[FEATURE FLAGS] Error while computing feature flags, some flags may be missing or incorrect. Learn more at https://posthog.com/docs/feature-flags/best-practices');\r\n        }\r\n        // Add check for quota limitation on feature flags\r\n        if (decideResponse.quotaLimited?.includes(QuotaLimitedFeature.FeatureFlags)) {\r\n            console.warn('[FEATURE FLAGS] Feature flags quota limit exceeded - feature flags unavailable. Learn more about billing limits at https://posthog.com/docs/billing/limits-alerts');\r\n            return {\r\n                flags: {},\r\n                featureFlags: {},\r\n                featureFlagPayloads: {},\r\n                requestId: decideResponse?.requestId,\r\n            };\r\n        }\r\n        return decideResponse;\r\n    }\r\n    /***\r\n     *** SURVEYS\r\n     ***/\r\n    async getSurveysStateless() {\r\n        await this._initPromise;\r\n        if (this.disableSurveys === true) {\r\n            this.logMsgIfDebug(() => console.log('PostHog Debug', 'Loading surveys is disabled.'));\r\n            return [];\r\n        }\r\n        const url = `${this.host}/api/surveys/?token=${this.apiKey}`;\r\n        const fetchOptions = {\r\n            method: 'GET',\r\n            headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\r\n        };\r\n        const response = await this.fetchWithRetry(url, fetchOptions)\r\n            .then((response) => {\r\n            if (response.status !== 200 || !response.json) {\r\n                const msg = `Surveys API could not be loaded: ${response.status}`;\r\n                const error = new Error(msg);\r\n                this.logMsgIfDebug(() => console.error(error));\r\n                this._events.emit('error', new Error(msg));\r\n                return undefined;\r\n            }\r\n            return response.json();\r\n        })\r\n            .catch((error) => {\r\n            this.logMsgIfDebug(() => console.error('Surveys API could not be loaded', error));\r\n            this._events.emit('error', error);\r\n            return undefined;\r\n        });\r\n        const newSurveys = response?.surveys;\r\n        if (newSurveys) {\r\n            this.logMsgIfDebug(() => console.log('PostHog Debug', 'Surveys fetched from API: ', JSON.stringify(newSurveys)));\r\n        }\r\n        return newSurveys ?? [];\r\n    }\r\n    get props() {\r\n        if (!this._props) {\r\n            this._props = this.getPersistedProperty(PostHogPersistedProperty.Props);\r\n        }\r\n        return this._props || {};\r\n    }\r\n    set props(val) {\r\n        this._props = val;\r\n    }\r\n    async register(properties) {\r\n        this.wrap(() => {\r\n            this.props = {\r\n                ...this.props,\r\n                ...properties,\r\n            };\r\n            this.setPersistedProperty(PostHogPersistedProperty.Props, this.props);\r\n        });\r\n    }\r\n    async unregister(property) {\r\n        this.wrap(() => {\r\n            delete this.props[property];\r\n            this.setPersistedProperty(PostHogPersistedProperty.Props, this.props);\r\n        });\r\n    }\r\n    /***\r\n     *** QUEUEING AND FLUSHING\r\n     ***/\r\n    enqueue(type, _message, options) {\r\n        this.wrap(() => {\r\n            if (this.optedOut) {\r\n                this._events.emit(type, `Library is disabled. Not sending event. To re-enable, call posthog.optIn()`);\r\n                return;\r\n            }\r\n            const message = this.prepareMessage(type, _message, options);\r\n            const queue = this.getPersistedProperty(PostHogPersistedProperty.Queue) || [];\r\n            if (queue.length >= this.maxQueueSize) {\r\n                queue.shift();\r\n                this.logMsgIfDebug(() => console.info('Queue is full, the oldest event is dropped.'));\r\n            }\r\n            queue.push({ message });\r\n            this.setPersistedProperty(PostHogPersistedProperty.Queue, queue);\r\n            this._events.emit(type, message);\r\n            // Flush queued events if we meet the flushAt length\r\n            if (queue.length >= this.flushAt) {\r\n                this.flushBackground();\r\n            }\r\n            if (this.flushInterval && !this._flushTimer) {\r\n                this._flushTimer = safeSetTimeout(() => this.flushBackground(), this.flushInterval);\r\n            }\r\n        });\r\n    }\r\n    async sendImmediate(type, _message, options) {\r\n        if (this.disabled) {\r\n            this.logMsgIfDebug(() => console.warn('[PostHog] The client is disabled'));\r\n            return;\r\n        }\r\n        if (!this._isInitialized) {\r\n            await this._initPromise;\r\n        }\r\n        if (this.optedOut) {\r\n            this._events.emit(type, `Library is disabled. Not sending event. To re-enable, call posthog.optIn()`);\r\n            return;\r\n        }\r\n        const data = {\r\n            api_key: this.apiKey,\r\n            batch: [this.prepareMessage(type, _message, options)],\r\n            sent_at: currentISOTime(),\r\n        };\r\n        if (this.historicalMigration) {\r\n            data.historical_migration = true;\r\n        }\r\n        const payload = JSON.stringify(data);\r\n        const url = this.captureMode === 'form'\r\n            ? `${this.host}/e/?ip=1&_=${currentTimestamp()}&v=${this.getLibraryVersion()}`\r\n            : `${this.host}/batch/`;\r\n        const fetchOptions = this.captureMode === 'form'\r\n            ? {\r\n                method: 'POST',\r\n                mode: 'no-cors',\r\n                credentials: 'omit',\r\n                headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/x-www-form-urlencoded' },\r\n                body: `data=${encodeURIComponent(LZString.compressToBase64(payload))}&compression=lz64`,\r\n            }\r\n            : {\r\n                method: 'POST',\r\n                headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\r\n                body: payload,\r\n            };\r\n        try {\r\n            await this.fetchWithRetry(url, fetchOptions);\r\n        }\r\n        catch (err) {\r\n            this._events.emit('error', err);\r\n        }\r\n    }\r\n    prepareMessage(type, _message, options) {\r\n        const message = {\r\n            ..._message,\r\n            type: type,\r\n            library: this.getLibraryId(),\r\n            library_version: this.getLibraryVersion(),\r\n            timestamp: options?.timestamp ? options?.timestamp : currentISOTime(),\r\n            uuid: options?.uuid ? options.uuid : uuidv7(),\r\n        };\r\n        const addGeoipDisableProperty = options?.disableGeoip ?? this.disableGeoip;\r\n        if (addGeoipDisableProperty) {\r\n            if (!message.properties) {\r\n                message.properties = {};\r\n            }\r\n            message['properties']['$geoip_disable'] = true;\r\n        }\r\n        if (message.distinctId) {\r\n            message.distinct_id = message.distinctId;\r\n            delete message.distinctId;\r\n        }\r\n        return message;\r\n    }\r\n    clearFlushTimer() {\r\n        if (this._flushTimer) {\r\n            clearTimeout(this._flushTimer);\r\n            this._flushTimer = undefined;\r\n        }\r\n    }\r\n    /**\r\n     * Helper for flushing the queue in the background\r\n     * Avoids unnecessary promise errors\r\n     */\r\n    flushBackground() {\r\n        void this.flush().catch(async (err) => {\r\n            await logFlushError(err);\r\n        });\r\n    }\r\n    /**\r\n     * Flushes the queue\r\n     *\r\n     * This function will return a promise that will resolve when the flush is complete,\r\n     * or reject if there was an error (for example if the server or network is down).\r\n     *\r\n     * If there is already a flush in progress, this function will wait for that flush to complete.\r\n     *\r\n     * It's recommended to do error handling in the callback of the promise.\r\n     *\r\n     * @example\r\n     * posthog.flush().then(() => {\r\n     *   console.log('Flush complete')\r\n     * }).catch((err) => {\r\n     *   console.error('Flush failed', err)\r\n     * })\r\n     *\r\n     *\r\n     * @throws PostHogFetchHttpError\r\n     * @throws PostHogFetchNetworkError\r\n     * @throws Error\r\n     */\r\n    async flush() {\r\n        // Wait for the current flush operation to finish (regardless of success or failure), then try to flush again.\r\n        // Use allSettled instead of finally to be defensive around flush throwing errors immediately rather than rejecting.\r\n        // Use a custom allSettled implementation to avoid issues with patching Promise on RN\r\n        const nextFlushPromise = allSettled([this.flushPromise]).then(() => {\r\n            return this._flush();\r\n        });\r\n        this.flushPromise = nextFlushPromise;\r\n        void this.addPendingPromise(nextFlushPromise);\r\n        allSettled([nextFlushPromise]).then(() => {\r\n            // If there are no others waiting to flush, clear the promise.\r\n            // We don't strictly need to do this, but it could make debugging easier\r\n            if (this.flushPromise === nextFlushPromise) {\r\n                this.flushPromise = null;\r\n            }\r\n        });\r\n        return nextFlushPromise;\r\n    }\r\n    getCustomHeaders() {\r\n        // Don't set the user agent if we're not on a browser. The latest spec allows\r\n        // the User-Agent header (see https://fetch.spec.whatwg.org/#terminology-headers\r\n        // and https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/setRequestHeader),\r\n        // but browsers such as Chrome and Safari have not caught up.\r\n        const customUserAgent = this.getCustomUserAgent();\r\n        const headers = {};\r\n        if (customUserAgent && customUserAgent !== '') {\r\n            headers['User-Agent'] = customUserAgent;\r\n        }\r\n        return headers;\r\n    }\r\n    async _flush() {\r\n        this.clearFlushTimer();\r\n        await this._initPromise;\r\n        let queue = this.getPersistedProperty(PostHogPersistedProperty.Queue) || [];\r\n        if (!queue.length) {\r\n            return;\r\n        }\r\n        const sentMessages = [];\r\n        const originalQueueLength = queue.length;\r\n        while (queue.length > 0 && sentMessages.length < originalQueueLength) {\r\n            const batchItems = queue.slice(0, this.maxBatchSize);\r\n            const batchMessages = batchItems.map((item) => item.message);\r\n            const persistQueueChange = () => {\r\n                const refreshedQueue = this.getPersistedProperty(PostHogPersistedProperty.Queue) || [];\r\n                const newQueue = refreshedQueue.slice(batchItems.length);\r\n                this.setPersistedProperty(PostHogPersistedProperty.Queue, newQueue);\r\n                queue = newQueue;\r\n            };\r\n            const data = {\r\n                api_key: this.apiKey,\r\n                batch: batchMessages,\r\n                sent_at: currentISOTime(),\r\n            };\r\n            if (this.historicalMigration) {\r\n                data.historical_migration = true;\r\n            }\r\n            const payload = JSON.stringify(data);\r\n            const url = this.captureMode === 'form'\r\n                ? `${this.host}/e/?ip=1&_=${currentTimestamp()}&v=${this.getLibraryVersion()}`\r\n                : `${this.host}/batch/`;\r\n            const fetchOptions = this.captureMode === 'form'\r\n                ? {\r\n                    method: 'POST',\r\n                    mode: 'no-cors',\r\n                    credentials: 'omit',\r\n                    headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/x-www-form-urlencoded' },\r\n                    body: `data=${encodeURIComponent(LZString.compressToBase64(payload))}&compression=lz64`,\r\n                }\r\n                : {\r\n                    method: 'POST',\r\n                    headers: { ...this.getCustomHeaders(), 'Content-Type': 'application/json' },\r\n                    body: payload,\r\n                };\r\n            const retryOptions = {\r\n                retryCheck: (err) => {\r\n                    // don't automatically retry on 413 errors, we want to reduce the batch size first\r\n                    if (isPostHogFetchContentTooLargeError(err)) {\r\n                        return false;\r\n                    }\r\n                    // otherwise, retry on network errors\r\n                    return isPostHogFetchError(err);\r\n                },\r\n            };\r\n            try {\r\n                await this.fetchWithRetry(url, fetchOptions, retryOptions);\r\n            }\r\n            catch (err) {\r\n                if (isPostHogFetchContentTooLargeError(err) && batchMessages.length > 1) {\r\n                    // if we get a 413 error, we want to reduce the batch size and try again\r\n                    this.maxBatchSize = Math.max(1, Math.floor(batchMessages.length / 2));\r\n                    this.logMsgIfDebug(() => console.warn(`Received 413 when sending batch of size ${batchMessages.length}, reducing batch size to ${this.maxBatchSize}`));\r\n                    // do not persist the queue change, we want to retry the same batch\r\n                    continue;\r\n                }\r\n                // depending on the error type, eg a malformed JSON or broken queue, it'll always return an error\r\n                // and this will be an endless loop, in this case, if the error isn't a network issue, we always remove the items from the queue\r\n                if (!(err instanceof PostHogFetchNetworkError)) {\r\n                    persistQueueChange();\r\n                }\r\n                this._events.emit('error', err);\r\n                throw err;\r\n            }\r\n            persistQueueChange();\r\n            sentMessages.push(...batchMessages);\r\n        }\r\n        this._events.emit('flush', sentMessages);\r\n    }\r\n    async fetchWithRetry(url, options, retryOptions, requestTimeout) {\r\n        var _a;\r\n        (_a = AbortSignal).timeout ?? (_a.timeout = function timeout(ms) {\r\n            const ctrl = new AbortController();\r\n            setTimeout(() => ctrl.abort(), ms);\r\n            return ctrl.signal;\r\n        });\r\n        const body = options.body ? options.body : '';\r\n        let reqByteLength = -1;\r\n        try {\r\n            reqByteLength = Buffer.byteLength(body, STRING_FORMAT);\r\n        }\r\n        catch {\r\n            const encoded = new TextEncoder().encode(body);\r\n            reqByteLength = encoded.length;\r\n        }\r\n        return await retriable(async () => {\r\n            let res = null;\r\n            try {\r\n                res = await this.fetch(url, {\r\n                    signal: AbortSignal.timeout(requestTimeout ?? this.requestTimeout),\r\n                    ...options,\r\n                });\r\n            }\r\n            catch (e) {\r\n                // fetch will only throw on network errors or on timeouts\r\n                throw new PostHogFetchNetworkError(e);\r\n            }\r\n            // If we're in no-cors mode, we can't access the response status\r\n            // We only throw on HTTP errors if we're not in no-cors mode\r\n            // https://developer.mozilla.org/en-US/docs/Web/API/Request/mode#no-cors\r\n            const isNoCors = options.mode === 'no-cors';\r\n            if (!isNoCors && (res.status < 200 || res.status >= 400)) {\r\n                throw new PostHogFetchHttpError(res, reqByteLength);\r\n            }\r\n            return res;\r\n        }, { ...this._retryOptions, ...retryOptions });\r\n    }\r\n    async _shutdown(shutdownTimeoutMs = 30000) {\r\n        // A little tricky - we want to have a max shutdown time and enforce it, even if that means we have some\r\n        // dangling promises. We'll keep track of the timeout and resolve/reject based on that.\r\n        await this._initPromise;\r\n        let hasTimedOut = false;\r\n        this.clearFlushTimer();\r\n        const doShutdown = async () => {\r\n            try {\r\n                await Promise.all(Object.values(this.pendingPromises));\r\n                while (true) {\r\n                    const queue = this.getPersistedProperty(PostHogPersistedProperty.Queue) || [];\r\n                    if (queue.length === 0) {\r\n                        break;\r\n                    }\r\n                    // flush again to make sure we send all events, some of which might've been added\r\n                    // while we were waiting for the pending promises to resolve\r\n                    // For example, see sendFeatureFlags in posthog-node/src/posthog-node.ts::capture\r\n                    await this.flush();\r\n                    if (hasTimedOut) {\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n            catch (e) {\r\n                if (!isPostHogFetchError(e)) {\r\n                    throw e;\r\n                }\r\n                await logFlushError(e);\r\n            }\r\n        };\r\n        return Promise.race([\r\n            new Promise((_, reject) => {\r\n                safeSetTimeout(() => {\r\n                    this.logMsgIfDebug(() => console.error('Timed out while shutting down PostHog'));\r\n                    hasTimedOut = true;\r\n                    reject('Timeout while shutting down PostHog. Some events may not have been sent.');\r\n                }, shutdownTimeoutMs);\r\n            }),\r\n            doShutdown(),\r\n        ]);\r\n    }\r\n    /**\r\n     *  Call shutdown() once before the node process exits, so ensure that all events have been sent and all promises\r\n     *  have resolved. Do not use this function if you intend to keep using this PostHog instance after calling it.\r\n     * @param shutdownTimeoutMs\r\n     */\r\n    async shutdown(shutdownTimeoutMs = 30000) {\r\n        if (this.shutdownPromise) {\r\n            this.logMsgIfDebug(() => console.warn('shutdown() called while already shutting down. shutdown() is meant to be called once before process exit - use flush() for per-request cleanup'));\r\n        }\r\n        else {\r\n            this.shutdownPromise = this._shutdown(shutdownTimeoutMs).finally(() => {\r\n                this.shutdownPromise = null;\r\n            });\r\n        }\r\n        return this.shutdownPromise;\r\n    }\r\n}\n\n/**\r\n * Fetch wrapper\r\n *\r\n * We want to polyfill fetch when not available with axios but use it when it is.\r\n * NOTE: The current version of Axios has an issue when in non-node environments like Clouflare Workers.\r\n * This is currently solved by using the global fetch if available instead.\r\n * See https://github.com/PostHog/posthog-js-lite/issues/127 for more info\r\n */\nlet _fetch = getFetch();\nif (!_fetch) {\n  // eslint-disable-next-line @typescript-eslint/no-var-requires\n  const axios = require('axios');\n  _fetch = async (url, options) => {\n    const res = await axios.request({\n      url,\n      headers: options.headers,\n      method: options.method.toLowerCase(),\n      data: options.body,\n      signal: options.signal,\n      // fetch only throws on network errors, not on HTTP errors\n      validateStatus: () => true\n    });\n    return {\n      status: res.status,\n      text: async () => res.data,\n      json: async () => res.data\n    };\n  };\n}\n// NOTE: We have to export this as default, even though we prefer named exports as we are relying on detecting \"fetch\" in the global scope\nvar fetch$1 = _fetch;\n\n/**\r\n * A lazy value that is only computed when needed. Inspired by C#'s Lazy<T> class.\r\n */\nclass Lazy {\n  constructor(factory) {\n    this.factory = factory;\n  }\n  /**\r\n   * Gets the value, initializing it if necessary.\r\n   * Multiple concurrent calls will share the same initialization promise.\r\n   */\n  async getValue() {\n    if (this.value !== undefined) {\n      return this.value;\n    }\n    if (this.initializationPromise === undefined) {\n      this.initializationPromise = (async () => {\n        try {\n          const result = await this.factory();\n          this.value = result;\n          return result;\n        } finally {\n          // Clear the promise so we can retry if needed\n          this.initializationPromise = undefined;\n        }\n      })();\n    }\n    return this.initializationPromise;\n  }\n  /**\r\n   * Returns true if the value has been initialized.\r\n   */\n  isInitialized() {\n    return this.value !== undefined;\n  }\n  /**\r\n   * Returns a promise that resolves when the value is initialized.\r\n   * If already initialized, resolves immediately.\r\n   */\n  async waitForInitialization() {\n    if (this.isInitialized()) {\n      return;\n    }\n    await this.getValue();\n  }\n}\n\n/// <reference lib=\"dom\" />\nconst nodeCrypto = new Lazy(async () => {\n  try {\n    return await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! crypto */ \"crypto\", 19));\n  } catch {\n    return undefined;\n  }\n});\nasync function getNodeCrypto() {\n  return await nodeCrypto.getValue();\n}\nconst webCrypto = new Lazy(async () => {\n  if (typeof globalThis.crypto?.subtle !== 'undefined') {\n    return globalThis.crypto.subtle;\n  }\n  try {\n    // Node.js: use built-in webcrypto and assign it if needed\n    const crypto = await nodeCrypto.getValue();\n    if (crypto?.webcrypto?.subtle) {\n      return crypto.webcrypto.subtle;\n    }\n  } catch {\n    // Ignore if not available\n  }\n  return undefined;\n});\nasync function getWebCrypto() {\n  return await webCrypto.getValue();\n}\n\n/// <reference lib=\"dom\" />\nasync function hashSHA1(text) {\n  // Try Node.js crypto first\n  const nodeCrypto = await getNodeCrypto();\n  if (nodeCrypto) {\n    return nodeCrypto.createHash('sha1').update(text).digest('hex');\n  }\n  const webCrypto = await getWebCrypto();\n  // Fall back to Web Crypto API\n  if (webCrypto) {\n    const hashBuffer = await webCrypto.digest('SHA-1', new TextEncoder().encode(text));\n    const hashArray = Array.from(new Uint8Array(hashBuffer));\n    return hashArray.map(byte => byte.toString(16).padStart(2, '0')).join('');\n  }\n  throw new Error('No crypto implementation available. Tried Node Crypto API and Web SubtleCrypto API');\n}\n\nconst SIXTY_SECONDS = 60 * 1000;\n// eslint-disable-next-line\nconst LONG_SCALE = 0xfffffffffffffff;\nconst NULL_VALUES_ALLOWED_OPERATORS = ['is_not'];\nclass ClientError extends Error {\n  constructor(message) {\n    super();\n    Error.captureStackTrace(this, this.constructor);\n    this.name = 'ClientError';\n    this.message = message;\n    Object.setPrototypeOf(this, ClientError.prototype);\n  }\n}\nclass InconclusiveMatchError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = this.constructor.name;\n    Error.captureStackTrace(this, this.constructor);\n    // instanceof doesn't work in ES3 or ES5\n    // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/\n    // this is the workaround\n    Object.setPrototypeOf(this, InconclusiveMatchError.prototype);\n  }\n}\nclass FeatureFlagsPoller {\n  constructor({\n    pollingInterval,\n    personalApiKey,\n    projectApiKey,\n    timeout,\n    host,\n    customHeaders,\n    ...options\n  }) {\n    this.debugMode = false;\n    this.shouldBeginExponentialBackoff = false;\n    this.backOffCount = 0;\n    this.pollingInterval = pollingInterval;\n    this.personalApiKey = personalApiKey;\n    this.featureFlags = [];\n    this.featureFlagsByKey = {};\n    this.groupTypeMapping = {};\n    this.cohorts = {};\n    this.loadedSuccessfullyOnce = false;\n    this.timeout = timeout;\n    this.projectApiKey = projectApiKey;\n    this.host = host;\n    this.poller = undefined;\n    this.fetch = options.fetch || fetch$1;\n    this.onError = options.onError;\n    this.customHeaders = customHeaders;\n    this.onLoad = options.onLoad;\n    void this.loadFeatureFlags();\n  }\n  debug(enabled = true) {\n    this.debugMode = enabled;\n  }\n  logMsgIfDebug(fn) {\n    if (this.debugMode) {\n      fn();\n    }\n  }\n  async getFeatureFlag(key, distinctId, groups = {}, personProperties = {}, groupProperties = {}) {\n    await this.loadFeatureFlags();\n    let response = undefined;\n    let featureFlag = undefined;\n    if (!this.loadedSuccessfullyOnce) {\n      return response;\n    }\n    for (const flag of this.featureFlags) {\n      if (key === flag.key) {\n        featureFlag = flag;\n        break;\n      }\n    }\n    if (featureFlag !== undefined) {\n      try {\n        response = await this.computeFlagLocally(featureFlag, distinctId, groups, personProperties, groupProperties);\n        this.logMsgIfDebug(() => console.debug(`Successfully computed flag locally: ${key} -> ${response}`));\n      } catch (e) {\n        if (e instanceof InconclusiveMatchError) {\n          this.logMsgIfDebug(() => console.debug(`InconclusiveMatchError when computing flag locally: ${key}: ${e}`));\n        } else if (e instanceof Error) {\n          this.onError?.(new Error(`Error computing flag locally: ${key}: ${e}`));\n        }\n      }\n    }\n    return response;\n  }\n  async computeFeatureFlagPayloadLocally(key, matchValue) {\n    await this.loadFeatureFlags();\n    let response = undefined;\n    if (!this.loadedSuccessfullyOnce) {\n      return undefined;\n    }\n    if (typeof matchValue == 'boolean') {\n      response = this.featureFlagsByKey?.[key]?.filters?.payloads?.[matchValue.toString()];\n    } else if (typeof matchValue == 'string') {\n      response = this.featureFlagsByKey?.[key]?.filters?.payloads?.[matchValue];\n    }\n    // Undefined means a loading or missing data issue. Null means evaluation happened and there was no match\n    if (response === undefined || response === null) {\n      return null;\n    }\n    try {\n      return JSON.parse(response);\n    } catch {\n      return response;\n    }\n  }\n  async getAllFlagsAndPayloads(distinctId, groups = {}, personProperties = {}, groupProperties = {}) {\n    await this.loadFeatureFlags();\n    const response = {};\n    const payloads = {};\n    let fallbackToDecide = this.featureFlags.length == 0;\n    await Promise.all(this.featureFlags.map(async flag => {\n      try {\n        const matchValue = await this.computeFlagLocally(flag, distinctId, groups, personProperties, groupProperties);\n        response[flag.key] = matchValue;\n        const matchPayload = await this.computeFeatureFlagPayloadLocally(flag.key, matchValue);\n        if (matchPayload) {\n          payloads[flag.key] = matchPayload;\n        }\n      } catch (e) {\n        if (e instanceof InconclusiveMatchError) ; else if (e instanceof Error) {\n          this.onError?.(new Error(`Error computing flag locally: ${flag.key}: ${e}`));\n        }\n        fallbackToDecide = true;\n      }\n    }));\n    return {\n      response,\n      payloads,\n      fallbackToDecide\n    };\n  }\n  async computeFlagLocally(flag, distinctId, groups = {}, personProperties = {}, groupProperties = {}) {\n    if (flag.ensure_experience_continuity) {\n      throw new InconclusiveMatchError('Flag has experience continuity enabled');\n    }\n    if (!flag.active) {\n      return false;\n    }\n    const flagFilters = flag.filters || {};\n    const aggregation_group_type_index = flagFilters.aggregation_group_type_index;\n    if (aggregation_group_type_index != undefined) {\n      const groupName = this.groupTypeMapping[String(aggregation_group_type_index)];\n      if (!groupName) {\n        this.logMsgIfDebug(() => console.warn(`[FEATURE FLAGS] Unknown group type index ${aggregation_group_type_index} for feature flag ${flag.key}`));\n        throw new InconclusiveMatchError('Flag has unknown group type index');\n      }\n      if (!(groupName in groups)) {\n        this.logMsgIfDebug(() => console.warn(`[FEATURE FLAGS] Can't compute group feature flag: ${flag.key} without group names passed in`));\n        return false;\n      }\n      const focusedGroupProperties = groupProperties[groupName];\n      return await this.matchFeatureFlagProperties(flag, groups[groupName], focusedGroupProperties);\n    } else {\n      return await this.matchFeatureFlagProperties(flag, distinctId, personProperties);\n    }\n  }\n  async matchFeatureFlagProperties(flag, distinctId, properties) {\n    const flagFilters = flag.filters || {};\n    const flagConditions = flagFilters.groups || [];\n    let isInconclusive = false;\n    let result = undefined;\n    // # Stable sort conditions with variant overrides to the top. This ensures that if overrides are present, they are\n    // # evaluated first, and the variant override is applied to the first matching condition.\n    const sortedFlagConditions = [...flagConditions].sort((conditionA, conditionB) => {\n      const AHasVariantOverride = !!conditionA.variant;\n      const BHasVariantOverride = !!conditionB.variant;\n      if (AHasVariantOverride && BHasVariantOverride) {\n        return 0;\n      } else if (AHasVariantOverride) {\n        return -1;\n      } else if (BHasVariantOverride) {\n        return 1;\n      } else {\n        return 0;\n      }\n    });\n    for (const condition of sortedFlagConditions) {\n      try {\n        if (await this.isConditionMatch(flag, distinctId, condition, properties)) {\n          const variantOverride = condition.variant;\n          const flagVariants = flagFilters.multivariate?.variants || [];\n          if (variantOverride && flagVariants.some(variant => variant.key === variantOverride)) {\n            result = variantOverride;\n          } else {\n            result = (await this.getMatchingVariant(flag, distinctId)) || true;\n          }\n          break;\n        }\n      } catch (e) {\n        if (e instanceof InconclusiveMatchError) {\n          isInconclusive = true;\n        } else {\n          throw e;\n        }\n      }\n    }\n    if (result !== undefined) {\n      return result;\n    } else if (isInconclusive) {\n      throw new InconclusiveMatchError(\"Can't determine if feature flag is enabled or not with given properties\");\n    }\n    // We can only return False when all conditions are False\n    return false;\n  }\n  async isConditionMatch(flag, distinctId, condition, properties) {\n    const rolloutPercentage = condition.rollout_percentage;\n    const warnFunction = msg => {\n      this.logMsgIfDebug(() => console.warn(msg));\n    };\n    if ((condition.properties || []).length > 0) {\n      for (const prop of condition.properties) {\n        const propertyType = prop.type;\n        let matches = false;\n        if (propertyType === 'cohort') {\n          matches = matchCohort(prop, properties, this.cohorts, this.debugMode);\n        } else {\n          matches = matchProperty(prop, properties, warnFunction);\n        }\n        if (!matches) {\n          return false;\n        }\n      }\n      if (rolloutPercentage == undefined) {\n        return true;\n      }\n    }\n    if (rolloutPercentage != undefined && (await _hash(flag.key, distinctId)) > rolloutPercentage / 100.0) {\n      return false;\n    }\n    return true;\n  }\n  async getMatchingVariant(flag, distinctId) {\n    const hashValue = await _hash(flag.key, distinctId, 'variant');\n    const matchingVariant = this.variantLookupTable(flag).find(variant => {\n      return hashValue >= variant.valueMin && hashValue < variant.valueMax;\n    });\n    if (matchingVariant) {\n      return matchingVariant.key;\n    }\n    return undefined;\n  }\n  variantLookupTable(flag) {\n    const lookupTable = [];\n    let valueMin = 0;\n    let valueMax = 0;\n    const flagFilters = flag.filters || {};\n    const multivariates = flagFilters.multivariate?.variants || [];\n    multivariates.forEach(variant => {\n      valueMax = valueMin + variant.rollout_percentage / 100.0;\n      lookupTable.push({\n        valueMin,\n        valueMax,\n        key: variant.key\n      });\n      valueMin = valueMax;\n    });\n    return lookupTable;\n  }\n  async loadFeatureFlags(forceReload = false) {\n    if (!this.loadedSuccessfullyOnce || forceReload) {\n      await this._loadFeatureFlags();\n    }\n  }\n  /**\r\n   * Returns true if the feature flags poller has loaded successfully at least once and has more than 0 feature flags.\r\n   * This is useful to check if local evaluation is ready before calling getFeatureFlag.\r\n   */\n  isLocalEvaluationReady() {\n    return (this.loadedSuccessfullyOnce ?? false) && (this.featureFlags?.length ?? 0) > 0;\n  }\n  /**\r\n   * If a client is misconfigured with an invalid or improper API key, the polling interval is doubled each time\r\n   * until a successful request is made, up to a maximum of 60 seconds.\r\n   *\r\n   * @returns The polling interval to use for the next request.\r\n   */\n  getPollingInterval() {\n    if (!this.shouldBeginExponentialBackoff) {\n      return this.pollingInterval;\n    }\n    return Math.min(SIXTY_SECONDS, this.pollingInterval * 2 ** this.backOffCount);\n  }\n  async _loadFeatureFlags() {\n    if (this.poller) {\n      clearTimeout(this.poller);\n      this.poller = undefined;\n    }\n    this.poller = setTimeout(() => this._loadFeatureFlags(), this.getPollingInterval());\n    try {\n      const res = await this._requestFeatureFlagDefinitions();\n      // Handle undefined res case, this shouldn't happen, but it doesn't hurt to handle it anyway\n      if (!res) {\n        // Don't override existing flags when something goes wrong\n        return;\n      }\n      // NB ON ERROR HANDLING & `loadedSuccessfullyOnce`:\n      //\n      // `loadedSuccessfullyOnce` indicates we've successfully loaded a valid set of flags at least once.\n      // If we set it to `true` in an error scenario (e.g. 402 Over Quota, 401 Invalid Key, etc.),\n      // any manual call to `loadFeatureFlags()` (without forceReload) will skip refetching entirely,\n      // leaving us stuck with zero or outdated flags. The poller does keep running, but we also want\n      // manual reloads to be possible as soon as the error condition is resolved.\n      //\n      // Therefore, on error statuses, we do *not* set `loadedSuccessfullyOnce = true`, ensuring that\n      // both the background poller and any subsequent manual calls can keep trying to load flags\n      // once the issue (quota, permission, rate limit, etc.) is resolved.\n      switch (res.status) {\n        case 401:\n          // Invalid API key\n          this.shouldBeginExponentialBackoff = true;\n          this.backOffCount += 1;\n          throw new ClientError(`Your project key or personal API key is invalid. Setting next polling interval to ${this.getPollingInterval()}ms. More information: https://posthog.com/docs/api#rate-limiting`);\n        case 402:\n          // Quota exceeded - clear all flags\n          console.warn('[FEATURE FLAGS] Feature flags quota limit exceeded - unsetting all local flags. Learn more about billing limits at https://posthog.com/docs/billing/limits-alerts');\n          this.featureFlags = [];\n          this.featureFlagsByKey = {};\n          this.groupTypeMapping = {};\n          this.cohorts = {};\n          return;\n        case 403:\n          // Permissions issue\n          this.shouldBeginExponentialBackoff = true;\n          this.backOffCount += 1;\n          throw new ClientError(`Your personal API key does not have permission to fetch feature flag definitions for local evaluation. Setting next polling interval to ${this.getPollingInterval()}ms. Are you sure you're using the correct personal and Project API key pair? More information: https://posthog.com/docs/api/overview`);\n        case 429:\n          // Rate limited\n          this.shouldBeginExponentialBackoff = true;\n          this.backOffCount += 1;\n          throw new ClientError(`You are being rate limited. Setting next polling interval to ${this.getPollingInterval()}ms. More information: https://posthog.com/docs/api#rate-limiting`);\n        case 200:\n          {\n            // Process successful response\n            const responseJson = (await res.json()) ?? {};\n            if (!('flags' in responseJson)) {\n              this.onError?.(new Error(`Invalid response when getting feature flags: ${JSON.stringify(responseJson)}`));\n              return;\n            }\n            this.featureFlags = responseJson.flags ?? [];\n            this.featureFlagsByKey = this.featureFlags.reduce((acc, curr) => (acc[curr.key] = curr, acc), {});\n            this.groupTypeMapping = responseJson.group_type_mapping || {};\n            this.cohorts = responseJson.cohorts || {};\n            this.loadedSuccessfullyOnce = true;\n            this.shouldBeginExponentialBackoff = false;\n            this.backOffCount = 0;\n            this.onLoad?.(this.featureFlags.length);\n            break;\n          }\n        default:\n          // Something else went wrong, or the server is down.\n          // In this case, don't override existing flags\n          return;\n      }\n    } catch (err) {\n      if (err instanceof ClientError) {\n        this.onError?.(err);\n      }\n    }\n  }\n  getPersonalApiKeyRequestOptions(method = 'GET') {\n    return {\n      method,\n      headers: {\n        ...this.customHeaders,\n        'Content-Type': 'application/json',\n        Authorization: `Bearer ${this.personalApiKey}`\n      }\n    };\n  }\n  async _requestFeatureFlagDefinitions() {\n    const url = `${this.host}/api/feature_flag/local_evaluation?token=${this.projectApiKey}&send_cohorts`;\n    const options = this.getPersonalApiKeyRequestOptions();\n    let abortTimeout = null;\n    if (this.timeout && typeof this.timeout === 'number') {\n      const controller = new AbortController();\n      abortTimeout = safeSetTimeout(() => {\n        controller.abort();\n      }, this.timeout);\n      options.signal = controller.signal;\n    }\n    try {\n      return await this.fetch(url, options);\n    } finally {\n      clearTimeout(abortTimeout);\n    }\n  }\n  stopPoller() {\n    clearTimeout(this.poller);\n  }\n  _requestRemoteConfigPayload(flagKey) {\n    const url = `${this.host}/api/projects/@current/feature_flags/${flagKey}/remote_config/`;\n    const options = this.getPersonalApiKeyRequestOptions();\n    let abortTimeout = null;\n    if (this.timeout && typeof this.timeout === 'number') {\n      const controller = new AbortController();\n      abortTimeout = safeSetTimeout(() => {\n        controller.abort();\n      }, this.timeout);\n      options.signal = controller.signal;\n    }\n    try {\n      return this.fetch(url, options);\n    } finally {\n      clearTimeout(abortTimeout);\n    }\n  }\n}\n// # This function takes a distinct_id and a feature flag key and returns a float between 0 and 1.\n// # Given the same distinct_id and key, it'll always return the same float. These floats are\n// # uniformly distributed between 0 and 1, so if we want to show this feature to 20% of traffic\n// # we can do _hash(key, distinct_id) < 0.2\nasync function _hash(key, distinctId, salt = '') {\n  const hashString = await hashSHA1(`${key}.${distinctId}${salt}`);\n  return parseInt(hashString.slice(0, 15), 16) / LONG_SCALE;\n}\nfunction matchProperty(property, propertyValues, warnFunction) {\n  const key = property.key;\n  const value = property.value;\n  const operator = property.operator || 'exact';\n  if (!(key in propertyValues)) {\n    throw new InconclusiveMatchError(`Property ${key} not found in propertyValues`);\n  } else if (operator === 'is_not_set') {\n    throw new InconclusiveMatchError(`Operator is_not_set is not supported`);\n  }\n  const overrideValue = propertyValues[key];\n  if (overrideValue == null && !NULL_VALUES_ALLOWED_OPERATORS.includes(operator)) {\n    // if the value is null, just fail the feature flag comparison\n    // this isn't an InconclusiveMatchError because the property value was provided.\n    if (warnFunction) {\n      warnFunction(`Property ${key} cannot have a value of null/undefined with the ${operator} operator`);\n    }\n    return false;\n  }\n  function computeExactMatch(value, overrideValue) {\n    if (Array.isArray(value)) {\n      return value.map(val => String(val).toLowerCase()).includes(String(overrideValue).toLowerCase());\n    }\n    return String(value).toLowerCase() === String(overrideValue).toLowerCase();\n  }\n  function compare(lhs, rhs, operator) {\n    if (operator === 'gt') {\n      return lhs > rhs;\n    } else if (operator === 'gte') {\n      return lhs >= rhs;\n    } else if (operator === 'lt') {\n      return lhs < rhs;\n    } else if (operator === 'lte') {\n      return lhs <= rhs;\n    } else {\n      throw new Error(`Invalid operator: ${operator}`);\n    }\n  }\n  switch (operator) {\n    case 'exact':\n      return computeExactMatch(value, overrideValue);\n    case 'is_not':\n      return !computeExactMatch(value, overrideValue);\n    case 'is_set':\n      return key in propertyValues;\n    case 'icontains':\n      return String(overrideValue).toLowerCase().includes(String(value).toLowerCase());\n    case 'not_icontains':\n      return !String(overrideValue).toLowerCase().includes(String(value).toLowerCase());\n    case 'regex':\n      return isValidRegex(String(value)) && String(overrideValue).match(String(value)) !== null;\n    case 'not_regex':\n      return isValidRegex(String(value)) && String(overrideValue).match(String(value)) === null;\n    case 'gt':\n    case 'gte':\n    case 'lt':\n    case 'lte':\n      {\n        // :TRICKY: We adjust comparison based on the override value passed in,\n        // to make sure we handle both numeric and string comparisons appropriately.\n        let parsedValue = typeof value === 'number' ? value : null;\n        if (typeof value === 'string') {\n          try {\n            parsedValue = parseFloat(value);\n          } catch (err) {\n            // pass\n          }\n        }\n        if (parsedValue != null && overrideValue != null) {\n          // check both null and undefined\n          if (typeof overrideValue === 'string') {\n            return compare(overrideValue, String(value), operator);\n          } else {\n            return compare(overrideValue, parsedValue, operator);\n          }\n        } else {\n          return compare(String(overrideValue), String(value), operator);\n        }\n      }\n    case 'is_date_after':\n    case 'is_date_before':\n      {\n        let parsedDate = relativeDateParseForFeatureFlagMatching(String(value));\n        if (parsedDate == null) {\n          parsedDate = convertToDateTime(value);\n        }\n        if (parsedDate == null) {\n          throw new InconclusiveMatchError(`Invalid date: ${value}`);\n        }\n        const overrideDate = convertToDateTime(overrideValue);\n        if (['is_date_before'].includes(operator)) {\n          return overrideDate < parsedDate;\n        }\n        return overrideDate > parsedDate;\n      }\n    default:\n      throw new InconclusiveMatchError(`Unknown operator: ${operator}`);\n  }\n}\nfunction matchCohort(property, propertyValues, cohortProperties, debugMode = false) {\n  const cohortId = String(property.value);\n  if (!(cohortId in cohortProperties)) {\n    throw new InconclusiveMatchError(\"can't match cohort without a given cohort property value\");\n  }\n  const propertyGroup = cohortProperties[cohortId];\n  return matchPropertyGroup(propertyGroup, propertyValues, cohortProperties, debugMode);\n}\nfunction matchPropertyGroup(propertyGroup, propertyValues, cohortProperties, debugMode = false) {\n  if (!propertyGroup) {\n    return true;\n  }\n  const propertyGroupType = propertyGroup.type;\n  const properties = propertyGroup.values;\n  if (!properties || properties.length === 0) {\n    // empty groups are no-ops, always match\n    return true;\n  }\n  let errorMatchingLocally = false;\n  if ('values' in properties[0]) {\n    // a nested property group\n    for (const prop of properties) {\n      try {\n        const matches = matchPropertyGroup(prop, propertyValues, cohortProperties, debugMode);\n        if (propertyGroupType === 'AND') {\n          if (!matches) {\n            return false;\n          }\n        } else {\n          // OR group\n          if (matches) {\n            return true;\n          }\n        }\n      } catch (err) {\n        if (err instanceof InconclusiveMatchError) {\n          if (debugMode) {\n            console.debug(`Failed to compute property ${prop} locally: ${err}`);\n          }\n          errorMatchingLocally = true;\n        } else {\n          throw err;\n        }\n      }\n    }\n    if (errorMatchingLocally) {\n      throw new InconclusiveMatchError(\"Can't match cohort without a given cohort property value\");\n    }\n    // if we get here, all matched in AND case, or none matched in OR case\n    return propertyGroupType === 'AND';\n  } else {\n    for (const prop of properties) {\n      try {\n        let matches;\n        if (prop.type === 'cohort') {\n          matches = matchCohort(prop, propertyValues, cohortProperties, debugMode);\n        } else {\n          matches = matchProperty(prop, propertyValues);\n        }\n        const negation = prop.negation || false;\n        if (propertyGroupType === 'AND') {\n          // if negated property, do the inverse\n          if (!matches && !negation) {\n            return false;\n          }\n          if (matches && negation) {\n            return false;\n          }\n        } else {\n          // OR group\n          if (matches && !negation) {\n            return true;\n          }\n          if (!matches && negation) {\n            return true;\n          }\n        }\n      } catch (err) {\n        if (err instanceof InconclusiveMatchError) {\n          if (debugMode) {\n            console.debug(`Failed to compute property ${prop} locally: ${err}`);\n          }\n          errorMatchingLocally = true;\n        } else {\n          throw err;\n        }\n      }\n    }\n    if (errorMatchingLocally) {\n      throw new InconclusiveMatchError(\"can't match cohort without a given cohort property value\");\n    }\n    // if we get here, all matched in AND case, or none matched in OR case\n    return propertyGroupType === 'AND';\n  }\n}\nfunction isValidRegex(regex) {\n  try {\n    new RegExp(regex);\n    return true;\n  } catch (err) {\n    return false;\n  }\n}\nfunction convertToDateTime(value) {\n  if (value instanceof Date) {\n    return value;\n  } else if (typeof value === 'string' || typeof value === 'number') {\n    const date = new Date(value);\n    if (!isNaN(date.valueOf())) {\n      return date;\n    }\n    throw new InconclusiveMatchError(`${value} is in an invalid date format`);\n  } else {\n    throw new InconclusiveMatchError(`The date provided ${value} must be a string, number, or date object`);\n  }\n}\nfunction relativeDateParseForFeatureFlagMatching(value) {\n  const regex = /^-?(?<number>[0-9]+)(?<interval>[a-z])$/;\n  const match = value.match(regex);\n  const parsedDt = new Date(new Date().toISOString());\n  if (match) {\n    if (!match.groups) {\n      return null;\n    }\n    const number = parseInt(match.groups['number']);\n    if (number >= 10000) {\n      // Guard against overflow, disallow numbers greater than 10_000\n      return null;\n    }\n    const interval = match.groups['interval'];\n    if (interval == 'h') {\n      parsedDt.setUTCHours(parsedDt.getUTCHours() - number);\n    } else if (interval == 'd') {\n      parsedDt.setUTCDate(parsedDt.getUTCDate() - number);\n    } else if (interval == 'w') {\n      parsedDt.setUTCDate(parsedDt.getUTCDate() - number * 7);\n    } else if (interval == 'm') {\n      parsedDt.setUTCMonth(parsedDt.getUTCMonth() - number);\n    } else if (interval == 'y') {\n      parsedDt.setUTCFullYear(parsedDt.getUTCFullYear() - number);\n    } else {\n      return null;\n    }\n    return parsedDt;\n  } else {\n    return null;\n  }\n}\n\nclass PostHogMemoryStorage {\n  constructor() {\n    this._memoryStorage = {};\n  }\n  getProperty(key) {\n    return this._memoryStorage[key];\n  }\n  setProperty(key, value) {\n    this._memoryStorage[key] = value !== null ? value : undefined;\n  }\n}\n\n// Standard local evaluation rate limit is 600 per minute (10 per second),\n// so the fastest a poller should ever be set is 100ms.\nconst MINIMUM_POLLING_INTERVAL = 100;\nconst THIRTY_SECONDS = 30 * 1000;\nconst MAX_CACHE_SIZE = 50 * 1000;\n// The actual exported Nodejs API.\nclass PostHogBackendClient extends PostHogCoreStateless {\n  constructor(apiKey, options = {}) {\n    super(apiKey, options);\n    this._memoryStorage = new PostHogMemoryStorage();\n    this.options = options;\n    this.options.featureFlagsPollingInterval = typeof options.featureFlagsPollingInterval === 'number' ? Math.max(options.featureFlagsPollingInterval, MINIMUM_POLLING_INTERVAL) : THIRTY_SECONDS;\n    if (options.personalApiKey) {\n      if (options.personalApiKey.includes('phc_')) {\n        throw new Error('Your Personal API key is invalid. These keys are prefixed with \"phx_\" and can be created in PostHog project settings.');\n      }\n      this.featureFlagsPoller = new FeatureFlagsPoller({\n        pollingInterval: this.options.featureFlagsPollingInterval,\n        personalApiKey: options.personalApiKey,\n        projectApiKey: apiKey,\n        timeout: options.requestTimeout ?? 10000,\n        host: this.host,\n        fetch: options.fetch,\n        onError: err => {\n          this._events.emit('error', err);\n        },\n        onLoad: count => {\n          this._events.emit('localEvaluationFlagsLoaded', count);\n        },\n        customHeaders: this.getCustomHeaders()\n      });\n    }\n    this.errorTracking = new ErrorTracking(this, options);\n    this.distinctIdHasSentFlagCalls = {};\n    this.maxCacheSize = options.maxCacheSize || MAX_CACHE_SIZE;\n  }\n  getPersistedProperty(key) {\n    return this._memoryStorage.getProperty(key);\n  }\n  setPersistedProperty(key, value) {\n    return this._memoryStorage.setProperty(key, value);\n  }\n  fetch(url, options) {\n    return this.options.fetch ? this.options.fetch(url, options) : fetch$1(url, options);\n  }\n  getLibraryVersion() {\n    return version;\n  }\n  getCustomUserAgent() {\n    return `${this.getLibraryId()}/${this.getLibraryVersion()}`;\n  }\n  enable() {\n    return super.optIn();\n  }\n  disable() {\n    return super.optOut();\n  }\n  debug(enabled = true) {\n    super.debug(enabled);\n    this.featureFlagsPoller?.debug(enabled);\n  }\n  capture(props) {\n    if (typeof props === 'string') {\n      this.logMsgIfDebug(() => console.warn('Called capture() with a string as the first argument when an object was expected.'));\n    }\n    const {\n      distinctId,\n      event,\n      properties,\n      groups,\n      sendFeatureFlags,\n      timestamp,\n      disableGeoip,\n      uuid\n    } = props;\n    const _capture = props => {\n      super.captureStateless(distinctId, event, props, {\n        timestamp,\n        disableGeoip,\n        uuid\n      });\n    };\n    const _getFlags = async (distinctId, groups, disableGeoip) => {\n      return (await super.getFeatureFlagsStateless(distinctId, groups, undefined, undefined, disableGeoip)).flags;\n    };\n    // :TRICKY: If we flush, or need to shut down, to not lose events we want this promise to resolve before we flush\n    const capturePromise = Promise.resolve().then(async () => {\n      if (sendFeatureFlags) {\n        // If we are sending feature flags, we need to make sure we have the latest flags\n        // return await super.getFeatureFlagsStateless(distinctId, groups, undefined, undefined, disableGeoip)\n        return await _getFlags(distinctId, groups, disableGeoip);\n      }\n      if (event === '$feature_flag_called') {\n        // If we're capturing a $feature_flag_called event, we don't want to enrich the event with cached flags that may be out of date.\n        return {};\n      }\n      if ((this.featureFlagsPoller?.featureFlags?.length || 0) > 0) {\n        // Otherwise we may as well check for the flags locally and include them if they are already loaded\n        const groupsWithStringValues = {};\n        for (const [key, value] of Object.entries(groups || {})) {\n          groupsWithStringValues[key] = String(value);\n        }\n        return await this.getAllFlags(distinctId, {\n          groups: groupsWithStringValues,\n          disableGeoip,\n          onlyEvaluateLocally: true\n        });\n      }\n      return {};\n    }).then(flags => {\n      // Derive the relevant flag properties to add\n      const additionalProperties = {};\n      if (flags) {\n        for (const [feature, variant] of Object.entries(flags)) {\n          additionalProperties[`$feature/${feature}`] = variant;\n        }\n      }\n      const activeFlags = Object.keys(flags || {}).filter(flag => flags?.[flag] !== false).sort();\n      if (activeFlags.length > 0) {\n        additionalProperties['$active_feature_flags'] = activeFlags;\n      }\n      return additionalProperties;\n    }).catch(() => {\n      // Something went wrong getting the flag info - we should capture the event anyways\n      return {};\n    }).then(additionalProperties => {\n      // No matter what - capture the event\n      _capture({\n        ...additionalProperties,\n        ...properties,\n        $groups: groups\n      });\n    });\n    this.addPendingPromise(capturePromise);\n  }\n  async captureImmediate(props) {\n    if (typeof props === 'string') {\n      this.logMsgIfDebug(() => console.warn('Called capture() with a string as the first argument when an object was expected.'));\n    }\n    const {\n      distinctId,\n      event,\n      properties,\n      groups,\n      sendFeatureFlags,\n      timestamp,\n      disableGeoip,\n      uuid\n    } = props;\n    const _capture = props => {\n      return super.captureStatelessImmediate(distinctId, event, props, {\n        timestamp,\n        disableGeoip,\n        uuid\n      });\n    };\n    const _getFlags = async (distinctId, groups, disableGeoip) => {\n      return (await super.getFeatureFlagsStateless(distinctId, groups, undefined, undefined, disableGeoip)).flags;\n    };\n    const capturePromise = Promise.resolve().then(async () => {\n      if (sendFeatureFlags) {\n        // If we are sending feature flags, we need to make sure we have the latest flags\n        // return await super.getFeatureFlagsStateless(distinctId, groups, undefined, undefined, disableGeoip)\n        return await _getFlags(distinctId, groups, disableGeoip);\n      }\n      if (event === '$feature_flag_called') {\n        // If we're capturing a $feature_flag_called event, we don't want to enrich the event with cached flags that may be out of date.\n        return {};\n      }\n      if ((this.featureFlagsPoller?.featureFlags?.length || 0) > 0) {\n        // Otherwise we may as well check for the flags locally and include them if they are already loaded\n        const groupsWithStringValues = {};\n        for (const [key, value] of Object.entries(groups || {})) {\n          groupsWithStringValues[key] = String(value);\n        }\n        return await this.getAllFlags(distinctId, {\n          groups: groupsWithStringValues,\n          disableGeoip,\n          onlyEvaluateLocally: true\n        });\n      }\n      return {};\n    }).then(flags => {\n      // Derive the relevant flag properties to add\n      const additionalProperties = {};\n      if (flags) {\n        for (const [feature, variant] of Object.entries(flags)) {\n          additionalProperties[`$feature/${feature}`] = variant;\n        }\n      }\n      const activeFlags = Object.keys(flags || {}).filter(flag => flags?.[flag] !== false).sort();\n      if (activeFlags.length > 0) {\n        additionalProperties['$active_feature_flags'] = activeFlags;\n      }\n      return additionalProperties;\n    }).catch(() => {\n      // Something went wrong getting the flag info - we should capture the event anyways\n      return {};\n    }).then(additionalProperties => {\n      // No matter what - capture the event\n      _capture({\n        ...additionalProperties,\n        ...properties,\n        $groups: groups\n      });\n    });\n    await capturePromise;\n  }\n  identify({\n    distinctId,\n    properties,\n    disableGeoip\n  }) {\n    // Catch properties passed as $set and move them to the top level\n    // promote $set and $set_once to top level\n    const userPropsOnce = properties?.$set_once;\n    delete properties?.$set_once;\n    // if no $set is provided we assume all properties are $set\n    const userProps = properties?.$set || properties;\n    super.identifyStateless(distinctId, {\n      $set: userProps,\n      $set_once: userPropsOnce\n    }, {\n      disableGeoip\n    });\n  }\n  async identifyImmediate({\n    distinctId,\n    properties,\n    disableGeoip\n  }) {\n    // promote $set and $set_once to top level\n    const userPropsOnce = properties?.$set_once;\n    delete properties?.$set_once;\n    // if no $set is provided we assume all properties are $set\n    const userProps = properties?.$set || properties;\n    await super.identifyStatelessImmediate(distinctId, {\n      $set: userProps,\n      $set_once: userPropsOnce\n    }, {\n      disableGeoip\n    });\n  }\n  alias(data) {\n    super.aliasStateless(data.alias, data.distinctId, undefined, {\n      disableGeoip: data.disableGeoip\n    });\n  }\n  async aliasImmediate(data) {\n    await super.aliasStatelessImmediate(data.alias, data.distinctId, undefined, {\n      disableGeoip: data.disableGeoip\n    });\n  }\n  isLocalEvaluationReady() {\n    return this.featureFlagsPoller?.isLocalEvaluationReady() ?? false;\n  }\n  async waitForLocalEvaluationReady(timeoutMs = THIRTY_SECONDS) {\n    if (this.isLocalEvaluationReady()) {\n      return true;\n    }\n    if (this.featureFlagsPoller === undefined) {\n      return false;\n    }\n    return new Promise(resolve => {\n      const timeout = setTimeout(() => {\n        cleanup();\n        resolve(false);\n      }, timeoutMs);\n      const cleanup = this._events.on('localEvaluationFlagsLoaded', count => {\n        clearTimeout(timeout);\n        cleanup();\n        resolve(count > 0);\n      });\n    });\n  }\n  async getFeatureFlag(key, distinctId, options) {\n    const {\n      groups,\n      disableGeoip\n    } = options || {};\n    let {\n      onlyEvaluateLocally,\n      sendFeatureFlagEvents,\n      personProperties,\n      groupProperties\n    } = options || {};\n    const adjustedProperties = this.addLocalPersonAndGroupProperties(distinctId, groups, personProperties, groupProperties);\n    personProperties = adjustedProperties.allPersonProperties;\n    groupProperties = adjustedProperties.allGroupProperties;\n    // set defaults\n    if (onlyEvaluateLocally == undefined) {\n      onlyEvaluateLocally = false;\n    }\n    if (sendFeatureFlagEvents == undefined) {\n      sendFeatureFlagEvents = true;\n    }\n    let response = await this.featureFlagsPoller?.getFeatureFlag(key, distinctId, groups, personProperties, groupProperties);\n    const flagWasLocallyEvaluated = response !== undefined;\n    let requestId = undefined;\n    let flagDetail = undefined;\n    if (!flagWasLocallyEvaluated && !onlyEvaluateLocally) {\n      const remoteResponse = await super.getFeatureFlagDetailStateless(key, distinctId, groups, personProperties, groupProperties, disableGeoip);\n      if (remoteResponse === undefined) {\n        return undefined;\n      }\n      flagDetail = remoteResponse.response;\n      response = getFeatureFlagValue(flagDetail);\n      requestId = remoteResponse?.requestId;\n    }\n    const featureFlagReportedKey = `${key}_${response}`;\n    if (sendFeatureFlagEvents && (!(distinctId in this.distinctIdHasSentFlagCalls) || !this.distinctIdHasSentFlagCalls[distinctId].includes(featureFlagReportedKey))) {\n      if (Object.keys(this.distinctIdHasSentFlagCalls).length >= this.maxCacheSize) {\n        this.distinctIdHasSentFlagCalls = {};\n      }\n      if (Array.isArray(this.distinctIdHasSentFlagCalls[distinctId])) {\n        this.distinctIdHasSentFlagCalls[distinctId].push(featureFlagReportedKey);\n      } else {\n        this.distinctIdHasSentFlagCalls[distinctId] = [featureFlagReportedKey];\n      }\n      this.capture({\n        distinctId,\n        event: '$feature_flag_called',\n        properties: {\n          $feature_flag: key,\n          $feature_flag_response: response,\n          $feature_flag_id: flagDetail?.metadata?.id,\n          $feature_flag_version: flagDetail?.metadata?.version,\n          $feature_flag_reason: flagDetail?.reason?.description ?? flagDetail?.reason?.code,\n          locally_evaluated: flagWasLocallyEvaluated,\n          [`$feature/${key}`]: response,\n          $feature_flag_request_id: requestId\n        },\n        groups,\n        disableGeoip\n      });\n    }\n    return response;\n  }\n  async getFeatureFlagPayload(key, distinctId, matchValue, options) {\n    const {\n      groups,\n      disableGeoip\n    } = options || {};\n    let {\n      onlyEvaluateLocally,\n      sendFeatureFlagEvents,\n      personProperties,\n      groupProperties\n    } = options || {};\n    const adjustedProperties = this.addLocalPersonAndGroupProperties(distinctId, groups, personProperties, groupProperties);\n    personProperties = adjustedProperties.allPersonProperties;\n    groupProperties = adjustedProperties.allGroupProperties;\n    let response = undefined;\n    const localEvaluationEnabled = this.featureFlagsPoller !== undefined;\n    if (localEvaluationEnabled) {\n      // Try to get match value locally if not provided\n      if (!matchValue) {\n        matchValue = await this.getFeatureFlag(key, distinctId, {\n          ...options,\n          onlyEvaluateLocally: true,\n          sendFeatureFlagEvents: false\n        });\n      }\n      if (matchValue) {\n        response = await this.featureFlagsPoller?.computeFeatureFlagPayloadLocally(key, matchValue);\n      }\n    }\n    //}\n    // set defaults\n    if (onlyEvaluateLocally == undefined) {\n      onlyEvaluateLocally = false;\n    }\n    if (sendFeatureFlagEvents == undefined) {\n      sendFeatureFlagEvents = true;\n    }\n    // set defaults\n    if (onlyEvaluateLocally == undefined) {\n      onlyEvaluateLocally = false;\n    }\n    const payloadWasLocallyEvaluated = response !== undefined;\n    if (!payloadWasLocallyEvaluated && !onlyEvaluateLocally) {\n      response = await super.getFeatureFlagPayloadStateless(key, distinctId, groups, personProperties, groupProperties, disableGeoip);\n    }\n    return response;\n  }\n  async getRemoteConfigPayload(flagKey) {\n    return (await this.featureFlagsPoller?._requestRemoteConfigPayload(flagKey))?.json();\n  }\n  async isFeatureEnabled(key, distinctId, options) {\n    const feat = await this.getFeatureFlag(key, distinctId, options);\n    if (feat === undefined) {\n      return undefined;\n    }\n    return !!feat || false;\n  }\n  async getAllFlags(distinctId, options) {\n    const response = await this.getAllFlagsAndPayloads(distinctId, options);\n    return response.featureFlags || {};\n  }\n  async getAllFlagsAndPayloads(distinctId, options) {\n    const {\n      groups,\n      disableGeoip\n    } = options || {};\n    let {\n      onlyEvaluateLocally,\n      personProperties,\n      groupProperties\n    } = options || {};\n    const adjustedProperties = this.addLocalPersonAndGroupProperties(distinctId, groups, personProperties, groupProperties);\n    personProperties = adjustedProperties.allPersonProperties;\n    groupProperties = adjustedProperties.allGroupProperties;\n    // set defaults\n    if (onlyEvaluateLocally == undefined) {\n      onlyEvaluateLocally = false;\n    }\n    const localEvaluationResult = await this.featureFlagsPoller?.getAllFlagsAndPayloads(distinctId, groups, personProperties, groupProperties);\n    let featureFlags = {};\n    let featureFlagPayloads = {};\n    let fallbackToDecide = true;\n    if (localEvaluationResult) {\n      featureFlags = localEvaluationResult.response;\n      featureFlagPayloads = localEvaluationResult.payloads;\n      fallbackToDecide = localEvaluationResult.fallbackToDecide;\n    }\n    if (fallbackToDecide && !onlyEvaluateLocally) {\n      const remoteEvaluationResult = await super.getFeatureFlagsAndPayloadsStateless(distinctId, groups, personProperties, groupProperties, disableGeoip);\n      featureFlags = {\n        ...featureFlags,\n        ...(remoteEvaluationResult.flags || {})\n      };\n      featureFlagPayloads = {\n        ...featureFlagPayloads,\n        ...(remoteEvaluationResult.payloads || {})\n      };\n    }\n    return {\n      featureFlags,\n      featureFlagPayloads\n    };\n  }\n  groupIdentify({\n    groupType,\n    groupKey,\n    properties,\n    distinctId,\n    disableGeoip\n  }) {\n    super.groupIdentifyStateless(groupType, groupKey, properties, {\n      disableGeoip\n    }, distinctId);\n  }\n  /**\r\n   * Reloads the feature flag definitions from the server for local evaluation.\r\n   * This is useful to call if you want to ensure that the feature flags are up to date before calling getFeatureFlag.\r\n   */\n  async reloadFeatureFlags() {\n    await this.featureFlagsPoller?.loadFeatureFlags(true);\n  }\n  async _shutdown(shutdownTimeoutMs) {\n    this.featureFlagsPoller?.stopPoller();\n    return super._shutdown(shutdownTimeoutMs);\n  }\n  addLocalPersonAndGroupProperties(distinctId, groups, personProperties, groupProperties) {\n    const allPersonProperties = {\n      distinct_id: distinctId,\n      ...(personProperties || {})\n    };\n    const allGroupProperties = {};\n    if (groups) {\n      for (const groupName of Object.keys(groups)) {\n        allGroupProperties[groupName] = {\n          $group_key: groups[groupName],\n          ...(groupProperties?.[groupName] || {})\n        };\n      }\n    }\n    return {\n      allPersonProperties,\n      allGroupProperties\n    };\n  }\n  captureException(error, distinctId, additionalProperties) {\n    const syntheticException = new Error('PostHog syntheticException');\n    ErrorTracking.captureException(this, error, {\n      syntheticException\n    }, distinctId, additionalProperties);\n  }\n}\n\n// Portions of this file are derived from getsentry/sentry-javascript by Software, Inc. dba Sentry\n// Licensed under the MIT License\n// This was originally forked from https://github.com/csnover/TraceKit, and was largely\n// re-written as part of raven - js.\n//\n// This code was later copied to the JavaScript mono - repo and further modified and\n// refactored over the years.\n// Copyright (c) 2013 Onur <NAME_EMAIL> and all TraceKit contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy of this\n// software and associated documentation files(the 'Software'), to deal in the Software\n// without restriction, including without limitation the rights to use, copy, modify,\n// merge, publish, distribute, sublicense, and / or sell copies of the Software, and to\n// permit persons to whom the Software is furnished to do so, subject to the following\n// conditions:\n//\n// The above copyright notice and this permission notice shall be included in all copies\n// or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,\n// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A\n// PARTICULAR PURPOSE AND NONINFRINGEMENT.IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n// HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF\n// CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE\n// OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\nconst WEBPACK_ERROR_REGEXP = /\\(error: (.*)\\)/;\nconst STACKTRACE_FRAME_LIMIT = 50;\nconst UNKNOWN_FUNCTION = '?';\n/** Node Stack line parser */\nfunction node(getModule) {\n  const FILENAME_MATCH = /^\\s*[-]{4,}$/;\n  const FULL_MATCH = /at (?:async )?(?:(.+?)\\s+\\()?(?:(.+):(\\d+):(\\d+)?|([^)]+))\\)?/;\n  return line => {\n    const lineMatch = line.match(FULL_MATCH);\n    if (lineMatch) {\n      let object;\n      let method;\n      let functionName;\n      let typeName;\n      let methodName;\n      if (lineMatch[1]) {\n        functionName = lineMatch[1];\n        let methodStart = functionName.lastIndexOf('.');\n        if (functionName[methodStart - 1] === '.') {\n          methodStart--;\n        }\n        if (methodStart > 0) {\n          object = functionName.slice(0, methodStart);\n          method = functionName.slice(methodStart + 1);\n          const objectEnd = object.indexOf('.Module');\n          if (objectEnd > 0) {\n            functionName = functionName.slice(objectEnd + 1);\n            object = object.slice(0, objectEnd);\n          }\n        }\n        typeName = undefined;\n      }\n      if (method) {\n        typeName = object;\n        methodName = method;\n      }\n      if (method === '<anonymous>') {\n        methodName = undefined;\n        functionName = undefined;\n      }\n      if (functionName === undefined) {\n        methodName = methodName || UNKNOWN_FUNCTION;\n        functionName = typeName ? `${typeName}.${methodName}` : methodName;\n      }\n      let filename = lineMatch[2]?.startsWith('file://') ? lineMatch[2].slice(7) : lineMatch[2];\n      const isNative = lineMatch[5] === 'native';\n      // If it's a Windows path, trim the leading slash so that `/C:/foo` becomes `C:/foo`\n      if (filename?.match(/\\/[A-Z]:/)) {\n        filename = filename.slice(1);\n      }\n      if (!filename && lineMatch[5] && !isNative) {\n        filename = lineMatch[5];\n      }\n      return {\n        filename: filename ? decodeURI(filename) : undefined,\n        module: getModule ? getModule(filename) : undefined,\n        function: functionName,\n        lineno: _parseIntOrUndefined(lineMatch[3]),\n        colno: _parseIntOrUndefined(lineMatch[4]),\n        in_app: filenameIsInApp(filename || '', isNative),\n        platform: 'node:javascript'\n      };\n    }\n    if (line.match(FILENAME_MATCH)) {\n      return {\n        filename: line,\n        platform: 'node:javascript'\n      };\n    }\n    return undefined;\n  };\n}\n/**\r\n * Does this filename look like it's part of the app code?\r\n */\nfunction filenameIsInApp(filename, isNative = false) {\n  const isInternal = isNative || filename &&\n  // It's not internal if it's an absolute linux path\n  !filename.startsWith('/') &&\n  // It's not internal if it's an absolute windows path\n  !filename.match(/^[A-Z]:/) &&\n  // It's not internal if the path is starting with a dot\n  !filename.startsWith('.') &&\n  // It's not internal if the frame has a protocol. In node, this is usually the case if the file got pre-processed with a bundler like webpack\n  !filename.match(/^[a-zA-Z]([a-zA-Z0-9.\\-+])*:\\/\\//); // Schema from: https://stackoverflow.com/a/3641782\n  // in_app is all that's not an internal Node function or a module within node_modules\n  // note that isNative appears to return true even for node core libraries\n  // see https://github.com/getsentry/raven-node/issues/176\n  return !isInternal && filename !== undefined && !filename.includes('node_modules/');\n}\nfunction _parseIntOrUndefined(input) {\n  return parseInt(input || '', 10) || undefined;\n}\nfunction nodeStackLineParser(getModule) {\n  return [90, node(getModule)];\n}\nfunction createStackParser(getModule) {\n  const parsers = [nodeStackLineParser(getModule)];\n  const sortedParsers = parsers.sort((a, b) => a[0] - b[0]).map(p => p[1]);\n  return (stack, skipFirstLines = 0) => {\n    const frames = [];\n    const lines = stack.split('\\n');\n    for (let i = skipFirstLines; i < lines.length; i++) {\n      const line = lines[i];\n      // Ignore lines over 1kb as they are unlikely to be stack frames.\n      if (line.length > 1024) {\n        continue;\n      }\n      // https://github.com/getsentry/sentry-javascript/issues/5459\n      // Remove webpack (error: *) wrappers\n      const cleanedLine = WEBPACK_ERROR_REGEXP.test(line) ? line.replace(WEBPACK_ERROR_REGEXP, '$1') : line;\n      // https://github.com/getsentry/sentry-javascript/issues/7813\n      // Skip Error: lines\n      if (cleanedLine.match(/\\S*Error: /)) {\n        continue;\n      }\n      for (const parser of sortedParsers) {\n        const frame = parser(cleanedLine);\n        if (frame) {\n          frames.push(frame);\n          break;\n        }\n      }\n      if (frames.length >= STACKTRACE_FRAME_LIMIT) {\n        break;\n      }\n    }\n    return reverseAndStripFrames(frames);\n  };\n}\nfunction reverseAndStripFrames(stack) {\n  if (!stack.length) {\n    return [];\n  }\n  const localStack = Array.from(stack);\n  localStack.reverse();\n  return localStack.slice(0, STACKTRACE_FRAME_LIMIT).map(frame => ({\n    ...frame,\n    filename: frame.filename || getLastStackFrame(localStack).filename,\n    function: frame.function || UNKNOWN_FUNCTION\n  }));\n}\nfunction getLastStackFrame(arr) {\n  return arr[arr.length - 1] || {};\n}\n\nErrorTracking.stackParser = createStackParser(createGetModuleFromFilename());\nErrorTracking.frameModifiers = [addSourceContext];\nclass PostHog extends PostHogBackendClient {\n  getLibraryId() {\n    return 'posthog-node';\n  }\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/posthog-node@4.17.2/node_modules/posthog-node/lib/node/index.mjs\n");

/***/ })

};
;