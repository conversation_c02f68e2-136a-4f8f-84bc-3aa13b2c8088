"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17";
exports.ids = ["vendor-chunks/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/dist/module.js":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/dist/module.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COPY_AUTOCAPTURE_EVENT: () => (/* binding */ f),\n/* harmony export */   Compression: () => (/* binding */ g),\n/* harmony export */   PostHog: () => (/* binding */ Yo),\n/* harmony export */   SurveyPosition: () => (/* binding */ Zo),\n/* harmony export */   SurveyQuestionBranchingType: () => (/* binding */ ea),\n/* harmony export */   SurveyQuestionType: () => (/* binding */ ia),\n/* harmony export */   SurveySchedule: () => (/* binding */ ra),\n/* harmony export */   SurveyType: () => (/* binding */ ta),\n/* harmony export */   SurveyWidgetType: () => (/* binding */ Qo),\n/* harmony export */   \"default\": () => (/* binding */ sa),\n/* harmony export */   knownUnsafeEditableEvent: () => (/* binding */ p),\n/* harmony export */   posthog: () => (/* binding */ sa),\n/* harmony export */   severityLevels: () => (/* binding */ _)\n/* harmony export */ });\nvar t=\"undefined\"!=typeof window?window:void 0,i=\"undefined\"!=typeof globalThis?globalThis:t,e=Array.prototype,r=e.forEach,s=e.indexOf,n=null==i?void 0:i.navigator,o=null==i?void 0:i.document,a=null==i?void 0:i.location,l=null==i?void 0:i.fetch,u=null!=i&&i.XMLHttpRequest&&\"withCredentials\"in new i.XMLHttpRequest?i.XMLHttpRequest:void 0,h=null==i?void 0:i.AbortController,d=null==n?void 0:n.userAgent,v=null!=t?t:{},c={DEBUG:!1,LIB_VERSION:\"1.246.0\"},f=\"$copy_autocapture\",p=[\"$snapshot\",\"$pageview\",\"$pageleave\",\"$set\",\"survey dismissed\",\"survey sent\",\"survey shown\",\"$identify\",\"$groupidentify\",\"$create_alias\",\"$$client_ingestion_warning\",\"$web_experiment_applied\",\"$feature_enrollment_update\",\"$feature_flag_called\"],g=function(t){return t.GZipJS=\"gzip-js\",t.Base64=\"base64\",t}({}),_=[\"fatal\",\"error\",\"warning\",\"log\",\"info\",\"debug\"];function m(t,i){return-1!==t.indexOf(i)}var b=function(t){return t.trim()},w=function(t){return t.replace(/^\\$/,\"\")};var y=Array.isArray,S=Object.prototype,$=S.hasOwnProperty,k=S.toString,x=y||function(t){return\"[object Array]\"===k.call(t)},E=t=>\"function\"==typeof t,I=t=>t===Object(t)&&!x(t),P=t=>{if(I(t)){for(var i in t)if($.call(t,i))return!1;return!0}return!1},R=t=>void 0===t,T=t=>\"[object String]\"==k.call(t),M=t=>T(t)&&0===t.trim().length,C=t=>null===t,O=t=>R(t)||C(t),F=t=>\"[object Number]\"==k.call(t),A=t=>\"[object Boolean]\"===k.call(t),D=t=>t instanceof FormData,L=t=>m(p,t),N=i=>{var e={t:function(e){if(t&&(c.DEBUG||v.POSTHOG_DEBUG)&&!R(t.console)&&t.console){for(var r=(\"__rrweb_original__\"in t.console[e]?t.console[e].__rrweb_original__:t.console[e]),s=arguments.length,n=new Array(s>1?s-1:0),o=1;o<s;o++)n[o-1]=arguments[o];r(i,...n)}},info:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.t(\"log\",...i)},warn:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.t(\"warn\",...i)},error:function(){for(var t=arguments.length,i=new Array(t),r=0;r<t;r++)i[r]=arguments[r];e.t(\"error\",...i)},critical:function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];console.error(i,...e)},uninitializedWarning:t=>{e.error(\"You must initialize PostHog before calling \"+t)},createLogger:t=>N(i+\" \"+t)};return e},j=N(\"[PostHog.js]\"),z=j.createLogger,U=z(\"[ExternalScriptsLoader]\"),q=(t,i,e)=>{if(t.config.disable_external_dependency_loading)return U.warn(i+\" was requested but loading of external scripts is disabled.\"),e(\"Loading of external scripts is disabled\");var r=null==o?void 0:o.querySelectorAll(\"script\");if(r)for(var s=0;s<r.length;s++)if(r[s].src===i)return e();var n=()=>{if(!o)return e(\"document not found\");var r=o.createElement(\"script\");if(r.type=\"text/javascript\",r.crossOrigin=\"anonymous\",r.src=i,r.onload=t=>e(void 0,t),r.onerror=t=>e(t),t.config.prepare_external_dependency_script&&(r=t.config.prepare_external_dependency_script(r)),!r)return e(\"prepare_external_dependency_script returned null\");var s,n=o.querySelectorAll(\"body > script\");n.length>0?null==(s=n[0].parentNode)||s.insertBefore(r,n[0]):o.body.appendChild(r)};null!=o&&o.body?n():null==o||o.addEventListener(\"DOMContentLoaded\",n)};function B(){return B=Object.assign?Object.assign.bind():function(t){for(var i=1;i<arguments.length;i++){var e=arguments[i];for(var r in e)({}).hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t},B.apply(null,arguments)}function H(t,i){if(null==t)return{};var e={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(-1!==i.indexOf(r))continue;e[r]=t[r]}return e}v.__PosthogExtensions__=v.__PosthogExtensions__||{},v.__PosthogExtensions__.loadExternalDependency=(t,i,e)=>{var r=\"/static/\"+i+\".js?v=\"+t.version;if(\"remote-config\"===i&&(r=\"/array/\"+t.config.token+\"/config.js\"),\"toolbar\"===i){var s=3e5;r=r+\"&t=\"+Math.floor(Date.now()/s)*s}var n=t.requestRouter.endpointFor(\"assets\",r);q(t,n,e)},v.__PosthogExtensions__.loadSiteApp=(t,i,e)=>{var r=t.requestRouter.endpointFor(\"api\",i);q(t,r,e)};var W={};function G(t,i,e){if(x(t))if(r&&t.forEach===r)t.forEach(i,e);else if(\"length\"in t&&t.length===+t.length)for(var s=0,n=t.length;s<n;s++)if(s in t&&i.call(e,t[s],s)===W)return}function J(t,i,e){if(!O(t)){if(x(t))return G(t,i,e);if(D(t)){for(var r of t.entries())if(i.call(e,r[1],r[0])===W)return}else for(var s in t)if($.call(t,s)&&i.call(e,t[s],s)===W)return}}var V=function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),r=1;r<i;r++)e[r-1]=arguments[r];return G(e,(function(i){for(var e in i)void 0!==i[e]&&(t[e]=i[e])})),t},K=function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),r=1;r<i;r++)e[r-1]=arguments[r];return G(e,(function(i){G(i,(function(i){t.push(i)}))})),t};function Y(t){for(var i=Object.keys(t),e=i.length,r=new Array(e);e--;)r[e]=[i[e],t[i[e]]];return r}var X=function(t){try{return t()}catch(t){return}},Q=function(t){return function(){try{for(var i=arguments.length,e=new Array(i),r=0;r<i;r++)e[r]=arguments[r];return t.apply(this,e)}catch(t){j.critical(\"Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A.\"),j.critical(t)}}},Z=function(t){var i={};return J(t,(function(t,e){(T(t)&&t.length>0||F(t))&&(i[e]=t)})),i};function tt(t,i){return e=t,r=t=>T(t)&&!C(i)?t.slice(0,i):t,s=new Set,function t(i,e){return i!==Object(i)?r?r(i,e):i:s.has(i)?void 0:(s.add(i),x(i)?(n=[],G(i,(i=>{n.push(t(i))}))):(n={},J(i,((i,e)=>{s.has(i)||(n[e]=t(i,e))}))),n);var n}(e);var e,r,s}var it=[\"herokuapp.com\",\"vercel.app\",\"netlify.app\"];function et(t){var i=null==t?void 0:t.hostname;if(!T(i))return!1;var e=i.split(\".\").slice(-2).join(\".\");for(var r of it)if(e===r)return!1;return!0}function rt(t,i){for(var e=0;e<t.length;e++)if(i(t[e]))return t[e]}function st(t,i,e,r){var{capture:s=!1,passive:n=!0}=null!=r?r:{};null==t||t.addEventListener(i,e,{capture:s,passive:n})}var nt=\"$people_distinct_id\",ot=\"__alias\",at=\"__timers\",lt=\"$autocapture_disabled_server_side\",ut=\"$heatmaps_enabled_server_side\",ht=\"$exception_capture_enabled_server_side\",dt=\"$error_tracking_suppression_rules\",vt=\"$web_vitals_enabled_server_side\",ct=\"$dead_clicks_enabled_server_side\",ft=\"$web_vitals_allowed_metrics\",pt=\"$session_recording_enabled_server_side\",gt=\"$console_log_recording_enabled_server_side\",_t=\"$session_recording_network_payload_capture\",mt=\"$session_recording_masking\",bt=\"$session_recording_canvas_recording\",wt=\"$replay_sample_rate\",yt=\"$replay_minimum_duration\",St=\"$replay_script_config\",$t=\"$sesid\",kt=\"$session_is_sampled\",xt=\"$session_recording_url_trigger_activated_session\",Et=\"$session_recording_event_trigger_activated_session\",It=\"$enabled_feature_flags\",Pt=\"$early_access_features\",Rt=\"$feature_flag_details\",Tt=\"$stored_person_properties\",Mt=\"$stored_group_properties\",Ct=\"$surveys\",Ot=\"$surveys_activated\",Ft=\"$flag_call_reported\",At=\"$user_state\",Dt=\"$client_session_props\",Lt=\"$capture_rate_limit\",Nt=\"$initial_campaign_params\",jt=\"$initial_referrer_info\",zt=\"$initial_person_info\",Ut=\"$epp\",qt=\"__POSTHOG_TOOLBAR__\",Bt=\"$posthog_cookieless\",Ht=[nt,ot,\"__cmpns\",at,pt,ut,$t,It,dt,At,Pt,Rt,Mt,Tt,Ct,Ft,Dt,Lt,Nt,jt,Ut,zt];function Wt(t){return t instanceof Element&&(t.id===qt||!(null==t.closest||!t.closest(\".toolbar-global-fade-container\")))}function Gt(t){return!!t&&1===t.nodeType}function Jt(t,i){return!!t&&!!t.tagName&&t.tagName.toLowerCase()===i.toLowerCase()}function Vt(t){return!!t&&3===t.nodeType}function Kt(t){return!!t&&11===t.nodeType}function Yt(t){return t?b(t).split(/\\s+/):[]}function Xt(i){var e=null==t?void 0:t.location.href;return!!(e&&i&&i.some((t=>e.match(t))))}function Qt(t){var i=\"\";switch(typeof t.className){case\"string\":i=t.className;break;case\"object\":i=(t.className&&\"baseVal\"in t.className?t.className.baseVal:null)||t.getAttribute(\"class\")||\"\";break;default:i=\"\"}return Yt(i)}function Zt(t){return O(t)?null:b(t).split(/(\\s+)/).filter((t=>ci(t))).join(\"\").replace(/[\\r\\n]/g,\" \").replace(/[ ]+/g,\" \").substring(0,255)}function ti(t){var i=\"\";return ni(t)&&!oi(t)&&t.childNodes&&t.childNodes.length&&J(t.childNodes,(function(t){var e;Vt(t)&&t.textContent&&(i+=null!==(e=Zt(t.textContent))&&void 0!==e?e:\"\")})),b(i)}function ii(t){return R(t.target)?t.srcElement||null:null!=(i=t.target)&&i.shadowRoot?t.composedPath()[0]||null:t.target||null;var i}var ei=[\"a\",\"button\",\"form\",\"input\",\"select\",\"textarea\",\"label\"];function ri(t){var i=t.parentNode;return!(!i||!Gt(i))&&i}function si(i,e,r,s,n){var o,a,l;if(void 0===r&&(r=void 0),!t||!i||Jt(i,\"html\")||!Gt(i))return!1;if(null!=(o=r)&&o.url_allowlist&&!Xt(r.url_allowlist))return!1;if(null!=(a=r)&&a.url_ignorelist&&Xt(r.url_ignorelist))return!1;if(null!=(l=r)&&l.dom_event_allowlist){var u=r.dom_event_allowlist;if(u&&!u.some((t=>e.type===t)))return!1}for(var h=!1,d=[i],v=!0,c=i;c.parentNode&&!Jt(c,\"body\");)if(Kt(c.parentNode))d.push(c.parentNode.host),c=c.parentNode.host;else{if(!(v=ri(c)))break;if(s||ei.indexOf(v.tagName.toLowerCase())>-1)h=!0;else{var f=t.getComputedStyle(v);f&&\"pointer\"===f.getPropertyValue(\"cursor\")&&(h=!0)}d.push(v),c=v}if(!function(t,i){var e=null==i?void 0:i.element_allowlist;if(R(e))return!0;var r,s=function(t){if(e.some((i=>t.tagName.toLowerCase()===i)))return{v:!0}};for(var n of t)if(r=s(n))return r.v;return!1}(d,r))return!1;if(!function(t,i){var e=null==i?void 0:i.css_selector_allowlist;if(R(e))return!0;var r,s=function(t){if(e.some((i=>t.matches(i))))return{v:!0}};for(var n of t)if(r=s(n))return r.v;return!1}(d,r))return!1;var p=t.getComputedStyle(i);if(p&&\"pointer\"===p.getPropertyValue(\"cursor\")&&\"click\"===e.type)return!0;var g=i.tagName.toLowerCase();switch(g){case\"html\":return!1;case\"form\":return(n||[\"submit\"]).indexOf(e.type)>=0;case\"input\":case\"select\":case\"textarea\":return(n||[\"change\",\"click\"]).indexOf(e.type)>=0;default:return h?(n||[\"click\"]).indexOf(e.type)>=0:(n||[\"click\"]).indexOf(e.type)>=0&&(ei.indexOf(g)>-1||\"true\"===i.getAttribute(\"contenteditable\"))}}function ni(t){for(var i=t;i.parentNode&&!Jt(i,\"body\");i=i.parentNode){var e=Qt(i);if(m(e,\"ph-sensitive\")||m(e,\"ph-no-capture\"))return!1}if(m(Qt(t),\"ph-include\"))return!0;var r=t.type||\"\";if(T(r))switch(r.toLowerCase()){case\"hidden\":case\"password\":return!1}var s=t.name||t.id||\"\";if(T(s)){if(/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(s.replace(/[^a-zA-Z0-9]/g,\"\")))return!1}return!0}function oi(t){return!!(Jt(t,\"input\")&&![\"button\",\"checkbox\",\"submit\",\"reset\"].includes(t.type)||Jt(t,\"select\")||Jt(t,\"textarea\")||\"true\"===t.getAttribute(\"contenteditable\"))}var ai=\"(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})\",li=new RegExp(\"^(?:\"+ai+\")$\"),ui=new RegExp(ai),hi=\"\\\\d{3}-?\\\\d{2}-?\\\\d{4}\",di=new RegExp(\"^(\"+hi+\")$\"),vi=new RegExp(\"(\"+hi+\")\");function ci(t,i){if(void 0===i&&(i=!0),O(t))return!1;if(T(t)){if(t=b(t),(i?li:ui).test((t||\"\").replace(/[- ]/g,\"\")))return!1;if((i?di:vi).test(t))return!1}return!0}function fi(t){var i=ti(t);return ci(i=(i+\" \"+pi(t)).trim())?i:\"\"}function pi(t){var i=\"\";return t&&t.childNodes&&t.childNodes.length&&J(t.childNodes,(function(t){var e;if(t&&\"span\"===(null==(e=t.tagName)?void 0:e.toLowerCase()))try{var r=ti(t);i=(i+\" \"+r).trim(),t.childNodes&&t.childNodes.length&&(i=(i+\" \"+pi(t)).trim())}catch(t){j.error(\"[AutoCapture]\",t)}})),i}function gi(t){return function(t){var i=t.map((t=>{var i,e,r=\"\";if(t.tag_name&&(r+=t.tag_name),t.attr_class)for(var s of(t.attr_class.sort(),t.attr_class))r+=\".\"+s.replace(/\"/g,\"\");var n=B({},t.text?{text:t.text}:{},{\"nth-child\":null!==(i=t.nth_child)&&void 0!==i?i:0,\"nth-of-type\":null!==(e=t.nth_of_type)&&void 0!==e?e:0},t.href?{href:t.href}:{},t.attr_id?{attr_id:t.attr_id}:{},t.attributes),o={};return Y(n).sort(((t,i)=>{var[e]=t,[r]=i;return e.localeCompare(r)})).forEach((t=>{var[i,e]=t;return o[_i(i.toString())]=_i(e.toString())})),r+=\":\",r+=Y(o).map((t=>{var[i,e]=t;return i+'=\"'+e+'\"'})).join(\"\")}));return i.join(\";\")}(function(t){return t.map((t=>{var i,e,r={text:null==(i=t.$el_text)?void 0:i.slice(0,400),tag_name:t.tag_name,href:null==(e=t.attr__href)?void 0:e.slice(0,2048),attr_class:mi(t),attr_id:t.attr__id,nth_child:t.nth_child,nth_of_type:t.nth_of_type,attributes:{}};return Y(t).filter((t=>{var[i]=t;return 0===i.indexOf(\"attr__\")})).forEach((t=>{var[i,e]=t;return r.attributes[i]=e})),r}))}(t))}function _i(t){return t.replace(/\"|\\\\\"/g,'\\\\\"')}function mi(t){var i=t.attr__class;return i?x(i)?i:Yt(i):void 0}class bi{constructor(){this.clicks=[]}isRageClick(t,i,e){var r=this.clicks[this.clicks.length-1];if(r&&Math.abs(t-r.x)+Math.abs(i-r.y)<30&&e-r.timestamp<1e3){if(this.clicks.push({x:t,y:i,timestamp:e}),3===this.clicks.length)return!0}else this.clicks=[{x:t,y:i,timestamp:e}];return!1}}var wi=[\"localhost\",\"127.0.0.1\"],yi=t=>{var i=null==o?void 0:o.createElement(\"a\");return R(i)?null:(i.href=t,i)},Si=function(t,i){var e,r;void 0===i&&(i=\"&\");var s=[];return J(t,(function(t,i){R(t)||R(i)||\"undefined\"===i||(e=encodeURIComponent((t=>t instanceof File)(t)?t.name:t.toString()),r=encodeURIComponent(i),s[s.length]=r+\"=\"+e)})),s.join(i)},$i=function(t,i){for(var e,r=((t.split(\"#\")[0]||\"\").split(/\\?(.*)/)[1]||\"\").replace(/^\\?+/g,\"\").split(\"&\"),s=0;s<r.length;s++){var n=r[s].split(\"=\");if(n[0]===i){e=n;break}}if(!x(e)||e.length<2)return\"\";var o=e[1];try{o=decodeURIComponent(o)}catch(t){j.error(\"Skipping decoding for malformed query param: \"+o)}return o.replace(/\\+/g,\" \")},ki=function(t,i,e){if(!t||!i||!i.length)return t;for(var r=t.split(\"#\"),s=r[0]||\"\",n=r[1],o=s.split(\"?\"),a=o[1],l=o[0],u=(a||\"\").split(\"&\"),h=[],d=0;d<u.length;d++){var v=u[d].split(\"=\");x(v)&&(i.includes(v[0])?h.push(v[0]+\"=\"+e):h.push(u[d]))}var c=l;return null!=a&&(c+=\"?\"+h.join(\"&\")),null!=n&&(c+=\"#\"+n),c},xi=function(t,i){var e=t.match(new RegExp(i+\"=([^&]*)\"));return e?e[1]:null},Ei=z(\"[AutoCapture]\");function Ii(t,i){return i.length>t?i.slice(0,t)+\"...\":i}function Pi(t){if(t.previousElementSibling)return t.previousElementSibling;var i=t;do{i=i.previousSibling}while(i&&!Gt(i));return i}function Ri(t,i,e,r){var s=t.tagName.toLowerCase(),n={tag_name:s};ei.indexOf(s)>-1&&!e&&(\"a\"===s.toLowerCase()||\"button\"===s.toLowerCase()?n.$el_text=Ii(1024,fi(t)):n.$el_text=Ii(1024,ti(t)));var o=Qt(t);o.length>0&&(n.classes=o.filter((function(t){return\"\"!==t}))),J(t.attributes,(function(e){var s;if((!oi(t)||-1!==[\"name\",\"id\",\"class\",\"aria-label\"].indexOf(e.name))&&((null==r||!r.includes(e.name))&&!i&&ci(e.value)&&(s=e.name,!T(s)||\"_ngcontent\"!==s.substring(0,10)&&\"_nghost\"!==s.substring(0,7)))){var o=e.value;\"class\"===e.name&&(o=Yt(o).join(\" \")),n[\"attr__\"+e.name]=Ii(1024,o)}}));for(var a=1,l=1,u=t;u=Pi(u);)a++,u.tagName===t.tagName&&l++;return n.nth_child=a,n.nth_of_type=l,n}function Ti(i,e){for(var r,s,{e:n,maskAllElementAttributes:o,maskAllText:a,elementAttributeIgnoreList:l,elementsChainAsString:u}=e,h=[i],d=i;d.parentNode&&!Jt(d,\"body\");)Kt(d.parentNode)?(h.push(d.parentNode.host),d=d.parentNode.host):(h.push(d.parentNode),d=d.parentNode);var v,c=[],f={},p=!1,g=!1;if(J(h,(t=>{var i=ni(t);\"a\"===t.tagName.toLowerCase()&&(p=t.getAttribute(\"href\"),p=i&&p&&ci(p)&&p),m(Qt(t),\"ph-no-capture\")&&(g=!0),c.push(Ri(t,o,a,l));var e=function(t){if(!ni(t))return{};var i={};return J(t.attributes,(function(t){if(t.name&&0===t.name.indexOf(\"data-ph-capture-attribute\")){var e=t.name.replace(\"data-ph-capture-attribute-\",\"\"),r=t.value;e&&r&&ci(r)&&(i[e]=r)}})),i}(t);V(f,e)})),g)return{props:{},explicitNoCapture:g};if(a||(\"a\"===i.tagName.toLowerCase()||\"button\"===i.tagName.toLowerCase()?c[0].$el_text=fi(i):c[0].$el_text=ti(i)),p){var _,b;c[0].attr__href=p;var w=null==(_=yi(p))?void 0:_.host,y=null==t||null==(b=t.location)?void 0:b.host;w&&y&&w!==y&&(v=p)}return{props:V({$event_type:n.type,$ce_version:1},u?{}:{$elements:c},{$elements_chain:gi(c)},null!=(r=c[0])&&r.$el_text?{$el_text:null==(s=c[0])?void 0:s.$el_text}:{},v&&\"click\"===n.type?{$external_click_url:v}:{},f)}}class Mi{constructor(t){this.i=!1,this.o=null,this.rageclicks=new bi,this.h=!1,this.instance=t,this.m=null}get S(){var t,i,e=I(this.instance.config.autocapture)?this.instance.config.autocapture:{};return e.url_allowlist=null==(t=e.url_allowlist)?void 0:t.map((t=>new RegExp(t))),e.url_ignorelist=null==(i=e.url_ignorelist)?void 0:i.map((t=>new RegExp(t))),e}$(){if(this.isBrowserSupported()){if(t&&o){var i=i=>{i=i||(null==t?void 0:t.event);try{this.k(i)}catch(t){Ei.error(\"Failed to capture event\",t)}};if(st(o,\"submit\",i,{capture:!0}),st(o,\"change\",i,{capture:!0}),st(o,\"click\",i,{capture:!0}),this.S.capture_copied_text){var e=i=>{i=i||(null==t?void 0:t.event),this.k(i,f)};st(o,\"copy\",e,{capture:!0}),st(o,\"cut\",e,{capture:!0})}}}else Ei.info(\"Disabling Automatic Event Collection because this browser is not supported\")}startIfEnabled(){this.isEnabled&&!this.i&&(this.$(),this.i=!0)}onRemoteConfig(t){t.elementsChainAsString&&(this.h=t.elementsChainAsString),this.instance.persistence&&this.instance.persistence.register({[lt]:!!t.autocapture_opt_out}),this.o=!!t.autocapture_opt_out,this.startIfEnabled()}setElementSelectors(t){this.m=t}getElementSelectors(t){var i,e=[];return null==(i=this.m)||i.forEach((i=>{var r=null==o?void 0:o.querySelectorAll(i);null==r||r.forEach((r=>{t===r&&e.push(i)}))})),e}get isEnabled(){var t,i,e=null==(t=this.instance.persistence)?void 0:t.props[lt],r=this.o;if(C(r)&&!A(e)&&!this.instance.config.advanced_disable_decide)return!1;var s=null!==(i=this.o)&&void 0!==i?i:!!e;return!!this.instance.config.autocapture&&!s}k(i,e){if(void 0===e&&(e=\"$autocapture\"),this.isEnabled){var r,s=ii(i);if(Vt(s)&&(s=s.parentNode||null),\"$autocapture\"===e&&\"click\"===i.type&&i instanceof MouseEvent)this.instance.config.rageclick&&null!=(r=this.rageclicks)&&r.isRageClick(i.clientX,i.clientY,(new Date).getTime())&&this.k(i,\"$rageclick\");var n=e===f;if(s&&si(s,i,this.S,n,n?[\"copy\",\"cut\"]:void 0)){var{props:o,explicitNoCapture:a}=Ti(s,{e:i,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this.S.element_attribute_ignorelist,elementsChainAsString:this.h});if(a)return!1;var l=this.getElementSelectors(s);if(l&&l.length>0&&(o.$element_selectors=l),e===f){var u,h=Zt(null==t||null==(u=t.getSelection())?void 0:u.toString()),d=i.type||\"clipboard\";if(!h)return!1;o.$selected_content=h,o.$copy_type=d}return this.instance.capture(e,o),!0}}}isBrowserSupported(){return E(null==o?void 0:o.querySelectorAll)}}Math.trunc||(Math.trunc=function(t){return t<0?Math.ceil(t):Math.floor(t)}),Number.isInteger||(Number.isInteger=function(t){return F(t)&&isFinite(t)&&Math.floor(t)===t});var Ci=\"0123456789abcdef\";class Oi{constructor(t){if(this.bytes=t,16!==t.length)throw new TypeError(\"not 128-bit length\")}static fromFieldsV7(t,i,e,r){if(!Number.isInteger(t)||!Number.isInteger(i)||!Number.isInteger(e)||!Number.isInteger(r)||t<0||i<0||e<0||r<0||t>0xffffffffffff||i>4095||e>1073741823||r>4294967295)throw new RangeError(\"invalid field value\");var s=new Uint8Array(16);return s[0]=t/Math.pow(2,40),s[1]=t/Math.pow(2,32),s[2]=t/Math.pow(2,24),s[3]=t/Math.pow(2,16),s[4]=t/Math.pow(2,8),s[5]=t,s[6]=112|i>>>8,s[7]=i,s[8]=128|e>>>24,s[9]=e>>>16,s[10]=e>>>8,s[11]=e,s[12]=r>>>24,s[13]=r>>>16,s[14]=r>>>8,s[15]=r,new Oi(s)}toString(){for(var t=\"\",i=0;i<this.bytes.length;i++)t=t+Ci.charAt(this.bytes[i]>>>4)+Ci.charAt(15&this.bytes[i]),3!==i&&5!==i&&7!==i&&9!==i||(t+=\"-\");if(36!==t.length)throw new Error(\"Invalid UUIDv7 was generated\");return t}clone(){return new Oi(this.bytes.slice(0))}equals(t){return 0===this.compareTo(t)}compareTo(t){for(var i=0;i<16;i++){var e=this.bytes[i]-t.bytes[i];if(0!==e)return Math.sign(e)}return 0}}class Fi{constructor(){this.I=0,this.P=0,this.R=new Li}generate(){var t=this.generateOrAbort();if(R(t)){this.I=0;var i=this.generateOrAbort();if(R(i))throw new Error(\"Could not generate UUID after timestamp reset\");return i}return t}generateOrAbort(){var t=Date.now();if(t>this.I)this.I=t,this.T();else{if(!(t+1e4>this.I))return;this.P++,this.P>4398046511103&&(this.I++,this.T())}return Oi.fromFieldsV7(this.I,Math.trunc(this.P/Math.pow(2,30)),this.P&Math.pow(2,30)-1,this.R.nextUint32())}T(){this.P=1024*this.R.nextUint32()+(1023&this.R.nextUint32())}}var Ai,Di=t=>{if(\"undefined\"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw new Error(\"no cryptographically strong RNG available\");for(var i=0;i<t.length;i++)t[i]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return t};t&&!R(t.crypto)&&crypto.getRandomValues&&(Di=t=>crypto.getRandomValues(t));class Li{constructor(){this.M=new Uint32Array(8),this.C=1/0}nextUint32(){return this.C>=this.M.length&&(Di(this.M),this.C=0),this.M[this.C++]}}var Ni=()=>ji().toString(),ji=()=>(Ai||(Ai=new Fi)).generate(),zi=\"\";var Ui=/[a-z0-9][a-z0-9-]+\\.[a-z]{2,}$/i;function qi(t,i){if(i){var e=function(t,i){if(void 0===i&&(i=o),zi)return zi;if(!i)return\"\";if([\"localhost\",\"127.0.0.1\"].includes(t))return\"\";for(var e=t.split(\".\"),r=Math.min(e.length,8),s=\"dmn_chk_\"+Ni();!zi&&r--;){var n=e.slice(r).join(\".\"),a=s+\"=1;domain=.\"+n+\";path=/\";i.cookie=a+\";max-age=3\",i.cookie.includes(s)&&(i.cookie=a+\";max-age=0\",zi=n)}return zi}(t);if(!e){var r=(t=>{var i=t.match(Ui);return i?i[0]:\"\"})(t);r!==e&&j.info(\"Warning: cookie subdomain discovery mismatch\",r,e),e=r}return e?\"; domain=.\"+e:\"\"}return\"\"}var Bi={O:()=>!!o,F:function(t){j.error(\"cookieStore error: \"+t)},A:function(t){if(o){try{for(var i=t+\"=\",e=o.cookie.split(\";\").filter((t=>t.length)),r=0;r<e.length;r++){for(var s=e[r];\" \"==s.charAt(0);)s=s.substring(1,s.length);if(0===s.indexOf(i))return decodeURIComponent(s.substring(i.length,s.length))}}catch(t){}return null}},D:function(t){var i;try{i=JSON.parse(Bi.A(t))||{}}catch(t){}return i},L:function(t,i,e,r,s){if(o)try{var n=\"\",a=\"\",l=qi(o.location.hostname,r);if(e){var u=new Date;u.setTime(u.getTime()+24*e*60*60*1e3),n=\"; expires=\"+u.toUTCString()}s&&(a=\"; secure\");var h=t+\"=\"+encodeURIComponent(JSON.stringify(i))+n+\"; SameSite=Lax; path=/\"+l+a;return h.length>3686.4&&j.warn(\"cookieStore warning: large cookie, len=\"+h.length),o.cookie=h,h}catch(t){return}},N:function(t,i){try{Bi.L(t,\"\",-1,i)}catch(t){return}}},Hi=null,Wi={O:function(){if(!C(Hi))return Hi;var i=!0;if(R(t))i=!1;else try{var e=\"__mplssupport__\";Wi.L(e,\"xyz\"),'\"xyz\"'!==Wi.A(e)&&(i=!1),Wi.N(e)}catch(t){i=!1}return i||j.error(\"localStorage unsupported; falling back to cookie store\"),Hi=i,i},F:function(t){j.error(\"localStorage error: \"+t)},A:function(i){try{return null==t?void 0:t.localStorage.getItem(i)}catch(t){Wi.F(t)}return null},D:function(t){try{return JSON.parse(Wi.A(t))||{}}catch(t){}return null},L:function(i,e){try{null==t||t.localStorage.setItem(i,JSON.stringify(e))}catch(t){Wi.F(t)}},N:function(i){try{null==t||t.localStorage.removeItem(i)}catch(t){Wi.F(t)}}},Gi=[\"distinct_id\",$t,kt,Ut,zt],Ji=B({},Wi,{D:function(t){try{var i={};try{i=Bi.D(t)||{}}catch(t){}var e=V(i,JSON.parse(Wi.A(t)||\"{}\"));return Wi.L(t,e),e}catch(t){}return null},L:function(t,i,e,r,s,n){try{Wi.L(t,i,void 0,void 0,n);var o={};Gi.forEach((t=>{i[t]&&(o[t]=i[t])})),Object.keys(o).length&&Bi.L(t,o,e,r,s,n)}catch(t){Wi.F(t)}},N:function(i,e){try{null==t||t.localStorage.removeItem(i),Bi.N(i,e)}catch(t){Wi.F(t)}}}),Vi={},Ki={O:function(){return!0},F:function(t){j.error(\"memoryStorage error: \"+t)},A:function(t){return Vi[t]||null},D:function(t){return Vi[t]||null},L:function(t,i){Vi[t]=i},N:function(t){delete Vi[t]}},Yi=null,Xi={O:function(){if(!C(Yi))return Yi;if(Yi=!0,R(t))Yi=!1;else try{var i=\"__support__\";Xi.L(i,\"xyz\"),'\"xyz\"'!==Xi.A(i)&&(Yi=!1),Xi.N(i)}catch(t){Yi=!1}return Yi},F:function(t){j.error(\"sessionStorage error: \",t)},A:function(i){try{return null==t?void 0:t.sessionStorage.getItem(i)}catch(t){Xi.F(t)}return null},D:function(t){try{return JSON.parse(Xi.A(t))||null}catch(t){}return null},L:function(i,e){try{null==t||t.sessionStorage.setItem(i,JSON.stringify(e))}catch(t){Xi.F(t)}},N:function(i){try{null==t||t.sessionStorage.removeItem(i)}catch(t){Xi.F(t)}}},Qi=function(t){return t[t.PENDING=-1]=\"PENDING\",t[t.DENIED=0]=\"DENIED\",t[t.GRANTED=1]=\"GRANTED\",t}({});class Zi{constructor(t){this._instance=t}get S(){return this._instance.config}get consent(){return this.j()?Qi.DENIED:this.U}isOptedOut(){return this.consent===Qi.DENIED||this.consent===Qi.PENDING&&this.S.opt_out_capturing_by_default}isOptedIn(){return!this.isOptedOut()}optInOut(t){this.q.L(this.B,t?1:0,this.S.cookie_expiration,this.S.cross_subdomain_cookie,this.S.secure_cookie)}reset(){this.q.N(this.B,this.S.cross_subdomain_cookie)}get B(){var{token:t,opt_out_capturing_cookie_prefix:i}=this._instance.config;return(i||\"__ph_opt_in_out_\")+t}get U(){var t=this.q.A(this.B);return\"1\"===t?Qi.GRANTED:\"0\"===t?Qi.DENIED:Qi.PENDING}get q(){if(!this.H){var t=this.S.opt_out_capturing_persistence_type;this.H=\"localStorage\"===t?Wi:Bi;var i=\"localStorage\"===t?Bi:Wi;i.A(this.B)&&(this.H.A(this.B)||this.optInOut(\"1\"===i.A(this.B)),i.N(this.B,this.S.cross_subdomain_cookie))}return this.H}j(){return!!this.S.respect_dnt&&!!rt([null==n?void 0:n.doNotTrack,null==n?void 0:n.msDoNotTrack,v.doNotTrack],(t=>m([!0,1,\"1\",\"yes\"],t)))}}var te=z(\"[Dead Clicks]\"),ie=()=>!0,ee=t=>{var i,e=!(null==(i=t.instance.persistence)||!i.get_property(ct)),r=t.instance.config.capture_dead_clicks;return A(r)?r:e};class re{get lazyLoadedDeadClicksAutocapture(){return this.W}constructor(t,i,e){this.instance=t,this.isEnabled=i,this.onCapture=e,this.startIfEnabled()}onRemoteConfig(t){this.instance.persistence&&this.instance.persistence.register({[ct]:null==t?void 0:t.captureDeadClicks}),this.startIfEnabled()}startIfEnabled(){this.isEnabled(this)&&this.G((()=>{this.J()}))}G(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.initDeadClicksAutocapture&&t(),null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this.instance,\"dead-clicks-autocapture\",(i=>{i?te.error(\"failed to load script\",i):t()}))}J(){var t;if(o){if(!this.W&&null!=(t=v.__PosthogExtensions__)&&t.initDeadClicksAutocapture){var i=I(this.instance.config.capture_dead_clicks)?this.instance.config.capture_dead_clicks:{};i.__onCapture=this.onCapture,this.W=v.__PosthogExtensions__.initDeadClicksAutocapture(this.instance,i),this.W.start(o),te.info(\"starting...\")}}else te.error(\"`document` not found. Cannot start.\")}stop(){this.W&&(this.W.stop(),this.W=void 0,te.info(\"stopping...\"))}}var se=z(\"[ExceptionAutocapture]\");class ne{constructor(i){var e;this.V=()=>{var i;if(t&&this.isEnabled&&null!=(i=v.__PosthogExtensions__)&&i.errorWrappingFunctions){var e=v.__PosthogExtensions__.errorWrappingFunctions.wrapOnError,r=v.__PosthogExtensions__.errorWrappingFunctions.wrapUnhandledRejection,s=v.__PosthogExtensions__.errorWrappingFunctions.wrapConsoleError;try{!this.K&&this.S.capture_unhandled_errors&&(this.K=e(this.captureException.bind(this))),!this.Y&&this.S.capture_unhandled_rejections&&(this.Y=r(this.captureException.bind(this))),!this.X&&this.S.capture_console_errors&&(this.X=s(this.captureException.bind(this)))}catch(t){se.error(\"failed to start\",t),this.Z()}}},this._instance=i,this.tt=!(null==(e=this._instance.persistence)||!e.props[ht]),this.S=this.it(),this.startIfEnabled()}it(){var t=this._instance.config.capture_exceptions,i={capture_unhandled_errors:!1,capture_unhandled_rejections:!1,capture_console_errors:!1};return I(t)?i=B({},i,t):(R(t)?this.tt:t)&&(i=B({},i,{capture_unhandled_errors:!0,capture_unhandled_rejections:!0})),i}get isEnabled(){return this.S.capture_console_errors||this.S.capture_unhandled_errors||this.S.capture_unhandled_rejections}startIfEnabled(){this.isEnabled&&(se.info(\"enabled\"),this.G(this.V))}G(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.errorWrappingFunctions&&t(),null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,\"exception-autocapture\",(i=>{if(i)return se.error(\"failed to load script\",i);t()}))}Z(){var t,i,e;null==(t=this.K)||t.call(this),this.K=void 0,null==(i=this.Y)||i.call(this),this.Y=void 0,null==(e=this.X)||e.call(this),this.X=void 0}onRemoteConfig(t){var i=t.autocaptureExceptions;this.tt=!!i||!1,this.S=this.it(),this._instance.persistence&&this._instance.persistence.register({[ht]:this.tt}),this.startIfEnabled()}captureException(t){var i=this._instance.requestRouter.endpointFor(\"ui\");t.$exception_personURL=i+\"/project/\"+this._instance.config.token+\"/person/\"+this._instance.get_distinct_id(),this._instance.exceptions.sendExceptionEvent(t)}}function oe(t){return!R(Event)&&ae(t,Event)}function ae(t,i){try{return t instanceof i}catch(t){return!1}}function le(t){switch(Object.prototype.toString.call(t)){case\"[object Error]\":case\"[object Exception]\":case\"[object DOMException]\":case\"[object DOMError]\":return!0;default:return ae(t,Error)}}function ue(t,i){return Object.prototype.toString.call(t)===\"[object \"+i+\"]\"}function he(t){return ue(t,\"DOMError\")}var de=/\\(error: (.*)\\)/,ve=50,ce=\"?\";function fe(t,i,e,r){var s={platform:\"web:javascript\",filename:t,function:\"<anonymous>\"===i?ce:i,in_app:!0};return R(e)||(s.lineno=e),R(r)||(s.colno=r),s}var pe=/^\\s*at (\\S+?)(?::(\\d+))(?::(\\d+))\\s*$/i,ge=/^\\s*at (?:(.+?\\)(?: \\[.+\\])?|.*?) ?\\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\\/)?.*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i,_e=/\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/,me=/^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)?((?:[-a-z]+)?:\\/.*?|\\[native code\\]|[^@]*(?:bundle|\\d+\\.js)|\\/[\\w\\-. /=]+)(?::(\\d+))?(?::(\\d+))?\\s*$/i,be=/(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i,we=function(){for(var t=arguments.length,i=new Array(t),e=0;e<t;e++)i[e]=arguments[e];var r=i.sort(((t,i)=>t[0]-i[0])).map((t=>t[1]));return function(t,i){void 0===i&&(i=0);for(var e=[],s=t.split(\"\\n\"),n=i;n<s.length;n++){var o=s[n];if(!(o.length>1024)){var a=de.test(o)?o.replace(de,\"$1\"):o;if(!a.match(/\\S*Error: /)){for(var l of r){var u=l(a);if(u){e.push(u);break}}if(e.length>=ve)break}}}return function(t){if(!t.length)return[];var i=Array.from(t);return i.reverse(),i.slice(0,ve).map((t=>B({},t,{filename:t.filename||ye(i).filename,function:t.function||ce})))}(e)}}(...[[30,t=>{var i=pe.exec(t);if(i){var[,e,r,s]=i;return fe(e,ce,+r,+s)}var n=ge.exec(t);if(n){if(n[2]&&0===n[2].indexOf(\"eval\")){var o=_e.exec(n[2]);o&&(n[2]=o[1],n[3]=o[2],n[4]=o[3])}var[a,l]=xe(n[1]||ce,n[2]);return fe(l,a,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,t=>{var i=me.exec(t);if(i){if(i[3]&&i[3].indexOf(\" > eval\")>-1){var e=be.exec(i[3]);e&&(i[1]=i[1]||\"eval\",i[3]=e[1],i[4]=e[2],i[5]=\"\")}var r=i[3],s=i[1]||ce;return[s,r]=xe(s,r),fe(r,s,i[4]?+i[4]:void 0,i[5]?+i[5]:void 0)}}]]);function ye(t){return t[t.length-1]||{}}var Se,$e,ke,xe=(t,i)=>{var e=-1!==t.indexOf(\"safari-extension\"),r=-1!==t.indexOf(\"safari-web-extension\");return e||r?[-1!==t.indexOf(\"@\")?t.split(\"@\")[0]:ce,e?\"safari-extension:\"+i:\"safari-web-extension:\"+i]:[t,i]};var Ee=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function Ie(t,i){void 0===i&&(i=0);var e=t.stacktrace||t.stack||\"\",r=function(t){if(t&&Pe.test(t.message))return 1;return 0}(t);try{var s=we,n=function(t,i){var e=function(t){var i=globalThis._posthogChunkIds;if(!i)return{};var e=Object.keys(i);return ke&&e.length===$e||($e=e.length,ke=e.reduce(((e,r)=>{Se||(Se={});var s=Se[r];if(s)e[s[0]]=s[1];else for(var n=t(r),o=n.length-1;o>=0;o--){var a=n[o],l=null==a?void 0:a.filename,u=i[r];if(l&&u){e[l]=u,Se[r]=[l,u];break}}return e}),{})),ke}(i);return t.forEach((t=>{t.filename&&(t.chunk_id=e[t.filename])})),t}(s(e,r),s);return n.slice(0,n.length-i)}catch(t){}return[]}var Pe=/Minified React error #\\d+;/i;function Re(t,i){var e,r,s=Ie(t),n=null===(e=null==i?void 0:i.handled)||void 0===e||e,o=null!==(r=null==i?void 0:i.synthetic)&&void 0!==r&&r;return{type:null!=i&&i.overrideExceptionType?i.overrideExceptionType:t.name,value:function(t){var i=t.message;if(i.error&&\"string\"==typeof i.error.message)return String(i.error.message);return String(i)}(t),stacktrace:{frames:s,type:\"raw\"},mechanism:{handled:n,synthetic:o}}}function Te(t,i){var e=Re(t,i);return t.cause&&le(t.cause)&&t.cause!==t?[e,...Te(t.cause,{handled:null==i?void 0:i.handled,synthetic:null==i?void 0:i.synthetic})]:[e]}function Me(t,i){return{$exception_list:Te(t,i),$exception_level:\"error\"}}function Ce(t,i){var e,r,s,n=null===(e=null==i?void 0:i.handled)||void 0===e||e,o=null===(r=null==i?void 0:i.synthetic)||void 0===r||r,a={type:null!=i&&i.overrideExceptionType?i.overrideExceptionType:null!==(s=null==i?void 0:i.defaultExceptionType)&&void 0!==s?s:\"Error\",value:t||(null==i?void 0:i.defaultExceptionMessage),mechanism:{handled:n,synthetic:o}};if(null!=i&&i.syntheticException){var l=Ie(i.syntheticException,1);l.length&&(a.stacktrace={frames:l,type:\"raw\"})}return{$exception_list:[a],$exception_level:\"error\"}}function Oe(t){return T(t)&&!M(t)&&_.indexOf(t)>=0}function Fe(t,i){var e,r,s=null===(e=null==i?void 0:i.handled)||void 0===e||e,n=null===(r=null==i?void 0:i.synthetic)||void 0===r||r,o=null!=i&&i.overrideExceptionType?i.overrideExceptionType:oe(t)?t.constructor.name:\"Error\",a=\"Non-Error 'exception' captured with keys: \"+function(t,i){void 0===i&&(i=40);var e=Object.keys(t);if(e.sort(),!e.length)return\"[object has no keys]\";for(var r=e.length;r>0;r--){var s=e.slice(0,r).join(\", \");if(!(s.length>i))return r===e.length||s.length<=i?s:s.slice(0,i)+\"...\"}return\"\"}(t),l={type:o,value:a,mechanism:{handled:s,synthetic:n}};if(null!=i&&i.syntheticException){var u=Ie(null==i?void 0:i.syntheticException,1);u.length&&(l.stacktrace={frames:u,type:\"raw\"})}return{$exception_list:[l],$exception_level:Oe(t.level)?t.level:\"error\"}}function Ae(t,i){var{error:e,event:r}=t,s={$exception_list:[]},n=e||r;if(he(n)||function(t){return ue(t,\"DOMException\")}(n)){var o=n;if(function(t){return\"stack\"in t}(n))s=Me(n,i);else{var a=o.name||(he(o)?\"DOMError\":\"DOMException\"),l=o.message?a+\": \"+o.message:a;s=Ce(l,B({},i,{overrideExceptionType:he(o)?\"DOMError\":\"DOMException\",defaultExceptionMessage:l}))}return\"code\"in o&&(s.$exception_DOMException_code=\"\"+o.code),s}if(function(t){return ue(t,\"ErrorEvent\")}(n)&&n.error)return Me(n.error,i);if(le(n))return Me(n,i);if(function(t){return ue(t,\"Object\")}(n)||oe(n))return Fe(n,i);if(R(e)&&T(r)){var u=\"Error\",h=r,d=r.match(Ee);return d&&(u=d[1],h=d[2]),Ce(h,B({},i,{overrideExceptionType:u,defaultExceptionMessage:h}))}return Ce(n,i)}function De(t,i,e){try{if(!(i in t))return()=>{};var r=t[i],s=e(r);return E(s)&&(s.prototype=s.prototype||{},Object.defineProperties(s,{__posthog_wrapped__:{enumerable:!1,value:!0}})),t[i]=s,()=>{t[i]=r}}catch(t){return()=>{}}}class Le{constructor(i){var e;this._instance=i,this.et=(null==t||null==(e=t.location)?void 0:e.pathname)||\"\"}get isEnabled(){return\"history_change\"===this._instance.config.capture_pageview}startIfEnabled(){this.isEnabled&&(j.info(\"History API monitoring enabled, starting...\"),this.monitorHistoryChanges())}stop(){this.rt&&this.rt(),this.rt=void 0,j.info(\"History API monitoring stopped\")}monitorHistoryChanges(){var i,e;if(t&&t.history){var r=this;null!=(i=t.history.pushState)&&i.__posthog_wrapped__||De(t.history,\"pushState\",(t=>function(i,e,s){t.call(this,i,e,s),r.st(\"pushState\")})),null!=(e=t.history.replaceState)&&e.__posthog_wrapped__||De(t.history,\"replaceState\",(t=>function(i,e,s){t.call(this,i,e,s),r.st(\"replaceState\")})),this.nt()}}st(i){try{var e,r=null==t||null==(e=t.location)?void 0:e.pathname;if(!r)return;r!==this.et&&this.isEnabled&&this._instance.capture(\"$pageview\",{navigation_type:i}),this.et=r}catch(t){j.error(\"Error capturing \"+i+\" pageview\",t)}}nt(){if(!this.rt){var i=()=>{this.st(\"popstate\")};st(t,\"popstate\",i),this.rt=()=>{t&&t.removeEventListener(\"popstate\",i)}}}}function Ne(t){var i,e;return(null==(i=JSON.stringify(t,(e=[],function(t,i){if(I(i)){for(;e.length>0&&e[e.length-1]!==this;)e.pop();return e.includes(i)?\"[Circular]\":(e.push(i),i)}return i})))?void 0:i.length)||0}function je(t,i){if(void 0===i&&(i=6606028.8),t.size>=i&&t.data.length>1){var e=Math.floor(t.data.length/2),r=t.data.slice(0,e),s=t.data.slice(e);return[je({size:Ne(r),data:r,sessionId:t.sessionId,windowId:t.windowId}),je({size:Ne(s),data:s,sessionId:t.sessionId,windowId:t.windowId})].flatMap((t=>t))}return[t]}var ze=(t=>(t[t.DomContentLoaded=0]=\"DomContentLoaded\",t[t.Load=1]=\"Load\",t[t.FullSnapshot=2]=\"FullSnapshot\",t[t.IncrementalSnapshot=3]=\"IncrementalSnapshot\",t[t.Meta=4]=\"Meta\",t[t.Custom=5]=\"Custom\",t[t.Plugin=6]=\"Plugin\",t))(ze||{}),Ue=(t=>(t[t.Mutation=0]=\"Mutation\",t[t.MouseMove=1]=\"MouseMove\",t[t.MouseInteraction=2]=\"MouseInteraction\",t[t.Scroll=3]=\"Scroll\",t[t.ViewportResize=4]=\"ViewportResize\",t[t.Input=5]=\"Input\",t[t.TouchMove=6]=\"TouchMove\",t[t.MediaInteraction=7]=\"MediaInteraction\",t[t.StyleSheetRule=8]=\"StyleSheetRule\",t[t.CanvasMutation=9]=\"CanvasMutation\",t[t.Font=10]=\"Font\",t[t.Log=11]=\"Log\",t[t.Drag=12]=\"Drag\",t[t.StyleDeclaration=13]=\"StyleDeclaration\",t[t.Selection=14]=\"Selection\",t[t.AdoptedStyleSheet=15]=\"AdoptedStyleSheet\",t[t.CustomElement=16]=\"CustomElement\",t))(Ue||{}),qe=\"[SessionRecording]\",Be=\"redacted\",He={initiatorTypes:[\"audio\",\"beacon\",\"body\",\"css\",\"early-hint\",\"embed\",\"fetch\",\"frame\",\"iframe\",\"icon\",\"image\",\"img\",\"input\",\"link\",\"navigation\",\"object\",\"ping\",\"script\",\"track\",\"video\",\"xmlhttprequest\"],maskRequestFn:t=>t,recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:[\"first-input\",\"navigation\",\"paint\",\"resource\"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[\".lr-ingest.io\",\".ingest.sentry.io\",\".clarity.ms\",\"analytics.google.com\",\"bam.nr-data.net\"]},We=[\"authorization\",\"x-forwarded-for\",\"authorization\",\"cookie\",\"set-cookie\",\"x-api-key\",\"x-real-ip\",\"remote-addr\",\"forwarded\",\"proxy-authorization\",\"x-csrf-token\",\"x-csrftoken\",\"x-xsrf-token\"],Ge=[\"password\",\"secret\",\"passwd\",\"api_key\",\"apikey\",\"auth\",\"credentials\",\"mysql_pwd\",\"privatekey\",\"private_key\",\"token\"],Je=[\"/s/\",\"/e/\",\"/i/\"];function Ve(t,i,e,r){if(O(t))return t;var s=(null==i?void 0:i[\"content-length\"])||function(t){return new Blob([t]).size}(t);return T(s)&&(s=parseInt(s)),s>e?qe+\" \"+r+\" body too large to record (\"+s+\" bytes)\":t}function Ke(t,i){if(O(t))return t;var e=t;return ci(e,!1)||(e=qe+\" \"+i+\" body \"+Be),J(Ge,(t=>{var r,s;null!=(r=e)&&r.length&&-1!==(null==(s=e)?void 0:s.indexOf(t))&&(e=qe+\" \"+i+\" body \"+Be+\" as might contain: \"+t)})),e}var Ye=(t,i)=>{var e,r,s,n={payloadSizeLimitBytes:He.payloadSizeLimitBytes,performanceEntryTypeToObserve:[...He.performanceEntryTypeToObserve],payloadHostDenyList:[...i.payloadHostDenyList||[],...He.payloadHostDenyList]},o=!1!==t.session_recording.recordHeaders&&i.recordHeaders,a=!1!==t.session_recording.recordBody&&i.recordBody,l=!1!==t.capture_performance&&i.recordPerformance,u=(e=n,s=Math.min(1e6,null!==(r=e.payloadSizeLimitBytes)&&void 0!==r?r:1e6),t=>(null!=t&&t.requestBody&&(t.requestBody=Ve(t.requestBody,t.requestHeaders,s,\"Request\")),null!=t&&t.responseBody&&(t.responseBody=Ve(t.responseBody,t.responseHeaders,s,\"Response\")),t)),h=i=>{return u(((t,i)=>{var e,r=yi(t.name),s=0===i.indexOf(\"http\")?null==(e=yi(i))?void 0:e.pathname:i;\"/\"===s&&(s=\"\");var n=null==r?void 0:r.pathname.replace(s||\"\",\"\");if(!(r&&n&&Je.some((t=>0===n.indexOf(t)))))return t})((r=(e=i).requestHeaders,O(r)||J(Object.keys(null!=r?r:{}),(t=>{We.includes(t.toLowerCase())&&(r[t]=Be)})),e),t.api_host));var e,r},d=E(t.session_recording.maskNetworkRequestFn);return d&&E(t.session_recording.maskCapturedNetworkRequestFn)&&j.warn(\"Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored.\"),d&&(t.session_recording.maskCapturedNetworkRequestFn=i=>{var e=t.session_recording.maskNetworkRequestFn({url:i.name});return B({},i,{name:null==e?void 0:e.url})}),n.maskRequestFn=E(t.session_recording.maskCapturedNetworkRequestFn)?i=>{var e,r=h(i);return r&&null!==(e=null==t.session_recording.maskCapturedNetworkRequestFn?void 0:t.session_recording.maskCapturedNetworkRequestFn(r))&&void 0!==e?e:void 0}:t=>function(t){if(!R(t))return t.requestBody=Ke(t.requestBody,\"Request\"),t.responseBody=Ke(t.responseBody,\"Response\"),t}(h(t)),B({},He,n,{recordHeaders:o,recordBody:a,recordPerformance:l,recordInitialRequests:l})};function Xe(t,i,e,r,s){return i>e&&(j.warn(\"min cannot be greater than max.\"),i=e),F(t)?t>e?(r&&j.warn(r+\" cannot be  greater than max: \"+e+\". Using max value instead.\"),e):t<i?(r&&j.warn(r+\" cannot be less than min: \"+i+\". Using min value instead.\"),i):t:(r&&j.warn(r+\" must be a number. using max or fallback. max: \"+e+\", fallback: \"+s),Xe(s||e,i,e,r))}class Qe{constructor(t,i){var e,r;void 0===i&&(i={}),this.ot=100,this.lt=10,this.ut={},this.ht={},this.dt=()=>{Object.keys(this.ut).forEach((t=>{this.ut[t]=this.ut[t]+this.lt,this.ut[t]>=this.ot&&delete this.ut[t]}))},this.vt=t=>{var i=this._rrweb.mirror.getNode(t);if(\"svg\"!==(null==i?void 0:i.nodeName)&&i instanceof Element){var e=i.closest(\"svg\");if(e)return[this._rrweb.mirror.getId(e),e]}return[t,i]},this.ct=t=>{var i,e,r,s,n,o,a,l;return(null!==(i=null==(e=t.removes)?void 0:e.length)&&void 0!==i?i:0)+(null!==(r=null==(s=t.attributes)?void 0:s.length)&&void 0!==r?r:0)+(null!==(n=null==(o=t.texts)?void 0:o.length)&&void 0!==n?n:0)+(null!==(a=null==(l=t.adds)?void 0:l.length)&&void 0!==a?a:0)},this.throttleMutations=t=>{if(3!==t.type||0!==t.data.source)return t;var i=t.data,e=this.ct(i);i.attributes&&(i.attributes=i.attributes.filter((t=>{var i,e,r,[s,n]=this.vt(t.id);if(0===this.ut[s])return!1;(this.ut[s]=null!==(i=this.ut[s])&&void 0!==i?i:this.ot,this.ut[s]=Math.max(this.ut[s]-1,0),0===this.ut[s])&&(this.ht[s]||(this.ht[s]=!0,null==(e=(r=this.ft).onBlockedNode)||e.call(r,s,n)));return t})));var r=this.ct(i);return 0!==r||e===r?t:void 0},this._rrweb=t,this.ft=i,this.lt=Xe(null!==(e=this.ft.refillRate)&&void 0!==e?e:this.lt,0,100,\"mutation throttling refill rate\"),this.ot=Xe(null!==(r=this.ft.bucketSize)&&void 0!==r?r:this.ot,0,100,\"mutation throttling bucket size\"),setInterval((()=>{this.dt()}),1e3)}}var Ze=Uint8Array,tr=Uint16Array,ir=Uint32Array,er=new Ze([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),rr=new Ze([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),sr=new Ze([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),nr=function(t,i){for(var e=new tr(31),r=0;r<31;++r)e[r]=i+=1<<t[r-1];var s=new ir(e[30]);for(r=1;r<30;++r)for(var n=e[r];n<e[r+1];++n)s[n]=n-e[r]<<5|r;return[e,s]},or=nr(er,2),ar=or[0],lr=or[1];ar[28]=258,lr[258]=28;for(var ur=nr(rr,0)[1],hr=new tr(32768),dr=0;dr<32768;++dr){var vr=(43690&dr)>>>1|(21845&dr)<<1;vr=(61680&(vr=(52428&vr)>>>2|(13107&vr)<<2))>>>4|(3855&vr)<<4,hr[dr]=((65280&vr)>>>8|(255&vr)<<8)>>>1}var cr=function(t,i,e){for(var r=t.length,s=0,n=new tr(i);s<r;++s)++n[t[s]-1];var o,a=new tr(i);for(s=0;s<i;++s)a[s]=a[s-1]+n[s-1]<<1;if(e){o=new tr(1<<i);var l=15-i;for(s=0;s<r;++s)if(t[s])for(var u=s<<4|t[s],h=i-t[s],d=a[t[s]-1]++<<h,v=d|(1<<h)-1;d<=v;++d)o[hr[d]>>>l]=u}else for(o=new tr(r),s=0;s<r;++s)o[s]=hr[a[t[s]-1]++]>>>15-t[s];return o},fr=new Ze(288);for(dr=0;dr<144;++dr)fr[dr]=8;for(dr=144;dr<256;++dr)fr[dr]=9;for(dr=256;dr<280;++dr)fr[dr]=7;for(dr=280;dr<288;++dr)fr[dr]=8;var pr=new Ze(32);for(dr=0;dr<32;++dr)pr[dr]=5;var gr=cr(fr,9,0),_r=cr(pr,5,0),mr=function(t){return(t/8>>0)+(7&t&&1)},br=function(t,i,e){(null==e||e>t.length)&&(e=t.length);var r=new(t instanceof tr?tr:t instanceof ir?ir:Ze)(e-i);return r.set(t.subarray(i,e)),r},wr=function(t,i,e){e<<=7&i;var r=i/8>>0;t[r]|=e,t[r+1]|=e>>>8},yr=function(t,i,e){e<<=7&i;var r=i/8>>0;t[r]|=e,t[r+1]|=e>>>8,t[r+2]|=e>>>16},Sr=function(t,i){for(var e=[],r=0;r<t.length;++r)t[r]&&e.push({s:r,f:t[r]});var s=e.length,n=e.slice();if(!s)return[new Ze(0),0];if(1==s){var o=new Ze(e[0].s+1);return o[e[0].s]=1,[o,1]}e.sort((function(t,i){return t.f-i.f})),e.push({s:-1,f:25001});var a=e[0],l=e[1],u=0,h=1,d=2;for(e[0]={s:-1,f:a.f+l.f,l:a,r:l};h!=s-1;)a=e[e[u].f<e[d].f?u++:d++],l=e[u!=h&&e[u].f<e[d].f?u++:d++],e[h++]={s:-1,f:a.f+l.f,l:a,r:l};var v=n[0].s;for(r=1;r<s;++r)n[r].s>v&&(v=n[r].s);var c=new tr(v+1),f=$r(e[h-1],c,0);if(f>i){r=0;var p=0,g=f-i,_=1<<g;for(n.sort((function(t,i){return c[i.s]-c[t.s]||t.f-i.f}));r<s;++r){var m=n[r].s;if(!(c[m]>i))break;p+=_-(1<<f-c[m]),c[m]=i}for(p>>>=g;p>0;){var b=n[r].s;c[b]<i?p-=1<<i-c[b]++-1:++r}for(;r>=0&&p;--r){var w=n[r].s;c[w]==i&&(--c[w],++p)}f=i}return[new Ze(c),f]},$r=function(t,i,e){return-1==t.s?Math.max($r(t.l,i,e+1),$r(t.r,i,e+1)):i[t.s]=e},kr=function(t){for(var i=t.length;i&&!t[--i];);for(var e=new tr(++i),r=0,s=t[0],n=1,o=function(t){e[r++]=t},a=1;a<=i;++a)if(t[a]==s&&a!=i)++n;else{if(!s&&n>2){for(;n>138;n-=138)o(32754);n>2&&(o(n>10?n-11<<5|28690:n-3<<5|12305),n=0)}else if(n>3){for(o(s),--n;n>6;n-=6)o(8304);n>2&&(o(n-3<<5|8208),n=0)}for(;n--;)o(s);n=1,s=t[a]}return[e.subarray(0,r),i]},xr=function(t,i){for(var e=0,r=0;r<i.length;++r)e+=t[r]*i[r];return e},Er=function(t,i,e){var r=e.length,s=mr(i+2);t[s]=255&r,t[s+1]=r>>>8,t[s+2]=255^t[s],t[s+3]=255^t[s+1];for(var n=0;n<r;++n)t[s+n+4]=e[n];return 8*(s+4+r)},Ir=function(t,i,e,r,s,n,o,a,l,u,h){wr(i,h++,e),++s[256];for(var d=Sr(s,15),v=d[0],c=d[1],f=Sr(n,15),p=f[0],g=f[1],_=kr(v),m=_[0],b=_[1],w=kr(p),y=w[0],S=w[1],$=new tr(19),k=0;k<m.length;++k)$[31&m[k]]++;for(k=0;k<y.length;++k)$[31&y[k]]++;for(var x=Sr($,7),E=x[0],I=x[1],P=19;P>4&&!E[sr[P-1]];--P);var R,T,M,C,O=u+5<<3,F=xr(s,fr)+xr(n,pr)+o,A=xr(s,v)+xr(n,p)+o+14+3*P+xr($,E)+(2*$[16]+3*$[17]+7*$[18]);if(O<=F&&O<=A)return Er(i,h,t.subarray(l,l+u));if(wr(i,h,1+(A<F)),h+=2,A<F){R=cr(v,c,0),T=v,M=cr(p,g,0),C=p;var D=cr(E,I,0);wr(i,h,b-257),wr(i,h+5,S-1),wr(i,h+10,P-4),h+=14;for(k=0;k<P;++k)wr(i,h+3*k,E[sr[k]]);h+=3*P;for(var L=[m,y],N=0;N<2;++N){var j=L[N];for(k=0;k<j.length;++k){var z=31&j[k];wr(i,h,D[z]),h+=E[z],z>15&&(wr(i,h,j[k]>>>5&127),h+=j[k]>>>12)}}}else R=gr,T=fr,M=_r,C=pr;for(k=0;k<a;++k)if(r[k]>255){z=r[k]>>>18&31;yr(i,h,R[z+257]),h+=T[z+257],z>7&&(wr(i,h,r[k]>>>23&31),h+=er[z]);var U=31&r[k];yr(i,h,M[U]),h+=C[U],U>3&&(yr(i,h,r[k]>>>5&8191),h+=rr[U])}else yr(i,h,R[r[k]]),h+=T[r[k]];return yr(i,h,R[256]),h+T[256]},Pr=new ir([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Rr=function(){for(var t=new ir(256),i=0;i<256;++i){for(var e=i,r=9;--r;)e=(1&e&&3988292384)^e>>>1;t[i]=e}return t}(),Tr=function(){var t=4294967295;return{p:function(i){for(var e=t,r=0;r<i.length;++r)e=Rr[255&e^i[r]]^e>>>8;t=e},d:function(){return 4294967295^t}}},Mr=function(t,i,e,r,s){return function(t,i,e,r,s,n){var o=t.length,a=new Ze(r+o+5*(1+Math.floor(o/7e3))+s),l=a.subarray(r,a.length-s),u=0;if(!i||o<8)for(var h=0;h<=o;h+=65535){var d=h+65535;d<o?u=Er(l,u,t.subarray(h,d)):(l[h]=n,u=Er(l,u,t.subarray(h,o)))}else{for(var v=Pr[i-1],c=v>>>13,f=8191&v,p=(1<<e)-1,g=new tr(32768),_=new tr(p+1),m=Math.ceil(e/3),b=2*m,w=function(i){return(t[i]^t[i+1]<<m^t[i+2]<<b)&p},y=new ir(25e3),S=new tr(288),$=new tr(32),k=0,x=0,E=(h=0,0),I=0,P=0;h<o;++h){var R=w(h),T=32767&h,M=_[R];if(g[T]=M,_[R]=T,I<=h){var C=o-h;if((k>7e3||E>24576)&&C>423){u=Ir(t,l,0,y,S,$,x,E,P,h-P,u),E=k=x=0,P=h;for(var O=0;O<286;++O)S[O]=0;for(O=0;O<30;++O)$[O]=0}var F=2,A=0,D=f,L=T-M&32767;if(C>2&&R==w(h-L))for(var N=Math.min(c,C)-1,j=Math.min(32767,h),z=Math.min(258,C);L<=j&&--D&&T!=M;){if(t[h+F]==t[h+F-L]){for(var U=0;U<z&&t[h+U]==t[h+U-L];++U);if(U>F){if(F=U,A=L,U>N)break;var q=Math.min(L,U-2),B=0;for(O=0;O<q;++O){var H=h-L+O+32768&32767,W=H-g[H]+32768&32767;W>B&&(B=W,M=H)}}}L+=(T=M)-(M=g[T])+32768&32767}if(A){y[E++]=268435456|lr[F]<<18|ur[A];var G=31&lr[F],J=31&ur[A];x+=er[G]+rr[J],++S[257+G],++$[J],I=h+F,++k}else y[E++]=t[h],++S[t[h]]}}u=Ir(t,l,n,y,S,$,x,E,P,h-P,u)}return br(a,0,r+mr(u)+s)}(t,null==i.level?6:i.level,null==i.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(t.length)))):12+i.mem,e,r,!s)},Cr=function(t,i,e){for(;e;++i)t[i]=e,e>>>=8},Or=function(t,i){var e=i.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=i.level<2?4:9==i.level?2:0,t[9]=3,0!=i.mtime&&Cr(t,4,Math.floor(new Date(i.mtime||Date.now())/1e3)),e){t[3]=8;for(var r=0;r<=e.length;++r)t[r+10]=e.charCodeAt(r)}},Fr=function(t){return 10+(t.filename&&t.filename.length+1||0)};function Ar(t,i){void 0===i&&(i={});var e=Tr(),r=t.length;e.p(t);var s=Mr(t,i,Fr(i),8),n=s.length;return Or(s,i),Cr(s,n-8,e.d()),Cr(s,n-4,r),s}function Dr(t,i){var e=t.length;if(\"undefined\"!=typeof TextEncoder)return(new TextEncoder).encode(t);for(var r=new Ze(t.length+(t.length>>>1)),s=0,n=function(t){r[s++]=t},o=0;o<e;++o){if(s+5>r.length){var a=new Ze(s+8+(e-o<<1));a.set(r),r=a}var l=t.charCodeAt(o);l<128||i?n(l):l<2048?(n(192|l>>>6),n(128|63&l)):l>55295&&l<57344?(n(240|(l=65536+(1047552&l)|1023&t.charCodeAt(++o))>>>18),n(128|l>>>12&63),n(128|l>>>6&63),n(128|63&l)):(n(224|l>>>12),n(128|l>>>6&63),n(128|63&l))}return br(r,0,s)}function Lr(t,i){return function(t){for(var i=0,e=0;e<t.length;e++)i=(i<<5)-i+t.charCodeAt(e),i|=0;return Math.abs(i)}(t)%100<Xe(100*i,0,100)}var Nr=\"disabled\",jr=\"sampled\",zr=\"active\",Ur=\"buffering\",qr=\"paused\",Br=\"trigger\",Hr=Br+\"_activated\",Wr=Br+\"_pending\",Gr=Br+\"_\"+Nr;function Jr(t,i){return i.some((i=>\"regex\"===i.matching&&new RegExp(i.url).test(t)))}class Vr{constructor(t){this.gt=t}triggerStatus(t){var i=this.gt.map((i=>i.triggerStatus(t)));return i.includes(Hr)?Hr:i.includes(Wr)?Wr:Gr}stop(){this.gt.forEach((t=>t.stop()))}}class Kr{constructor(t){this.gt=t}triggerStatus(t){var i=new Set;for(var e of this.gt)i.add(e.triggerStatus(t));switch(i.delete(Gr),i.size){case 0:return Gr;case 1:return Array.from(i)[0];default:return Wr}}stop(){this.gt.forEach((t=>t.stop()))}}class Yr{triggerStatus(){return Wr}stop(){}}class Xr{constructor(t){this._t=[],this.bt=[],this.urlBlocked=!1,this._instance=t}onRemoteConfig(t){var i,e;this._t=(null==(i=t.sessionRecording)?void 0:i.urlTriggers)||[],this.bt=(null==(e=t.sessionRecording)?void 0:e.urlBlocklist)||[]}wt(t){var i;return 0===this._t.length?Gr:(null==(i=this._instance)?void 0:i.get_property(xt))===t?Hr:Wr}triggerStatus(t){var i=this.wt(t),e=i===Hr?Hr:i===Wr?Wr:Gr;return this._instance.register_for_session({$sdk_debug_replay_url_trigger_status:e}),e}checkUrlTriggerConditions(i,e,r){if(void 0!==t&&t.location.href){var s=t.location.href,n=this.urlBlocked,o=Jr(s,this.bt);n&&o||(o&&!n?i():!o&&n&&e(),Jr(s,this._t)&&r(\"url\"))}}stop(){}}class Qr{constructor(t){this.linkedFlag=null,this.linkedFlagSeen=!1,this.yt=()=>{},this._instance=t}triggerStatus(){var t=Wr;return O(this.linkedFlag)&&(t=Gr),this.linkedFlagSeen&&(t=Hr),this._instance.register_for_session({$sdk_debug_replay_linked_flag_trigger_status:t}),t}onRemoteConfig(t,i){var e;if(this.linkedFlag=(null==(e=t.sessionRecording)?void 0:e.linkedFlag)||null,!O(this.linkedFlag)&&!this.linkedFlagSeen){var r=T(this.linkedFlag)?this.linkedFlag:this.linkedFlag.flag,s=T(this.linkedFlag)?null:this.linkedFlag.variant;this.yt=this._instance.onFeatureFlags(((t,e)=>{var n=!1;if(I(e)&&r in e){var o=e[r];n=A(o)?!0===o:s?o===s:!!o}this.linkedFlagSeen=n,n&&i(r,s)}))}}stop(){this.yt()}}class Zr{constructor(t){this.St=[],this._instance=t}onRemoteConfig(t){var i;this.St=(null==(i=t.sessionRecording)?void 0:i.eventTriggers)||[]}$t(t){var i;return 0===this.St.length?Gr:(null==(i=this._instance)?void 0:i.get_property(Et))===t?Hr:Wr}triggerStatus(t){var i=this.$t(t),e=i===Hr?Hr:i===Wr?Wr:Gr;return this._instance.register_for_session({$sdk_debug_replay_event_trigger_status:e}),e}stop(){}}function ts(t){return t.isRecordingEnabled?Ur:Nr}function is(t){if(!t.receivedDecide)return Ur;if(!t.isRecordingEnabled)return Nr;if(t.urlTriggerMatching.urlBlocked)return qr;var i=!0===t.isSampled,e=new Vr([t.eventTriggerMatching,t.urlTriggerMatching,t.linkedFlagMatching]).triggerStatus(t.sessionId);return i?jr:e===Hr?zr:e===Wr?Ur:!1===t.isSampled?Nr:zr}function es(t){if(!t.receivedDecide)return Ur;if(!t.isRecordingEnabled)return Nr;if(t.urlTriggerMatching.urlBlocked)return qr;var i=new Kr([t.eventTriggerMatching,t.urlTriggerMatching,t.linkedFlagMatching]).triggerStatus(t.sessionId),e=i!==Gr,r=A(t.isSampled);return e&&i===Wr?Ur:e&&i===Gr||r&&!t.isSampled?Nr:!0===t.isSampled?jr:zr}var rs=\"[SessionRecording]\",ss=z(rs);function ns(){var t;return null==v||null==(t=v.__PosthogExtensions__)||null==(t=t.rrweb)?void 0:t.record}var os=3e5,as=[Ue.MouseMove,Ue.MouseInteraction,Ue.Scroll,Ue.ViewportResize,Ue.Input,Ue.TouchMove,Ue.MediaInteraction,Ue.Drag],ls=t=>({rrwebMethod:t,enqueuedAt:Date.now(),attempt:1});function us(t){return function(t,i){for(var e=\"\",r=0;r<t.length;){var s=t[r++];s<128||i?e+=String.fromCharCode(s):s<224?e+=String.fromCharCode((31&s)<<6|63&t[r++]):s<240?e+=String.fromCharCode((15&s)<<12|(63&t[r++])<<6|63&t[r++]):(s=((15&s)<<18|(63&t[r++])<<12|(63&t[r++])<<6|63&t[r++])-65536,e+=String.fromCharCode(55296|s>>10,56320|1023&s))}return e}(Ar(Dr(JSON.stringify(t))),!0)}function hs(t){return t.type===ze.Custom&&\"sessionIdle\"===t.data.tag}class ds{get sessionId(){return this.kt}get xt(){return this._instance.config.session_recording.session_idle_threshold_ms||3e5}get started(){return this.Et}get It(){if(!this._instance.sessionManager)throw new Error(rs+\" must be started with a valid sessionManager.\");return this._instance.sessionManager}get Pt(){var t,i;return this.Rt.triggerStatus(this.sessionId)===Wr?6e4:null!==(t=null==(i=this._instance.config.session_recording)?void 0:i.full_snapshot_interval_millis)&&void 0!==t?t:os}get Tt(){var t=this._instance.get_property(kt);return A(t)?t:null}get Mt(){var t,i,e=null==(t=this.M)?void 0:t.data[(null==(i=this.M)?void 0:i.data.length)-1],{sessionStartTimestamp:r}=this.It.checkAndGetSessionAndWindowId(!0);return e?e.timestamp-r:null}get Ct(){var i=!!this._instance.get_property(pt),e=!this._instance.config.disable_session_recording;return t&&i&&e}get Ot(){var t=!!this._instance.get_property(gt),i=this._instance.config.enable_recording_console_log;return null!=i?i:t}get Ft(){var t,i,e,r,s,n,o=this._instance.config.session_recording.captureCanvas,a=this._instance.get_property(bt),l=null!==(t=null!==(i=null==o?void 0:o.recordCanvas)&&void 0!==i?i:null==a?void 0:a.enabled)&&void 0!==t&&t,u=null!==(e=null!==(r=null==o?void 0:o.canvasFps)&&void 0!==r?r:null==a?void 0:a.fps)&&void 0!==e?e:4,h=null!==(s=null!==(n=null==o?void 0:o.canvasQuality)&&void 0!==n?n:null==a?void 0:a.quality)&&void 0!==s?s:.4;if(\"string\"==typeof h){var d=parseFloat(h);h=isNaN(d)?.4:d}return{enabled:l,fps:Xe(u,0,12,\"canvas recording fps\",4),quality:Xe(h,0,1,\"canvas recording quality\",.4)}}get At(){var t,i,e=this._instance.get_property(_t),r={recordHeaders:null==(t=this._instance.config.session_recording)?void 0:t.recordHeaders,recordBody:null==(i=this._instance.config.session_recording)?void 0:i.recordBody},s=(null==r?void 0:r.recordHeaders)||(null==e?void 0:e.recordHeaders),n=(null==r?void 0:r.recordBody)||(null==e?void 0:e.recordBody),o=I(this._instance.config.capture_performance)?this._instance.config.capture_performance.network_timing:this._instance.config.capture_performance,a=!!(A(o)?o:null==e?void 0:e.capturePerformance);return s||n||a?{recordHeaders:s,recordBody:n,recordPerformance:a}:void 0}get Dt(){var t,i,e,r,s,n,o=this._instance.get_property(mt),a={maskAllInputs:null==(t=this._instance.config.session_recording)?void 0:t.maskAllInputs,maskTextSelector:null==(i=this._instance.config.session_recording)?void 0:i.maskTextSelector,blockSelector:null==(e=this._instance.config.session_recording)?void 0:e.blockSelector},l=null!==(r=null==a?void 0:a.maskAllInputs)&&void 0!==r?r:null==o?void 0:o.maskAllInputs,u=null!==(s=null==a?void 0:a.maskTextSelector)&&void 0!==s?s:null==o?void 0:o.maskTextSelector,h=null!==(n=null==a?void 0:a.blockSelector)&&void 0!==n?n:null==o?void 0:o.blockSelector;return R(l)&&R(u)&&R(h)?void 0:{maskAllInputs:null==l||l,maskTextSelector:u,blockSelector:h}}get Lt(){var t=this._instance.get_property(wt);return F(t)?t:null}get Nt(){var t=this._instance.get_property(yt);return F(t)?t:null}get status(){return this.jt?this.zt({receivedDecide:this.jt,isRecordingEnabled:this.Ct,isSampled:this.Tt,urlTriggerMatching:this.Ut,eventTriggerMatching:this.qt,linkedFlagMatching:this.Bt,sessionId:this.sessionId}):Ur}constructor(t){if(this.zt=ts,this.jt=!1,this.Ht=[],this.Wt=\"unknown\",this.Gt=Date.now(),this.Rt=new Yr,this.Jt=void 0,this.Vt=void 0,this.Kt=void 0,this.Yt=void 0,this.Xt=void 0,this._forceAllowLocalhostNetworkCapture=!1,this.Qt=()=>{this.Zt()},this.ti=()=>{this.ii(\"browser offline\",{})},this.ei=()=>{this.ii(\"browser online\",{})},this.ri=()=>{if(null!=o&&o.visibilityState){var t=\"window \"+o.visibilityState;this.ii(t,{})}},this._instance=t,this.Et=!1,this.si=\"/s/\",this.ni=void 0,this.jt=!1,!this._instance.sessionManager)throw ss.error(\"started without valid sessionManager\"),new Error(rs+\" started without valid sessionManager. This is a bug.\");if(this._instance.config.__preview_experimental_cookieless_mode)throw new Error(rs+\" cannot be used with __preview_experimental_cookieless_mode.\");this.Bt=new Qr(this._instance),this.Ut=new Xr(this._instance),this.qt=new Zr(this._instance);var{sessionId:i,windowId:e}=this.It.checkAndGetSessionAndWindowId();this.kt=i,this.oi=e,this.M=this.ai(),this.xt>=this.It.sessionTimeoutMs&&ss.warn(\"session_idle_threshold_ms (\"+this.xt+\") is greater than the session timeout (\"+this.It.sessionTimeoutMs+\"). Session will never be detected as idle\")}startIfEnabledOrStop(i){this.Ct?(this.li(i),st(t,\"beforeunload\",this.Qt),st(t,\"offline\",this.ti),st(t,\"online\",this.ei),st(t,\"visibilitychange\",this.ri),this.ui(),this.hi(),O(this.Jt)&&(this.Jt=this._instance.on(\"eventCaptured\",(t=>{try{if(\"$pageview\"===t.event){var i=null!=t&&t.properties.$current_url?this.di(null==t?void 0:t.properties.$current_url):\"\";if(!i)return;this.ii(\"$pageview\",{href:i})}}catch(t){ss.error(\"Could not add $pageview to rrweb session\",t)}}))),this.Vt||(this.Vt=this.It.onSessionId(((t,i,e)=>{var r,s;e&&(this.ii(\"$session_id_change\",{sessionId:t,windowId:i,changeReason:e}),null==(r=this._instance)||null==(r=r.persistence)||r.unregister(Et),null==(s=this._instance)||null==(s=s.persistence)||s.unregister(xt))})))):this.stopRecording()}stopRecording(){var i,e,r,s;this.Et&&this.ni&&(this.ni(),this.ni=void 0,this.Et=!1,null==t||t.removeEventListener(\"beforeunload\",this.Qt),null==t||t.removeEventListener(\"offline\",this.ti),null==t||t.removeEventListener(\"online\",this.ei),null==t||t.removeEventListener(\"visibilitychange\",this.ri),this.ai(),clearInterval(this.vi),null==(i=this.Jt)||i.call(this),this.Jt=void 0,null==(e=this.Xt)||e.call(this),this.Xt=void 0,null==(r=this.Vt)||r.call(this),this.Vt=void 0,null==(s=this.Yt)||s.call(this),this.Yt=void 0,this.qt.stop(),this.Ut.stop(),this.Bt.stop(),ss.info(\"stopped\"))}ci(){var t;null==(t=this._instance.persistence)||t.unregister(kt)}fi(t){var i,e=this.kt!==t,r=this.Lt;if(F(r)){var s=this.Tt,n=e||!A(s),o=n?Lr(t,r):s;n&&(o?this.pi(jr):ss.warn(\"Sample rate (\"+r+\") has determined that this sessionId (\"+t+\") will not be sent to the server.\"),this.ii(\"samplingDecisionMade\",{sampleRate:r,isSampled:o})),null==(i=this._instance.persistence)||i.register({[kt]:o})}else this.ci()}onRemoteConfig(t){var i,e,r,s;(this.ii(\"$remote_config_received\",t),this.gi(t),null!=(i=t.sessionRecording)&&i.endpoint)&&(this.si=null==(s=t.sessionRecording)?void 0:s.endpoint);this.ui(),\"any\"===(null==(e=t.sessionRecording)?void 0:e.triggerMatchType)?(this.zt=is,this.Rt=new Vr([this.qt,this.Ut])):(this.zt=es,this.Rt=new Kr([this.qt,this.Ut])),this._instance.register_for_session({$sdk_debug_replay_remote_trigger_matching_config:null==(r=t.sessionRecording)?void 0:r.triggerMatchType}),this.Ut.onRemoteConfig(t),this.qt.onRemoteConfig(t),this.Bt.onRemoteConfig(t,((t,i)=>{this.pi(\"linked_flag_matched\",{flag:t,variant:i})})),this.jt=!0,this.startIfEnabledOrStop()}ui(){F(this.Lt)&&O(this.Yt)&&(this.Yt=this.It.onSessionId((t=>{this.fi(t)})))}gi(t){if(this._instance.persistence){var i,e=this._instance.persistence,r=()=>{var i,r,s,n,o,a,l,u,h,d=null==(i=t.sessionRecording)?void 0:i.sampleRate,v=O(d)?null:parseFloat(d);O(v)&&this.ci();var c=null==(r=t.sessionRecording)?void 0:r.minimumDurationMilliseconds;e.register({[pt]:!!t.sessionRecording,[gt]:null==(s=t.sessionRecording)?void 0:s.consoleLogRecordingEnabled,[_t]:B({capturePerformance:t.capturePerformance},null==(n=t.sessionRecording)?void 0:n.networkPayloadCapture),[mt]:null==(o=t.sessionRecording)?void 0:o.masking,[bt]:{enabled:null==(a=t.sessionRecording)?void 0:a.recordCanvas,fps:null==(l=t.sessionRecording)?void 0:l.canvasFps,quality:null==(u=t.sessionRecording)?void 0:u.canvasQuality},[wt]:v,[yt]:R(c)?null:c,[St]:null==(h=t.sessionRecording)?void 0:h.scriptConfig})};r(),null==(i=this.Kt)||i.call(this),this.Kt=this.It.onSessionId(r)}}log(t,i){var e;void 0===i&&(i=\"log\"),null==(e=this._instance.sessionRecording)||e.onRRwebEmit({type:6,data:{plugin:\"rrweb/console@1\",payload:{level:i,trace:[],payload:[JSON.stringify(t)]}},timestamp:Date.now()})}li(t){if(!R(Object.assign)&&!R(Array.from)&&!(this.Et||this._instance.config.disable_session_recording||this._instance.consent.isOptedOut())){var i;if(this.Et=!0,this.It.checkAndGetSessionAndWindowId(),ns())this.mi();else null==(i=v.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,this.bi,(t=>{if(t)return ss.error(\"could not load recorder\",t);this.mi()}));ss.info(\"starting\"),this.status===zr&&this.pi(t||\"recording_initialized\")}}get bi(){var t;return(null==(t=this._instance)||null==(t=t.persistence)||null==(t=t.get_property(St))?void 0:t.script)||\"recorder\"}wi(t){var i;return 3===t.type&&-1!==as.indexOf(null==(i=t.data)?void 0:i.source)}yi(t){var i=this.wi(t);i||this.Wt||t.timestamp-this.Gt>this.xt&&(this.Wt=!0,clearInterval(this.vi),this.ii(\"sessionIdle\",{eventTimestamp:t.timestamp,lastActivityTimestamp:this.Gt,threshold:this.xt,bufferLength:this.M.data.length,bufferSize:this.M.size}),this.Zt());var e=!1;if(i&&(this.Gt=t.timestamp,this.Wt)){var r=\"unknown\"===this.Wt;this.Wt=!1,r||(this.ii(\"sessionNoLongerIdle\",{reason:\"user activity\",type:t.type}),e=!0)}if(!this.Wt){var{windowId:s,sessionId:n}=this.It.checkAndGetSessionAndWindowId(!i,t.timestamp),o=this.kt!==n,a=this.oi!==s;this.oi=s,this.kt=n,o||a?(this.stopRecording(),this.startIfEnabledOrStop(\"session_id_changed\")):e&&this.Si()}}$i(t){try{return t.rrwebMethod(),!0}catch(i){return this.Ht.length<10?this.Ht.push({enqueuedAt:t.enqueuedAt||Date.now(),attempt:t.attempt++,rrwebMethod:t.rrwebMethod}):ss.warn(\"could not emit queued rrweb event.\",i,t),!1}}ii(t,i){return this.$i(ls((()=>ns().addCustomEvent(t,i))))}ki(){return this.$i(ls((()=>ns().takeFullSnapshot())))}mi(){var t,i,e,r,s={blockClass:\"ph-no-capture\",blockSelector:void 0,ignoreClass:\"ph-ignore-input\",maskTextClass:\"ph-mask\",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1},n=this._instance.config.session_recording;for(var[o,a]of Object.entries(n||{}))o in s&&(\"maskInputOptions\"===o?s.maskInputOptions=B({password:!0},a):s[o]=a);(this.Ft&&this.Ft.enabled&&(s.recordCanvas=!0,s.sampling={canvas:this.Ft.fps},s.dataURLOptions={type:\"image/webp\",quality:this.Ft.quality}),this.Dt)&&(s.maskAllInputs=null===(i=this.Dt.maskAllInputs)||void 0===i||i,s.maskTextSelector=null!==(e=this.Dt.maskTextSelector)&&void 0!==e?e:void 0,s.blockSelector=null!==(r=this.Dt.blockSelector)&&void 0!==r?r:void 0);var l=ns();if(l){this.xi=null!==(t=this.xi)&&void 0!==t?t:new Qe(l,{refillRate:this._instance.config.session_recording.__mutationRateLimiterRefillRate,bucketSize:this._instance.config.session_recording.__mutationRateLimiterBucketSize,onBlockedNode:(t,i)=>{var e=\"Too many mutations on node '\"+t+\"'. Rate limiting. This could be due to SVG animations or something similar\";ss.info(e,{node:i}),this.log(rs+\" \"+e,\"warn\")}});var u=this.Ei();this.ni=l(B({emit:t=>{this.onRRwebEmit(t)},plugins:u},s)),this.Gt=Date.now(),this.Wt=A(this.Wt)?this.Wt:\"unknown\",this.ii(\"$session_options\",{sessionRecordingOptions:s,activePlugins:u.map((t=>null==t?void 0:t.name))}),this.ii(\"$posthog_config\",{config:this._instance.config})}else ss.error(\"onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.\")}Si(){if(this.vi&&clearInterval(this.vi),!0!==this.Wt){var t=this.Pt;t&&(this.vi=setInterval((()=>{this.ki()}),t))}}Ei(){var t,i,e=[],r=null==(t=v.__PosthogExtensions__)||null==(t=t.rrwebPlugins)?void 0:t.getRecordConsolePlugin;r&&this.Ot&&e.push(r());var s=null==(i=v.__PosthogExtensions__)||null==(i=i.rrwebPlugins)?void 0:i.getRecordNetworkPlugin;this.At&&E(s)&&(!wi.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?e.push(s(Ye(this._instance.config,this.At))):ss.info(\"NetworkCapture not started because we are on localhost.\"));return e}onRRwebEmit(t){var i;if(this.Ii(),t&&I(t)){if(t.type===ze.Meta){var e=this.di(t.data.href);if(this.Pi=e,!e)return;t.data.href=e}else this.Ri();if(this.Ut.checkUrlTriggerConditions((()=>this.Ti()),(()=>this.Mi()),(t=>this.Ci(t))),!this.Ut.urlBlocked||(r=t).type===ze.Custom&&\"recording paused\"===r.data.tag){var r;t.type===ze.FullSnapshot&&this.Si(),t.type===ze.FullSnapshot&&this.jt&&this.Rt.triggerStatus(this.sessionId)===Wr&&this.ai();var s=this.xi?this.xi.throttleMutations(t):t;if(s){var n=function(t){var i=t;if(i&&I(i)&&6===i.type&&I(i.data)&&\"rrweb/console@1\"===i.data.plugin){i.data.payload.payload.length>10&&(i.data.payload.payload=i.data.payload.payload.slice(0,10),i.data.payload.payload.push(\"...[truncated]\"));for(var e=[],r=0;r<i.data.payload.payload.length;r++)i.data.payload.payload[r]&&i.data.payload.payload[r].length>2e3?e.push(i.data.payload.payload[r].slice(0,2e3)+\"...[truncated]\"):e.push(i.data.payload.payload[r]);return i.data.payload.payload=e,t}return t}(s);if(this.yi(n),!0!==this.Wt||hs(n)){if(hs(n)){var o=n.data.payload;if(o){var a=o.lastActivityTimestamp,l=o.threshold;n.timestamp=a+l}}var u=null===(i=this._instance.config.session_recording.compress_events)||void 0===i||i?function(t){if(Ne(t)<1024)return t;try{if(t.type===ze.FullSnapshot)return B({},t,{data:us(t.data),cv:\"2024-10\"});if(t.type===ze.IncrementalSnapshot&&t.data.source===Ue.Mutation)return B({},t,{cv:\"2024-10\",data:B({},t.data,{texts:us(t.data.texts),attributes:us(t.data.attributes),removes:us(t.data.removes),adds:us(t.data.adds)})});if(t.type===ze.IncrementalSnapshot&&t.data.source===Ue.StyleSheetRule)return B({},t,{cv:\"2024-10\",data:B({},t.data,{adds:t.data.adds?us(t.data.adds):void 0,removes:t.data.removes?us(t.data.removes):void 0})})}catch(t){ss.error(\"could not compress event - will use uncompressed event\",t)}return t}(n):n,h={$snapshot_bytes:Ne(u),$snapshot_data:u,$session_id:this.kt,$window_id:this.oi};this.status!==Nr?this.Oi(h):this.ai()}}}}}Ri(){if(!this._instance.config.capture_pageview&&t){var i=this.di(t.location.href);this.Pi!==i&&(this.ii(\"$url_changed\",{href:i}),this.Pi=i)}}Ii(){if(this.Ht.length){var t=[...this.Ht];this.Ht=[],t.forEach((t=>{Date.now()-t.enqueuedAt<=2e3&&this.$i(t)}))}}di(t){var i=this._instance.config.session_recording;if(i.maskNetworkRequestFn){var e,r={url:t};return null==(e=r=i.maskNetworkRequestFn(r))?void 0:e.url}return t}ai(){return this.M={size:0,data:[],sessionId:this.kt,windowId:this.oi},this.M}Zt(){this.Fi&&(clearTimeout(this.Fi),this.Fi=void 0);var t=this.Nt,i=this.Mt,e=F(i)&&i>=0,r=F(t)&&e&&i<t;if(this.status===Ur||this.status===qr||this.status===Nr||r)return this.Fi=setTimeout((()=>{this.Zt()}),2e3),this.M;this.M.data.length>0&&je(this.M).forEach((t=>{this.Ai({$snapshot_bytes:t.size,$snapshot_data:t.data,$session_id:t.sessionId,$window_id:t.windowId,$lib:\"web\",$lib_version:c.LIB_VERSION})}));return this.ai()}Oi(t){var i,e=2+((null==(i=this.M)?void 0:i.data.length)||0);!this.Wt&&(this.M.size+t.$snapshot_bytes+e>943718.4||this.M.sessionId!==this.kt)&&(this.M=this.Zt()),this.M.size+=t.$snapshot_bytes,this.M.data.push(t.$snapshot_data),this.Fi||this.Wt||(this.Fi=setTimeout((()=>{this.Zt()}),2e3))}Ai(t){this._instance.capture(\"$snapshot\",t,{_url:this._instance.requestRouter.endpointFor(\"api\",this.si),_noTruncate:!0,_batchKey:\"recordings\",skip_client_rate_limiting:!0})}Ci(t){var i;this.Rt.triggerStatus(this.sessionId)===Wr&&(null==(i=this._instance)||null==(i=i.persistence)||i.register({[\"url\"===t?xt:Et]:this.kt}),this.Zt(),this.pi(t+\"_trigger_matched\"))}Ti(){this.Ut.urlBlocked||(this.Ut.urlBlocked=!0,clearInterval(this.vi),ss.info(\"recording paused due to URL blocker\"),this.ii(\"recording paused\",{reason:\"url blocker\"}))}Mi(){this.Ut.urlBlocked&&(this.Ut.urlBlocked=!1,this.ki(),this.Si(),this.ii(\"recording resumed\",{reason:\"left blocked url\"}),ss.info(\"recording resumed\"))}hi(){0!==this.qt.St.length&&O(this.Xt)&&(this.Xt=this._instance.on(\"eventCaptured\",(t=>{try{this.qt.St.includes(t.event)&&this.Ci(\"event\")}catch(t){ss.error(\"Could not activate event trigger\",t)}})))}overrideLinkedFlag(){this.Bt.linkedFlagSeen=!0,this.ki(),this.pi(\"linked_flag_overridden\")}overrideSampling(){var t;null==(t=this._instance.persistence)||t.register({[kt]:!0}),this.ki(),this.pi(\"sampling_overridden\")}overrideTrigger(t){this.Ci(t)}pi(t,i){this._instance.register_for_session({$session_recording_start_reason:t}),ss.info(t.replace(\"_\",\" \"),i),m([\"recording_initialized\",\"session_id_changed\"],t)||this.ii(t,i)}get sdkDebugProperties(){var{sessionStartTimestamp:t}=this.It.checkAndGetSessionAndWindowId(!0);return{$recording_status:this.status,$sdk_debug_replay_internal_buffer_length:this.M.data.length,$sdk_debug_replay_internal_buffer_size:this.M.size,$sdk_debug_current_session_duration:this.Mt,$sdk_debug_session_start:t}}}var vs=z(\"[SegmentIntegration]\");function cs(t,i){var e=t.config.segment;if(!e)return i();!function(t,i){var e=t.config.segment;if(!e)return i();var r=e=>{var r=()=>e.anonymousId()||Ni();t.config.get_device_id=r,e.id()&&(t.register({distinct_id:e.id(),$device_id:r()}),t.persistence.set_property(At,\"identified\")),i()},s=e.user();\"then\"in s&&E(s.then)?s.then((t=>r(t))):r(s)}(t,(()=>{e.register((t=>{Promise&&Promise.resolve||vs.warn(\"This browser does not have Promise support, and can not use the segment integration\");var i=(i,e)=>{if(!e)return i;i.event.userId||i.event.anonymousId===t.get_distinct_id()||(vs.info(\"No userId set, resetting PostHog\"),t.reset()),i.event.userId&&i.event.userId!==t.get_distinct_id()&&(vs.info(\"UserId set, identifying with PostHog\"),t.identify(i.event.userId));var r=t.calculateEventProperties(e,i.event.properties);return i.event.properties=Object.assign({},r,i.event.properties),i};return{name:\"PostHog JS\",type:\"enrichment\",version:\"1.0.0\",isLoaded:()=>!0,load:()=>Promise.resolve(),track:t=>i(t,t.event.event),page:t=>i(t,\"$pageview\"),identify:t=>i(t,\"$identify\"),screen:t=>i(t,\"$screen\")}})(t)).then((()=>{i()}))}))}var fs=\"posthog-js\";function ps(t,i){var{organization:e,projectId:r,prefix:s,severityAllowList:n=[\"error\"]}=void 0===i?{}:i;return i=>{var o,a,l,u,h;if(!(\"*\"===n||n.includes(i.level))||!t.__loaded)return i;i.tags||(i.tags={});var d=t.requestRouter.endpointFor(\"ui\",\"/project/\"+t.config.token+\"/person/\"+t.get_distinct_id());i.tags[\"PostHog Person URL\"]=d,t.sessionRecordingStarted()&&(i.tags[\"PostHog Recording URL\"]=t.get_session_replay_url({withTimestamp:!0}));var v=(null==(o=i.exception)?void 0:o.values)||[],c=v.map((t=>B({},t,{stacktrace:t.stacktrace?B({},t.stacktrace,{type:\"raw\",frames:(t.stacktrace.frames||[]).map((t=>B({},t,{platform:\"web:javascript\"})))}):void 0}))),f={$exception_message:(null==(a=v[0])?void 0:a.value)||i.message,$exception_type:null==(l=v[0])?void 0:l.type,$exception_personURL:d,$exception_level:i.level,$exception_list:c,$sentry_event_id:i.event_id,$sentry_exception:i.exception,$sentry_exception_message:(null==(u=v[0])?void 0:u.value)||i.message,$sentry_exception_type:null==(h=v[0])?void 0:h.type,$sentry_tags:i.tags};return e&&r&&(f.$sentry_url=(s||\"https://sentry.io/organizations/\")+e+\"/issues/?project=\"+r+\"&query=\"+i.event_id),t.exceptions.sendExceptionEvent(f),i}}class gs{constructor(t,i,e,r,s){this.name=fs,this.setupOnce=function(n){n(ps(t,{organization:i,projectId:e,prefix:r,severityAllowList:s}))}}}var _s=null!=t&&t.location?xi(t.location.hash,\"__posthog\")||xi(location.hash,\"state\"):null,ms=\"_postHogToolbarParams\",bs=z(\"[Toolbar]\"),ws=function(t){return t[t.UNINITIALIZED=0]=\"UNINITIALIZED\",t[t.LOADING=1]=\"LOADING\",t[t.LOADED=2]=\"LOADED\",t}(ws||{});class ys{constructor(t){this.instance=t}Di(t){v.ph_toolbar_state=t}Li(){var t;return null!==(t=v.ph_toolbar_state)&&void 0!==t?t:ws.UNINITIALIZED}maybeLoadToolbar(i,e,r){if(void 0===i&&(i=void 0),void 0===e&&(e=void 0),void 0===r&&(r=void 0),!t||!o)return!1;i=null!=i?i:t.location,r=null!=r?r:t.history;try{if(!e){try{t.localStorage.setItem(\"test\",\"test\"),t.localStorage.removeItem(\"test\")}catch(t){return!1}e=null==t?void 0:t.localStorage}var s,n=_s||xi(i.hash,\"__posthog\")||xi(i.hash,\"state\"),a=n?X((()=>JSON.parse(atob(decodeURIComponent(n)))))||X((()=>JSON.parse(decodeURIComponent(n)))):null;return a&&\"ph_authorize\"===a.action?((s=a).source=\"url\",s&&Object.keys(s).length>0&&(a.desiredHash?i.hash=a.desiredHash:r?r.replaceState(r.state,\"\",i.pathname+i.search):i.hash=\"\")):((s=JSON.parse(e.getItem(ms)||\"{}\")).source=\"localstorage\",delete s.userIntent),!(!s.token||this.instance.config.token!==s.token)&&(this.loadToolbar(s),!0)}catch(t){return!1}}Ni(t){var i=v.ph_load_toolbar||v.ph_load_editor;!O(i)&&E(i)?i(t,this.instance):bs.warn(\"No toolbar load function found\")}loadToolbar(i){var e=!(null==o||!o.getElementById(qt));if(!t||e)return!1;var r=\"custom\"===this.instance.requestRouter.region&&this.instance.config.advanced_disable_toolbar_metrics,s=B({token:this.instance.config.token},i,{apiURL:this.instance.requestRouter.endpointFor(\"ui\")},r?{instrument:!1}:{});if(t.localStorage.setItem(ms,JSON.stringify(B({},s,{source:void 0}))),this.Li()===ws.LOADED)this.Ni(s);else if(this.Li()===ws.UNINITIALIZED){var n;this.Di(ws.LOADING),null==(n=v.__PosthogExtensions__)||null==n.loadExternalDependency||n.loadExternalDependency(this.instance,\"toolbar\",(t=>{if(t)return bs.error(\"[Toolbar] Failed to load\",t),void this.Di(ws.UNINITIALIZED);this.Di(ws.LOADED),this.Ni(s)})),st(t,\"turbolinks:load\",(()=>{this.Di(ws.UNINITIALIZED),this.loadToolbar(s)}))}return!0}ji(t){return this.loadToolbar(t)}maybeLoadEditor(t,i,e){return void 0===t&&(t=void 0),void 0===i&&(i=void 0),void 0===e&&(e=void 0),this.maybeLoadToolbar(t,i,e)}}var Ss=z(\"[TracingHeaders]\");class $s{constructor(t){this.zi=void 0,this.Ui=void 0,this.V=()=>{var t,i;R(this.zi)&&(null==(t=v.__PosthogExtensions__)||null==(t=t.tracingHeadersPatchFns)||t._patchXHR(this._instance.sessionManager));R(this.Ui)&&(null==(i=v.__PosthogExtensions__)||null==(i=i.tracingHeadersPatchFns)||i._patchFetch(this._instance.sessionManager))},this._instance=t}G(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.tracingHeadersPatchFns&&t(),null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,\"tracing-headers\",(i=>{if(i)return Ss.error(\"failed to load script\",i);t()}))}startIfEnabledOrStop(){var t,i;this._instance.config.__add_tracing_headers?this.G(this.V):(null==(t=this.zi)||t.call(this),null==(i=this.Ui)||i.call(this),this.zi=void 0,this.Ui=void 0)}}var ks=z(\"[Web Vitals]\"),xs=9e5;class Es{constructor(t){var i;this.qi=!1,this.i=!1,this.M={url:void 0,metrics:[],firstMetricTimestamp:void 0},this.Bi=()=>{clearTimeout(this.Hi),0!==this.M.metrics.length&&(this._instance.capture(\"$web_vitals\",this.M.metrics.reduce(((t,i)=>B({},t,{[\"$web_vitals_\"+i.name+\"_event\"]:B({},i),[\"$web_vitals_\"+i.name+\"_value\"]:i.value})),{})),this.M={url:void 0,metrics:[],firstMetricTimestamp:void 0})},this.Wi=t=>{var i,e=null==(i=this._instance.sessionManager)?void 0:i.checkAndGetSessionAndWindowId(!0);if(R(e))ks.error(\"Could not read session ID. Dropping metrics!\");else{this.M=this.M||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var r=this.Gi();if(!R(r))if(O(null==t?void 0:t.name)||O(null==t?void 0:t.value))ks.error(\"Invalid metric received\",t);else if(this.Ji&&t.value>=this.Ji)ks.error(\"Ignoring metric with value >= \"+this.Ji,t);else this.M.url!==r&&(this.Bi(),this.Hi=setTimeout(this.Bi,this.flushToCaptureTimeoutMs)),R(this.M.url)&&(this.M.url=r),this.M.firstMetricTimestamp=R(this.M.firstMetricTimestamp)?Date.now():this.M.firstMetricTimestamp,t.attribution&&t.attribution.interactionTargetElement&&(t.attribution.interactionTargetElement=void 0),this.M.metrics.push(B({},t,{$current_url:r,$session_id:e.sessionId,$window_id:e.windowId,timestamp:Date.now()})),this.M.metrics.length===this.allowedMetrics.length&&this.Bi()}},this.V=()=>{var t,i,e,r,s=v.__PosthogExtensions__;R(s)||R(s.postHogWebVitalsCallbacks)||({onLCP:t,onCLS:i,onFCP:e,onINP:r}=s.postHogWebVitalsCallbacks),t&&i&&e&&r?(this.allowedMetrics.indexOf(\"LCP\")>-1&&t(this.Wi.bind(this)),this.allowedMetrics.indexOf(\"CLS\")>-1&&i(this.Wi.bind(this)),this.allowedMetrics.indexOf(\"FCP\")>-1&&e(this.Wi.bind(this)),this.allowedMetrics.indexOf(\"INP\")>-1&&r(this.Wi.bind(this)),this.i=!0):ks.error(\"web vitals callbacks not loaded - not starting\")},this._instance=t,this.qi=!(null==(i=this._instance.persistence)||!i.props[vt]),this.startIfEnabled()}get allowedMetrics(){var t,i,e=I(this._instance.config.capture_performance)?null==(t=this._instance.config.capture_performance)?void 0:t.web_vitals_allowed_metrics:void 0;return R(e)?(null==(i=this._instance.persistence)?void 0:i.props[ft])||[\"CLS\",\"FCP\",\"INP\",\"LCP\"]:e}get flushToCaptureTimeoutMs(){return(I(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals_delayed_flush_ms:void 0)||5e3}get Ji(){var t=I(this._instance.config.capture_performance)&&F(this._instance.config.capture_performance.__web_vitals_max_value)?this._instance.config.capture_performance.__web_vitals_max_value:xs;return 0<t&&t<=6e4?xs:t}get isEnabled(){var t=null==a?void 0:a.protocol;if(\"http:\"!==t&&\"https:\"!==t)return ks.info(\"Web Vitals are disabled on non-http/https protocols\"),!1;var i=I(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals:A(this._instance.config.capture_performance)?this._instance.config.capture_performance:void 0;return A(i)?i:this.qi}startIfEnabled(){this.isEnabled&&!this.i&&(ks.info(\"enabled, starting...\"),this.G(this.V))}onRemoteConfig(t){var i=I(t.capturePerformance)&&!!t.capturePerformance.web_vitals,e=I(t.capturePerformance)?t.capturePerformance.web_vitals_allowed_metrics:void 0;this._instance.persistence&&(this._instance.persistence.register({[vt]:i}),this._instance.persistence.register({[ft]:e})),this.qi=i,this.startIfEnabled()}G(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.postHogWebVitalsCallbacks&&t(),null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,\"web-vitals\",(i=>{i?ks.error(\"failed to load script\",i):t()}))}Gi(){var i=t?t.location.href:void 0;return i||ks.error(\"Could not determine current URL\"),i}}var Is=z(\"[Heatmaps]\");function Ps(t){return I(t)&&\"clientX\"in t&&\"clientY\"in t&&F(t.clientX)&&F(t.clientY)}class Rs{constructor(t){var i;this.rageclicks=new bi,this.qi=!1,this.i=!1,this.Vi=null,this.instance=t,this.qi=!(null==(i=this.instance.persistence)||!i.props[ut])}get flushIntervalMilliseconds(){var t=5e3;return I(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(t=this.instance.config.capture_heatmaps.flush_interval_milliseconds),t}get isEnabled(){return R(this.instance.config.capture_heatmaps)?R(this.instance.config.enable_heatmaps)?this.qi:this.instance.config.enable_heatmaps:!1!==this.instance.config.capture_heatmaps}startIfEnabled(){if(this.isEnabled){if(this.i)return;Is.info(\"starting...\"),this.Ki(),this.Vi=setInterval(this.Yi.bind(this),this.flushIntervalMilliseconds)}else{var t,i;clearInterval(null!==(t=this.Vi)&&void 0!==t?t:void 0),null==(i=this.Xi)||i.stop(),this.getAndClearBuffer()}}onRemoteConfig(t){var i=!!t.heatmaps;this.instance.persistence&&this.instance.persistence.register({[ut]:i}),this.qi=i,this.startIfEnabled()}getAndClearBuffer(){var t=this.M;return this.M=void 0,t}Qi(t){this.Zi(t.originalEvent,\"deadclick\")}Ki(){t&&o&&(st(t,\"beforeunload\",this.Yi.bind(this)),st(o,\"click\",(i=>this.Zi(i||(null==t?void 0:t.event))),{capture:!0}),st(o,\"mousemove\",(i=>this.te(i||(null==t?void 0:t.event))),{capture:!0}),this.Xi=new re(this.instance,ie,this.Qi.bind(this)),this.Xi.startIfEnabled(),this.i=!0)}ie(i,e){var r=this.instance.scrollManager.scrollY(),s=this.instance.scrollManager.scrollX(),n=this.instance.scrollManager.scrollElement(),o=function(i,e,r){for(var s=i;s&&Gt(s)&&!Jt(s,\"body\");){if(s===r)return!1;if(m(e,null==t?void 0:t.getComputedStyle(s).position))return!0;s=ri(s)}return!1}(ii(i),[\"fixed\",\"sticky\"],n);return{x:i.clientX+(o?0:s),y:i.clientY+(o?0:r),target_fixed:o,type:e}}Zi(t,i){var e;if(void 0===i&&(i=\"click\"),!Wt(t.target)&&Ps(t)){var r=this.ie(t,i);null!=(e=this.rageclicks)&&e.isRageClick(t.clientX,t.clientY,(new Date).getTime())&&this.ee(B({},r,{type:\"rageclick\"})),this.ee(r)}}te(t){!Wt(t.target)&&Ps(t)&&(clearTimeout(this.re),this.re=setTimeout((()=>{this.ee(this.ie(t,\"mousemove\"))}),500))}ee(i){if(t){var e=t.location.href;this.M=this.M||{},this.M[e]||(this.M[e]=[]),this.M[e].push(i)}}Yi(){this.M&&!P(this.M)&&this.instance.capture(\"$$heatmap\",{$heatmap_data:this.getAndClearBuffer()})}}class Ts{constructor(t){this._instance=t}doPageView(i,e){var r,s=this.se(i,e);return this.ne={pathname:null!==(r=null==t?void 0:t.location.pathname)&&void 0!==r?r:\"\",pageViewId:e,timestamp:i},this._instance.scrollManager.resetContext(),s}doPageLeave(t){var i;return this.se(t,null==(i=this.ne)?void 0:i.pageViewId)}doEvent(){var t;return{$pageview_id:null==(t=this.ne)?void 0:t.pageViewId}}se(t,i){var e=this.ne;if(!e)return{$pageview_id:i};var r={$pageview_id:i,$prev_pageview_id:e.pageViewId},s=this._instance.scrollManager.getContext();if(s&&!this._instance.config.disable_scroll_properties){var{maxScrollHeight:n,lastScrollY:o,maxScrollY:a,maxContentHeight:l,lastContentY:u,maxContentY:h}=s;if(!(R(n)||R(o)||R(a)||R(l)||R(u)||R(h))){n=Math.ceil(n),o=Math.ceil(o),a=Math.ceil(a),l=Math.ceil(l),u=Math.ceil(u),h=Math.ceil(h);var d=n<=1?1:Xe(o/n,0,1),v=n<=1?1:Xe(a/n,0,1),c=l<=1?1:Xe(u/l,0,1),f=l<=1?1:Xe(h/l,0,1);r=V(r,{$prev_pageview_last_scroll:o,$prev_pageview_last_scroll_percentage:d,$prev_pageview_max_scroll:a,$prev_pageview_max_scroll_percentage:v,$prev_pageview_last_content:u,$prev_pageview_last_content_percentage:c,$prev_pageview_max_content:h,$prev_pageview_max_content_percentage:f})}}return e.pathname&&(r.$prev_pageview_pathname=e.pathname),e.timestamp&&(r.$prev_pageview_duration=(t.getTime()-e.timestamp.getTime())/1e3),r}}var Ms=function(t){var i,e,r,s,n=\"\";for(i=e=0,r=(t=(t+\"\").replace(/\\r\\n/g,\"\\n\").replace(/\\r/g,\"\\n\")).length,s=0;s<r;s++){var o=t.charCodeAt(s),a=null;o<128?e++:a=o>127&&o<2048?String.fromCharCode(o>>6|192,63&o|128):String.fromCharCode(o>>12|224,o>>6&63|128,63&o|128),C(a)||(e>i&&(n+=t.substring(i,e)),n+=a,i=e=s+1)}return e>i&&(n+=t.substring(i,t.length)),n},Cs=!!u||!!l,Os=\"text/plain\",Fs=(t,i)=>{var[e,r]=t.split(\"?\"),s=B({},i);null==r||r.split(\"&\").forEach((t=>{var[i]=t.split(\"=\");delete s[i]}));var n=Si(s);return e+\"?\"+(n=n?(r?r+\"&\":\"\")+n:r)},As=(t,i)=>JSON.stringify(t,((t,i)=>\"bigint\"==typeof i?i.toString():i),i),Ds=t=>{var{data:i,compression:e}=t;if(i){if(e===g.GZipJS){var r=Ar(Dr(As(i)),{mtime:0}),s=new Blob([r],{type:Os});return{contentType:Os,body:s,estimatedSize:s.size}}if(e===g.Base64){var n=function(t){var i,e,r,s,n,o=\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\",a=0,l=0,u=\"\",h=[];if(!t)return t;t=Ms(t);do{i=(n=t.charCodeAt(a++)<<16|t.charCodeAt(a++)<<8|t.charCodeAt(a++))>>18&63,e=n>>12&63,r=n>>6&63,s=63&n,h[l++]=o.charAt(i)+o.charAt(e)+o.charAt(r)+o.charAt(s)}while(a<t.length);switch(u=h.join(\"\"),t.length%3){case 1:u=u.slice(0,-2)+\"==\";break;case 2:u=u.slice(0,-1)+\"=\"}return u}(As(i)),o=(t=>\"data=\"+encodeURIComponent(\"string\"==typeof t?t:As(t)))(n);return{contentType:\"application/x-www-form-urlencoded\",body:o,estimatedSize:new Blob([o]).size}}var a=As(i);return{contentType:\"application/json\",body:a,estimatedSize:new Blob([a]).size}}},Ls=[];l&&Ls.push({transport:\"fetch\",method:t=>{var i,e,{contentType:r,body:s,estimatedSize:n}=null!==(i=Ds(t))&&void 0!==i?i:{},o=new Headers;J(t.headers,(function(t,i){o.append(i,t)})),r&&o.append(\"Content-Type\",r);var a=t.url,u=null;if(h){var d=new h;u={signal:d.signal,timeout:setTimeout((()=>d.abort()),t.timeout)}}l(a,B({method:(null==t?void 0:t.method)||\"GET\",headers:o,keepalive:\"POST\"===t.method&&(n||0)<52428.8,body:s,signal:null==(e=u)?void 0:e.signal},t.fetchOptions)).then((i=>i.text().then((e=>{var r={statusCode:i.status,text:e};if(200===i.status)try{r.json=JSON.parse(e)}catch(t){j.error(t)}null==t.callback||t.callback(r)})))).catch((i=>{j.error(i),null==t.callback||t.callback({statusCode:0,text:i})})).finally((()=>u?clearTimeout(u.timeout):null))}}),u&&Ls.push({transport:\"XHR\",method:t=>{var i,e=new u;e.open(t.method||\"GET\",t.url,!0);var{contentType:r,body:s}=null!==(i=Ds(t))&&void 0!==i?i:{};J(t.headers,(function(t,i){e.setRequestHeader(i,t)})),r&&e.setRequestHeader(\"Content-Type\",r),t.timeout&&(e.timeout=t.timeout),e.withCredentials=!0,e.onreadystatechange=()=>{if(4===e.readyState){var i={statusCode:e.status,text:e.responseText};if(200===e.status)try{i.json=JSON.parse(e.responseText)}catch(t){}null==t.callback||t.callback(i)}},e.send(s)}}),null!=n&&n.sendBeacon&&Ls.push({transport:\"sendBeacon\",method:t=>{var i=Fs(t.url,{beacon:\"1\"});try{var e,{contentType:r,body:s}=null!==(e=Ds(t))&&void 0!==e?e:{},o=\"string\"==typeof s?new Blob([s],{type:r}):s;n.sendBeacon(i,o)}catch(t){}}});var Ns=function(t,i){if(!function(t){try{new RegExp(t)}catch(t){return!1}return!0}(i))return!1;try{return new RegExp(i).test(t)}catch(t){return!1}};function js(t,i,e){return As({distinct_id:t,userPropertiesToSet:i,userPropertiesToSetOnce:e})}var zs={exact:(t,i)=>i.some((i=>t.some((t=>i===t)))),is_not:(t,i)=>i.every((i=>t.every((t=>i!==t)))),regex:(t,i)=>i.some((i=>t.some((t=>Ns(i,t))))),not_regex:(t,i)=>i.every((i=>t.every((t=>!Ns(i,t))))),icontains:(t,i)=>i.map(Us).some((i=>t.map(Us).some((t=>i.includes(t))))),not_icontains:(t,i)=>i.map(Us).every((i=>t.map(Us).every((t=>!i.includes(t)))))},Us=t=>t.toLowerCase(),qs=z(\"[Error tracking]\");class Bs{constructor(t){var i,e;this.oe=[],this._instance=t,this.oe=null!==(i=null==(e=this._instance.persistence)?void 0:e.get_property(dt))&&void 0!==i?i:[]}onRemoteConfig(t){var i,e,r=null!==(i=null==(e=t.errorTracking)?void 0:e.suppressionRules)&&void 0!==i?i:[];this.oe=r,this._instance.persistence&&this._instance.persistence.register({[dt]:this.oe})}sendExceptionEvent(t){this.ae(t)?qs.info(\"Skipping exception capture because a suppression rule matched\"):this._instance.capture(\"$exception\",t,{_noTruncate:!0,_batchKey:\"exceptionEvent\"})}ae(t){var i=t.$exception_list;if(!i||!x(i)||0===i.length)return!1;var e=i.reduce(((t,i)=>{var{type:e,value:r}=i;return T(e)&&e.length>0&&t.$exception_types.push(e),T(r)&&r.length>0&&t.$exception_messages.push(r),t}),{$exception_types:[],$exception_messages:[]});return this.oe.some((t=>{var i=t.values.map((t=>{var i=zs[t.operator],r=x(t.value)?t.value:[t.value],s=e[t.key];return r.length>0&&i(r,s)}));return\"OR\"===t.type?i.some(Boolean):i.every(Boolean)}))}}var Hs=\"Mobile\",Ws=\"iOS\",Gs=\"Android\",Js=\"Tablet\",Vs=Gs+\" \"+Js,Ks=\"iPad\",Ys=\"Apple\",Xs=Ys+\" Watch\",Qs=\"Safari\",Zs=\"BlackBerry\",tn=\"Samsung\",en=tn+\"Browser\",rn=tn+\" Internet\",sn=\"Chrome\",nn=sn+\" OS\",on=sn+\" \"+Ws,an=\"Internet Explorer\",ln=an+\" \"+Hs,un=\"Opera\",hn=un+\" Mini\",dn=\"Edge\",vn=\"Microsoft \"+dn,cn=\"Firefox\",fn=cn+\" \"+Ws,pn=\"Nintendo\",gn=\"PlayStation\",_n=\"Xbox\",mn=Gs+\" \"+Hs,bn=Hs+\" \"+Qs,wn=\"Windows\",yn=wn+\" Phone\",Sn=\"Nokia\",$n=\"Ouya\",kn=\"Generic\",xn=kn+\" \"+Hs.toLowerCase(),En=kn+\" \"+Js.toLowerCase(),In=\"Konqueror\",Pn=\"(\\\\d+(\\\\.\\\\d+)?)\",Rn=new RegExp(\"Version/\"+Pn),Tn=new RegExp(_n,\"i\"),Mn=new RegExp(gn+\" \\\\w+\",\"i\"),Cn=new RegExp(pn+\" \\\\w+\",\"i\"),On=new RegExp(Zs+\"|PlayBook|BB10\",\"i\"),Fn={\"NT3.51\":\"NT 3.11\",\"NT4.0\":\"NT 4.0\",\"5.0\":\"2000\",5.1:\"XP\",5.2:\"XP\",\"6.0\":\"Vista\",6.1:\"7\",6.2:\"8\",6.3:\"8.1\",6.4:\"10\",\"10.0\":\"10\"};var An=(t,i)=>i&&m(i,Ys)||function(t){return m(t,Qs)&&!m(t,sn)&&!m(t,Gs)}(t),Dn=function(t,i){return i=i||\"\",m(t,\" OPR/\")&&m(t,\"Mini\")?hn:m(t,\" OPR/\")?un:On.test(t)?Zs:m(t,\"IE\"+Hs)||m(t,\"WPDesktop\")?ln:m(t,en)?rn:m(t,dn)||m(t,\"Edg/\")?vn:m(t,\"FBIOS\")?\"Facebook \"+Hs:m(t,\"UCWEB\")||m(t,\"UCBrowser\")?\"UC Browser\":m(t,\"CriOS\")?on:m(t,\"CrMo\")||m(t,sn)?sn:m(t,Gs)&&m(t,Qs)?mn:m(t,\"FxiOS\")?fn:m(t.toLowerCase(),In.toLowerCase())?In:An(t,i)?m(t,Hs)?bn:Qs:m(t,cn)?cn:m(t,\"MSIE\")||m(t,\"Trident/\")?an:m(t,\"Gecko\")?cn:\"\"},Ln={[ln]:[new RegExp(\"rv:\"+Pn)],[vn]:[new RegExp(dn+\"?\\\\/\"+Pn)],[sn]:[new RegExp(\"(\"+sn+\"|CrMo)\\\\/\"+Pn)],[on]:[new RegExp(\"CriOS\\\\/\"+Pn)],\"UC Browser\":[new RegExp(\"(UCBrowser|UCWEB)\\\\/\"+Pn)],[Qs]:[Rn],[bn]:[Rn],[un]:[new RegExp(\"(Opera|OPR)\\\\/\"+Pn)],[cn]:[new RegExp(cn+\"\\\\/\"+Pn)],[fn]:[new RegExp(\"FxiOS\\\\/\"+Pn)],[In]:[new RegExp(\"Konqueror[:/]?\"+Pn,\"i\")],[Zs]:[new RegExp(Zs+\" \"+Pn),Rn],[mn]:[new RegExp(\"android\\\\s\"+Pn,\"i\")],[rn]:[new RegExp(en+\"\\\\/\"+Pn)],[an]:[new RegExp(\"(rv:|MSIE )\"+Pn)],Mozilla:[new RegExp(\"rv:\"+Pn)]},Nn=function(t,i){var e=Dn(t,i),r=Ln[e];if(R(r))return null;for(var s=0;s<r.length;s++){var n=r[s],o=t.match(n);if(o)return parseFloat(o[o.length-2])}return null},jn=[[new RegExp(_n+\"; \"+_n+\" (.*?)[);]\",\"i\"),t=>[_n,t&&t[1]||\"\"]],[new RegExp(pn,\"i\"),[pn,\"\"]],[new RegExp(gn,\"i\"),[gn,\"\"]],[On,[Zs,\"\"]],[new RegExp(wn,\"i\"),(t,i)=>{if(/Phone/.test(i)||/WPDesktop/.test(i))return[yn,\"\"];if(new RegExp(Hs).test(i)&&!/IEMobile\\b/.test(i))return[wn+\" \"+Hs,\"\"];var e=/Windows NT ([0-9.]+)/i.exec(i);if(e&&e[1]){var r=e[1],s=Fn[r]||\"\";return/arm/i.test(i)&&(s=\"RT\"),[wn,s]}return[wn,\"\"]}],[/((iPhone|iPad|iPod).*?OS (\\d+)_(\\d+)_?(\\d+)?|iPhone)/,t=>{if(t&&t[3]){var i=[t[3],t[4],t[5]||\"0\"];return[Ws,i.join(\".\")]}return[Ws,\"\"]}],[/(watch.*\\/(\\d+\\.\\d+\\.\\d+)|watch os,(\\d+\\.\\d+),)/i,t=>{var i=\"\";return t&&t.length>=3&&(i=R(t[2])?t[3]:t[2]),[\"watchOS\",i]}],[new RegExp(\"(\"+Gs+\" (\\\\d+)\\\\.(\\\\d+)\\\\.?(\\\\d+)?|\"+Gs+\")\",\"i\"),t=>{if(t&&t[2]){var i=[t[2],t[3],t[4]||\"0\"];return[Gs,i.join(\".\")]}return[Gs,\"\"]}],[/Mac OS X (\\d+)[_.](\\d+)[_.]?(\\d+)?/i,t=>{var i=[\"Mac OS X\",\"\"];if(t&&t[1]){var e=[t[1],t[2],t[3]||\"0\"];i[1]=e.join(\".\")}return i}],[/Mac/i,[\"Mac OS X\",\"\"]],[/CrOS/,[nn,\"\"]],[/Linux|debian/i,[\"Linux\",\"\"]]],zn=function(t){return Cn.test(t)?pn:Mn.test(t)?gn:Tn.test(t)?_n:new RegExp($n,\"i\").test(t)?$n:new RegExp(\"(\"+yn+\"|WPDesktop)\",\"i\").test(t)?yn:/iPad/.test(t)?Ks:/iPod/.test(t)?\"iPod Touch\":/iPhone/.test(t)?\"iPhone\":/(watch)(?: ?os[,/]|\\d,\\d\\/)[\\d.]+/i.test(t)?Xs:On.test(t)?Zs:/(kobo)\\s(ereader|touch)/i.test(t)?\"Kobo\":new RegExp(Sn,\"i\").test(t)?Sn:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\\))/i.test(t)||/(kf[a-z]+)( bui|\\)).+silk\\//i.test(t)?\"Kindle Fire\":/(Android|ZTE)/i.test(t)?!new RegExp(Hs).test(t)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(t)?/pixel[\\daxl ]{1,6}/i.test(t)&&!/pixel c/i.test(t)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(t)||/lmy47v/i.test(t)&&!/QTAQZ3/i.test(t)?Gs:Vs:Gs:new RegExp(\"(pda|\"+Hs+\")\",\"i\").test(t)?xn:new RegExp(Js,\"i\").test(t)&&!new RegExp(Js+\" pc\",\"i\").test(t)?En:\"\"},Un=\"https?://(.*)\",qn=[\"gclid\",\"gclsrc\",\"dclid\",\"gbraid\",\"wbraid\",\"fbclid\",\"msclkid\",\"twclid\",\"li_fat_id\",\"igshid\",\"ttclid\",\"rdt_cid\",\"epik\",\"qclid\",\"sccid\",\"irclid\",\"_kx\"],Bn=K([\"utm_source\",\"utm_medium\",\"utm_campaign\",\"utm_content\",\"utm_term\",\"gad_source\",\"mc_cid\"],qn),Hn=\"<masked>\";function Wn(t,i,e){if(!o)return{};var r=i?K([],qn,e||[]):[];return Gn(ki(o.URL,r,Hn),t)}function Gn(t,i){var e=Bn.concat(i||[]),r={};return J(e,(function(i){var e=$i(t,i);r[i]=e||null})),r}function Jn(t){var i=function(t){return t?0===t.search(Un+\"google.([^/?]*)\")?\"google\":0===t.search(Un+\"bing.com\")?\"bing\":0===t.search(Un+\"yahoo.com\")?\"yahoo\":0===t.search(Un+\"duckduckgo.com\")?\"duckduckgo\":null:null}(t),e=\"yahoo\"!=i?\"q\":\"p\",r={};if(!C(i)){r.$search_engine=i;var s=o?$i(o.referrer,e):\"\";s.length&&(r.ph_keyword=s)}return r}function Vn(){return navigator.language||navigator.userLanguage}function Kn(){return(null==o?void 0:o.referrer)||\"$direct\"}function Yn(t,i){var e=t?K([],qn,i||[]):[],r=null==a?void 0:a.href.substring(0,1e3);return{r:Kn().substring(0,1e3),u:r?ki(r,e,Hn):void 0}}function Xn(t){var i,{r:e,u:r}=t,s={$referrer:e,$referring_domain:null==e?void 0:\"$direct\"==e?\"$direct\":null==(i=yi(e))?void 0:i.host};if(r){s.$current_url=r;var n=yi(r);s.$host=null==n?void 0:n.host,s.$pathname=null==n?void 0:n.pathname;var o=Gn(r);V(s,o)}if(e){var a=Jn(e);V(s,a)}return s}function Qn(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(t){return}}function Zn(){try{return(new Date).getTimezoneOffset()}catch(t){return}}function to(i,e){if(!d)return{};var r,s,n,o=i?K([],qn,e||[]):[],[l,u]=function(t){for(var i=0;i<jn.length;i++){var[e,r]=jn[i],s=e.exec(t),n=s&&(E(r)?r(s,t):r);if(n)return n}return[\"\",\"\"]}(d);return V(Z({$os:l,$os_version:u,$browser:Dn(d,navigator.vendor),$device:zn(d),$device_type:(s=d,n=zn(s),n===Ks||n===Vs||\"Kobo\"===n||\"Kindle Fire\"===n||n===En?Js:n===pn||n===_n||n===gn||n===$n?\"Console\":n===Xs?\"Wearable\":n?Hs:\"Desktop\"),$timezone:Qn(),$timezone_offset:Zn()}),{$current_url:ki(null==a?void 0:a.href,o,Hn),$host:null==a?void 0:a.host,$pathname:null==a?void 0:a.pathname,$raw_user_agent:d.length>1e3?d.substring(0,997)+\"...\":d,$browser_version:Nn(d,navigator.vendor),$browser_language:Vn(),$browser_language_prefix:(r=Vn(),\"string\"==typeof r?r.split(\"-\")[0]:void 0),$screen_height:null==t?void 0:t.screen.height,$screen_width:null==t?void 0:t.screen.width,$viewport_height:null==t?void 0:t.innerHeight,$viewport_width:null==t?void 0:t.innerWidth,$lib:\"web\",$lib_version:c.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})}var io=z(\"[FeatureFlags]\"),eo=\"$active_feature_flags\",ro=\"$override_feature_flags\",so=\"$feature_flag_payloads\",no=\"$override_feature_flag_payloads\",oo=\"$feature_flag_request_id\",ao=t=>{var i={};for(var[e,r]of Y(t||{}))r&&(i[e]=r);return i},lo=t=>{var i=t.flags;return i?(t.featureFlags=Object.fromEntries(Object.keys(i).map((t=>{var e;return[t,null!==(e=i[t].variant)&&void 0!==e?e:i[t].enabled]}))),t.featureFlagPayloads=Object.fromEntries(Object.keys(i).filter((t=>i[t].enabled)).filter((t=>{var e;return null==(e=i[t].metadata)?void 0:e.payload})).map((t=>{var e;return[t,null==(e=i[t].metadata)?void 0:e.payload]})))):io.warn(\"Using an older version of the feature flags endpoint. Please upgrade your PostHog server to the latest version\"),t},uo=function(t){return t.FeatureFlags=\"feature_flags\",t.Recordings=\"recordings\",t}({});class ho{constructor(t){this.le=!1,this.ue=!1,this.he=!1,this.de=!1,this.ve=!1,this.ce=!1,this.fe=!1,this._instance=t,this.featureFlagEventHandlers=[]}decide(){if(this._instance.config.__preview_remote_config)this.ce=!0;else{var t=!this.pe&&(this._instance.config.advanced_disable_feature_flags||this._instance.config.advanced_disable_feature_flags_on_first_load);this.ge({disableFlags:t})}}get hasLoadedFlags(){return this.ue}getFlags(){return Object.keys(this.getFlagVariants())}getFlagsWithDetails(){var t=this._instance.get_property(Rt),i=this._instance.get_property(ro),e=this._instance.get_property(no);if(!e&&!i)return t||{};var r=V({},t||{}),s=[...new Set([...Object.keys(e||{}),...Object.keys(i||{})])];for(var n of s){var o,a,l=r[n],u=null==i?void 0:i[n],h=R(u)?null!==(o=null==l?void 0:l.enabled)&&void 0!==o&&o:!!u,d=R(u)?l.variant:\"string\"==typeof u?u:void 0,v=null==e?void 0:e[n],c=B({},l,{enabled:h,variant:h?null!=d?d:null==l?void 0:l.variant:void 0});if(h!==(null==l?void 0:l.enabled)&&(c.original_enabled=null==l?void 0:l.enabled),d!==(null==l?void 0:l.variant)&&(c.original_variant=null==l?void 0:l.variant),v)c.metadata=B({},null==l?void 0:l.metadata,{payload:v,original_payload:null==l||null==(a=l.metadata)?void 0:a.payload});r[n]=c}return this.le||(io.warn(\" Overriding feature flag details!\",{flagDetails:t,overriddenPayloads:e,finalDetails:r}),this.le=!0),r}getFlagVariants(){var t=this._instance.get_property(It),i=this._instance.get_property(ro);if(!i)return t||{};for(var e=V({},t),r=Object.keys(i),s=0;s<r.length;s++)e[r[s]]=i[r[s]];return this.le||(io.warn(\" Overriding feature flags!\",{enabledFlags:t,overriddenFlags:i,finalFlags:e}),this.le=!0),e}getFlagPayloads(){var t=this._instance.get_property(so),i=this._instance.get_property(no);if(!i)return t||{};for(var e=V({},t||{}),r=Object.keys(i),s=0;s<r.length;s++)e[r[s]]=i[r[s]];return this.le||(io.warn(\" Overriding feature flag payloads!\",{flagPayloads:t,overriddenPayloads:i,finalPayloads:e}),this.le=!0),e}reloadFeatureFlags(){this.de||this._instance.config.advanced_disable_feature_flags||this.pe||(this.pe=setTimeout((()=>{this.ge()}),5))}_e(){clearTimeout(this.pe),this.pe=void 0}ensureFlagsLoaded(){this.ue||this.he||this.pe||this.reloadFeatureFlags()}setAnonymousDistinctId(t){this.$anon_distinct_id=t}setReloadingPaused(t){this.de=t}ge(t){var i;if(this._e(),!this._instance.config.advanced_disable_decide)if(this.he)this.ve=!0;else{var e={token:this._instance.config.token,distinct_id:this._instance.get_distinct_id(),groups:this._instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:B({},(null==(i=this._instance.persistence)?void 0:i.get_initial_props())||{},this._instance.get_property(Tt)||{}),group_properties:this._instance.get_property(Mt)};(null!=t&&t.disableFlags||this._instance.config.advanced_disable_feature_flags)&&(e.disable_flags=!0);var r=this._instance.config.__preview_flags_v2&&this._instance.config.__preview_remote_config;r&&(e.timezone=Qn()),this.he=!0,this._instance.me({method:\"POST\",url:this._instance.requestRouter.endpointFor(\"api\",r?\"/flags/?v=2\":\"/decide/?v=4\"),data:e,compression:this._instance.config.disable_compression?void 0:g.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:t=>{var i,r,s=!0;(200===t.statusCode&&(this.ve||(this.$anon_distinct_id=void 0),s=!1),this.he=!1,this.ce)||(this.ce=!0,this._instance.be(null!==(r=t.json)&&void 0!==r?r:{}));if(!e.disable_flags||this.ve)if(this.fe=!s,t.json&&null!=(i=t.json.quotaLimited)&&i.includes(uo.FeatureFlags))io.warn(\"You have hit your feature flags quota limit, and will not be able to load feature flags until the quota is reset.  Please visit https://posthog.com/docs/billing/limits-alerts to learn more.\");else{var n;if(!e.disable_flags)this.receivedFeatureFlags(null!==(n=t.json)&&void 0!==n?n:{},s);this.ve&&(this.ve=!1,this.ge())}}})}}getFeatureFlag(t,i){if(void 0===i&&(i={}),this.ue||this.getFlags()&&this.getFlags().length>0){var e=this.getFlagVariants()[t],r=\"\"+e,s=this._instance.get_property(oo)||void 0,n=this._instance.get_property(Ft)||{};if((i.send_event||!(\"send_event\"in i))&&(!(t in n)||!n[t].includes(r))){var o,a,l,u,h,d,v,c,f;x(n[t])?n[t].push(r):n[t]=[r],null==(o=this._instance.persistence)||o.register({[Ft]:n});var p=this.getFeatureFlagDetails(t),g={$feature_flag:t,$feature_flag_response:e,$feature_flag_payload:this.getFeatureFlagPayload(t)||null,$feature_flag_request_id:s,$feature_flag_bootstrapped_response:(null==(a=this._instance.config.bootstrap)||null==(a=a.featureFlags)?void 0:a[t])||null,$feature_flag_bootstrapped_payload:(null==(l=this._instance.config.bootstrap)||null==(l=l.featureFlagPayloads)?void 0:l[t])||null,$used_bootstrap_value:!this.fe};R(null==p||null==(u=p.metadata)?void 0:u.version)||(g.$feature_flag_version=p.metadata.version);var _,m=null!==(h=null==p||null==(d=p.reason)?void 0:d.description)&&void 0!==h?h:null==p||null==(v=p.reason)?void 0:v.code;if(m&&(g.$feature_flag_reason=m),null!=p&&null!=(c=p.metadata)&&c.id&&(g.$feature_flag_id=p.metadata.id),R(null==p?void 0:p.original_variant)&&R(null==p?void 0:p.original_enabled)||(g.$feature_flag_original_response=R(p.original_variant)?p.original_enabled:p.original_variant),null!=p&&null!=(f=p.metadata)&&f.original_payload)g.$feature_flag_original_payload=null==p||null==(_=p.metadata)?void 0:_.original_payload;this._instance.capture(\"$feature_flag_called\",g)}return e}io.warn('getFeatureFlag for key \"'+t+\"\\\" failed. Feature flags didn't load in time.\")}getFeatureFlagDetails(t){return this.getFlagsWithDetails()[t]}getFeatureFlagPayload(t){return this.getFlagPayloads()[t]}getRemoteConfigPayload(t,i){var e=this._instance.config.token;this._instance.me({method:\"POST\",url:this._instance.requestRouter.endpointFor(\"api\",\"/decide/?v=4\"),data:{distinct_id:this._instance.get_distinct_id(),token:e},compression:this._instance.config.disable_compression?void 0:g.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:e=>{var r,s=null==(r=e.json)?void 0:r.featureFlagPayloads;i((null==s?void 0:s[t])||void 0)}})}isFeatureEnabled(t,i){if(void 0===i&&(i={}),this.ue||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(t,i);io.warn('isFeatureEnabled for key \"'+t+\"\\\" failed. Feature flags didn't load in time.\")}addFeatureFlagsHandler(t){this.featureFlagEventHandlers.push(t)}removeFeatureFlagsHandler(t){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter((i=>i!==t))}receivedFeatureFlags(t,i){if(this._instance.persistence){this.ue=!0;var e=this.getFlagVariants(),r=this.getFlagPayloads(),s=this.getFlagsWithDetails();!function(t,i,e,r,s){void 0===e&&(e={}),void 0===r&&(r={}),void 0===s&&(s={});var n=lo(t),o=n.flags,a=n.featureFlags,l=n.featureFlagPayloads;if(a){var u=t.requestId;if(x(a)){io.warn(\"v1 of the feature flags endpoint is deprecated. Please use the latest version.\");var h={};if(a)for(var d=0;d<a.length;d++)h[a[d]]=!0;i&&i.register({[eo]:a,[It]:h})}else{var v=a,c=l,f=o;t.errorsWhileComputingFlags&&(v=B({},e,v),c=B({},r,c),f=B({},s,f)),i&&i.register(B({[eo]:Object.keys(ao(v)),[It]:v||{},[so]:c||{},[Rt]:f||{}},u?{[oo]:u}:{}))}}}(t,this._instance.persistence,e,r,s),this.we(i)}}override(t,i){void 0===i&&(i=!1),io.warn(\"override is deprecated. Please use overrideFeatureFlags instead.\"),this.overrideFeatureFlags({flags:t,suppressWarning:i})}overrideFeatureFlags(t){if(!this._instance.__loaded||!this._instance.persistence)return io.uninitializedWarning(\"posthog.featureFlags.overrideFeatureFlags\");if(!1===t)return this._instance.persistence.unregister(ro),this._instance.persistence.unregister(no),void this.we();if(t&&\"object\"==typeof t&&(\"flags\"in t||\"payloads\"in t)){var i,e=t;if(this.le=Boolean(null!==(i=e.suppressWarning)&&void 0!==i&&i),\"flags\"in e)if(!1===e.flags)this._instance.persistence.unregister(ro);else if(e.flags)if(x(e.flags)){for(var r={},s=0;s<e.flags.length;s++)r[e.flags[s]]=!0;this._instance.persistence.register({[ro]:r})}else this._instance.persistence.register({[ro]:e.flags});return\"payloads\"in e&&(!1===e.payloads?this._instance.persistence.unregister(no):e.payloads&&this._instance.persistence.register({[no]:e.payloads})),void this.we()}this.we()}onFeatureFlags(t){if(this.addFeatureFlagsHandler(t),this.ue){var{flags:i,flagVariants:e}=this.ye();t(i,e)}return()=>this.removeFeatureFlagsHandler(t)}updateEarlyAccessFeatureEnrollment(t,i){var e,r=(this._instance.get_property(Pt)||[]).find((i=>i.flagKey===t)),s={[\"$feature_enrollment/\"+t]:i},n={$feature_flag:t,$feature_enrollment:i,$set:s};r&&(n.$early_access_feature_name=r.name),this._instance.capture(\"$feature_enrollment_update\",n),this.setPersonPropertiesForFlags(s,!1);var o=B({},this.getFlagVariants(),{[t]:i});null==(e=this._instance.persistence)||e.register({[eo]:Object.keys(ao(o)),[It]:o}),this.we()}getEarlyAccessFeatures(t,i,e){void 0===i&&(i=!1);var r=this._instance.get_property(Pt),s=e?\"&\"+e.map((t=>\"stage=\"+t)).join(\"&\"):\"\";if(r&&!i)return t(r);this._instance.me({url:this._instance.requestRouter.endpointFor(\"api\",\"/api/early_access_features/?token=\"+this._instance.config.token+s),method:\"GET\",callback:i=>{var e;if(i.json){var r=i.json.earlyAccessFeatures;return null==(e=this._instance.persistence)||e.register({[Pt]:r}),t(r)}}})}ye(){var t=this.getFlags(),i=this.getFlagVariants();return{flags:t.filter((t=>i[t])),flagVariants:Object.keys(i).filter((t=>i[t])).reduce(((t,e)=>(t[e]=i[e],t)),{})}}we(t){var{flags:i,flagVariants:e}=this.ye();this.featureFlagEventHandlers.forEach((r=>r(i,e,{errorsLoading:t})))}setPersonPropertiesForFlags(t,i){void 0===i&&(i=!0);var e=this._instance.get_property(Tt)||{};this._instance.register({[Tt]:B({},e,t)}),i&&this._instance.reloadFeatureFlags()}resetPersonPropertiesForFlags(){this._instance.unregister(Tt)}setGroupPropertiesForFlags(t,i){void 0===i&&(i=!0);var e=this._instance.get_property(Mt)||{};0!==Object.keys(e).length&&Object.keys(e).forEach((i=>{e[i]=B({},e[i],t[i]),delete t[i]})),this._instance.register({[Mt]:B({},e,t)}),i&&this._instance.reloadFeatureFlags()}resetGroupPropertiesForFlags(t){if(t){var i=this._instance.get_property(Mt)||{};this._instance.register({[Mt]:B({},i,{[t]:{}})})}else this._instance.unregister(Mt)}}var vo=[\"cookie\",\"localstorage\",\"localstorage+cookie\",\"sessionstorage\",\"memory\"];class co{constructor(t){this.S=t,this.props={},this.Se=!1,this.$e=(t=>{var i=\"\";return t.token&&(i=t.token.replace(/\\+/g,\"PL\").replace(/\\//g,\"SL\").replace(/=/g,\"EQ\")),t.persistence_name?\"ph_\"+t.persistence_name:\"ph_\"+i+\"_posthog\"})(t),this.q=this.ke(t),this.load(),t.debug&&j.info(\"Persistence loaded\",t.persistence,B({},this.props)),this.update_config(t,t),this.save()}ke(t){-1===vo.indexOf(t.persistence.toLowerCase())&&(j.critical(\"Unknown persistence type \"+t.persistence+\"; falling back to localStorage+cookie\"),t.persistence=\"localStorage+cookie\");var i=t.persistence.toLowerCase();return\"localstorage\"===i&&Wi.O()?Wi:\"localstorage+cookie\"===i&&Ji.O()?Ji:\"sessionstorage\"===i&&Xi.O()?Xi:\"memory\"===i?Ki:\"cookie\"===i?Bi:Ji.O()?Ji:Bi}properties(){var t={};return J(this.props,(function(i,e){if(e===It&&I(i))for(var r=Object.keys(i),n=0;n<r.length;n++)t[\"$feature/\"+r[n]]=i[r[n]];else a=e,l=!1,(C(o=Ht)?l:s&&o.indexOf===s?-1!=o.indexOf(a):(J(o,(function(t){if(l||(l=t===a))return W})),l))||(t[e]=i);var o,a,l})),t}load(){if(!this.xe){var t=this.q.D(this.$e);t&&(this.props=V({},t))}}save(){this.xe||this.q.L(this.$e,this.props,this.Ee,this.Ie,this.Pe,this.S.debug)}remove(){this.q.N(this.$e,!1),this.q.N(this.$e,!0)}clear(){this.remove(),this.props={}}register_once(t,i,e){if(I(t)){R(i)&&(i=\"None\"),this.Ee=R(e)?this.Re:e;var r=!1;if(J(t,((t,e)=>{this.props.hasOwnProperty(e)&&this.props[e]!==i||(this.props[e]=t,r=!0)})),r)return this.save(),!0}return!1}register(t,i){if(I(t)){this.Ee=R(i)?this.Re:i;var e=!1;if(J(t,((i,r)=>{t.hasOwnProperty(r)&&this.props[r]!==i&&(this.props[r]=i,e=!0)})),e)return this.save(),!0}return!1}unregister(t){t in this.props&&(delete this.props[t],this.save())}update_campaign_params(){if(!this.Se){var t=Wn(this.S.custom_campaign_params,this.S.mask_personal_data_properties,this.S.custom_personal_data_properties);P(Z(t))||this.register(t),this.Se=!0}}update_search_keyword(){var t;this.register((t=null==o?void 0:o.referrer)?Jn(t):{})}update_referrer_info(){var t;this.register_once({$referrer:Kn(),$referring_domain:null!=o&&o.referrer&&(null==(t=yi(o.referrer))?void 0:t.host)||\"$direct\"},void 0)}set_initial_person_info(){this.props[Nt]||this.props[jt]||this.register_once({[zt]:Yn(this.S.mask_personal_data_properties,this.S.custom_personal_data_properties)},void 0)}get_initial_props(){var t={};J([jt,Nt],(i=>{var e=this.props[i];e&&J(e,(function(i,e){t[\"$initial_\"+w(e)]=i}))}));var i,e,r=this.props[zt];if(r){var s=(i=Xn(r),e={},J(i,(function(t,i){e[\"$initial_\"+w(i)]=t})),e);V(t,s)}return t}safe_merge(t){return J(this.props,(function(i,e){e in t||(t[e]=i)})),t}update_config(t,i){if(this.Re=this.Ee=t.cookie_expiration,this.set_disabled(t.disable_persistence),this.set_cross_subdomain(t.cross_subdomain_cookie),this.set_secure(t.secure_cookie),t.persistence!==i.persistence){var e=this.ke(t),r=this.props;this.clear(),this.q=e,this.props=r,this.save()}}set_disabled(t){this.xe=t,this.xe?this.remove():this.save()}set_cross_subdomain(t){t!==this.Ie&&(this.Ie=t,this.remove(),this.save())}set_secure(t){t!==this.Pe&&(this.Pe=t,this.remove(),this.save())}set_event_timer(t,i){var e=this.props[at]||{};e[t]=i,this.props[at]=e,this.save()}remove_event_timer(t){var i=(this.props[at]||{})[t];return R(i)||(delete this.props[at][t],this.save()),i}get_property(t){return this.props[t]}set_property(t,i){this.props[t]=i,this.save()}}class fo{constructor(){this.Te={},this.Te={}}on(t,i){return this.Te[t]||(this.Te[t]=[]),this.Te[t].push(i),()=>{this.Te[t]=this.Te[t].filter((t=>t!==i))}}emit(t,i){for(var e of this.Te[t]||[])e(i);for(var r of this.Te[\"*\"]||[])r(t,i)}}class po{constructor(t){this.Me=new fo,this.Ce=(t,i)=>this.Oe(t,i)&&this.Fe(t,i)&&this.Ae(t,i),this.Oe=(t,i)=>null==i||!i.event||(null==t?void 0:t.event)===(null==i?void 0:i.event),this._instance=t,this.De=new Set,this.Le=new Set}init(){var t;if(!R(null==(t=this._instance)?void 0:t.Ne)){var i;null==(i=this._instance)||i.Ne(((t,i)=>{this.on(t,i)}))}}register(t){var i,e;if(!R(null==(i=this._instance)?void 0:i.Ne)&&(t.forEach((t=>{var i,e;null==(i=this.Le)||i.add(t),null==(e=t.steps)||e.forEach((t=>{var i;null==(i=this.De)||i.add((null==t?void 0:t.event)||\"\")}))})),null!=(e=this._instance)&&e.autocapture)){var r,s=new Set;t.forEach((t=>{var i;null==(i=t.steps)||i.forEach((t=>{null!=t&&t.selector&&s.add(null==t?void 0:t.selector)}))})),null==(r=this._instance)||r.autocapture.setElementSelectors(s)}}on(t,i){var e;null!=i&&0!=t.length&&(this.De.has(t)||this.De.has(null==i?void 0:i.event))&&this.Le&&(null==(e=this.Le)?void 0:e.size)>0&&this.Le.forEach((t=>{this.je(i,t)&&this.Me.emit(\"actionCaptured\",t.name)}))}ze(t){this.onAction(\"actionCaptured\",(i=>t(i)))}je(t,i){if(null==(null==i?void 0:i.steps))return!1;for(var e of i.steps)if(this.Ce(t,e))return!0;return!1}onAction(t,i){return this.Me.on(t,i)}Fe(t,i){if(null!=i&&i.url){var e,r=null==t||null==(e=t.properties)?void 0:e.$current_url;if(!r||\"string\"!=typeof r)return!1;if(!po.Ue(r,null==i?void 0:i.url,(null==i?void 0:i.url_matching)||\"contains\"))return!1}return!0}static Ue(i,e,r){switch(r){case\"regex\":return!!t&&Ns(i,e);case\"exact\":return e===i;case\"contains\":var s=po.qe(e).replace(/_/g,\".\").replace(/%/g,\".*\");return Ns(i,s);default:return!1}}static qe(t){return t.replace(/[|\\\\{}()[\\]^$+*?.]/g,\"\\\\$&\").replace(/-/g,\"\\\\x2d\")}Ae(t,i){if((null!=i&&i.href||null!=i&&i.tag_name||null!=i&&i.text)&&!this.Be(t).some((t=>!(null!=i&&i.href&&!po.Ue(t.href||\"\",null==i?void 0:i.href,(null==i?void 0:i.href_matching)||\"exact\"))&&((null==i||!i.tag_name||t.tag_name===(null==i?void 0:i.tag_name))&&!(null!=i&&i.text&&!po.Ue(t.text||\"\",null==i?void 0:i.text,(null==i?void 0:i.text_matching)||\"exact\")&&!po.Ue(t.$el_text||\"\",null==i?void 0:i.text,(null==i?void 0:i.text_matching)||\"exact\"))))))return!1;if(null!=i&&i.selector){var e,r=null==t||null==(e=t.properties)?void 0:e.$element_selectors;if(!r)return!1;if(!r.includes(null==i?void 0:i.selector))return!1}return!0}Be(t){return null==(null==t?void 0:t.properties.$elements)?[]:null==t?void 0:t.properties.$elements}}var go=z(\"[Surveys]\");class _o{constructor(t){this._instance=t,this.He=new Map,this.We=new Map}register(t){var i;R(null==(i=this._instance)?void 0:i.Ne)||(this.Ge(t),this.Je(t))}Je(t){var i=t.filter((t=>{var i,e;return(null==(i=t.conditions)?void 0:i.actions)&&(null==(e=t.conditions)||null==(e=e.actions)||null==(e=e.values)?void 0:e.length)>0}));if(0!==i.length){if(null==this.Ve){this.Ve=new po(this._instance),this.Ve.init();this.Ve.ze((t=>{this.onAction(t)}))}i.forEach((t=>{var i,e,r,s,n;t.conditions&&null!=(i=t.conditions)&&i.actions&&null!=(e=t.conditions)&&null!=(e=e.actions)&&e.values&&(null==(r=t.conditions)||null==(r=r.actions)||null==(r=r.values)?void 0:r.length)>0&&(null==(s=this.Ve)||s.register(t.conditions.actions.values),null==(n=t.conditions)||null==(n=n.actions)||null==(n=n.values)||n.forEach((i=>{if(i&&i.name){var e=this.We.get(i.name);e&&e.push(t.id),this.We.set(i.name,e||[t.id])}})))}))}}Ge(t){var i;if(0!==t.filter((t=>{var i,e;return(null==(i=t.conditions)?void 0:i.events)&&(null==(e=t.conditions)||null==(e=e.events)||null==(e=e.values)?void 0:e.length)>0})).length){null==(i=this._instance)||i.Ne(((t,i)=>{this.onEvent(t,i)})),t.forEach((t=>{var i;null==(i=t.conditions)||null==(i=i.events)||null==(i=i.values)||i.forEach((i=>{if(i&&i.name){var e=this.He.get(i.name);e&&e.push(t.id),this.He.set(i.name,e||[t.id])}}))}))}}onEvent(t,i){var e,r=(null==(e=this._instance)||null==(e=e.persistence)?void 0:e.props[Ot])||[];if(\"survey shown\"===t&&i&&r.length>0){var s;go.info(\"survey event matched, removing survey from activated surveys\",{event:t,eventPayload:i,existingActivatedSurveys:r});var n=null==i||null==(s=i.properties)?void 0:s.$survey_id;if(n){var o=r.indexOf(n);o>=0&&(r.splice(o,1),this.Ke(r))}}else this.He.has(t)&&(go.info(\"survey event matched, updating activated surveys\",{event:t,surveys:this.He.get(t)}),this.Ke(r.concat(this.He.get(t)||[])))}onAction(t){var i,e=(null==(i=this._instance)||null==(i=i.persistence)?void 0:i.props[Ot])||[];this.We.has(t)&&this.Ke(e.concat(this.We.get(t)||[]))}Ke(t){var i;null==(i=this._instance)||null==(i=i.persistence)||i.register({[Ot]:[...new Set(t)]})}getSurveys(){var t,i=null==(t=this._instance)||null==(t=t.persistence)?void 0:t.props[Ot];return i||[]}getEventToSurveys(){return this.He}Ye(){return this.Ve}}class mo{constructor(t){this.Xe=null,this.Qe=!1,this.Ze=!1,this.tr=[],this._instance=t,this._surveyEventReceiver=null}onRemoteConfig(t){var i=t.surveys;if(O(i))return go.warn(\"Decide not loaded yet. Not loading surveys.\");var e=x(i);this.ir=e?i.length>0:i,go.info(\"decide response received, hasSurveys: \"+this.ir),this.ir&&this.loadIfEnabled()}reset(){localStorage.removeItem(\"lastSeenSurveyDate\");for(var t=[],i=0;i<localStorage.length;i++){var e=localStorage.key(i);(null!=e&&e.startsWith(\"seenSurvey_\")||null!=e&&e.startsWith(\"inProgressSurvey_\"))&&t.push(e)}t.forEach((t=>localStorage.removeItem(t)))}loadIfEnabled(){if(!this.Xe)if(this.Ze)go.info(\"Already initializing surveys, skipping...\");else if(this._instance.config.disable_surveys)go.info(\"Disabled. Not loading surveys.\");else if(this.ir){var t=null==v?void 0:v.__PosthogExtensions__;if(t){this.Ze=!0;try{var i=t.generateSurveys;if(i)return void this.er(i);var e=t.loadExternalDependency;if(!e)return void this.rr(\"PostHog loadExternalDependency extension not found.\");e(this._instance,\"surveys\",(i=>{i||!t.generateSurveys?this.rr(\"Could not load surveys script\",i):this.er(t.generateSurveys)}))}catch(t){throw this.rr(\"Error initializing surveys\",t),t}finally{this.Ze=!1}}else go.error(\"PostHog Extensions not found.\")}else go.info(\"No surveys to load.\")}er(t){this.Xe=t(this._instance),this._surveyEventReceiver=new _o(this._instance),go.info(\"Surveys loaded successfully\"),this.sr({isLoaded:!0})}rr(t,i){go.error(t,i),this.sr({isLoaded:!1,error:t})}onSurveysLoaded(t){return this.tr.push(t),this.Xe&&this.sr({isLoaded:!0}),()=>{this.tr=this.tr.filter((i=>i!==t))}}getSurveys(t,i){if(void 0===i&&(i=!1),this._instance.config.disable_surveys)return go.info(\"Disabled. Not loading surveys.\"),t([]);var e=this._instance.get_property(Ct);if(e&&!i)return t(e,{isLoaded:!0});if(this.Qe)return t([],{isLoaded:!1,error:\"Surveys are already being loaded\"});try{this.Qe=!0,this._instance.me({url:this._instance.requestRouter.endpointFor(\"api\",\"/api/surveys/?token=\"+this._instance.config.token),method:\"GET\",timeout:this._instance.config.surveys_request_timeout_ms,callback:i=>{var e;this.Qe=!1;var r=i.statusCode;if(200!==r||!i.json){var s=\"Surveys API could not be loaded, status: \"+r;return go.error(s),t([],{isLoaded:!1,error:s})}var n,o=i.json.surveys||[],a=o.filter((t=>function(t){return!(!t.start_date||t.end_date)}(t)&&(function(t){var i;return!(null==(i=t.conditions)||null==(i=i.events)||null==(i=i.values)||!i.length)}(t)||function(t){var i;return!(null==(i=t.conditions)||null==(i=i.actions)||null==(i=i.values)||!i.length)}(t))));a.length>0&&(null==(n=this._surveyEventReceiver)||n.register(a));return null==(e=this._instance.persistence)||e.register({[Ct]:o}),t(o,{isLoaded:!0})}})}catch(t){throw this.Qe=!1,t}}sr(t){for(var i of this.tr)try{t.isLoaded?this.getSurveys(i):i([],t)}catch(t){go.error(\"Error in survey callback\",t)}}getActiveMatchingSurveys(t,i){if(void 0===i&&(i=!1),!O(this.Xe))return this.Xe.getActiveMatchingSurveys(t,i);go.warn(\"init was not called\")}nr(t){var i=null;return this.getSurveys((e=>{var r;i=null!==(r=e.find((i=>i.id===t)))&&void 0!==r?r:null})),i}ar(t){if(O(this.Xe))return{eligible:!1,reason:\"SDK is not enabled or survey functionality is not yet loaded\"};var i=\"string\"==typeof t?this.nr(t):t;return i?this.Xe.checkSurveyEligibility(i):{eligible:!1,reason:\"Survey not found\"}}canRenderSurvey(t){if(O(this.Xe))return go.warn(\"init was not called\"),{visible:!1,disabledReason:\"SDK is not enabled or survey functionality is not yet loaded\"};var i=this.ar(t);return{visible:i.eligible,disabledReason:i.reason}}canRenderSurveyAsync(t,i){return O(this.Xe)?(go.warn(\"init was not called\"),Promise.resolve({visible:!1,disabledReason:\"SDK is not enabled or survey functionality is not yet loaded\"})):new Promise((e=>{this.getSurveys((i=>{var r,s=null!==(r=i.find((i=>i.id===t)))&&void 0!==r?r:null;if(s){var n=this.ar(s);e({visible:n.eligible,disabledReason:n.reason})}else e({visible:!1,disabledReason:\"Survey not found\"})}),i)}))}renderSurvey(t,i){if(O(this.Xe))go.warn(\"init was not called\");else{var e=this.nr(t),r=null==o?void 0:o.querySelector(i);e?r?this.Xe.renderSurvey(e,r):go.warn(\"Survey element not found\"):go.warn(\"Survey not found\")}}}var bo=z(\"[RateLimiter]\");class wo{constructor(t){var i,e;this.serverLimits={},this.lastEventRateLimited=!1,this.checkForLimiting=t=>{var i=t.text;if(i&&i.length)try{(JSON.parse(i).quota_limited||[]).forEach((t=>{bo.info((t||\"events\")+\" is quota limited.\"),this.serverLimits[t]=(new Date).getTime()+6e4}))}catch(t){return void bo.warn('could not rate limit - continuing. Error: \"'+(null==t?void 0:t.message)+'\"',{text:i})}},this.instance=t,this.captureEventsPerSecond=(null==(i=t.config.rate_limiting)?void 0:i.events_per_second)||10,this.captureEventsBurstLimit=Math.max((null==(e=t.config.rate_limiting)?void 0:e.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}clientRateLimitContext(t){var i,e,r;void 0===t&&(t=!1);var s=(new Date).getTime(),n=null!==(i=null==(e=this.instance.persistence)?void 0:e.get_property(Lt))&&void 0!==i?i:{tokens:this.captureEventsBurstLimit,last:s};n.tokens+=(s-n.last)/1e3*this.captureEventsPerSecond,n.last=s,n.tokens>this.captureEventsBurstLimit&&(n.tokens=this.captureEventsBurstLimit);var o=n.tokens<1;return o||t||(n.tokens=Math.max(0,n.tokens-1)),!o||this.lastEventRateLimited||t||this.instance.capture(\"$$client_ingestion_warning\",{$$client_ingestion_warning_message:\"posthog-js client rate limited. Config is set to \"+this.captureEventsPerSecond+\" events per second and \"+this.captureEventsBurstLimit+\" events burst limit.\"},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=o,null==(r=this.instance.persistence)||r.set_property(Lt,n),{isRateLimited:o,remainingTokens:n.tokens}}isServerRateLimited(t){var i=this.serverLimits[t||\"events\"]||!1;return!1!==i&&(new Date).getTime()<i}}var yo=z(\"[RemoteConfig]\");class So{constructor(t){this._instance=t}get remoteConfig(){var t;return null==(t=v._POSTHOG_REMOTE_CONFIG)||null==(t=t[this._instance.config.token])?void 0:t.config}lr(t){var i,e;null!=(i=v.__PosthogExtensions__)&&i.loadExternalDependency?null==(e=v.__PosthogExtensions__)||null==e.loadExternalDependency||e.loadExternalDependency(this._instance,\"remote-config\",(()=>t(this.remoteConfig))):(yo.error(\"PostHog Extensions not found. Cannot load remote config.\"),t())}ur(t){this._instance.me({method:\"GET\",url:this._instance.requestRouter.endpointFor(\"assets\",\"/array/\"+this._instance.config.token+\"/config\"),callback:i=>{t(i.json)}})}load(){try{if(this.remoteConfig)return yo.info(\"Using preloaded remote config\",this.remoteConfig),void this.be(this.remoteConfig);if(this._instance.config.advanced_disable_decide)return void yo.warn(\"Remote config is disabled. Falling back to local config.\");this.lr((t=>{if(!t)return yo.info(\"No config found after loading remote JS config. Falling back to JSON.\"),void this.ur((t=>{this.be(t)}));this.be(t)}))}catch(t){yo.error(\"Error loading remote config\",t)}}be(t){t?this._instance.config.__preview_remote_config?(this._instance.be(t),!1!==t.hasFeatureFlags&&this._instance.featureFlags.ensureFlagsLoaded()):yo.info(\"__preview_remote_config is disabled. Logging config instead\",t):yo.error(\"Failed to fetch remote config from PostHog.\")}}var $o=3e3;class ko{constructor(t,i){this.hr=!0,this.dr=[],this.vr=Xe((null==i?void 0:i.flush_interval_ms)||$o,250,5e3,\"flush interval\",$o),this.cr=t}enqueue(t){this.dr.push(t),this.pr||this.gr()}unload(){this._r();var t=this.dr.length>0?this.mr():{},i=Object.values(t);[...i.filter((t=>0===t.url.indexOf(\"/e\"))),...i.filter((t=>0!==t.url.indexOf(\"/e\")))].map((t=>{this.cr(B({},t,{transport:\"sendBeacon\"}))}))}enable(){this.hr=!1,this.gr()}gr(){var t=this;this.hr||(this.pr=setTimeout((()=>{if(this._r(),this.dr.length>0){var i=this.mr(),e=function(){var e=i[r],s=(new Date).getTime();e.data&&x(e.data)&&J(e.data,(t=>{t.offset=Math.abs(t.timestamp-s),delete t.timestamp})),t.cr(e)};for(var r in i)e()}}),this.vr))}_r(){clearTimeout(this.pr),this.pr=void 0}mr(){var t={};return J(this.dr,(i=>{var e,r=i,s=(r?r.batchKey:null)||r.url;R(t[s])&&(t[s]=B({},r,{data:[]})),null==(e=t[s].data)||e.push(r.data)})),this.dr=[],t}}var xo=[\"retriesPerformedSoFar\"];class Eo{constructor(i){this.br=!1,this.wr=3e3,this.dr=[],this._instance=i,this.dr=[],this.yr=!0,!R(t)&&\"onLine\"in t.navigator&&(this.yr=t.navigator.onLine,st(t,\"online\",(()=>{this.yr=!0,this.Yi()})),st(t,\"offline\",(()=>{this.yr=!1})))}get length(){return this.dr.length}retriableRequest(t){var{retriesPerformedSoFar:i}=t,e=H(t,xo);F(i)&&i>0&&(e.url=Fs(e.url,{retry_count:i})),this._instance.me(B({},e,{callback:t=>{200!==t.statusCode&&(t.statusCode<400||t.statusCode>=500)&&(null!=i?i:0)<10?this.Sr(B({retriesPerformedSoFar:i},e)):null==e.callback||e.callback(t)}}))}Sr(t){var i=t.retriesPerformedSoFar||0;t.retriesPerformedSoFar=i+1;var e=function(t){var i=3e3*Math.pow(2,t),e=i/2,r=Math.min(18e5,i),s=(Math.random()-.5)*(r-e);return Math.ceil(r+s)}(i),r=Date.now()+e;this.dr.push({retryAt:r,requestOptions:t});var s=\"Enqueued failed request for retry in \"+e;navigator.onLine||(s+=\" (Browser is offline)\"),j.warn(s),this.br||(this.br=!0,this.$r())}$r(){this.kr&&clearTimeout(this.kr),this.kr=setTimeout((()=>{this.yr&&this.dr.length>0&&this.Yi(),this.$r()}),this.wr)}Yi(){var t=Date.now(),i=[],e=this.dr.filter((e=>e.retryAt<t||(i.push(e),!1)));if(this.dr=i,e.length>0)for(var{requestOptions:r}of e)this.retriableRequest(r)}unload(){for(var{requestOptions:t}of(this.kr&&(clearTimeout(this.kr),this.kr=void 0),this.dr))try{this._instance.me(B({},t,{transport:\"sendBeacon\"}))}catch(t){j.error(t)}this.dr=[]}}class Io{constructor(t){this.Er=()=>{var t,i,e,r;this.Ir||(this.Ir={});var s=this.scrollElement(),n=this.scrollY(),o=s?Math.max(0,s.scrollHeight-s.clientHeight):0,a=n+((null==s?void 0:s.clientHeight)||0),l=(null==s?void 0:s.scrollHeight)||0;this.Ir.lastScrollY=Math.ceil(n),this.Ir.maxScrollY=Math.max(n,null!==(t=this.Ir.maxScrollY)&&void 0!==t?t:0),this.Ir.maxScrollHeight=Math.max(o,null!==(i=this.Ir.maxScrollHeight)&&void 0!==i?i:0),this.Ir.lastContentY=a,this.Ir.maxContentY=Math.max(a,null!==(e=this.Ir.maxContentY)&&void 0!==e?e:0),this.Ir.maxContentHeight=Math.max(l,null!==(r=this.Ir.maxContentHeight)&&void 0!==r?r:0)},this._instance=t}getContext(){return this.Ir}resetContext(){var t=this.Ir;return setTimeout(this.Er,0),t}startMeasuringScrollPosition(){st(t,\"scroll\",this.Er,{capture:!0}),st(t,\"scrollend\",this.Er,{capture:!0}),st(t,\"resize\",this.Er)}scrollElement(){if(!this._instance.config.scroll_root_selector)return null==t?void 0:t.document.documentElement;var i=x(this._instance.config.scroll_root_selector)?this._instance.config.scroll_root_selector:[this._instance.config.scroll_root_selector];for(var e of i){var r=null==t?void 0:t.document.querySelector(e);if(r)return r}}scrollY(){if(this._instance.config.scroll_root_selector){var i=this.scrollElement();return i&&i.scrollTop||0}return t&&(t.scrollY||t.pageYOffset||t.document.documentElement.scrollTop)||0}scrollX(){if(this._instance.config.scroll_root_selector){var i=this.scrollElement();return i&&i.scrollLeft||0}return t&&(t.scrollX||t.pageXOffset||t.document.documentElement.scrollLeft)||0}}var Po=t=>Yn(null==t?void 0:t.config.mask_personal_data_properties,null==t?void 0:t.config.custom_personal_data_properties);class Ro{constructor(t,i,e,r){this.Pr=t=>{var i=this.Rr();if(!i||i.sessionId!==t){var e={sessionId:t,props:this.Tr(this._instance)};this.Mr.register({[Dt]:e})}},this._instance=t,this.Cr=i,this.Mr=e,this.Tr=r||Po,this.Cr.onSessionId(this.Pr)}Rr(){return this.Mr.props[Dt]}getSetOnceProps(){var t,i=null==(t=this.Rr())?void 0:t.props;return i?\"r\"in i?Xn(i):{$referring_domain:i.referringDomain,$pathname:i.initialPathName,utm_source:i.utm_source,utm_campaign:i.utm_campaign,utm_medium:i.utm_medium,utm_content:i.utm_content,utm_term:i.utm_term}:{}}getSessionProps(){var t={};return J(Z(this.getSetOnceProps()),((i,e)=>{\"$current_url\"===e&&(e=\"url\"),t[\"$session_entry_\"+w(e)]=i})),t}}var To=z(\"[SessionId]\");class Mo{constructor(t,i,e){var r;if(this.Or=[],!t.persistence)throw new Error(\"SessionIdManager requires a PostHogPersistence instance\");if(t.config.__preview_experimental_cookieless_mode)throw new Error(\"SessionIdManager cannot be used with __preview_experimental_cookieless_mode\");this.S=t.config,this.Mr=t.persistence,this.oi=void 0,this.kt=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this.Fr=i||Ni,this.Ar=e||Ni;var s=this.S.persistence_name||this.S.token,n=this.S.session_idle_timeout_seconds||1800;if(this._sessionTimeoutMs=1e3*Xe(n,60,36e3,\"session_idle_timeout_seconds\",1800),t.register({$configured_session_timeout_ms:this._sessionTimeoutMs}),this.Dr(),this.Lr=\"ph_\"+s+\"_window_id\",this.Nr=\"ph_\"+s+\"_primary_window_exists\",this.jr()){var o=Xi.D(this.Lr),a=Xi.D(this.Nr);o&&!a?this.oi=o:Xi.N(this.Lr),Xi.L(this.Nr,!0)}if(null!=(r=this.S.bootstrap)&&r.sessionID)try{var l=(t=>{var i=t.replace(/-/g,\"\");if(32!==i.length)throw new Error(\"Not a valid UUID\");if(\"7\"!==i[12])throw new Error(\"Not a UUIDv7\");return parseInt(i.substring(0,12),16)})(this.S.bootstrap.sessionID);this.zr(this.S.bootstrap.sessionID,(new Date).getTime(),l)}catch(t){To.error(\"Invalid sessionID in bootstrap\",t)}this.Ur()}get sessionTimeoutMs(){return this._sessionTimeoutMs}onSessionId(t){return R(this.Or)&&(this.Or=[]),this.Or.push(t),this.kt&&t(this.kt,this.oi),()=>{this.Or=this.Or.filter((i=>i!==t))}}jr(){return\"memory\"!==this.S.persistence&&!this.Mr.xe&&Xi.O()}qr(t){t!==this.oi&&(this.oi=t,this.jr()&&Xi.L(this.Lr,t))}Br(){return this.oi?this.oi:this.jr()?Xi.D(this.Lr):null}zr(t,i,e){t===this.kt&&i===this._sessionActivityTimestamp&&e===this._sessionStartTimestamp||(this._sessionStartTimestamp=e,this._sessionActivityTimestamp=i,this.kt=t,this.Mr.register({[$t]:[i,t,e]}))}Hr(){if(this.kt&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this.kt,this._sessionStartTimestamp];var t=this.Mr.props[$t];return x(t)&&2===t.length&&t.push(t[0]),t||[0,null,0]}resetSessionId(){this.zr(null,null,null)}Ur(){st(t,\"beforeunload\",(()=>{this.jr()&&Xi.N(this.Nr)}),{capture:!1})}checkAndGetSessionAndWindowId(t,i){if(void 0===t&&(t=!1),void 0===i&&(i=null),this.S.__preview_experimental_cookieless_mode)throw new Error(\"checkAndGetSessionAndWindowId should not be called in __preview_experimental_cookieless_mode\");var e=i||(new Date).getTime(),[r,s,n]=this.Hr(),o=this.Br(),a=F(n)&&n>0&&Math.abs(e-n)>864e5,l=!1,u=!s,h=!t&&Math.abs(e-r)>this.sessionTimeoutMs;u||h||a?(s=this.Fr(),o=this.Ar(),To.info(\"new session ID generated\",{sessionId:s,windowId:o,changeReason:{noSessionId:u,activityTimeout:h,sessionPastMaximumLength:a}}),n=e,l=!0):o||(o=this.Ar(),l=!0);var d=0===r||!t||a?e:r,v=0===n?(new Date).getTime():n;return this.qr(o),this.zr(s,d,v),t||this.Dr(),l&&this.Or.forEach((t=>t(s,o,l?{noSessionId:u,activityTimeout:h,sessionPastMaximumLength:a}:void 0))),{sessionId:s,windowId:o,sessionStartTimestamp:v,changeReason:l?{noSessionId:u,activityTimeout:h,sessionPastMaximumLength:a}:void 0,lastActivityTimestamp:r}}Dr(){clearTimeout(this.Wr),this.Wr=setTimeout((()=>{this.resetSessionId()}),1.1*this.sessionTimeoutMs)}}var Co=[\"$set_once\",\"$set\"],Oo=z(\"[SiteApps]\");class Fo{constructor(t){this._instance=t,this.Gr=[],this.apps={}}get isEnabled(){return!!this._instance.config.opt_in_site_apps}Jr(t,i){if(i){var e=this.globalsForEvent(i);this.Gr.push(e),this.Gr.length>1e3&&(this.Gr=this.Gr.slice(10))}}get siteAppLoaders(){var t;return null==(t=v._POSTHOG_REMOTE_CONFIG)||null==(t=t[this._instance.config.token])?void 0:t.siteApps}init(){if(this.isEnabled){var t=this._instance.Ne(this.Jr.bind(this));this.Vr=()=>{t(),this.Gr=[],this.Vr=void 0}}}globalsForEvent(t){var i,e,r,s,n,o,a;if(!t)throw new Error(\"Event payload is required\");var l={},u=this._instance.get_property(\"$groups\")||[],h=this._instance.get_property(\"$stored_group_properties\")||{};for(var[d,v]of Object.entries(h))l[d]={id:u[d],type:d,properties:v};var{$set_once:c,$set:f}=t;return{event:B({},H(t,Co),{properties:B({},t.properties,f?{$set:B({},null!==(i=null==(e=t.properties)?void 0:e.$set)&&void 0!==i?i:{},f)}:{},c?{$set_once:B({},null!==(r=null==(s=t.properties)?void 0:s.$set_once)&&void 0!==r?r:{},c)}:{}),elements_chain:null!==(n=null==(o=t.properties)?void 0:o.$elements_chain)&&void 0!==n?n:\"\",distinct_id:null==(a=t.properties)?void 0:a.distinct_id}),person:{properties:this._instance.get_property(\"$stored_person_properties\")},groups:l}}setupSiteApp(t){var i=this.apps[t.id],e=()=>{var e;(!i.errored&&this.Gr.length&&(Oo.info(\"Processing \"+this.Gr.length+\" events for site app with id \"+t.id),this.Gr.forEach((t=>null==i.processEvent?void 0:i.processEvent(t))),i.processedBuffer=!0),Object.values(this.apps).every((t=>t.processedBuffer||t.errored)))&&(null==(e=this.Vr)||e.call(this))},r=!1,s=s=>{i.errored=!s,i.loaded=!0,Oo.info(\"Site app with id \"+t.id+\" \"+(s?\"loaded\":\"errored\")),r&&e()};try{var{processEvent:n}=t.init({posthog:this._instance,callback:t=>{s(t)}});n&&(i.processEvent=n),r=!0}catch(i){Oo.error(\"Error while initializing PostHog app with config id \"+t.id,i),s(!1)}if(r&&i.loaded)try{e()}catch(e){Oo.error(\"Error while processing buffered events PostHog app with config id \"+t.id,e),i.errored=!0}}Kr(){var t=this.siteAppLoaders||[];for(var i of t)this.apps[i.id]={id:i.id,loaded:!1,errored:!1,processedBuffer:!1};for(var e of t)this.setupSiteApp(e)}Yr(t){if(0!==Object.keys(this.apps).length){var i=this.globalsForEvent(t);for(var e of Object.values(this.apps))try{null==e.processEvent||e.processEvent(i)}catch(i){Oo.error(\"Error while processing event \"+t.event+\" for site app \"+e.id,i)}}}onRemoteConfig(t){var i,e,r,s=this;if(null!=(i=this.siteAppLoaders)&&i.length)return this.isEnabled?(this.Kr(),void this._instance.on(\"eventCaptured\",(t=>this.Yr(t)))):void Oo.error('PostHog site apps are disabled. Enable the \"opt_in_site_apps\" config to proceed.');if(null==(e=this.Vr)||e.call(this),null!=(r=t.siteApps)&&r.length)if(this.isEnabled){var n=function(t){var i;v[\"__$$ph_site_app_\"+t]=s._instance,null==(i=v.__PosthogExtensions__)||null==i.loadSiteApp||i.loadSiteApp(s._instance,a,(i=>{if(i)return Oo.error(\"Error while initializing PostHog app with config id \"+t,i)}))};for(var{id:o,url:a}of t.siteApps)n(o)}else Oo.error('PostHog site apps are disabled. Enable the \"opt_in_site_apps\" config to proceed.')}}var Ao=[\"amazonbot\",\"amazonproductbot\",\"app.hypefactors.com\",\"applebot\",\"archive.org_bot\",\"awariobot\",\"backlinksextendedbot\",\"baiduspider\",\"bingbot\",\"bingpreview\",\"chrome-lighthouse\",\"dataforseobot\",\"deepscan\",\"duckduckbot\",\"facebookexternal\",\"facebookcatalog\",\"http://yandex.com/bots\",\"hubspot\",\"ia_archiver\",\"linkedinbot\",\"meta-externalagent\",\"mj12bot\",\"msnbot\",\"nessus\",\"petalbot\",\"pinterest\",\"prerender\",\"rogerbot\",\"screaming frog\",\"sebot-wa\",\"sitebulb\",\"slackbot\",\"slurp\",\"trendictionbot\",\"turnitin\",\"twitterbot\",\"vercelbot\",\"yahoo! slurp\",\"yandexbot\",\"zoombot\",\"bot.htm\",\"bot.php\",\"(bot;\",\"bot/\",\"crawler\",\"ahrefsbot\",\"ahrefssiteaudit\",\"semrushbot\",\"siteauditbot\",\"splitsignalbot\",\"gptbot\",\"oai-searchbot\",\"chatgpt-user\",\"perplexitybot\",\"better uptime bot\",\"sentryuptimebot\",\"uptimerobot\",\"headlesschrome\",\"cypress\",\"google-hoteladsverifier\",\"adsbot-google\",\"apis-google\",\"duplexweb-google\",\"feedfetcher-google\",\"google favicon\",\"google web preview\",\"google-read-aloud\",\"googlebot\",\"googleother\",\"google-cloudvertexbot\",\"googleweblight\",\"mediapartners-google\",\"storebot-google\",\"google-inspectiontool\",\"bytespider\"],Do=function(t,i){if(!t)return!1;var e=t.toLowerCase();return Ao.concat(i||[]).some((t=>{var i=t.toLowerCase();return-1!==e.indexOf(i)}))},Lo=function(t,i){if(!t)return!1;var e=t.userAgent;if(e&&Do(e,i))return!0;try{var r=null==t?void 0:t.userAgentData;if(null!=r&&r.brands&&r.brands.some((t=>Do(null==t?void 0:t.brand,i))))return!0}catch(t){}return!!t.webdriver},No=function(t){return t.US=\"us\",t.EU=\"eu\",t.CUSTOM=\"custom\",t}({}),jo=\"i.posthog.com\";class zo{constructor(t){this.Xr={},this.instance=t}get apiHost(){var t=this.instance.config.api_host.trim().replace(/\\/$/,\"\");return\"https://app.posthog.com\"===t?\"https://us.i.posthog.com\":t}get uiHost(){var t,i=null==(t=this.instance.config.ui_host)?void 0:t.replace(/\\/$/,\"\");return i||(i=this.apiHost.replace(\".\"+jo,\".posthog.com\")),\"https://app.posthog.com\"===i?\"https://us.posthog.com\":i}get region(){return this.Xr[this.apiHost]||(/https:\\/\\/(app|us|us-assets)(\\.i)?\\.posthog\\.com/i.test(this.apiHost)?this.Xr[this.apiHost]=No.US:/https:\\/\\/(eu|eu-assets)(\\.i)?\\.posthog\\.com/i.test(this.apiHost)?this.Xr[this.apiHost]=No.EU:this.Xr[this.apiHost]=No.CUSTOM),this.Xr[this.apiHost]}endpointFor(t,i){if(void 0===i&&(i=\"\"),i&&(i=\"/\"===i[0]?i:\"/\"+i),\"ui\"===t)return this.uiHost+i;if(this.region===No.CUSTOM)return this.apiHost+i;var e=jo+i;switch(t){case\"assets\":return\"https://\"+this.region+\"-assets.\"+e;case\"api\":return\"https://\"+this.region+\".\"+e}}}var Uo={icontains:(i,e)=>!!t&&e.href.toLowerCase().indexOf(i.toLowerCase())>-1,not_icontains:(i,e)=>!!t&&-1===e.href.toLowerCase().indexOf(i.toLowerCase()),regex:(i,e)=>!!t&&Ns(e.href,i),not_regex:(i,e)=>!!t&&!Ns(e.href,i),exact:(t,i)=>i.href===t,is_not:(t,i)=>i.href!==t};class qo{constructor(t){var i=this;this.getWebExperimentsAndEvaluateDisplayLogic=function(t){void 0===t&&(t=!1),i.getWebExperiments((t=>{qo.Qr(\"retrieved web experiments from the server\"),i.Zr=new Map,t.forEach((t=>{if(t.feature_flag_key){var e;if(i.Zr)qo.Qr(\"setting flag key \",t.feature_flag_key,\" to web experiment \",t),null==(e=i.Zr)||e.set(t.feature_flag_key,t);var r=i._instance.getFeatureFlag(t.feature_flag_key);T(r)&&t.variants[r]&&i.ts(t.name,r,t.variants[r].transforms)}else if(t.variants)for(var s in t.variants){var n=t.variants[s];qo.es(n)&&i.ts(t.name,s,n.transforms)}}))}),t)},this._instance=t,this._instance.onFeatureFlags((t=>{this.onFeatureFlags(t)}))}onFeatureFlags(t){if(this._is_bot())qo.Qr(\"Refusing to render web experiment since the viewer is a likely bot\");else if(!this._instance.config.disable_web_experiments){if(O(this.Zr))return this.Zr=new Map,this.loadIfEnabled(),void this.previewWebExperiment();qo.Qr(\"applying feature flags\",t),t.forEach((t=>{var i;if(this.Zr&&null!=(i=this.Zr)&&i.has(t)){var e,r=this._instance.getFeatureFlag(t),s=null==(e=this.Zr)?void 0:e.get(t);r&&null!=s&&s.variants[r]&&this.ts(s.name,r,s.variants[r].transforms)}}))}}previewWebExperiment(){var t=qo.getWindowLocation();if(null!=t&&t.search){var i=$i(null==t?void 0:t.search,\"__experiment_id\"),e=$i(null==t?void 0:t.search,\"__experiment_variant\");i&&e&&(qo.Qr(\"previewing web experiments \"+i+\" && \"+e),this.getWebExperiments((t=>{this.rs(parseInt(i),e,t)}),!1,!0))}}loadIfEnabled(){this._instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()}getWebExperiments(t,i,e){if(this._instance.config.disable_web_experiments&&!e)return t([]);var r=this._instance.get_property(\"$web_experiments\");if(r&&!i)return t(r);this._instance.me({url:this._instance.requestRouter.endpointFor(\"api\",\"/api/web_experiments/?token=\"+this._instance.config.token),method:\"GET\",callback:i=>{if(200!==i.statusCode||!i.json)return t([]);var e=i.json.experiments||[];return t(e)}})}rs(t,i,e){var r=e.filter((i=>i.id===t));r&&r.length>0&&(qo.Qr(\"Previewing web experiment [\"+r[0].name+\"] with variant [\"+i+\"]\"),this.ts(r[0].name,i,r[0].variants[i].transforms))}static es(t){return!O(t.conditions)&&(qo.ss(t)&&qo.ns(t))}static ss(t){var i;if(O(t.conditions)||O(null==(i=t.conditions)?void 0:i.url))return!0;var e,r,s,n=qo.getWindowLocation();return!!n&&(null==(e=t.conditions)||!e.url||Uo[null!==(r=null==(s=t.conditions)?void 0:s.urlMatchType)&&void 0!==r?r:\"icontains\"](t.conditions.url,n))}static getWindowLocation(){return null==t?void 0:t.location}static ns(t){var i;if(O(t.conditions)||O(null==(i=t.conditions)?void 0:i.utm))return!0;var e=Wn();if(e.utm_source){var r,s,n,o,a,l,u,h,d=null==(r=t.conditions)||null==(r=r.utm)||!r.utm_campaign||(null==(s=t.conditions)||null==(s=s.utm)?void 0:s.utm_campaign)==e.utm_campaign,v=null==(n=t.conditions)||null==(n=n.utm)||!n.utm_source||(null==(o=t.conditions)||null==(o=o.utm)?void 0:o.utm_source)==e.utm_source,c=null==(a=t.conditions)||null==(a=a.utm)||!a.utm_medium||(null==(l=t.conditions)||null==(l=l.utm)?void 0:l.utm_medium)==e.utm_medium,f=null==(u=t.conditions)||null==(u=u.utm)||!u.utm_term||(null==(h=t.conditions)||null==(h=h.utm)?void 0:h.utm_term)==e.utm_term;return d&&c&&f&&v}return!1}static Qr(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),r=1;r<i;r++)e[r-1]=arguments[r];j.info(\"[WebExperiments] \"+t,e)}ts(t,i,e){this._is_bot()?qo.Qr(\"Refusing to render web experiment since the viewer is a likely bot\"):\"control\"!==i?e.forEach((e=>{if(e.selector){var r;qo.Qr(\"applying transform of variant \"+i+\" for experiment \"+t+\" \",e);var s=null==(r=document)?void 0:r.querySelectorAll(e.selector);null==s||s.forEach((t=>{var i=t;e.html&&(i.innerHTML=e.html),e.css&&i.setAttribute(\"style\",e.css)}))}})):qo.Qr(\"Control variants leave the page unmodified.\")}_is_bot(){return n&&this._instance?Lo(n,this._instance.config.custom_blocked_useragents):void 0}}var Bo={},Ho=()=>{},Wo=\"posthog\",Go=!Cs&&-1===(null==d?void 0:d.indexOf(\"MSIE\"))&&-1===(null==d?void 0:d.indexOf(\"Mozilla\")),Jo=()=>{var i;return{api_host:\"https://us.i.posthog.com\",ui_host:null,token:\"\",autocapture:!0,rageclick:!0,cross_subdomain_cookie:et(null==o?void 0:o.location),persistence:\"localStorage+cookie\",persistence_name:\"\",loaded:Ho,save_campaign_params:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:!0,capture_pageleave:\"if_capture_pageview\",debug:a&&T(null==a?void 0:a.search)&&-1!==a.search.indexOf(\"__posthog_debug=true\")||!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,disable_external_dependency_loading:!1,enable_recording_console_log:void 0,secure_cookie:\"https:\"===(null==t||null==(i=t.location)?void 0:i.protocol),ip:!0,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:\"localStorage\",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,mask_personal_data_properties:!1,custom_personal_data_properties:[],advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,surveys_request_timeout_ms:1e4,on_request_error:t=>{var i=\"Bad HTTP status: \"+t.statusCode+\" \"+t.text;j.error(i)},get_device_id:t=>t,capture_performance:void 0,name:\"posthog\",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:\"identified_only\",before_send:void 0,request_queue_config:{flush_interval_ms:$o},_onCapture:Ho}},Vo=t=>{var i={};R(t.process_person)||(i.person_profiles=t.process_person),R(t.xhr_headers)||(i.request_headers=t.xhr_headers),R(t.cookie_name)||(i.persistence_name=t.cookie_name),R(t.disable_cookie)||(i.disable_persistence=t.disable_cookie),R(t.store_google)||(i.save_campaign_params=t.store_google),R(t.verbose)||(i.debug=t.verbose);var e=V({},i,t);return x(t.property_blacklist)&&(R(t.property_denylist)?e.property_denylist=t.property_blacklist:x(t.property_denylist)?e.property_denylist=[...t.property_blacklist,...t.property_denylist]:j.error(\"Invalid value for property_denylist config: \"+t.property_denylist)),e};class Ko{constructor(){this.__forceAllowLocalhost=!1}get os(){return this.__forceAllowLocalhost}set os(t){j.error(\"WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`\"),this.__forceAllowLocalhost=t}}class Yo{get decideEndpointWasHit(){var t,i;return null!==(t=null==(i=this.featureFlags)?void 0:i.hasLoadedFlags)&&void 0!==t&&t}constructor(){this.webPerformance=new Ko,this.ls=!1,this.version=c.LIB_VERSION,this.us=new fo,this._calculate_event_properties=this.calculateEventProperties.bind(this),this.config=Jo(),this.SentryIntegration=gs,this.sentryIntegration=t=>function(t,i){var e=ps(t,i);return{name:fs,processEvent:t=>e(t)}}(this,t),this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint=\"/e/\",this.hs=!1,this.ds=null,this.vs=null,this.cs=null,this.featureFlags=new ho(this),this.toolbar=new ys(this),this.scrollManager=new Io(this),this.pageViewManager=new Ts(this),this.surveys=new mo(this),this.experiments=new qo(this),this.exceptions=new Bs(this),this.rateLimiter=new wo(this),this.requestRouter=new zo(this),this.consent=new Zi(this),this.people={set:(t,i,e)=>{var r=T(t)?{[t]:i}:t;this.setPersonProperties(r),null==e||e({})},set_once:(t,i,e)=>{var r=T(t)?{[t]:i}:t;this.setPersonProperties(void 0,r),null==e||e({})}},this.on(\"eventCaptured\",(t=>j.info('send \"'+(null==t?void 0:t.event)+'\"',t)))}init(t,i,e){if(e&&e!==Wo){var r,s=null!==(r=Bo[e])&&void 0!==r?r:new Yo;return s._init(t,i,e),Bo[e]=s,Bo[Wo][e]=s,s}return this._init(t,i,e)}_init(i,e,r){var s,n;if(void 0===e&&(e={}),R(i)||M(i))return j.critical(\"PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()\"),this;if(this.__loaded)return j.warn(\"You have already initialized PostHog! Re-initializing is a no-op\"),this;this.__loaded=!0,this.config={},this.fs=[],e.person_profiles&&(this.vs=e.person_profiles),this.set_config(V({},Jo(),Vo(e),{name:r,token:i})),this.config.on_xhr_error&&j.error(\"on_xhr_error is deprecated. Use on_request_error instead\"),this.compression=e.disable_compression?void 0:g.GZipJS,this.persistence=new co(this.config),this.sessionPersistence=\"sessionStorage\"===this.config.persistence||\"memory\"===this.config.persistence?this.persistence:new co(B({},this.config,{persistence:\"sessionStorage\"}));var o=B({},this.persistence.props),a=B({},this.sessionPersistence.props);if(this.ps=new ko((t=>this.gs(t)),this.config.request_queue_config),this._s=new Eo(this),this.__request_queue=[],this.config.__preview_experimental_cookieless_mode||(this.sessionManager=new Mo(this),this.sessionPropsManager=new Ro(this,this.sessionManager,this.persistence)),new $s(this).startIfEnabledOrStop(),this.siteApps=new Fo(this),null==(s=this.siteApps)||s.init(),this.config.__preview_experimental_cookieless_mode||(this.sessionRecording=new ds(this),this.sessionRecording.startIfEnabledOrStop()),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new Mi(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new Rs(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new Es(this),this.exceptionObserver=new ne(this),this.exceptionObserver.startIfEnabled(),this.deadClicksAutocapture=new re(this,ee),this.deadClicksAutocapture.startIfEnabled(),this.historyAutocapture=new Le(this),this.historyAutocapture.startIfEnabled(),c.DEBUG=c.DEBUG||this.config.debug,c.DEBUG&&j.info(\"Starting in debug mode\",{this:this,config:e,thisC:B({},this.config),p:o,s:a}),this.bs(),void 0!==(null==(n=e.bootstrap)?void 0:n.distinctID)){var l,u,h=this.config.get_device_id(Ni()),d=null!=(l=e.bootstrap)&&l.isIdentifiedID?h:e.bootstrap.distinctID;this.persistence.set_property(At,null!=(u=e.bootstrap)&&u.isIdentifiedID?\"identified\":\"anonymous\"),this.register({distinct_id:e.bootstrap.distinctID,$device_id:d})}if(this.ws()){var v,f,p=Object.keys((null==(v=e.bootstrap)?void 0:v.featureFlags)||{}).filter((t=>{var i;return!(null==(i=e.bootstrap)||null==(i=i.featureFlags)||!i[t])})).reduce(((t,i)=>{var r;return t[i]=(null==(r=e.bootstrap)||null==(r=r.featureFlags)?void 0:r[i])||!1,t}),{}),_=Object.keys((null==(f=e.bootstrap)?void 0:f.featureFlagPayloads)||{}).filter((t=>p[t])).reduce(((t,i)=>{var r,s;null!=(r=e.bootstrap)&&null!=(r=r.featureFlagPayloads)&&r[i]&&(t[i]=null==(s=e.bootstrap)||null==(s=s.featureFlagPayloads)?void 0:s[i]);return t}),{});this.featureFlags.receivedFeatureFlags({featureFlags:p,featureFlagPayloads:_})}if(this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Bt,$device_id:null},\"\");else if(!this.get_distinct_id()){var m=this.config.get_device_id(Ni());this.register_once({distinct_id:m,$device_id:m},\"\"),this.persistence.set_property(At,\"anonymous\")}return st(t,\"onpagehide\"in self?\"pagehide\":\"unload\",this._handle_unload.bind(this),{passive:!1}),this.toolbar.maybeLoadToolbar(),e.segment?cs(this,(()=>this.ys())):this.ys(),E(this.config._onCapture)&&this.config._onCapture!==Ho&&(j.warn(\"onCapture is deprecated. Please use `before_send` instead\"),this.on(\"eventCaptured\",(t=>this.config._onCapture(t.event,t)))),this}be(t){var i,e,r,s,n,a,l,u;if(!o||!o.body)return j.info(\"document not ready yet, trying again in 500 milliseconds...\"),void setTimeout((()=>{this.be(t)}),500);this.compression=void 0,t.supportedCompression&&!this.config.disable_compression&&(this.compression=m(t.supportedCompression,g.GZipJS)?g.GZipJS:m(t.supportedCompression,g.Base64)?g.Base64:void 0),null!=(i=t.analytics)&&i.endpoint&&(this.analyticsDefaultEndpoint=t.analytics.endpoint),this.set_config({person_profiles:this.vs?this.vs:\"identified_only\"}),null==(e=this.siteApps)||e.onRemoteConfig(t),null==(r=this.sessionRecording)||r.onRemoteConfig(t),null==(s=this.autocapture)||s.onRemoteConfig(t),null==(n=this.heatmaps)||n.onRemoteConfig(t),this.surveys.onRemoteConfig(t),null==(a=this.webVitalsAutocapture)||a.onRemoteConfig(t),null==(l=this.exceptionObserver)||l.onRemoteConfig(t),this.exceptions.onRemoteConfig(t),null==(u=this.deadClicksAutocapture)||u.onRemoteConfig(t)}ys(){try{this.config.loaded(this)}catch(t){j.critical(\"`loaded` function failed\",t)}this.Ss(),this.config.capture_pageview&&setTimeout((()=>{this.consent.isOptedIn()&&this.$s()}),1),new So(this).load(),this.featureFlags.decide()}Ss(){var t;this.has_opted_out_capturing()||this.config.request_batching&&(null==(t=this.ps)||t.enable())}_dom_loaded(){this.has_opted_out_capturing()||G(this.__request_queue,(t=>this.gs(t))),this.__request_queue=[],this.Ss()}_handle_unload(){var t,i;this.config.request_batching?(this.ks()&&this.capture(\"$pageleave\"),null==(t=this.ps)||t.unload(),null==(i=this._s)||i.unload()):this.ks()&&this.capture(\"$pageleave\",null,{transport:\"sendBeacon\"})}me(t){this.__loaded&&(Go?this.__request_queue.push(t):this.rateLimiter.isServerRateLimited(t.batchKey)||(t.transport=t.transport||this.config.api_transport,t.url=Fs(t.url,{ip:this.config.ip?1:0}),t.headers=B({},this.config.request_headers),t.compression=\"best-available\"===t.compression?this.compression:t.compression,t.fetchOptions=t.fetchOptions||this.config.fetch_options,(t=>{var i,e,r,s=B({},t);s.timeout=s.timeout||6e4,s.url=Fs(s.url,{_:(new Date).getTime().toString(),ver:c.LIB_VERSION,compression:s.compression});var n=null!==(i=s.transport)&&void 0!==i?i:\"fetch\",o=null!==(e=null==(r=rt(Ls,(t=>t.transport===n)))?void 0:r.method)&&void 0!==e?e:Ls[0].method;if(!o)throw new Error(\"No available transport method\");o(s)})(B({},t,{callback:i=>{var e,r;(this.rateLimiter.checkForLimiting(i),i.statusCode>=400)&&(null==(e=(r=this.config).on_request_error)||e.call(r,i));null==t.callback||t.callback(i)}}))))}gs(t){this._s?this._s.retriableRequest(t):this.me(t)}_execute_array(t){var i,e=[],r=[],s=[];G(t,(t=>{t&&(i=t[0],x(i)?s.push(t):E(t)?t.call(this):x(t)&&\"alias\"===i?e.push(t):x(t)&&-1!==i.indexOf(\"capture\")&&E(this[i])?s.push(t):r.push(t))}));var n=function(t,i){G(t,(function(t){if(x(t[0])){var e=i;J(t,(function(t){e=e[t[0]].apply(e,t.slice(1))}))}else this[t[0]].apply(this,t.slice(1))}),i)};n(e,this),n(r,this),n(s,this)}ws(){var t,i;return(null==(t=this.config.bootstrap)?void 0:t.featureFlags)&&Object.keys(null==(i=this.config.bootstrap)?void 0:i.featureFlags).length>0||!1}push(t){this._execute_array([t])}capture(t,i,e){var r;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this.ps){if(!this.consent.isOptedOut())if(!R(t)&&T(t)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var s=null!=e&&e.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(null==s||!s.isRateLimited){null!=i&&i.$current_url&&!T(null==i?void 0:i.$current_url)&&(j.error(\"Invalid `$current_url` property provided to `posthog.capture`. Input must be a string. Ignoring provided value.\"),null==i||delete i.$current_url),this.sessionPersistence.update_search_keyword(),this.config.save_campaign_params&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.save_campaign_params||this.config.save_referrer)&&this.persistence.set_initial_person_info();var n=new Date,o=(null==e?void 0:e.timestamp)||n,a=Ni(),l={uuid:a,event:t,properties:this.calculateEventProperties(t,i||{},o,a)};s&&(l.properties.$lib_rate_limit_remaining_tokens=s.remainingTokens),(null==e?void 0:e.$set)&&(l.$set=null==e?void 0:e.$set);var u=this.xs(null==e?void 0:e.$set_once);u&&(l.$set_once=u),(l=tt(l,null!=e&&e._noTruncate?null:this.config.properties_string_max_length)).timestamp=o,R(null==e?void 0:e.timestamp)||(l.properties.$event_time_override_provided=!0,l.properties.$event_time_override_system_time=n);var h=B({},l.properties.$set,l.$set);if(P(h)||this.setPersonPropertiesForFlags(h),!O(this.config.before_send)){var d=this.Es(l);if(!d)return;l=d}this.us.emit(\"eventCaptured\",l);var v={method:\"POST\",url:null!==(r=null==e?void 0:e._url)&&void 0!==r?r:this.requestRouter.endpointFor(\"api\",this.analyticsDefaultEndpoint),data:l,compression:\"best-available\",batchKey:null==e?void 0:e._batchKey};return!this.config.request_batching||e&&(null==e||!e._batchKey)||null!=e&&e.send_instantly?this.gs(v):this.ps.enqueue(v),l}j.critical(\"This capture call is ignored due to client rate limiting.\")}}else j.error(\"No event name provided to posthog.capture\")}else j.uninitializedWarning(\"posthog.capture\")}Ne(t){return this.on(\"eventCaptured\",(i=>t(i.event,i)))}calculateEventProperties(t,i,e,r,s){if(e=e||new Date,!this.persistence||!this.sessionPersistence)return i;var n=s?void 0:this.persistence.remove_event_timer(t),a=B({},i);if(a.token=this.config.token,this.config.__preview_experimental_cookieless_mode&&(a.$cookieless_mode=!0),\"$snapshot\"===t){var l=B({},this.persistence.properties(),this.sessionPersistence.properties());return a.distinct_id=l.distinct_id,(!T(a.distinct_id)&&!F(a.distinct_id)||M(a.distinct_id))&&j.error(\"Invalid distinct_id for replay event. This indicates a bug in your implementation\"),a}var u,h=to(this.config.mask_personal_data_properties,this.config.custom_personal_data_properties);if(this.sessionManager){var{sessionId:v,windowId:c}=this.sessionManager.checkAndGetSessionAndWindowId(s,e.getTime());a.$session_id=v,a.$window_id=c}this.sessionPropsManager&&V(a,this.sessionPropsManager.getSessionProps());try{var f;this.sessionRecording&&V(a,this.sessionRecording.sdkDebugProperties),a.$sdk_debug_retry_queue_size=null==(f=this._s)?void 0:f.length}catch(t){a.$sdk_debug_error_capturing_properties=String(t)}if(this.requestRouter.region===No.CUSTOM&&(a.$lib_custom_api_host=this.config.api_host),u=\"$pageview\"!==t||s?\"$pageleave\"!==t||s?this.pageViewManager.doEvent():this.pageViewManager.doPageLeave(e):this.pageViewManager.doPageView(e,r),a=V(a,u),\"$pageview\"===t&&o&&(a.title=o.title),!R(n)){var p=e.getTime()-n;a.$duration=parseFloat((p/1e3).toFixed(3))}d&&this.config.opt_out_useragent_filter&&(a.$browser_type=this._is_bot()?\"bot\":\"browser\"),(a=V({},h,this.persistence.properties(),this.sessionPersistence.properties(),a)).$is_identified=this._isIdentified(),x(this.config.property_denylist)?J(this.config.property_denylist,(function(t){delete a[t]})):j.error(\"Invalid value for property_denylist config: \"+this.config.property_denylist+\" or property_blacklist config: \"+this.config.property_blacklist);var g=this.config.sanitize_properties;g&&(j.error(\"sanitize_properties is deprecated. Use before_send instead\"),a=g(a,t));var _=this.Is();return a.$process_person_profile=_,_&&!s&&this.Ps(\"_calculate_event_properties\"),a}xs(t){var i;if(!this.persistence||!this.Is())return t;if(this.ls)return t;var e=this.persistence.get_initial_props(),r=null==(i=this.sessionPropsManager)?void 0:i.getSetOnceProps(),s=V({},e,r||{},t||{}),n=this.config.sanitize_properties;return n&&(j.error(\"sanitize_properties is deprecated. Use before_send instead\"),s=n(s,\"$set_once\")),this.ls=!0,P(s)?void 0:s}register(t,i){var e;null==(e=this.persistence)||e.register(t,i)}register_once(t,i,e){var r;null==(r=this.persistence)||r.register_once(t,i,e)}register_for_session(t){var i;null==(i=this.sessionPersistence)||i.register(t)}unregister(t){var i;null==(i=this.persistence)||i.unregister(t)}unregister_for_session(t){var i;null==(i=this.sessionPersistence)||i.unregister(t)}Rs(t,i){this.register({[t]:i})}getFeatureFlag(t,i){return this.featureFlags.getFeatureFlag(t,i)}getFeatureFlagPayload(t){var i=this.featureFlags.getFeatureFlagPayload(t);try{return JSON.parse(i)}catch(t){return i}}isFeatureEnabled(t,i){return this.featureFlags.isFeatureEnabled(t,i)}reloadFeatureFlags(){this.featureFlags.reloadFeatureFlags()}updateEarlyAccessFeatureEnrollment(t,i){this.featureFlags.updateEarlyAccessFeatureEnrollment(t,i)}getEarlyAccessFeatures(t,i,e){return void 0===i&&(i=!1),this.featureFlags.getEarlyAccessFeatures(t,i,e)}on(t,i){return this.us.on(t,i)}onFeatureFlags(t){return this.featureFlags.onFeatureFlags(t)}onSurveysLoaded(t){return this.surveys.onSurveysLoaded(t)}onSessionId(t){var i,e;return null!==(i=null==(e=this.sessionManager)?void 0:e.onSessionId(t))&&void 0!==i?i:()=>{}}getSurveys(t,i){void 0===i&&(i=!1),this.surveys.getSurveys(t,i)}getActiveMatchingSurveys(t,i){void 0===i&&(i=!1),this.surveys.getActiveMatchingSurveys(t,i)}renderSurvey(t,i){this.surveys.renderSurvey(t,i)}canRenderSurvey(t){return this.surveys.canRenderSurvey(t)}canRenderSurveyAsync(t,i){return void 0===i&&(i=!1),this.surveys.canRenderSurveyAsync(t,i)}identify(t,i,e){if(!this.__loaded||!this.persistence)return j.uninitializedWarning(\"posthog.identify\");if(F(t)&&(t=t.toString(),j.warn(\"The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.\")),t){if([\"distinct_id\",\"distinctid\"].includes(t.toLowerCase()))j.critical('The string \"'+t+'\" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.');else if(this.Ps(\"posthog.identify\")){var r=this.get_distinct_id();if(this.register({$user_id:t}),!this.get_property(\"$device_id\")){var s=r;this.register_once({$had_persisted_distinct_id:!0,$device_id:s},\"\")}t!==r&&t!==this.get_property(ot)&&(this.unregister(ot),this.register({distinct_id:t}));var n=\"anonymous\"===(this.persistence.get_property(At)||\"anonymous\");t!==r&&n?(this.persistence.set_property(At,\"identified\"),this.setPersonPropertiesForFlags(B({},e||{},i||{}),!1),this.capture(\"$identify\",{distinct_id:t,$anon_distinct_id:r},{$set:i||{},$set_once:e||{}}),this.cs=js(t,i,e),this.featureFlags.setAnonymousDistinctId(r)):(i||e)&&this.setPersonProperties(i,e),t!==r&&(this.reloadFeatureFlags(),this.unregister(Ft))}}else j.error(\"Unique user id has not been set in posthog.identify\")}setPersonProperties(t,i){if((t||i)&&this.Ps(\"posthog.setPersonProperties\")){var e=js(this.get_distinct_id(),t,i);this.cs!==e?(this.setPersonPropertiesForFlags(B({},i||{},t||{})),this.capture(\"$set\",{$set:t||{},$set_once:i||{}}),this.cs=e):j.info(\"A duplicate setPersonProperties call was made with the same properties. It has been ignored.\")}}group(t,i,e){if(t&&i){if(this.Ps(\"posthog.group\")){var r=this.getGroups();r[t]!==i&&this.resetGroupPropertiesForFlags(t),this.register({$groups:B({},r,{[t]:i})}),e&&(this.capture(\"$groupidentify\",{$group_type:t,$group_key:i,$group_set:e}),this.setGroupPropertiesForFlags({[t]:e})),r[t]===i||e||this.reloadFeatureFlags()}}else j.error(\"posthog.group requires a group type and group key\")}resetGroups(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()}setPersonPropertiesForFlags(t,i){void 0===i&&(i=!0),this.featureFlags.setPersonPropertiesForFlags(t,i)}resetPersonPropertiesForFlags(){this.featureFlags.resetPersonPropertiesForFlags()}setGroupPropertiesForFlags(t,i){void 0===i&&(i=!0),this.Ps(\"posthog.setGroupPropertiesForFlags\")&&this.featureFlags.setGroupPropertiesForFlags(t,i)}resetGroupPropertiesForFlags(t){this.featureFlags.resetGroupPropertiesForFlags(t)}reset(t){var i,e,r,s;if(j.info(\"reset\"),!this.__loaded)return j.uninitializedWarning(\"posthog.reset\");var n=this.get_property(\"$device_id\");if(this.consent.reset(),null==(i=this.persistence)||i.clear(),null==(e=this.sessionPersistence)||e.clear(),this.surveys.reset(),null==(r=this.persistence)||r.set_property(At,\"anonymous\"),null==(s=this.sessionManager)||s.resetSessionId(),this.cs=null,this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:Bt,$device_id:null},\"\");else{var o=this.config.get_device_id(Ni());this.register_once({distinct_id:o,$device_id:t?o:n},\"\")}this.register({$last_posthog_reset:(new Date).toISOString()},1)}get_distinct_id(){return this.get_property(\"distinct_id\")}getGroups(){return this.get_property(\"$groups\")||{}}get_session_id(){var t,i;return null!==(t=null==(i=this.sessionManager)?void 0:i.checkAndGetSessionAndWindowId(!0).sessionId)&&void 0!==t?t:\"\"}get_session_replay_url(t){if(!this.sessionManager)return\"\";var{sessionId:i,sessionStartTimestamp:e}=this.sessionManager.checkAndGetSessionAndWindowId(!0),r=this.requestRouter.endpointFor(\"ui\",\"/project/\"+this.config.token+\"/replay/\"+i);if(null!=t&&t.withTimestamp&&e){var s,n=null!==(s=t.timestampLookBack)&&void 0!==s?s:10;if(!e)return r;r+=\"?t=\"+Math.max(Math.floor(((new Date).getTime()-e)/1e3)-n,0)}return r}alias(t,i){return t===this.get_property(nt)?(j.critical(\"Attempting to create alias for existing People user - aborting.\"),-2):this.Ps(\"posthog.alias\")?(R(i)&&(i=this.get_distinct_id()),t!==i?(this.Rs(ot,t),this.capture(\"$create_alias\",{alias:t,distinct_id:i})):(j.warn(\"alias matches current distinct_id - skipping api call.\"),this.identify(t),-1)):void 0}set_config(t){var i,e,r,s,n=B({},this.config);I(t)&&(V(this.config,Vo(t)),null==(i=this.persistence)||i.update_config(this.config,n),this.sessionPersistence=\"sessionStorage\"===this.config.persistence||\"memory\"===this.config.persistence?this.persistence:new co(B({},this.config,{persistence:\"sessionStorage\"})),Wi.O()&&\"true\"===Wi.A(\"ph_debug\")&&(this.config.debug=!0),this.config.debug&&(c.DEBUG=!0,j.info(\"set_config\",JSON.stringify({config:t,oldConfig:n,newConfig:B({},this.config)},null,2))),null==(e=this.sessionRecording)||e.startIfEnabledOrStop(),null==(r=this.autocapture)||r.startIfEnabled(),null==(s=this.heatmaps)||s.startIfEnabled(),this.surveys.loadIfEnabled(),this.bs())}startSessionRecording(t){var i=!0===t,e={sampling:i||!(null==t||!t.sampling),linked_flag:i||!(null==t||!t.linked_flag),url_trigger:i||!(null==t||!t.url_trigger),event_trigger:i||!(null==t||!t.event_trigger)};if(Object.values(e).some(Boolean)){var r,s,n,o,a;if(null==(r=this.sessionManager)||r.checkAndGetSessionAndWindowId(),e.sampling)null==(s=this.sessionRecording)||s.overrideSampling();if(e.linked_flag)null==(n=this.sessionRecording)||n.overrideLinkedFlag();if(e.url_trigger)null==(o=this.sessionRecording)||o.overrideTrigger(\"url\");if(e.event_trigger)null==(a=this.sessionRecording)||a.overrideTrigger(\"event\")}this.set_config({disable_session_recording:!1})}stopSessionRecording(){this.set_config({disable_session_recording:!0})}sessionRecordingStarted(){var t;return!(null==(t=this.sessionRecording)||!t.started)}captureException(t,i){var e=new Error(\"PostHog syntheticException\");this.exceptions.sendExceptionEvent(B({},Ae((t=>t instanceof Error)(t)?{error:t,event:t.message}:{event:t},{syntheticException:e}),i))}loadToolbar(t){return this.toolbar.loadToolbar(t)}get_property(t){var i;return null==(i=this.persistence)?void 0:i.props[t]}getSessionProperty(t){var i;return null==(i=this.sessionPersistence)?void 0:i.props[t]}toString(){var t,i=null!==(t=this.config.name)&&void 0!==t?t:Wo;return i!==Wo&&(i=Wo+\".\"+i),i}_isIdentified(){var t,i;return\"identified\"===(null==(t=this.persistence)?void 0:t.get_property(At))||\"identified\"===(null==(i=this.sessionPersistence)?void 0:i.get_property(At))}Is(){var t,i;return!(\"never\"===this.config.person_profiles||\"identified_only\"===this.config.person_profiles&&!this._isIdentified()&&P(this.getGroups())&&(null==(t=this.persistence)||null==(t=t.props)||!t[ot])&&(null==(i=this.persistence)||null==(i=i.props)||!i[Ut]))}ks(){return!0===this.config.capture_pageleave||\"if_capture_pageview\"===this.config.capture_pageleave&&(!0===this.config.capture_pageview||\"history_change\"===this.config.capture_pageview)}createPersonProfile(){this.Is()||this.Ps(\"posthog.createPersonProfile\")&&this.setPersonProperties({},{})}Ps(t){return\"never\"===this.config.person_profiles?(j.error(t+' was called, but process_person is set to \"never\". This call will be ignored.'),!1):(this.Rs(Ut,!0),!0)}bs(){var t,i,e,r,s=this.consent.isOptedOut(),n=this.config.opt_out_persistence_by_default,o=this.config.disable_persistence||s&&!!n;(null==(t=this.persistence)?void 0:t.xe)!==o&&(null==(e=this.persistence)||e.set_disabled(o));(null==(i=this.sessionPersistence)?void 0:i.xe)!==o&&(null==(r=this.sessionPersistence)||r.set_disabled(o))}opt_in_capturing(t){var i;(this.consent.optInOut(!0),this.bs(),R(null==t?void 0:t.captureEventName)||null!=t&&t.captureEventName)&&this.capture(null!==(i=null==t?void 0:t.captureEventName)&&void 0!==i?i:\"$opt_in\",null==t?void 0:t.captureProperties,{send_instantly:!0});this.config.capture_pageview&&this.$s()}opt_out_capturing(){this.consent.optInOut(!1),this.bs()}has_opted_in_capturing(){return this.consent.isOptedIn()}has_opted_out_capturing(){return this.consent.isOptedOut()}clear_opt_in_out_capturing(){this.consent.reset(),this.bs()}_is_bot(){return n?Lo(n,this.config.custom_blocked_useragents):void 0}$s(){o&&(\"visible\"===o.visibilityState?this.hs||(this.hs=!0,this.capture(\"$pageview\",{title:o.title},{send_instantly:!0}),this.ds&&(o.removeEventListener(\"visibilitychange\",this.ds),this.ds=null)):this.ds||(this.ds=this.$s.bind(this),st(o,\"visibilitychange\",this.ds)))}debug(i){!1===i?(null==t||t.console.log(\"You've disabled debug mode.\"),localStorage&&localStorage.removeItem(\"ph_debug\"),this.set_config({debug:!1})):(null==t||t.console.log(\"You're now in debug mode. All calls to PostHog will be logged in your console.\\nYou can disable this with `posthog.debug(false)`.\"),localStorage&&localStorage.setItem(\"ph_debug\",\"true\"),this.set_config({debug:!0}))}Es(t){if(O(this.config.before_send))return t;var i=x(this.config.before_send)?this.config.before_send:[this.config.before_send],e=t;for(var r of i){if(e=r(e),O(e)){var s=\"Event '\"+t.event+\"' was rejected in beforeSend function\";return L(t.event)?j.warn(s+\". This can cause unexpected behavior.\"):j.info(s),null}e.properties&&!P(e.properties)||j.warn(\"Event '\"+t.event+\"' has no properties after beforeSend function, this is likely an error.\")}return e}getPageViewId(){var t;return null==(t=this.pageViewManager.ne)?void 0:t.pageViewId}captureTraceFeedback(t,i){this.capture(\"$ai_feedback\",{$ai_trace_id:String(t),$ai_feedback_text:i})}captureTraceMetric(t,i,e){this.capture(\"$ai_metric\",{$ai_trace_id:String(t),$ai_metric_name:i,$ai_metric_value:String(e)})}}!function(t,i){for(var e=0;e<i.length;e++)t.prototype[i[e]]=Q(t.prototype[i[e]])}(Yo,[\"identify\"]);var Xo,Qo=function(t){return t.Button=\"button\",t.Tab=\"tab\",t.Selector=\"selector\",t}({}),Zo=function(t){return t.TopLeft=\"top_left\",t.TopRight=\"top_right\",t.TopCenter=\"top_center\",t.MiddleLeft=\"middle_left\",t.MiddleRight=\"middle_right\",t.MiddleCenter=\"middle_center\",t.Left=\"left\",t.Center=\"center\",t.Right=\"right\",t.NextToTrigger=\"next_to_trigger\",t}({}),ta=function(t){return t.Popover=\"popover\",t.API=\"api\",t.Widget=\"widget\",t}({}),ia=function(t){return t.Open=\"open\",t.MultipleChoice=\"multiple_choice\",t.SingleChoice=\"single_choice\",t.Rating=\"rating\",t.Link=\"link\",t}({}),ea=function(t){return t.NextQuestion=\"next_question\",t.End=\"end\",t.ResponseBased=\"response_based\",t.SpecificQuestion=\"specific_question\",t}({}),ra=function(t){return t.Once=\"once\",t.Recurring=\"recurring\",t.Always=\"always\",t}({}),sa=(Xo=Bo[Wo]=new Yo,function(){function i(){i.done||(i.done=!0,Go=!1,J(Bo,(function(t){t._dom_loaded()})))}null!=o&&o.addEventListener?\"complete\"===o.readyState?i():st(o,\"DOMContentLoaded\",i,{capture:!1}):t&&j.error(\"Browser doesn't support `document.addEventListener` so PostHog couldn't be initialized\")}(),Xo);\n//# sourceMappingURL=module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3Bvc3Rob2ctanNAMS4yNDYuMF9AcnJ3ZWIrdHlwZXNAMi4wLjAtYWxwaGEuMTcvbm9kZV9tb2R1bGVzL3Bvc3Rob2ctanMvZGlzdC9tb2R1bGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGlhQUFpYSxJQUFJLCtCQUErQiw2UkFBNlIsOENBQThDLEdBQUcscURBQXFELGdCQUFnQix3QkFBd0Isa0JBQWtCLGdCQUFnQixlQUFlLDRCQUE0Qix3RkFBd0YsbUNBQW1DLDJEQUEyRCxTQUFTLHVDQUF1QyxTQUFTLFNBQVMsbU9BQW1PLE9BQU8sY0FBYyw0REFBNEQsMklBQTJJLElBQUksd0JBQXdCLFdBQVcsaUJBQWlCLDhDQUE4QyxJQUFJLHNCQUFzQixnQkFBZ0IsaUJBQWlCLDhDQUE4QyxJQUFJLHNCQUFzQixpQkFBaUIsa0JBQWtCLDhDQUE4QyxJQUFJLHNCQUFzQixrQkFBa0IscUJBQXFCLDhDQUE4QyxJQUFJLHNCQUFzQixzQkFBc0IsMEJBQTBCLHlEQUF5RCw2QkFBNkIsU0FBUyxpRkFBaUYsNEtBQTRLLGtEQUFrRCxpQkFBaUIsV0FBVywrQkFBK0IsV0FBVyxxQ0FBcUMsZ0NBQWdDLHdRQUF3USw0Q0FBNEMsb0ZBQW9GLHVFQUF1RSxhQUFhLHdEQUF3RCxZQUFZLG1CQUFtQixLQUFLLG1CQUFtQixrQkFBa0Isd0NBQXdDLFNBQVMseUJBQXlCLGdCQUFnQixvQkFBb0IsU0FBUyxvQkFBb0IsMkJBQTJCLDhCQUE4QixVQUFVLFNBQVMsbURBQW1ELDBEQUEwRCxzQ0FBc0MsaUZBQWlGLFVBQVUscUNBQXFDLDhDQUE4QyxTQUFTLCtDQUErQywyQ0FBMkMsVUFBVSxTQUFTLGtCQUFrQiwyQ0FBMkMsa0VBQWtFLElBQUksMkNBQTJDLGtCQUFrQixVQUFVLHdCQUF3QixTQUFTLDJEQUEyRCxpRUFBaUUsa0JBQWtCLHNEQUFzRCxJQUFJLHdCQUF3Qix3QkFBd0IsMENBQTBDLEtBQUssZUFBZSxzREFBc0QsSUFBSSx3QkFBd0Isd0JBQXdCLGlCQUFpQixVQUFVLEdBQUcsTUFBTSxjQUFjLG1EQUFtRCxJQUFJLHFCQUFxQixTQUFTLGtCQUFrQixJQUFJLFdBQVcsU0FBUyxRQUFRLGVBQWUsa0JBQWtCLElBQUksOENBQThDLElBQUksc0JBQXNCLHVCQUF1QixTQUFTLDRKQUE0SixlQUFlLFNBQVMsMEJBQTBCLG1DQUFtQyxNQUFNLGlCQUFpQixxRUFBcUUsOEVBQThFLGFBQWEsU0FBUyxjQUFjLHdCQUF3QixPQUFPLE1BQU0sSUFBSSxVQUFVLG9EQUFvRCxlQUFlLGdDQUFnQyxrQkFBa0IsdUNBQXVDLGtDQUFrQyxTQUFTLGlCQUFpQixZQUFZLFdBQVcsMkJBQTJCLHFCQUFxQixJQUFJLDBCQUEwQixjQUFjLGlDQUFpQyxvQkFBb0IsRUFBRSw2dUNBQTZ1QyxlQUFlLDJHQUEyRyxlQUFlLDBCQUEwQixpQkFBaUIsa0VBQWtFLGVBQWUsMEJBQTBCLGVBQWUsMkJBQTJCLGVBQWUsOEJBQThCLGVBQWUscUNBQXFDLHdDQUF3QyxlQUFlLFNBQVMsMkJBQTJCLDJCQUEyQixNQUFNLDRHQUE0RyxNQUFNLGFBQWEsYUFBYSxlQUFlLDhIQUE4SCxlQUFlLFNBQVMscUZBQXFGLE1BQU0seUVBQXlFLFFBQVEsZUFBZSxnSEFBZ0gsTUFBTSxpRUFBaUUsZUFBZSxtQkFBbUIsdUJBQXVCLHVCQUF1QixVQUFVLGdFQUFnRSwrREFBK0QsZ0VBQWdFLHVDQUF1Qyw0QkFBNEIsd0NBQXdDLDRCQUE0Qiw0QkFBNEIsbUVBQW1FLEtBQUssb0JBQW9CLGtEQUFrRCxLQUFLLDRCQUE0QixvREFBb0QsY0FBYyxrQkFBa0IseUNBQXlDLGlCQUFpQixvQkFBb0IsbURBQW1ELE9BQU8sb0NBQW9DLFNBQVMsZUFBZSxrQkFBa0IsOENBQThDLGlCQUFpQixvQkFBb0Isb0NBQW9DLE9BQU8sb0NBQW9DLFNBQVMsZUFBZSw0QkFBNEIsMEVBQTBFLDhCQUE4QixVQUFVLG9CQUFvQixvREFBb0QseUZBQXlGLHNKQUFzSixlQUFlLFlBQVksNEJBQTRCLGdCQUFnQixZQUFZLHNEQUFzRCxrQ0FBa0MsaUJBQWlCLGdDQUFnQyxxQ0FBcUMsdUJBQXVCLFNBQVMsdUtBQXVLLFNBQVMsZUFBZSxnS0FBZ0ssZ0JBQWdCLEdBQUcsU0FBUyxFQUFFLGlCQUFpQixHQUFHLGtCQUFrQixFQUFFLE9BQU8sR0FBRyxjQUFjLEdBQUcsOEJBQThCLEdBQUcsd0JBQXdCLEVBQUUsT0FBTyxHQUFHLDJEQUEyRCxFQUFFLE1BQU0sRUFBRSxNQUFNLEVBQUUsd0RBQXdELGlCQUFpQixvQ0FBb0MsU0FBUywrREFBK0QsOEJBQThCLFNBQVMsZUFBZSxZQUFZLHVDQUF1QyxlQUFlLFNBQVMseUVBQXlFLE1BQU0sZ0VBQWdFLFlBQVksK0VBQStFLFNBQVMsNEJBQTRCLEtBQUssZUFBZSxtQkFBbUIsaUJBQWlCLGFBQWEscUhBQXFILFVBQVUsU0FBUyxZQUFZLEdBQUcsRUFBRSwwR0FBMEcsU0FBUyxZQUFZLEdBQUcsWUFBWSxrQkFBa0IsR0FBRyxvQkFBb0IsMEJBQTBCLGVBQWUsMEJBQTBCLGdCQUFnQixXQUFXLDRDQUE0QywyQkFBMkIsV0FBVyxvQkFBb0IsWUFBWSxHQUFHLGdCQUFnQixHQUFHLGFBQWEsa0JBQWtCLFdBQVcsME5BQTBOLHdCQUF3QixTQUFTLCtCQUErQixnQkFBZ0IsV0FBVyx5QkFBeUIsS0FBSyxHQUFHLEtBQUssZUFBZSxpQ0FBaUMsZUFBZSxvQkFBb0IsNkJBQTZCLFNBQVMsY0FBYyxlQUFlLG1CQUFtQix3Q0FBd0MsNkRBQTZELHFCQUFxQixvQkFBb0Isa0NBQWtDLG1CQUFtQixvQkFBb0IsRUFBRSxVQUFVLHdDQUF3QywwQ0FBMEMsOEJBQThCLGtCQUFrQixRQUFRLG9CQUFvQixTQUFTLDBCQUEwQiwrSUFBK0ksYUFBYSxrQkFBa0IsOEZBQThGLFdBQVcsS0FBSyxzQkFBc0IsYUFBYSxJQUFJLE9BQU8sOEJBQThCLFdBQVcsSUFBSSx3QkFBd0IsU0FBUywyREFBMkQsNEJBQTRCLG9CQUFvQiw4QkFBOEIsb0dBQW9HLFdBQVcsS0FBSyxzQkFBc0IseURBQXlELFFBQVEsMkRBQTJELGtCQUFrQix3Q0FBd0MsbUJBQW1CLHVCQUF1QixpQkFBaUIsdUNBQXVDLGVBQWUsNERBQTRELFFBQVEsR0FBRyxvQkFBb0IsaUJBQWlCLFNBQVMscUJBQXFCLGlDQUFpQyxZQUFZLDhIQUE4SCxZQUFZLDZDQUE2QyxhQUFhLGdDQUFnQyxNQUFNLDJNQUEyTSxjQUFjLHFFQUFxRSxHQUFHLG9CQUFvQixRQUFRLGdDQUFnQyx1Q0FBdUMsaUJBQWlCLGFBQWEsa0dBQWtHLGFBQWEsNEJBQTRCLHdHQUF3RyxlQUFlLFdBQVcsWUFBWSxZQUFZLGdJQUFnSSxrQkFBa0IsbUJBQW1CLFNBQVMsbUNBQW1DLDREQUE0RCxnRUFBZ0UsdUJBQXVCLEtBQUssSUFBSSxPQUFPLFlBQVksUUFBUSxzQkFBc0IscUhBQXFILFFBQVEsa0JBQWtCLGtGQUFrRixtQkFBbUIsT0FBTyxTQUFTLGlDQUFpQyxLQUFLLEVBQUUsWUFBWSxFQUFFLHNCQUFzQiw2QkFBNkIsMENBQTBDLEdBQUcsc0JBQXNCLHNCQUFzQixHQUFHLEtBQUssU0FBUyxlQUFlLG1GQUFtRixRQUFRLGtGQUFrRixpS0FBaUssSUFBSSw4QkFBOEIsU0FBUyxVQUFVLDhCQUE4QixJQUFJLFVBQVUsU0FBUyx3Q0FBd0Msb0JBQW9CLFdBQVcsbUJBQW1CLFdBQVcsa0JBQWtCLFdBQVcsOEJBQThCLFVBQVUsMkNBQTJDLGVBQWUsV0FBVyxnQkFBZ0IsV0FBVyxJQUFJLDJGQUEyRixpQkFBaUIsOENBQThDLGtCQUFrQix5SEFBeUgsNkJBQTZCLHVEQUF1RCx1QkFBdUIsU0FBUyx1QkFBdUIsV0FBVyx3Q0FBd0MsMkNBQTJDLHdCQUF3QixpQkFBaUIsR0FBRyxLQUFLLGdCQUFnQiwwRUFBMEUsdUVBQXVFLDBDQUEwQyw2Q0FBNkMsT0FBTyxrREFBa0QsY0FBYywwT0FBME8sWUFBWSxnREFBZ0QsSUFBSSw0QkFBNEIsT0FBTyx5TkFBeU4sRUFBRSxjQUFjLGtDQUFrQyxrREFBa0QsMEZBQTBGLGVBQWUscUNBQXFDLHVDQUF1QyxxQkFBcUIsNkNBQTZDLG9DQUFvQyxzQ0FBc0Msa0RBQWtELDRDQUE0QyxFQUFFLDBCQUEwQixTQUFTLGVBQWUsd0VBQXdFLDZCQUE2QixnTkFBZ04seUJBQXlCLHlQQUF5UCxXQUFXLGlCQUFpQixvQkFBb0Isc0dBQXNHLGlFQUFpRSxTQUFTLFFBQVEsbUNBQW1DLFVBQVUsNkJBQTZCLGFBQWEsWUFBWSxLQUFLLEtBQUssK0JBQStCLDZCQUE2QixVQUFVLFNBQVMsY0FBYyxnQ0FBZ0MsV0FBVyw2QkFBNkIsU0FBUyxTQUFTLDZCQUE2Qix5RUFBeUUsU0FBUyxTQUFTLGtCQUFrQixpQkFBaUIsOEJBQThCLEtBQUssMEJBQTBCLG1EQUFtRCw2R0FBNkcsSUFBSSw0REFBNEQsY0FBYywrSEFBK0gsWUFBWSxXQUFXLCtFQUErRSxVQUFVLDJFQUEyRSxTQUFTLGNBQWMscUNBQXFDLGFBQWEsc0VBQXNFLHFFQUFxRSxrQ0FBa0MsR0FBRyxJQUFJLGlCQUFpQixNQUFNLG9CQUFvQixrQ0FBa0MsZUFBZSxrREFBa0QsZ0VBQWdFLFNBQVMsRUFBRSxtQ0FBbUMsY0FBYyxRQUFRLGFBQWEsK0NBQStDLGlCQUFpQixVQUFVLElBQUksT0FBTyxXQUFXLGtCQUFrQixpQkFBaUIsS0FBSyxzRUFBc0UsWUFBWSxlQUFlLFNBQVMsUUFBUSx3QkFBd0IsaUNBQWlDLGVBQWUsTUFBTSxJQUFJLG1DQUFtQyw2QkFBNkIsV0FBVyxLQUFLLGVBQWUsaUJBQWlCLDJCQUEyQiwrRUFBK0UsVUFBVSxhQUFhLGVBQWUsTUFBTSxJQUFJLDBCQUEwQixVQUFVLFNBQVMsdUJBQXVCLFNBQVMsMENBQTBDLE1BQU0sZUFBZSwyQ0FBMkMsMEJBQTBCLFNBQVMsU0FBUyx1REFBdUQsY0FBYyxZQUFZLGdHQUFnRyxTQUFTLFFBQVEsaUJBQWlCLElBQUksZ0JBQWdCLFNBQVMsU0FBUyxhQUFhLGFBQWEsb0JBQW9CLFNBQVMsYUFBYSxTQUFTLHdCQUF3QixnREFBZ0QsU0FBUyxLQUFLLDZDQUE2QyxzQ0FBc0MsZUFBZSxrQ0FBa0MsZUFBZSxJQUFJLGdEQUFnRCxTQUFTLFFBQVEsWUFBWSxlQUFlLElBQUksK0JBQStCLFVBQVUsWUFBWSxpQkFBaUIsSUFBSSxxREFBcUQsU0FBUyxTQUFTLGVBQWUsSUFBSSxzQ0FBc0MsU0FBUyxVQUFVLHVDQUF1QyxLQUFLLGNBQWMsSUFBSSxTQUFTLElBQUksY0FBYyxVQUFVLGlDQUFpQyxJQUFJLG1CQUFtQixVQUFVLFlBQVkseUJBQXlCLElBQUksMEJBQTBCLFNBQVMsZ0JBQWdCLGtCQUFrQiw0Q0FBNEMsU0FBUyxTQUFTLGlCQUFpQixJQUFJLGdEQUFnRCxTQUFTLFVBQVUsT0FBTyxLQUFLLGFBQWEsU0FBUyxlQUFlLG1DQUFtQyxlQUFlLG1CQUFtQixlQUFlLG1CQUFtQixpQkFBaUIsUUFBUSxlQUFlLGNBQWMsYUFBYSxhQUFhLG9CQUFvQixvQkFBb0IsU0FBUyxvQkFBb0IsaURBQWlELFNBQVMsTUFBTSxVQUFVLGVBQWUsb0NBQW9DLGVBQWUsSUFBSSxrREFBa0QsU0FBUyxRQUFRLFlBQVksZUFBZSxJQUFJLGlDQUFpQyxVQUFVLFlBQVksaUJBQWlCLElBQUksdURBQXVELFNBQVMsU0FBUyxlQUFlLElBQUksd0NBQXdDLFNBQVMsVUFBVSxnQkFBZ0IsbUZBQW1GLEdBQUcsRUFBRSxTQUFTLGVBQWUsaUJBQWlCLFFBQVEsNkJBQTZCLGNBQWMsaUNBQWlDLGFBQWEsZ0dBQWdHLFlBQVkseUJBQXlCLFlBQVksbUdBQW1HLFFBQVEsK0NBQStDLFFBQVEsSUFBSSwwQ0FBMEMsdUJBQXVCLGdDQUFnQyxRQUFRLHVCQUF1QixzREFBc0QsUUFBUSxZQUFZLGdEQUFnRCxnQ0FBZ0MsK0JBQStCLDRHQUE0RyxjQUFjLElBQUksdUlBQXVJLDJDQUEyQyx5R0FBeUcsaUJBQWlCLFNBQVMsc0NBQXNDLGNBQWMsbUJBQW1CLHdFQUF3RSxrQkFBa0IsK0RBQStELHdDQUF3Qyx3QkFBd0IsaUJBQWlCLG1DQUFtQyxTQUFTLEdBQUcsS0FBSyxRQUFRLDZNQUE2TSwwQ0FBMEMsR0FBRyxJQUFJLE1BQU0sTUFBTSw0RUFBNEUsOEZBQThGLCtJQUErSSxxREFBcUQsT0FBTyw4REFBOEQsbUNBQW1DLFNBQVMsZUFBZSxNQUFNLFlBQVksTUFBTSxtRkFBbUYsMk1BQTJNLElBQUksdVFBQXVRLFNBQVMseUNBQXlDLHVIQUF1SCxLQUFLLGtEQUFrRCx1RkFBdUYsa0JBQWtCLCtCQUErQixJQUFJLDREQUE0RCxLQUFLLGdCQUFnQiwyR0FBMkcsaUJBQWlCLG9EQUFvRCxLQUFLLFFBQVEseU1BQXlNLGdEQUFnRCxJQUFJLEdBQUcsSUFBSSxVQUFVLHVJQUF1SSxrQkFBa0IsOEJBQThCLGtHQUFrRyxhQUFhLHdCQUF3QixvQkFBb0IscURBQXFELDhKQUE4SixlQUFlLDZCQUE2QixpQkFBaUIsSUFBSSxzQkFBc0IsU0FBUyxVQUFVLGVBQWUsMENBQTBDLDJHQUEyRyw0QkFBNEIsaUJBQWlCLDREQUE0RCxlQUFlLHdCQUF3QixzQ0FBc0MscUJBQXFCLE9BQU8sZ0ZBQWdGLDhDQUE4Qyw4YUFBOGEsOENBQThDLElBQUksc0JBQXNCLGdEQUFnRCxxQkFBcUIsa0JBQWtCLGlDQUFpQyxXQUFXLEtBQUssV0FBVyxxQkFBcUIsc0NBQXNDLDJCQUEyQixnQkFBZ0IsV0FBVyxNQUFNLFVBQVUsT0FBTyx3QkFBd0IsbUJBQW1CLHNCQUFzQixvQkFBb0IsNkNBQTZDLElBQUksNERBQTRELElBQUksS0FBSyxhQUFhLGlCQUFpQixNQUFNLGNBQWMsc0JBQXNCLGlCQUFpQixNQUFNLG1DQUFtQyxvQkFBb0IsbUNBQW1DLDJCQUEyQixvREFBb0QsVUFBVSxpQkFBaUIsTUFBTSxxQ0FBcUMsb0JBQW9CLG1EQUFtRCxzQkFBc0IsaUVBQWlFLElBQUksZUFBZSx5QkFBeUIsd0JBQXdCLGtGQUFrRiw4R0FBOEcsa0hBQWtILGlCQUFpQixrQkFBa0IsOENBQThDLGtDQUFrQyxTQUFTLElBQUksSUFBSSx5QkFBeUIsa0JBQWtCLGtDQUFrQyxlQUFlLHFCQUFxQiw0REFBNEQsVUFBVSxFQUFFLFlBQVksa0JBQWtCLGlDQUFpQyxLQUFLLEtBQUssOENBQThDLFNBQVMsbUJBQW1CLE9BQU8sU0FBUyxJQUFJLE1BQU0sSUFBSSxzQkFBc0IsdUNBQXVDLEtBQUssV0FBVyw2QkFBNkIsVUFBVSxTQUFTLGtDQUFrQyxHQUFHLGlCQUFpQiw0SEFBNEgsT0FBTyx1RkFBdUYsZ0JBQWdCLDRFQUE0RSxpQkFBaUIsZ0JBQWdCLG9CQUFvQixZQUFZLHdCQUF3QixpQkFBaUIsY0FBYywyREFBMkQsc0VBQXNFLE9BQU8saUJBQWlCLE9BQU8sa0RBQWtELGlCQUFpQix5SEFBeUgsb01BQW9NLHdCQUF3QixrQ0FBa0MsaUNBQWlDLHlCQUF5QixvQkFBb0IsRUFBRSxPQUFPLDhDQUE4QyxlQUFlLG9DQUFvQyxpQkFBaUIsNlFBQTZRLG1CQUFtQixxQkFBcUIsbURBQW1ELG1CQUFtQixJQUFJLEtBQUssOEJBQThCLHVFQUF1RSxTQUFTLE9BQU8sMEJBQTBCLHdCQUF3QixrQ0FBa0MsZ0RBQWdELHlCQUF5QixvQkFBb0IsRUFBRSxPQUFPLGtFQUFrRSxpQkFBaUIsSUFBSSxnQkFBZ0IsTUFBTSxtQkFBbUIsUUFBUSxzQkFBc0IsNEJBQTRCLEtBQUssUUFBUSxlQUFlLGtCQUFrQixjQUFjLEtBQUssK0VBQStFLFdBQVcsSUFBSSxnRkFBZ0YsR0FBRywrREFBK0QsZUFBZSwwQkFBMEIsa0NBQWtDLHdCQUF3QixlQUFlLHNCQUFzQiwwQkFBMEIsZUFBZSxnQ0FBZ0MsbUNBQW1DLElBQUksa0RBQWtELEdBQUcsZUFBZSxtQkFBbUIsSUFBSSwwQkFBMEIsa0JBQWtCLHlDQUF5Qyw0QkFBNEIscUJBQXFCLHdCQUF3QixlQUFlLFFBQVEsU0FBUyxjQUFjLFNBQVMsZUFBZSxNQUFNLCtFQUErRSxnQkFBZ0IsZ0VBQWdFLGlCQUFpQixxR0FBcUcsT0FBTywyRUFBMkUsd0JBQXdCLFFBQVEsaUJBQWlCLFdBQVcsbUdBQW1HLHFDQUFxQyw0R0FBNEcsd0NBQXdDLGNBQWMsTUFBTSxJQUFJLHdEQUF3RCxhQUFhLGlFQUFpRSxrQkFBa0IsWUFBWSxTQUFTLDZDQUE2QyxLQUFLLGFBQWEsV0FBVyxxQkFBcUIsZ0NBQWdDLDBDQUEwQyxlQUFlLFFBQVEscURBQXFELFNBQVMsS0FBSyxpQ0FBaUMsU0FBUyxnREFBZ0QsU0FBUyx3QkFBd0IsaUJBQWlCLHlEQUF5RCx3RUFBd0UsV0FBVyw0REFBNEQsTUFBTSw0REFBNEQsbUJBQW1CLFVBQVUseU9BQXlPLHdqQkFBd2pCLDRDQUE0QyxpZ0JBQWlnQixrVkFBa1YscUJBQXFCLGlCQUFpQix3REFBd0QsMEJBQTBCLElBQUksc0ZBQXNGLGlCQUFpQixpQkFBaUIsUUFBUSxvREFBb0QsUUFBUSxnSEFBZ0gsS0FBSyxlQUFlLGFBQWEsZ01BQWdNLDhhQUE4YSxrQkFBa0IsK0VBQStFLGdCQUFnQixrREFBa0Qsb0RBQW9ELDBEQUEwRCxPQUFPLHdDQUF3QyxtQkFBbUIsUUFBUSwrQ0FBK0Msc1BBQXNQLGdEQUFnRCxXQUFXLEVBQUUsV0FBVyxJQUFJLDBCQUEwQixFQUFFLDBFQUEwRSxhQUFhLDRKQUE0SixnQkFBZ0IseUdBQXlHLFdBQVcsT0FBTyx5RUFBeUUsR0FBRyx1QkFBdUIsNFVBQTRVLFNBQVMsaUJBQWlCLFFBQVEsaUJBQWlCLG1DQUFtQyxXQUFXLGNBQWMsa0NBQWtDLHFFQUFxRSxHQUFHLGFBQWEsb0NBQW9DLDhEQUE4RCx1QkFBdUIsMkNBQTJDLFlBQVksYUFBYSxvQkFBb0Isd1FBQXdRLDRCQUE0QiwwQ0FBMEMsMEJBQTBCLHFEQUFxRCw4QkFBOEIsMkJBQTJCLDhMQUE4TCxTQUFTLElBQUksaUJBQWlCLDZCQUE2QiwyUEFBMlAsVUFBVSxRQUFRLCtSQUErUix5QkFBeUIsS0FBSyxzQkFBc0Isb0JBQW9CLFFBQVEsS0FBSyxtQkFBbUIsU0FBUyxxQkFBcUIsWUFBWSwrQkFBK0Isc0JBQXNCLDZDQUE2QyxTQUFTLE1BQU0sb0NBQW9DLHNHQUFzRyx1QkFBdUIsbUNBQW1DLElBQUksZ0JBQWdCLGtCQUFrQixRQUFRLElBQUksMEJBQTBCLE1BQU0sZUFBZSxXQUFXLFFBQVEsSUFBSSx1RUFBdUUsS0FBSyxtQkFBbUIseUJBQXlCLElBQUksbUNBQW1DLFNBQVMsZ0JBQWdCLFNBQVMsT0FBTyxjQUFjLFdBQVcsT0FBTyxjQUFjLFdBQVcsT0FBTyxjQUFjLFdBQVcsT0FBTyxjQUFjLGtCQUFrQixTQUFTLE1BQU0sY0FBYywrQ0FBK0Msd0JBQXdCLG9CQUFvQixvQ0FBb0MseURBQXlELGdDQUFnQyxvQkFBb0IsUUFBUSxhQUFhLHNCQUFzQixvQkFBb0IsUUFBUSxhQUFhLHFDQUFxQyxrQkFBa0IsaUJBQWlCLFdBQVcsa0JBQWtCLFdBQVcsRUFBRSwyQkFBMkIsMEJBQTBCLFNBQVMsdUJBQXVCLHlCQUF5QixzQkFBc0IsZUFBZSxXQUFXLGFBQWEsRUFBRSw4QkFBOEIsVUFBVSx3QkFBd0IsT0FBTyxxRUFBcUUsd0JBQXdCLGFBQWEsUUFBUSxJQUFJLHlCQUF5QixtQ0FBbUMsUUFBUSxJQUFJLHFCQUFxQiwwQkFBMEIsOEJBQThCLEdBQUcsSUFBSSxLQUFLLGFBQWEsbUJBQW1CLHdCQUF3QixXQUFXLElBQUksRUFBRSxhQUFhLDRCQUE0QixLQUFLLFFBQVEsS0FBSyxhQUFhLHNCQUFzQixJQUFJLG9CQUFvQixvQkFBb0IsNkRBQTZELGdCQUFnQixtQkFBbUIsV0FBVyxFQUFFLG1EQUFtRCxTQUFTLEtBQUssS0FBSyx5QkFBeUIsS0FBSyxZQUFZLEtBQUssTUFBTSxnQkFBZ0IsOENBQThDLGFBQWEsYUFBYSxJQUFJLGFBQWEsMEJBQTBCLEtBQUssSUFBSSxNQUFNLFdBQVcsMEJBQTBCLGtCQUFrQixnQkFBZ0IsV0FBVyxpQkFBaUIsU0FBUyxvQkFBb0IseUJBQXlCLDBEQUEwRCxZQUFZLElBQUksa0JBQWtCLGlCQUFpQixvQ0FBb0MscUJBQXFCLHVIQUF1SCxXQUFXLGlCQUFpQixRQUFRLFdBQVcsaUJBQWlCLHFDQUFxQyxpQkFBaUIsS0FBSyx3R0FBd0csK0NBQStDLDZCQUE2QixnQ0FBZ0MsZ0JBQWdCLGlEQUFpRCxRQUFRLElBQUkseUJBQXlCLE9BQU8sb0JBQW9CLElBQUksS0FBSyxXQUFXLFFBQVEsV0FBVyxLQUFLLGNBQWMsaUVBQWlFLHlCQUF5QixRQUFRLElBQUksaUJBQWlCLGVBQWUsa0VBQWtFLGNBQWMsMkRBQTJELGdDQUFnQywrQkFBK0IsOEZBQThGLDBCQUEwQixNQUFNLEtBQUssZ0JBQWdCLElBQUksMkJBQTJCLE9BQU8sU0FBUyxpQkFBaUIsaUJBQWlCLE9BQU8sY0FBYyxnQkFBZ0IsV0FBVywyQkFBMkIsSUFBSSxjQUFjLHNCQUFzQix3QkFBd0IsNkJBQTZCLHNGQUFzRix1QkFBdUIsS0FBSyxVQUFVLGNBQWMsaUVBQWlFLEtBQUssa0hBQWtILG1DQUFtQyxxRUFBcUUsSUFBSSxLQUFLLDRCQUE0Qix1QkFBdUIsVUFBVSw0QkFBNEIsMENBQTBDLFlBQVksTUFBTSxXQUFXLFFBQVEsS0FBSyxXQUFXLDRCQUE0QixrRkFBa0YsZ0JBQWdCLEVBQUUscUJBQXFCLFlBQVksc0JBQXNCLEtBQUssUUFBUSxxQkFBcUIsMEJBQTBCLFFBQVEsSUFBSSxLQUFLLDZDQUE2QyxpQkFBaUIsOEJBQThCLE1BQU0saUNBQWlDLDBCQUEwQiwyQ0FBMkMsNEJBQTRCLDhCQUE4Qix5QkFBeUIsbUhBQW1ILG9CQUFvQixLQUFLLEVBQUUsa0JBQWtCLGtCQUFrQixpQkFBaUIsdUlBQXVJLE9BQU8sWUFBWSxZQUFZLDZCQUE2QixnQkFBZ0IsZ0RBQWdELGlCQUFpQixpQkFBaUIsRUFBRSxzQkFBc0IsT0FBTyxpQ0FBaUMsNkNBQTZDLGlCQUFpQixlQUFlLHFFQUFxRSw0REFBNEQsU0FBUyxLQUFLLElBQUksS0FBSyxpQkFBaUIsMkJBQTJCLGFBQWEsc0JBQXNCLHFOQUFxTixpQkFBaUIsaUJBQWlCLG1CQUFtQixnQkFBZ0IsV0FBVyxvQ0FBb0MsbUJBQW1CLHdCQUF3QixvSUFBb0ksaUJBQWlCLG9FQUFvRSxTQUFTLGVBQWUsVUFBVSxpQkFBaUIsMkNBQTJDLDhDQUE4QyxPQUFPLGdDQUFnQyxTQUFTLGVBQWUsVUFBVSxpQkFBaUIsY0FBYywrQ0FBK0MsNEJBQTRCLGlCQUFpQiwrQkFBK0IsbUJBQW1CLE9BQU8sZ0NBQWdDLFNBQVMsZ0JBQWdCLFVBQVUsU0FBUyxTQUFTLGVBQWUsMERBQTBELGtCQUFrQixRQUFRLGlJQUFpSSxNQUFNLE1BQU0sNEZBQTRGLGlCQUFpQiwwQ0FBMEMsNENBQTRDLHVDQUF1QyxJQUFJLGlDQUFpQyxnQ0FBZ0Msd0RBQXdELHNEQUFzRCxTQUFTLFNBQVMsZUFBZSwwREFBMEQsa0JBQWtCLGdCQUFnQixTQUFTLG1HQUFtRywrQ0FBK0MsSUFBSSxvQkFBb0IsTUFBTSx1SEFBdUgsZ0hBQWdILCtDQUErQyxTQUFTLGlCQUFpQixXQUFXLDBCQUEwQixnQ0FBZ0MsSUFBSSxPQUFPLFdBQVcsU0FBUyxlQUFlLDRCQUE0QixrQkFBa0IsTUFBTSxrRUFBa0UsTUFBTSxNQUFNLDRGQUE0RixpQkFBaUIsMENBQTBDLDRDQUE0Qyx5Q0FBeUMsSUFBSSxTQUFTLGVBQWUsa0NBQWtDLGVBQWUsK0JBQStCLG1DQUFtQyw2Q0FBNkMsK0hBQStILHVEQUF1RCxlQUFlLCtCQUErQixtQ0FBbUMsNkNBQTZDLHNJQUFzSSx5RUFBeUUscUNBQXFDLGNBQWMsTUFBTSxxRkFBcUYsdUlBQXVJLDhDQUE4QyxFQUFFLGVBQWUscUJBQXFCLGlCQUFpQixXQUFXLEVBQUUsYUFBYSx3UUFBd1EsU0FBUywrQkFBK0IsZUFBZSxzREFBc0QsU0FBUyxnQkFBZ0IsZUFBZSxTQUFTLDhFQUE4RSxjQUFjLGVBQWUsU0FBUyxzR0FBc0cscUNBQXFDLFNBQVMsUUFBUSwyS0FBMkssU0FBUyxzQ0FBc0MsbUJBQW1CLFNBQVMscUZBQXFGLHdCQUF3QiwyQ0FBMkMsNEJBQTRCLFNBQVMsMkZBQTJGLGVBQWUsU0FBUyw2RkFBNkYsbUJBQW1CLFNBQVMsMmFBQTJhLHVCQUF1QixvQkFBb0IsZ0JBQWdCLE9BQU8sbUdBQW1HLFNBQVMsNkNBQTZDLHdLQUF3Syx3VUFBd1UsZ0JBQWdCLGlEQUFpRCxRQUFRLFNBQVMscURBQXFELDJRQUEyUSxrUkFBa1IsZ0NBQWdDLDZEQUE2RCxTQUFTLHNDQUFzQyxtQkFBbUIsU0FBUyxzQ0FBc0MsbUJBQW1CLGFBQWEsd0JBQXdCLGdMQUFnTCxLQUFLLGVBQWUsMk5BQTJOLFVBQVUsY0FBYyw0QkFBNEIsRUFBRSxjQUFjLDJCQUEyQixFQUFFLGNBQWMsK0JBQStCLGtDQUFrQyxZQUFZLEdBQUcsaU9BQWlPLG1KQUFtSiw2RkFBNkYsSUFBSSx1QkFBdUIseUNBQXlDLHNPQUFzTyx3QkFBd0IsaU5BQWlOLElBQUksMEJBQTBCLDhGQUE4RixhQUFhLHFCQUFxQixPQUFPLEdBQUcsU0FBUyx3REFBd0QscURBQXFELFFBQVEsa0NBQWtDLHNDQUFzQywySUFBMkksMEJBQTBCLGdCQUFnQixZQUFZLDBpQkFBMGlCLEtBQUssTUFBTSx1REFBdUQsTUFBTSw4QkFBOEIsU0FBUyx1Q0FBdUMsNEpBQTRKLHlCQUF5QixxREFBcUQsT0FBTyxFQUFFLGVBQWUsa0JBQWtCLFlBQVkscUpBQXFKLDhNQUE4TSx3R0FBd0csd0ZBQXdGLCtCQUErQixpQkFBaUIsRUFBRSwwQ0FBMEMsS0FBSywwREFBMEQsV0FBVyxJQUFJLE1BQU0sK0JBQStCLDBDQUEwQyxtR0FBbUcsZ0JBQWdCLHdFQUF3RSxZQUFZLHdHQUF3Ryx3Q0FBd0MsdUhBQXVILDJLQUEySyxpRkFBaUYsR0FBRyxvRUFBb0UsU0FBUyxNQUFNLGdGQUFnRixhQUFhLGtDQUFrQyw4Q0FBOEMsc0JBQXNCLEVBQUUsTUFBTSx3SUFBd0ksTUFBTSxxRUFBcUUsNkhBQTZILGtEQUFrRCxVQUFVLEdBQUcsMkVBQTJFLFNBQVMsTUFBTSxvSEFBb0gsTUFBTSxNQUFNLHFFQUFxRSxNQUFNLGlCQUFpQixtR0FBbUcsa0lBQWtJLGFBQWEsU0FBUyxxQ0FBcUMsMEJBQTBCLDhDQUE4QyxtQ0FBbUMsUUFBUSxhQUFhLElBQUksdUJBQXVCLG1GQUFtRiw4R0FBOEcsTUFBTSxJQUFJLDBCQUEwQixTQUFTLHVDQUF1QyxrRkFBa0Ysd0RBQXdELFFBQVEsbURBQW1ELEtBQUssa0RBQWtELEtBQUssZUFBZSxtTEFBbUwsWUFBWSxxQ0FBcUMsaUVBQWlFLDJDQUEyQyxtQ0FBbUMsd0RBQXdELFlBQVksWUFBWSwwREFBMEQsbUJBQW1CLG1CQUFtQiwwQ0FBMEMsZ09BQWdPLFdBQVcsTUFBTSxtREFBbUQsNExBQTRMLG9IQUFvSCxXQUFXLE9BQU8sNkJBQTZCLEVBQUUsZ0JBQWdCLGFBQWEsU0FBUyxvQkFBb0IsV0FBVyx5RkFBeUYsMEVBQTBFLDZCQUE2Qiw2QkFBNkIsRUFBRSxzSEFBc0gsS0FBSyxpREFBaUQsY0FBYyw4QkFBOEIsVUFBVSxPQUFPLEtBQUssMkdBQTJHLHdCQUF3QixrR0FBa0csME1BQTBNLFNBQVMsZUFBZSxNQUFNLHNCQUFzQixxQkFBcUIsMkJBQTJCLHVCQUF1QixjQUFjLGVBQWUsb0tBQW9LLE1BQU0sNkhBQTZILDZDQUE2QyxNQUFNLGtCQUFrQixRQUFRLHNFQUFzRSw0SUFBNEksaUJBQWlCLGdDQUFnQyxzS0FBc0ssa0NBQWtDLFNBQVMsSUFBSSxtQ0FBbUMsVUFBVSxxQkFBcUIsTUFBTSw0Q0FBNEMsaUJBQWlCLG9HQUFvRyx1QkFBdUIsSUFBSSx1Q0FBdUMsSUFBSSw2QkFBNkIsRUFBRSwyRUFBMkUsSUFBSSxzQkFBc0IsU0FBUyx3R0FBd0csRUFBRSxFQUFFLGlGQUFpRixJQUFJLHNCQUFzQixTQUFTLHlGQUF5RixFQUFFLEVBQUUsU0FBUyxxRUFBcUUsU0FBUyxTQUFTLCtFQUErRSwwQ0FBMEMsS0FBSywrQ0FBK0MsK0JBQStCLHNDQUFzQyxPQUFPLGNBQWMsS0FBSyxtQkFBbUIsbUJBQW1CLDBCQUEwQix5Q0FBeUMsSUFBSSxNQUFNLDhDQUE4QywyQkFBMkIsU0FBUyxPQUFPLDBEQUEwRCxTQUFTLEtBQUssZUFBZSxrREFBa0QsUUFBUSxLQUFLLGdEQUFnRCxvREFBb0QsMkZBQTJGLFVBQVUsY0FBYyw4Q0FBOEMsU0FBUyxpSUFBaUksRUFBRSxHQUFHLGlCQUFpQixNQUFNLHVEQUF1RCxtTkFBbU4sVUFBVSxRQUFRLE1BQU0sc0NBQXNDLGdJQUFnSSxFQUFFLE1BQU0sTUFBTSw0R0FBNEcsMEJBQTBCLDJDQUEyQyxLQUFLLDZJQUE2SSxxQkFBcUIsR0FBRyxLQUFLLDRGQUE0RiwwQkFBMEIsZ0NBQWdDLEtBQUssbUZBQW1GLElBQUksK0NBQStDLFNBQVMsZ0RBQWdELElBQUkscUJBQXFCLHNFQUFzRSxtQkFBbUIsTUFBTSxrREFBa0QsUUFBUSwyQ0FBMkMsbUJBQW1CLFdBQVcsUUFBUSxxQ0FBcUMsa0NBQWtDLGtHQUFrRyx5QkFBeUIsSUFBSSx3QkFBd0IsMkNBQTJDLE9BQU8sc05BQXNOLGlDQUFpQyxpQkFBaUIsdUJBQXVCLGlCQUFpQixlQUFlLHVCQUF1QixpQkFBaUIsVUFBVSxnQ0FBZ0MsOENBQThDLGtDQUFrQyxtREFBbUQsWUFBWSw2Q0FBNkMsU0FBUyxnQkFBZ0IseUhBQXlILGNBQWMsZUFBZSxzUEFBc1AsdURBQXVELDBDQUEwQywwQkFBMEIsT0FBTywyTUFBMk0saUJBQWlCLElBQUksR0FBRyxHQUFHLG9CQUFvQixpQkFBaUIsSUFBSSxrRUFBa0UsY0FBYyxHQUFHLFdBQVcsY0FBYyx5REFBeUQsa0JBQWtCLEVBQUUsa0dBQWtHLHVIQUF1SCxpQkFBaUIsR0FBRyxrRUFBa0UsSUFBSSw0QkFBNEIsZUFBZSx3REFBd0QsSUFBSSwwQkFBMEIsSUFBSSxTQUFTLE9BQU8scVhBQXFYLHdKQUF3SixTQUFTLHVCQUF1Qix3Q0FBd0MsUUFBUSx3REFBd0QsS0FBSyx1SkFBdUosOEZBQThGLE9BQU8sRUFBRSxTQUFTLGVBQWUsZ0JBQWdCLE1BQU0scUJBQXFCLEtBQUssTUFBTSxvRUFBb0Usd0JBQXdCLHdGQUF3Riw2Q0FBNkMsSUFBSSxPQUFPLElBQUksd0VBQXdFLFNBQVMsU0FBUyxnQ0FBZ0MsNkpBQTZKLHNOQUFzTiwySEFBMkgsU0FBUyxVQUFVLE1BQU0sMENBQTBDLHlFQUF5RSxlQUFlLHdDQUF3QyxrQkFBa0IsZ0hBQWdILGlDQUFpQyxJQUFJLHFEQUFxRCxJQUFJLGNBQWMsR0FBRyxFQUFFLGdEQUFnRCxJQUFJLGNBQWMscUNBQXFDLHNDQUFzQyxNQUFNLDZJQUE2SSxrRkFBa0YsOEJBQThCLGdDQUFnQyw4Q0FBOEMsR0FBRyxTQUFTLE1BQU0sMkJBQTJCLHVCQUF1QiwwR0FBMEcsNkJBQTZCLFNBQVMsZUFBZSwwQ0FBMEMsUUFBUSxnSUFBZ0ksa0lBQWtJLGtCQUFrQixLQUFLLFFBQVEsbU1BQW1NLGdEQUFnRCxJQUFJLEdBQUcsdUJBQXVCLFFBQVEsNEpBQTRKLGdDQUFnQyxTQUFTLGVBQWUsTUFBTSw2QkFBNkIsa0RBQWtELGNBQWMseUhBQXlILElBQUkscUNBQXFDLDZDQUE2QyxLQUFLLFdBQVcsa0RBQWtELEVBQUUsYUFBYSwyRkFBMkYsaUVBQWlFLEtBQUssZ0JBQWdCLG1EQUFtRCxnQkFBZ0Isc0dBQXNHLHVGQUF1Rix5VkFBeVYsSUFBSSxrRkFBa0Ysa0VBQWtFLGFBQWEsc0NBQXNDLHdDQUF3QyxnQ0FBZ0Msb1dBQW9XLHNHQUFzRyxxQkFBcUIsc0pBQXNKLG1HQUFtRyw4QkFBOEIsdUlBQXVJLFNBQVMsNExBQTRMLHdCQUF3QixnQkFBZ0IsZ0NBQWdDLHNHQUFzRyxzTUFBc00sc0JBQXNCLGlCQUFpQiwwRUFBMEUsa0JBQWtCLGtKQUFrSixrRUFBa0UsT0FBTyx1Q0FBdUMsT0FBTyxtQ0FBbUMsS0FBSyxRQUFRLGlNQUFpTSwwQ0FBMEMsR0FBRyxLQUFLLCtCQUErQix5REFBeUQsdUJBQXVCLGVBQWUsc0VBQXNFLFNBQVMsZUFBZSxNQUFNLHNJQUFzSSxnQ0FBZ0MsVUFBVSw0TEFBNEwsZ0JBQWdCLGdMQUFnTCxpQkFBaUIsbUJBQW1CLGlCQUFpQix3R0FBd0csS0FBSyxRQUFRLDZHQUE2RyxrQkFBa0IsbUJBQW1CLCtEQUErRCxPQUFPLGtDQUFrQyxvQkFBb0IsYUFBYSx1QkFBdUIsTUFBTSxxQ0FBcUMsS0FBSyx1R0FBdUcsV0FBVyw4REFBOEQsV0FBVywwRkFBMEYsUUFBUSxvSkFBb0osWUFBWSx3QkFBd0IsRUFBRSxrQkFBa0IsK0RBQStELFFBQVEsU0FBUyw2QkFBNkIsT0FBTywrREFBK0QsUUFBUSxNQUFNLGlEQUFpRCxtQkFBbUIsZ0dBQWdHLElBQUksaUJBQWlCLGVBQWUsTUFBTSxzRUFBc0UsZ0NBQWdDLFFBQVEsTUFBTSxNQUFNLHNCQUFzQixpQkFBaUIsOENBQThDLEtBQUssdURBQXVELHVDQUF1QyxHQUFHLFNBQVMsZUFBZSxpQkFBaUIsZ0JBQWdCLHFCQUFxQixnQkFBZ0IsaUdBQWlHLCtDQUErQyxlQUFlLE1BQU0sd0RBQXdELFVBQVUsTUFBTSxPQUFPLG9EQUFvRCxRQUFRLGNBQWMsYUFBYSxnQkFBZ0IsT0FBTyw4Q0FBOEMsNkNBQTZDLHdEQUF3RCxJQUFJLDZGQUE2RixHQUFHLDBDQUEwQywwRkFBMEYsd0ZBQXdGLE9BQU8sb1JBQW9SLEdBQUcsOElBQThJLG1CQUFtQixpQkFBaUIsNEVBQTRFLElBQUksS0FBSyw2QkFBNkIscUtBQXFLLDJDQUEyQyx3Q0FBd0MsNEJBQTRCLElBQUksbUNBQW1DLG9CQUFvQixZQUFZLEdBQUcsWUFBWSxvQ0FBb0MsaUZBQWlGLElBQUkscUJBQXFCLEdBQUcsTUFBTSxpQkFBaUIsb0JBQW9CLFFBQVEsa0JBQWtCLFFBQVEsRUFBRSxPQUFPLDRDQUE0QyxpQkFBaUIsa0JBQWtCLHNHQUFzRyxlQUFlLFFBQVEsR0FBRyw2SkFBNkosa0JBQWtCLGdDQUFnQyw0QkFBNEIsTUFBTSwyQkFBMkIsU0FBUyx5RUFBeUUsT0FBTyx5RkFBeUYsWUFBWSxPQUFPLHlFQUF5RSxPQUFPLFlBQVksNkJBQTZCLFNBQVMscUNBQXFDLGtDQUFrQyxlQUFlLDJCQUEyQixjQUFjLGlDQUFpQyxtQkFBbUIsTUFBTSxZQUFZLEdBQUcsK0RBQStELE9BQU8sd0lBQXdJLDhDQUE4QyxPQUFPLDRCQUE0QixzQkFBc0IscUJBQXFCLFNBQVMsV0FBVyxnQ0FBZ0MsZ0JBQWdCLHlDQUF5QyxvQkFBb0IsRUFBRSxrREFBa0QsY0FBYywyQkFBMkIsY0FBYyxpQ0FBaUMsSUFBSSxxQkFBcUIsbUNBQW1DLDJCQUEyQix3QkFBd0IsMkhBQTJILHFCQUFxQixPQUFPLHlDQUF5QyxzQkFBc0Isa0NBQWtDLFVBQVUsaUNBQWlDLFlBQVksa0NBQWtDLGtDQUFrQyxnQkFBZ0IsV0FBVyxFQUFFLElBQUksT0FBTyxxQkFBcUIsa0NBQWtDLG9DQUFvQyxPQUFPLElBQUksa0JBQWtCLFlBQVksRUFBRSxxQkFBcUIsZ0JBQWdCLElBQUksY0FBYyxTQUFTLFNBQVMsU0FBUyxhQUFhLElBQUksNkJBQTZCLFNBQVMsV0FBVyxtQkFBbUIsV0FBVyw4REFBOEQsRUFBRSxRQUFRLDJWQUEyVixnREFBZ0QsU0FBUyxlQUFlLFFBQVEsK0hBQStILGtCQUFrQiwwRkFBMEYsMkVBQTJFLGFBQWEsRUFBRSxzQkFBc0IsMkhBQTJILDBDQUEwQyxFQUFFLE1BQU0sd0JBQXdCLG9DQUFvQyx3QkFBd0IsSUFBSSxlQUFlLEdBQUcsc0dBQXNHLEdBQUcsMkNBQTJDLEVBQUUseUJBQXlCLHdCQUF3QiwrREFBK0QsMEJBQTBCLEdBQUcscURBQXFELElBQUksNnJCQUE2ckIsaUlBQWlJLHNDQUFzQyxtQ0FBbUMscUJBQXFCLDhaQUE4WixLQUFLLDBnQkFBMGdCLGtCQUFrQixzQkFBc0Isb0JBQW9CLFlBQVksV0FBVyxLQUFLLHdCQUF3QixzQ0FBc0MsWUFBWSx1QkFBdUIsZUFBZSxnSUFBZ0ksc0RBQXNELHNFQUFzRSxzQ0FBc0MsWUFBWSx1QkFBdUIsc0NBQXNDLGNBQWMsOERBQThELFlBQVksNEJBQTRCLHVCQUF1QixjQUFjLDBEQUEwRCxTQUFTLDJEQUEyRCxvRUFBb0UsWUFBWSw0QkFBNEIsdUJBQXVCLGNBQWMsNkNBQTZDLHNCQUFzQixZQUFZLDRCQUE0QixpQkFBaUIsU0FBUywyRkFBMkYsdVZBQXVWLEVBQUUsWUFBWSxFQUFFLGdPQUFnTyxJQUFJLDJQQUEyUCwrUkFBK1IsbUJBQW1CLGVBQWUsMEJBQTBCLDRCQUE0QixpQkFBaUIsNEJBQTRCLHdCQUF3QixjQUFjLGFBQWEsS0FBSyxlQUFlLGtCQUFrQixzTEFBc0wsOEJBQThCLFVBQVUsbUJBQW1CLDRCQUE0QiwyQkFBMkIsU0FBUyxjQUFjLGtEQUFrRCxjQUFjLDZDQUE2QyxpQkFBaUIsbUVBQW1FLE9BQU8sK0NBQStDLGVBQWUsT0FBTyxRQUFRLE1BQU0sbUdBQW1HLE1BQU0saUJBQWlCLFlBQVksb0VBQW9FLFlBQVksT0FBTyxNQUFNLFlBQVksT0FBTyxTQUFTLGNBQWMsSUFBSSx3REFBd0QsU0FBUyxRQUFRLGNBQWMsSUFBSSxxQ0FBcUMsU0FBUyxRQUFRLGlCQUFpQixlQUFlLGtEQUFrRCxZQUFZLFlBQVksS0FBSyxnREFBZ0QsY0FBYyxjQUFjLElBQUksWUFBWSxxUUFBcVEsR0FBRywrbkJBQStuQixFQUFFLHlMQUF5TCxTQUFTLHNCQUFzQixjQUFjLFNBQVMsUUFBUSxjQUFjLG9FQUFvRSxNQUFNLDZEQUE2RCxrR0FBa0csTUFBTSxnREFBZ0QsWUFBWSxNQUFNLG1EQUFtRCxpSUFBaUksZ0JBQWdCLGtFQUFrRSxHQUFHLEVBQUUsU0FBUyxlQUFlLCtIQUErSCxTQUFTLDREQUE0RCxLQUFLLDJJQUEySSxTQUFTLGVBQWUsR0FBRyxxQkFBcUIsZUFBZSxXQUFXLDJDQUEyQyxzQkFBc0IsMEdBQTBHLHVCQUF1QixVQUFVLE1BQU0scUNBQXFDLHNCQUFzQixLQUFLLGdCQUFnQiw0S0FBNEssSUFBSSw4REFBOEQsRUFBRSxnTEFBZ0wsNEJBQTRCLDBFQUEwRSxFQUFFLE9BQU8sOERBQThELGtEQUFrRCxnQkFBZ0Isa0JBQWtCLHdFQUF3RSxtQkFBbUIsY0FBYyx5QkFBeUIsV0FBVyxvQkFBb0IsdURBQXVELDhDQUE4QyxnQkFBZ0Isa0JBQWtCLHdFQUF3RSxtQkFBbUIsY0FBYyxNQUFNLHVCQUF1QixXQUFXLG9CQUFvQiwrREFBK0Qsb0RBQW9ELGdCQUFnQixxQkFBcUIsa0dBQWtHLFVBQVUsTUFBTSxLQUFLLHFDQUFxQyxvQkFBb0IscURBQXFELDBCQUEwQix5QkFBeUIsc0JBQXNCLFVBQVUsTUFBTSxNQUFNLGtGQUFrRixLQUFLLE9BQU8sZ0xBQWdMLHdFQUF3RSxvQ0FBb0Msb0RBQW9ELHNHQUFzRyw4RkFBOEYsbURBQW1ELHlQQUF5UCxhQUFhLDBKQUEwSixHQUFHLHVUQUF1VCxLQUFLLE1BQU0sZ0ZBQWdGLElBQUksa0NBQWtDLEdBQUcsb0JBQW9CLG9CQUFvQixzREFBc0QsdUhBQXVILHdFQUF3RSxzQkFBc0IsZ0ZBQWdGLE9BQU8sRUFBRSx1Q0FBdUMsNFpBQTRaLGdHQUFnRyw0SEFBNEgsZ2FBQWdhLGlEQUFpRCxTQUFTLHNGQUFzRix5QkFBeUIscUNBQXFDLHlCQUF5QixpQ0FBaUMsNEJBQTRCLGtDQUFrQyxtQkFBbUIsdUZBQXVGLHFEQUFxRCxrSkFBa0osc0RBQXNELGtDQUFrQyxFQUFFLHNCQUFzQixvQkFBb0Isc0ZBQXNGLHdGQUF3RiwwQkFBMEIsc0NBQXNDLDZCQUE2QiwrRUFBK0UsMEJBQTBCLCtCQUErQixXQUFXLG1GQUFtRixxQkFBcUIsaUJBQWlCLG1CQUFtQixtQkFBbUIsRUFBRSwrREFBK0QsTUFBTSxrQkFBa0IsU0FBUywwRkFBMEYsU0FBUyxpQkFBaUIsV0FBVyxlQUFlLGVBQWUsY0FBYyxFQUFFLEtBQUssZ0JBQWdCLG9DQUFvQyxZQUFZLFlBQVksd0JBQXdCLGtDQUFrQyxXQUFXLFlBQVksSUFBSSxPQUFPLEdBQUcsS0FBSyxpREFBaUQsY0FBYywwSEFBMEgsMEJBQTBCLEVBQUUsd0JBQXdCLHFJQUFxSSxvSEFBb0gseURBQXlELFVBQVUsc0lBQXNJLCtCQUErQixZQUFZLEtBQUssaUJBQWlCLHFCQUFxQixxQ0FBcUMsT0FBTyxFQUFFLDBDQUEwQyxhQUFhLEVBQUUsa0lBQWtJLGdCQUFnQixrQkFBa0IsVUFBVSxrQkFBa0IsMkNBQTJDLElBQUksdUJBQXVCLFdBQVcsT0FBTyw0Q0FBNEMsd0NBQXdDLDBFQUEwRSw2QkFBNkIsSUFBSSw4Q0FBOEMsdUlBQXVJLFVBQVUseUJBQXlCLE1BQU0sRUFBRSxrREFBa0QsK0JBQStCLFlBQVksOEJBQThCLG1CQUFtQixrRkFBa0YscUJBQXFCLG1CQUFtQixpSkFBaUosTUFBTSxXQUFXLGlDQUFpQyx5REFBeUQsT0FBTyxTQUFTLEVBQUUsS0FBSywrQ0FBK0MsT0FBTyx3R0FBd0csR0FBRyxNQUFNLElBQUksdUJBQXVCLFdBQVcsaURBQWlELGdCQUFnQixJQUFJLGlDQUFpQyxtQkFBbUIsMENBQTBDLHlCQUF5QixTQUFTLE1BQU0seUNBQXlDLGdDQUFnQyw4QkFBOEIsZ0NBQWdDLG1CQUFtQiwwQ0FBMEMsdURBQXVELFNBQVMsd0JBQXdCLDRCQUE0QixTQUFTLE1BQU0seUNBQXlDLGdDQUFnQyxNQUFNLDBDQUEwQyx5QkFBeUIsU0FBUyxJQUFJLE9BQU8sRUFBRSxFQUFFLG9DQUFvQyxpRkFBaUYsU0FBUyxlQUFlLHNCQUFzQix5QkFBeUIsU0FBUyxzSkFBc0osMEZBQTBGLGtEQUFrRCxNQUFNLHVHQUF1RywyRUFBMkUsa0NBQWtDLHNKQUFzSixhQUFhLFNBQVMsbUNBQW1DLDZDQUE2QyxXQUFXLGdDQUFnQyw2RUFBNkUseUJBQXlCLGlCQUFpQixVQUFVLEtBQUssT0FBTyxhQUFhLHdCQUF3QixtQkFBbUIsTUFBTSxPQUFPLDJFQUEyRSxTQUFTLDBDQUEwQyxRQUFRLDRCQUE0QixxQkFBcUIsU0FBUyx3Q0FBd0MsU0FBUyxnQkFBZ0Isd0VBQXdFLDJCQUEyQixTQUFTLGNBQWMsU0FBUyx1QkFBdUIsU0FBUyxnQkFBZ0IsK0RBQStELDJCQUEyQixTQUFTLGNBQWMsb0RBQW9ELHlCQUF5QixhQUFhLG9IQUFvSCxzQ0FBc0Msd0JBQXdCLE1BQU0sb0RBQW9ELEVBQUUsdUJBQXVCLE1BQU0sb0JBQW9CLDBHQUEwRyxTQUFTLDBCQUEwQixvREFBb0QscUZBQXFGLFNBQVMsb0JBQW9CLFNBQVMsZUFBZSxvQkFBb0Isc0JBQXNCLHNCQUFzQixHQUFHLEdBQUcseUJBQXlCLE1BQU0sbUJBQW1CLG9CQUFvQixzQkFBc0IsTUFBTSxPQUFPLFNBQVMsY0FBYyxtQ0FBbUMsaUJBQWlCLEtBQUssbUJBQW1CLG1NQUFtTSw4QkFBOEIsZ0RBQWdELGdCQUFnQiw0Q0FBNEMsdUJBQXVCLG1EQUFtRCxjQUFjLG1EQUFtRCxxQkFBcUIseUJBQXlCLG9DQUFvQyxzQkFBc0IseUJBQXlCLEtBQUssc0RBQXNELGdCQUFnQixxQkFBcUIsa0JBQWtCLDZCQUE2QixTQUFTLGNBQWMsVUFBVSxZQUFZLFFBQVEsMkRBQTJELDBDQUEwQyxVQUFVLGlDQUFpQyxzQ0FBc0MsU0FBUyxlQUFlLDhNQUE4TSxPQUFPLE1BQU0sNkNBQTZDLE1BQU0sd0NBQXdDLGFBQWEsSUFBSSxZQUFZLFFBQVEsNkRBQTZELFFBQVEsOERBQThELE1BQU0sdURBQXVELEdBQUcsNkNBQTZDLGdCQUFnQixlQUFlLE1BQU0sa0NBQWtDLHNEQUFzRCxHQUFHLG1FQUFtRSxRQUFRLE1BQU0sZ0pBQWdKLG9EQUFvRCxHQUFHLE1BQU0sMENBQTBDLFFBQVEsMkNBQTJDLDhDQUE4QyxTQUFTLGNBQWMsdUJBQXVCLFFBQVEsbUJBQW1CLDhEQUE4RCxtQ0FBbUMsdUZBQXVGLFNBQVMsaUJBQWlCLFVBQVUsK0JBQStCLHlCQUF5QixtRUFBbUUsZUFBZSxrQkFBa0IsYUFBYSx3QkFBd0IsNkNBQTZDLFFBQVEsdWNBQXVjLHdCQUF3QixvRUFBb0UsZUFBZSxtREFBbUQsU0FBUyxNQUFNLCtGQUErRixzQkFBc0IsU0FBUyxlQUFlLGlEQUFpRCxZQUFZLE1BQU0saUVBQWlFLE1BQU0sb0JBQW9CLFFBQVEscUlBQXFJLEdBQUcsaUJBQWlCLGtCQUFrQiw4Q0FBOEMsZ0JBQWdCLGlCQUFpQixHQUFHLGVBQWUsY0FBYyx5VUFBeVUsY0FBYywwQkFBMEIsK0NBQStDLElBQUksSUFBSSxNQUFNLE1BQU0scUJBQXFCLFFBQVEsbUlBQW1JLFdBQVcsd0NBQXdDLGtCQUFrQixrQkFBa0IsTUFBTSwrRUFBK0UsY0FBYywwQkFBMEIsK0NBQStDLEdBQUcsSUFBSSxhQUFhLG1GQUFtRixzQ0FBc0MsTUFBTSx3RUFBd0Usa0RBQWtELEVBQUUsMERBQTBELE1BQU0sbUJBQW1CLGtDQUFrQyxrRkFBa0YsK0JBQStCLHlDQUF5QyxZQUFZLG1GQUFtRixzREFBc0QsTUFBTSxNQUFNLCtEQUErRCxxQkFBcUIsRUFBRSxhQUFhLDZFQUE2RSxhQUFhLG9CQUFvQixlQUFlLEtBQUssZ0JBQWdCLFNBQVMsZUFBZSw4RkFBOEYsa0JBQWtCLGdCQUFnQixzRUFBc0UsV0FBVywrR0FBK0csUUFBUSw4Q0FBOEMsaUJBQWlCLHNCQUFzQixLQUFLLDBCQUEwQiw4RkFBOEYsMkNBQTJDLGdCQUFnQiw0RUFBNEUsd0ZBQXdGLGlCQUFpQiw2Q0FBNkMsTUFBTSxXQUFXLElBQUksd0JBQXdCLDRCQUE0QiwrQkFBK0IsaUZBQWlGLGdDQUFnQyw0RkFBNEYsR0FBRyxTQUFTLGdEQUFnRCxRQUFRLFlBQVksK0NBQStDLG9DQUFvQyxNQUFNLDJIQUEySCxZQUFZLEVBQUUsUUFBUSx1QkFBdUIsb0JBQW9CLEVBQUUsbUJBQW1CLHlDQUF5QyxZQUFZLE9BQU8sb0NBQW9DLGdCQUFnQixtSEFBbUgsc0NBQXNDLHFCQUFxQixZQUFZLEVBQUUsd0JBQXdCLHFEQUFxRCxFQUFFLElBQUksOEJBQThCLDBMQUEwTCxNQUFNLFdBQVcsbUJBQW1CLHFCQUFxQixvREFBb0QseUJBQXlCLG9CQUFvQixFQUFFLHNEQUFzRCxtQ0FBbUMsa0JBQWtCLE1BQU0sbUZBQW1GLGlCQUFpQixNQUFNLG9GQUFvRixPQUFPLGlFQUFpRSx5REFBeUQsT0FBTyxPQUFPLFlBQVksR0FBRyxFQUFFLFNBQVMsb0JBQW9CLE1BQU0seUJBQXlCLHNDQUFzQyxTQUFTLHdDQUF3Qyw4QkFBOEIsK0VBQStFLCtCQUErQixNQUFNLFdBQVcsNEJBQTRCLE1BQU0sc0RBQXNELEtBQUssTUFBTSxxQkFBcUIsbUZBQW1GLHNDQUFzQyw0Q0FBNEMsdUNBQXVDLG1CQUFtQixxREFBcUQsMEZBQTBGLGlCQUFpQixPQUFPLDRDQUE0QywwQkFBMEIsbUVBQW1FLHlGQUF5RixvQkFBb0IscUJBQXFCLDREQUE0RCxNQUFNLGlCQUFpQixHQUFHLDJDQUEyQyxFQUFFLFFBQVEsNkNBQTZDLEVBQUUsS0FBSyxHQUFHLGtCQUFrQiw2Q0FBNkMsS0FBSyxxREFBcUQsZ0dBQWdHLDBCQUEwQixTQUFTLGVBQWUsUUFBUSxvQkFBb0Isd0RBQXdELGFBQWEsbUJBQW1CLCtDQUErQywwRkFBMEYsR0FBRyxTQUFTLGtHQUFrRyxPQUFPLEdBQUcseVZBQXlWLDBCQUEwQixVQUFVLG1CQUFtQixxSEFBcUgsNENBQTRDLDZJQUE2SSxpQkFBaUIscUlBQXFJLGlNQUFpTSxFQUFFLDZCQUE2Qix5RkFBeUYsMENBQTBDLHVCQUF1Qix5Q0FBeUMsc0NBQXNDLDJCQUEyQixTQUFTLGVBQWUsaUJBQWlCLG1CQUFtQixNQUFNLG9HQUFvRyxNQUFNLFFBQVEsOFJBQThSLE1BQU0sbUJBQW1CLGlJQUFpSSxXQUFXLEVBQUUsT0FBTyxJQUFJLHVIQUF1SCxpSUFBaUksYUFBYSxnSEFBZ0gsV0FBVyxHQUFHLFdBQVcsR0FBRyxTQUFTLDJDQUEyQyxNQUFNLGlSQUFpUixXQUFXLFNBQVMsaUJBQWlCLGlIQUFpSCxXQUFXLG1DQUFtQyxTQUFTLFVBQVUsbUNBQW1DLG9CQUFvQiwrRkFBK0YsWUFBWSxJQUFJLHVCQUF1QixHQUFHLEdBQUcsU0FBUyxxQkFBcUIsS0FBSyxXQUFXLG1DQUFtQywrQkFBK0IsNkJBQTZCLGtDQUFrQyxpQ0FBaUMsb0RBQW9ELFlBQVksb0JBQW9CLFlBQVksS0FBSyxxQ0FBcUMsS0FBSyxTQUFTLHNCQUFzQix1Q0FBdUMsbUJBQW1CLElBQUksUUFBUSx1Q0FBdUMsaUJBQWlCLGlDQUFpQyxTQUFTLGVBQWUsd0pBQXdKLHFCQUFxQix3QkFBd0IsV0FBVyxJQUFJLGFBQWEsc0JBQXNCLG9CQUFvQixJQUFJLHdCQUF3QixhQUFhLDRCQUE0QixjQUFjLHlCQUF5QixJQUFJLGFBQWEsdUZBQXVGLHdCQUF3QixzQ0FBc0MsR0FBRyxNQUFNLGlDQUFpQyw0QkFBNEIsa0JBQWtCLDRFQUE0RSxzQkFBc0IsbUJBQW1CLGNBQWMsMkJBQTJCLEVBQUUsZ0RBQWdELHlGQUF5RixLQUFLLHdEQUF3RCwrQ0FBK0MsV0FBVyxLQUFLLHlFQUF5RSxnQ0FBZ0MsaUJBQWlCLDhCQUE4QixTQUFTLFFBQVEsaUJBQWlCLGdFQUFnRSxzQkFBc0IsSUFBSSx1QkFBdUIsR0FBRyxTQUFTLFdBQVcsWUFBWSxTQUFTLGVBQWUsYUFBYSxZQUFZLG9CQUFvQixFQUFFLDBLQUEwSyxvWUFBb1ksa0JBQWtCLGFBQWEsZUFBZSxlQUFlLGNBQWMsK0JBQStCLCtCQUErQix1QkFBdUIsV0FBVyw0QkFBNEIsV0FBVyx5QkFBeUIsZ0JBQWdCLGdHQUFnRyw0SUFBNEksZ0JBQWdCLGlEQUFpRCxlQUFlLFVBQVUsK0NBQStDLDJCQUEyQix5QkFBeUIsOEVBQThFLFVBQVUsK0NBQStDLDJCQUEyQiwwQkFBMEIsZ0ZBQWdGLDRIQUE0SCxTQUFTLHFCQUFxQixZQUFZLGdCQUFnQix3QkFBd0IsT0FBTywyQ0FBMkMsa0JBQWtCLE9BQU8sR0FBRyxpRkFBaUYsS0FBSyx5QkFBeUIsa0JBQWtCLDJDQUEyQyx3QkFBd0IsMExBQTBMLElBQUksa0JBQWtCLFNBQVMsNENBQTRDLDBEQUEwRCxNQUFNLHdCQUF3QixTQUFTLG1CQUFtQixNQUFNLHdHQUF3RyxrSkFBa0oscUtBQXFLLHdGQUF3Riw0RkFBNEYsc0RBQXNELDZGQUE2RixvQ0FBb0MsK0NBQStDLCtDQUErQyxXQUFXLHlCQUF5QixxREFBcUQsK0NBQStDLHNDQUFzQyw4QkFBOEIsMkRBQTJELFNBQVMsNkNBQTZDLFVBQVUsdUJBQXVCLDhCQUE4QixlQUFlLGlGQUFpRixvQ0FBb0MsS0FBSyx5REFBeUQsTUFBTSxvREFBb0QsS0FBSyxvREFBb0QsVUFBVSw4S0FBOEssYUFBYSxHQUFHLEtBQUssbUpBQW1KLHdCQUF3QixzREFBc0QsaUJBQWlCLHdCQUF3QixLQUFLLDBCQUEwQix5QkFBeUIsR0FBRyxXQUFXLEVBQUUsbUNBQW1DLHlNQUF5TSxpSkFBaUoscUVBQXFFLHFDQUFxQyw0REFBNEQsa0NBQWtDLHNEQUFzRCw4RUFBOEUsMkRBQTJELFlBQVksK0RBQStELDJEQUEyRCxpQ0FBaUMsS0FBSywrQ0FBK0Msc0JBQXNCLDhCQUE4QiwrQ0FBK0MsU0FBUyxlQUFlLHlDQUF5QyxnQkFBZ0IsK0NBQStDLFFBQVEsTUFBTSw4QkFBOEIsaUVBQWlFLHFCQUFxQixNQUFNLHNHQUFzRyxPQUFPLG1CQUFtQiw0Q0FBNEMsYUFBYSxnQ0FBZ0MsbUJBQW1CLGtCQUFrQixtREFBbUQsUUFBUSw0R0FBNEcsdUNBQXVDLDZCQUE2QixJQUFJLG1CQUFtQixHQUFHLE9BQU8sVUFBVSxVQUFVLGVBQWUsaUJBQWlCLFNBQVMsaUVBQWlFLElBQUksR0FBRyxJQUFJLGNBQWMsc0VBQXNFLElBQUksR0FBRyxxSkFBcUosVUFBVSxvRUFBb0UsV0FBVyxnQkFBZ0IsNkJBQTZCLE1BQU0seVNBQXlTLFlBQVksOEZBQThGLElBQUksSUFBSSxlQUFlLFNBQVMsb0NBQW9DLE1BQU0sRUFBRSwyQkFBMkIsU0FBUyw4RUFBOEUsbUJBQW1CLElBQUksU0FBUyxvR0FBb0csS0FBSyw4QkFBOEIsZ0NBQWdDLGlEQUFpRCxvQ0FBb0MsTUFBTSxzQ0FBc0MsOEJBQThCLDBDQUEwQyx3Q0FBd0MsU0FBUyw0RUFBNEUsa0JBQWtCLGlCQUFpQix1T0FBdU8scUZBQXFGLGtCQUFrQixNQUFNLDZIQUE2SCxpRkFBaUYsSUFBSSxRQUFRLFdBQVcsbUJBQW1CLG1HQUFtRyxpbEJBQWlsQixraUJBQWtpQixlQUFlLHNCQUFzQixrQ0FBa0Msc0JBQXNCLHdCQUF3QixHQUFHLGtCQUFrQixlQUFlLGtCQUFrQix1QkFBdUIsSUFBSSxxQ0FBcUMsZ0ZBQWdGLFVBQVUsb0JBQW9CLGdCQUFnQiwrQ0FBK0MsR0FBRyxxQkFBcUIsU0FBUyxlQUFlLFVBQVUsaUJBQWlCLGNBQWMsNkRBQTZELGlFQUFpRSxhQUFhLDBFQUEwRSxtSEFBbUgsYUFBYSx3UkFBd1IsaUJBQWlCLDhFQUE4RSxpREFBaUQsV0FBVyxVQUFVLHVEQUF1RCwrQ0FBK0MsUUFBUSx5UUFBeVEsU0FBUyxlQUFlLFdBQVcsMERBQTBELDRDQUE0QywrRUFBK0UsdUJBQXVCLE1BQU0sMEhBQTBILHFEQUFxRCw2REFBNkQsNENBQTRDLG9CQUFvQix1Q0FBdUMsR0FBRyxLQUFLLHFEQUFxRCx1QkFBdUIsR0FBRyxrQkFBa0IsOEZBQThGLHdEQUF3RCwyRkFBMkYsaURBQWlELE1BQU0seUNBQXlDLDZFQUE2RSx1RUFBdUUsSUFBSSx1QkFBdUIsNkJBQTZCLHNCQUFzQix5R0FBeUcsbUZBQW1GLHlCQUF5QixXQUFXLGdCQUFnQiwrRkFBK0YseUJBQXlCLGtFQUFrRSxzREFBc0QscUJBQXFCLG1CQUFtQix5SUFBeUksNENBQTRDLDZCQUE2QixhQUFhLEVBQUUsVUFBVSw4QkFBOEIsMElBQTBJLGFBQWEsNkNBQTZDLGFBQWEsTUFBTSxvRUFBb0UsbUNBQW1DLHVKQUF1SiwyQkFBMkIsaUNBQWlDLGFBQWEsTUFBTSxvRUFBb0UsV0FBVyxpQkFBaUIsNGlCQUE0aUIsa0JBQWtCLFNBQVMsYUFBYSxzREFBc0QsSUFBSSx3QkFBd0IsZ0NBQWdDLFVBQVUsd0hBQXdILGVBQWUsTUFBTSxxRUFBcUUsK0RBQStELHdCQUF3QixRQUFRLGtFQUFrRSxJQUFJLHdEQUF3RCxVQUFVLHVGQUF1RixTQUFTLFVBQVUsa0hBQWtILE1BQU0sT0FBTyxxL0JBQXEvQiw0RUFBNEUsOFZBQThWLGtEQUFrRCxXQUFXLDBFQUEwRSxxSUFBcUkscUJBQXFCLGdCQUFnQixRQUFRLFNBQVMsOFRBQThULFVBQVUsTUFBTSw2UUFBNlEsU0FBUyxjQUFjLDhCQUE4QixTQUFTLGtDQUFrQyxVQUFVLDJMQUEyTCxTQUFTLDJCQUEyQixRQUFRLHFGQUFxRixjQUFjLDZPQUE2TyxjQUFjLE9BQU8sOEJBQThCLDZiQUE2YixjQUFjLFlBQVksTUFBTSxHQUFHLHlDQUF5QyxFQUFFLG9CQUFvQixZQUFZLE1BQU0sR0FBRyxnREFBZ0QsR0FBRywrRUFBK0UsWUFBWSxjQUFjLDhDQUE4Qyw0Q0FBNEMseUJBQXlCLGFBQWEsUUFBUSxvQkFBb0IsbUxBQW1MLHdHQUF3RywrQkFBK0IsK0VBQStFLGFBQWEsZUFBZSxnVUFBZ1UsY0FBYyw2QkFBNkIsR0FBRyxVQUFVLCtCQUErQixnQ0FBZ0MsK2tDQUEra0MsNkJBQTZCLHNCQUFzQixrRUFBa0UsNkdBQTZHLGtIQUFrSCxnREFBZ0QsRUFBRSxjQUFjLHVFQUF1RSxjQUFjLE1BQU0sZ0VBQWdFLG1CQUFtQixNQUFNLGdGQUFnRixJQUFJLHdFQUF3RSxvQ0FBb0MsUUFBUSx3SUFBd0ksU0FBUyxJQUFJLEVBQUUsd0NBQXdDLHFDQUFxQyxFQUFFLDBFQUEwRSwrQkFBK0IsS0FBSyxpQ0FBaUMsc0NBQXNDLG9CQUFvQiwyQkFBMkIsbURBQW1ELG9GQUFvRixXQUFXLGtSQUFrUixNQUFNLG9CQUFvQixrSEFBa0gsV0FBVyxPQUFPLDZTQUE2UyxrREFBa0QsMmFBQTJhLEtBQUssSUFBSSx5QkFBeUIsU0FBUyx5Q0FBeUMseURBQXlELG9DQUFvQyxvREFBb0QsS0FBSyxNQUFNLDhGQUE4RixjQUFjLDBHQUEwRyxpQkFBaUIsUUFBUSw0S0FBNEssdUJBQXVCLEVBQUUsTUFBTSxzS0FBc0ssc0JBQXNCLGdCQUFnQiwwS0FBMEssZ0JBQWdCLElBQUkseUNBQXlDLDhFQUE4RSxFQUFFLGlKQUFpSix1REFBdUQsS0FBSyxNQUFNLElBQUksYUFBYSxRQUFRLG9IQUFvSCxpQ0FBaUMsS0FBSyxNQUFNLCtDQUErQyxrQkFBa0IscUJBQXFCLFNBQVMseUlBQXlJLEdBQUcsb0JBQW9CLGlCQUFpQixZQUFZLFFBQVEsaUJBQWlCLDhCQUE4QixHQUFHLHVDQUF1QyxNQUFNLDhCQUE4QixLQUFLLFFBQVEsK0lBQStJLFFBQVEseUJBQXlCLGVBQWUsTUFBTSxzRUFBc0UsOENBQThDLDBEQUEwRCw0RkFBNEYsOEJBQThCLCtnQkFBK2dCLDJEQUEyRCwrREFBK0QsT0FBTyw2SEFBNkgsMENBQTBDLDZPQUE2TyxVQUFVLDJCQUEyQiwwRUFBMEUsaUJBQWlCLGFBQWEsSUFBSSxnQ0FBZ0MsT0FBTyw4TUFBOE0sMkhBQTJILHlFQUF5RSwwREFBMEQsK0NBQStDLE1BQU0sa0RBQWtELG9DQUFvQyxzRUFBc0UsNERBQTRELElBQUksMEhBQTBILFVBQVUscUVBQXFFLDRMQUE0TCxrR0FBa0csd0JBQXdCLElBQUksdUJBQXVCLGtFQUFrRSwrQkFBK0IsMEVBQTBFLElBQUksTUFBTSxxSUFBcUksU0FBUyxrREFBa0QsK1JBQStSLG9CQUFvQiwyQ0FBMkMsaUdBQWlHLDRMQUE0TCxZQUFZLDBKQUEwSixzQ0FBc0Msb0ZBQW9GLGdCQUFnQixtRkFBbUYsTUFBTSxNQUFNLDBDQUEwQyxvQkFBb0IsaUhBQWlILFFBQVEsTUFBTSxvQ0FBb0MsOEhBQThILGNBQWMsTUFBTSw0Q0FBNEMscUJBQXFCLE1BQU0sbURBQW1ELHdCQUF3QixNQUFNLGlEQUFpRCxjQUFjLE1BQU0sNENBQTRDLDBCQUEwQixNQUFNLG1EQUFtRCxRQUFRLGVBQWUsTUFBTSxFQUFFLG9CQUFvQiw2Q0FBNkMseUJBQXlCLGlEQUFpRCxJQUFJLHFCQUFxQixTQUFTLFVBQVUsc0JBQXNCLCtDQUErQyxxQkFBcUIsdUNBQXVDLHdDQUF3QywwREFBMEQsOEJBQThCLDBFQUEwRSxRQUFRLHVCQUF1QixrQkFBa0IsMkNBQTJDLG1CQUFtQix1Q0FBdUMsZUFBZSxRQUFRLDZGQUE2RixnQkFBZ0IsZ0RBQWdELDhCQUE4Qiw4REFBOEQsa0JBQWtCLCtCQUErQixtQkFBbUIsdUNBQXVDLDBCQUEwQixpRUFBaUUsZ0JBQWdCLHVGQUF1RiwwSkFBMEosa05BQWtOLHFDQUFxQyw2QkFBNkIsa0JBQWtCLFdBQVcsb0NBQW9DLFFBQVEsb0JBQW9CLDJDQUEyQyxLQUFLLHNFQUFzRSxjQUFjLEdBQUcscUVBQXFFLDhGQUE4RixNQUFNLE1BQU0sZ0NBQWdDLGtDQUFrQyxFQUFFLFVBQVUsaUJBQWlCLCtKQUErSixvRUFBb0UseUJBQXlCLG1EQUFtRCxxQ0FBcUMsa0RBQWtELE1BQU0sTUFBTSx3QkFBd0IsVUFBVSxpQkFBaUIscUhBQXFILGFBQWEsU0FBUyw2QkFBNkIsdUJBQXVCLDhEQUE4RCxZQUFZLElBQUksTUFBTSxFQUFFLHFDQUFxQyx3Q0FBd0MsbUNBQW1DLE1BQU0sMkNBQTJDLGtFQUFrRSxjQUFjLGVBQWUsV0FBVyxnRUFBZ0UsaUNBQWlDLHNFQUFzRSxnQ0FBZ0Msa0RBQWtELGdDQUFnQyxvSEFBb0gsZ0NBQWdDLGtEQUFrRCxTQUFTLFlBQVksaUZBQWlGLHNDQUFzQyxpVUFBaVUsK0JBQStCLEtBQUssS0FBSyxzQ0FBc0Msb0JBQW9CLCtCQUErQixLQUFLLGVBQWUsNkNBQTZDLElBQUksa0JBQWtCLHdDQUF3QyxZQUFZLHdDQUF3QyxpQkFBaUIsUUFBUSxzSEFBc0gsMEJBQTBCLGlDQUFpQyxJQUFJLG9DQUFvQyx5SUFBeUksZ0NBQWdDLHdEQUF3RCxlQUFlLGdFQUFnRSxTQUFTLFdBQVcsa09BQWtPLHNCQUFzQixrR0FBa0csY0FBYyxrQkFBa0IsY0FBYywwTkFBME4sY0FBYyw2QkFBNkIsZ0lBQWdJLG1DQUFtQyxjQUFjLHdNQUF3TSx5QkFBeUIsZ0JBQWdCLHVLQUF1SyxtQ0FBbUMsY0FBYyxxSUFBcUkseUVBQXlFLDJFQUEyRSwrRUFBK0UsaUJBQWlCLDZCQUE2QixFQUFFLHVCQUF1QixpQkFBaUIsNkJBQTZCLEVBQUUsMEJBQTBCLE1BQU0scURBQXFELHNCQUFzQiw4Q0FBOEMsdUNBQXVDLGdDQUFnQyx3QkFBd0IsRUFBRSxRQUFRLEVBQUUscUJBQXFCLE1BQU0sZUFBZSxtQ0FBbUMsZ0JBQWdCLE1BQU0sb0RBQW9ELHNCQUFzQixNQUFNLDJEQUEyRCxXQUFXLHFEQUFxRCw4QkFBOEIsZ0JBQWdCLFFBQVEsMEpBQTBKLEtBQUssUUFBUSw4UEFBOFAsS0FBSyxzTEFBc0wsc0JBQXNCLDhFQUE4RSxHQUFHLEVBQUUsTUFBTSxnS0FBZ0ssS0FBSywrSEFBK0gsOEZBQThGLDRHQUE0RyxvQkFBb0IsTUFBTSwrTkFBK04sa0JBQWtCLEVBQUUsd0NBQXdDLG9CQUFvQixvQ0FBb0MseUJBQXlCLGdDQUFnQywwQkFBMEIsaUNBQWlDLDZCQUE2QiwrQkFBK0IsVUFBVSw0REFBNEQsS0FBSyxpRkFBaUYsY0FBYyxFQUFFLGtCQUFrQixxSkFBcUosU0FBUyxpSUFBaUksU0FBUyx1T0FBdU8sU0FBUyxHQUFHLE1BQU0sdUNBQXVDLHVGQUF1RixnQkFBZ0IsZ0JBQWdCLGdFQUFnRSxtRkFBbUYsb0lBQW9JLFNBQVMsZ0JBQWdCLE1BQU0sNkRBQTZELDBCQUEwQiw2QkFBNkIsMkNBQTJDLEVBQUUsMEJBQTBCLDJCQUEyQixvRUFBb0UsR0FBRyxlQUFlLFlBQVksV0FBVywyQ0FBMkMsa0JBQWtCLHNCQUFzQiw2REFBNkQsR0FBRyxpQkFBaUIsdVBBQXVQLEdBQUcsaUJBQWlCLDJEQUEyRCxHQUFHLGlCQUFpQix5SEFBeUgsR0FBRyxpQkFBaUIsNEhBQTRILEdBQUcsaUJBQWlCLGlFQUFpRSxHQUFHLGtDQUFrQyxhQUFhLDJDQUEyQyxnQkFBZ0IsSUFBSSxxRkFBcUYsV0FBVyx1R0FBdUcsT0FBcVM7QUFDOThrSyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHBvc3Rob2ctanNAMS4yNDYuMF9AcnJ3ZWIrdHlwZXNAMi4wLjAtYWxwaGEuMTdcXG5vZGVfbW9kdWxlc1xccG9zdGhvZy1qc1xcZGlzdFxcbW9kdWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciB0PVwidW5kZWZpbmVkXCIhPXR5cGVvZiB3aW5kb3c/d2luZG93OnZvaWQgMCxpPVwidW5kZWZpbmVkXCIhPXR5cGVvZiBnbG9iYWxUaGlzP2dsb2JhbFRoaXM6dCxlPUFycmF5LnByb3RvdHlwZSxyPWUuZm9yRWFjaCxzPWUuaW5kZXhPZixuPW51bGw9PWk/dm9pZCAwOmkubmF2aWdhdG9yLG89bnVsbD09aT92b2lkIDA6aS5kb2N1bWVudCxhPW51bGw9PWk/dm9pZCAwOmkubG9jYXRpb24sbD1udWxsPT1pP3ZvaWQgMDppLmZldGNoLHU9bnVsbCE9aSYmaS5YTUxIdHRwUmVxdWVzdCYmXCJ3aXRoQ3JlZGVudGlhbHNcImluIG5ldyBpLlhNTEh0dHBSZXF1ZXN0P2kuWE1MSHR0cFJlcXVlc3Q6dm9pZCAwLGg9bnVsbD09aT92b2lkIDA6aS5BYm9ydENvbnRyb2xsZXIsZD1udWxsPT1uP3ZvaWQgMDpuLnVzZXJBZ2VudCx2PW51bGwhPXQ/dDp7fSxjPXtERUJVRzohMSxMSUJfVkVSU0lPTjpcIjEuMjQ2LjBcIn0sZj1cIiRjb3B5X2F1dG9jYXB0dXJlXCIscD1bXCIkc25hcHNob3RcIixcIiRwYWdldmlld1wiLFwiJHBhZ2VsZWF2ZVwiLFwiJHNldFwiLFwic3VydmV5IGRpc21pc3NlZFwiLFwic3VydmV5IHNlbnRcIixcInN1cnZleSBzaG93blwiLFwiJGlkZW50aWZ5XCIsXCIkZ3JvdXBpZGVudGlmeVwiLFwiJGNyZWF0ZV9hbGlhc1wiLFwiJCRjbGllbnRfaW5nZXN0aW9uX3dhcm5pbmdcIixcIiR3ZWJfZXhwZXJpbWVudF9hcHBsaWVkXCIsXCIkZmVhdHVyZV9lbnJvbGxtZW50X3VwZGF0ZVwiLFwiJGZlYXR1cmVfZmxhZ19jYWxsZWRcIl0sZz1mdW5jdGlvbih0KXtyZXR1cm4gdC5HWmlwSlM9XCJnemlwLWpzXCIsdC5CYXNlNjQ9XCJiYXNlNjRcIix0fSh7fSksXz1bXCJmYXRhbFwiLFwiZXJyb3JcIixcIndhcm5pbmdcIixcImxvZ1wiLFwiaW5mb1wiLFwiZGVidWdcIl07ZnVuY3Rpb24gbSh0LGkpe3JldHVybi0xIT09dC5pbmRleE9mKGkpfXZhciBiPWZ1bmN0aW9uKHQpe3JldHVybiB0LnRyaW0oKX0sdz1mdW5jdGlvbih0KXtyZXR1cm4gdC5yZXBsYWNlKC9eXFwkLyxcIlwiKX07dmFyIHk9QXJyYXkuaXNBcnJheSxTPU9iamVjdC5wcm90b3R5cGUsJD1TLmhhc093blByb3BlcnR5LGs9Uy50b1N0cmluZyx4PXl8fGZ1bmN0aW9uKHQpe3JldHVyblwiW29iamVjdCBBcnJheV1cIj09PWsuY2FsbCh0KX0sRT10PT5cImZ1bmN0aW9uXCI9PXR5cGVvZiB0LEk9dD0+dD09PU9iamVjdCh0KSYmIXgodCksUD10PT57aWYoSSh0KSl7Zm9yKHZhciBpIGluIHQpaWYoJC5jYWxsKHQsaSkpcmV0dXJuITE7cmV0dXJuITB9cmV0dXJuITF9LFI9dD0+dm9pZCAwPT09dCxUPXQ9PlwiW29iamVjdCBTdHJpbmddXCI9PWsuY2FsbCh0KSxNPXQ9PlQodCkmJjA9PT10LnRyaW0oKS5sZW5ndGgsQz10PT5udWxsPT09dCxPPXQ9PlIodCl8fEModCksRj10PT5cIltvYmplY3QgTnVtYmVyXVwiPT1rLmNhbGwodCksQT10PT5cIltvYmplY3QgQm9vbGVhbl1cIj09PWsuY2FsbCh0KSxEPXQ9PnQgaW5zdGFuY2VvZiBGb3JtRGF0YSxMPXQ9Pm0ocCx0KSxOPWk9Pnt2YXIgZT17dDpmdW5jdGlvbihlKXtpZih0JiYoYy5ERUJVR3x8di5QT1NUSE9HX0RFQlVHKSYmIVIodC5jb25zb2xlKSYmdC5jb25zb2xlKXtmb3IodmFyIHI9KFwiX19ycndlYl9vcmlnaW5hbF9fXCJpbiB0LmNvbnNvbGVbZV0/dC5jb25zb2xlW2VdLl9fcnJ3ZWJfb3JpZ2luYWxfXzp0LmNvbnNvbGVbZV0pLHM9YXJndW1lbnRzLmxlbmd0aCxuPW5ldyBBcnJheShzPjE/cy0xOjApLG89MTtvPHM7bysrKW5bby0xXT1hcmd1bWVudHNbb107cihpLC4uLm4pfX0saW5mbzpmdW5jdGlvbigpe2Zvcih2YXIgdD1hcmd1bWVudHMubGVuZ3RoLGk9bmV3IEFycmF5KHQpLHI9MDtyPHQ7cisrKWlbcl09YXJndW1lbnRzW3JdO2UudChcImxvZ1wiLC4uLmkpfSx3YXJuOmZ1bmN0aW9uKCl7Zm9yKHZhciB0PWFyZ3VtZW50cy5sZW5ndGgsaT1uZXcgQXJyYXkodCkscj0wO3I8dDtyKyspaVtyXT1hcmd1bWVudHNbcl07ZS50KFwid2FyblwiLC4uLmkpfSxlcnJvcjpmdW5jdGlvbigpe2Zvcih2YXIgdD1hcmd1bWVudHMubGVuZ3RoLGk9bmV3IEFycmF5KHQpLHI9MDtyPHQ7cisrKWlbcl09YXJndW1lbnRzW3JdO2UudChcImVycm9yXCIsLi4uaSl9LGNyaXRpY2FsOmZ1bmN0aW9uKCl7Zm9yKHZhciB0PWFyZ3VtZW50cy5sZW5ndGgsZT1uZXcgQXJyYXkodCkscj0wO3I8dDtyKyspZVtyXT1hcmd1bWVudHNbcl07Y29uc29sZS5lcnJvcihpLC4uLmUpfSx1bmluaXRpYWxpemVkV2FybmluZzp0PT57ZS5lcnJvcihcIllvdSBtdXN0IGluaXRpYWxpemUgUG9zdEhvZyBiZWZvcmUgY2FsbGluZyBcIit0KX0sY3JlYXRlTG9nZ2VyOnQ9Pk4oaStcIiBcIit0KX07cmV0dXJuIGV9LGo9TihcIltQb3N0SG9nLmpzXVwiKSx6PWouY3JlYXRlTG9nZ2VyLFU9eihcIltFeHRlcm5hbFNjcmlwdHNMb2FkZXJdXCIpLHE9KHQsaSxlKT0+e2lmKHQuY29uZmlnLmRpc2FibGVfZXh0ZXJuYWxfZGVwZW5kZW5jeV9sb2FkaW5nKXJldHVybiBVLndhcm4oaStcIiB3YXMgcmVxdWVzdGVkIGJ1dCBsb2FkaW5nIG9mIGV4dGVybmFsIHNjcmlwdHMgaXMgZGlzYWJsZWQuXCIpLGUoXCJMb2FkaW5nIG9mIGV4dGVybmFsIHNjcmlwdHMgaXMgZGlzYWJsZWRcIik7dmFyIHI9bnVsbD09bz92b2lkIDA6by5xdWVyeVNlbGVjdG9yQWxsKFwic2NyaXB0XCIpO2lmKHIpZm9yKHZhciBzPTA7czxyLmxlbmd0aDtzKyspaWYocltzXS5zcmM9PT1pKXJldHVybiBlKCk7dmFyIG49KCk9PntpZighbylyZXR1cm4gZShcImRvY3VtZW50IG5vdCBmb3VuZFwiKTt2YXIgcj1vLmNyZWF0ZUVsZW1lbnQoXCJzY3JpcHRcIik7aWYoci50eXBlPVwidGV4dC9qYXZhc2NyaXB0XCIsci5jcm9zc09yaWdpbj1cImFub255bW91c1wiLHIuc3JjPWksci5vbmxvYWQ9dD0+ZSh2b2lkIDAsdCksci5vbmVycm9yPXQ9PmUodCksdC5jb25maWcucHJlcGFyZV9leHRlcm5hbF9kZXBlbmRlbmN5X3NjcmlwdCYmKHI9dC5jb25maWcucHJlcGFyZV9leHRlcm5hbF9kZXBlbmRlbmN5X3NjcmlwdChyKSksIXIpcmV0dXJuIGUoXCJwcmVwYXJlX2V4dGVybmFsX2RlcGVuZGVuY3lfc2NyaXB0IHJldHVybmVkIG51bGxcIik7dmFyIHMsbj1vLnF1ZXJ5U2VsZWN0b3JBbGwoXCJib2R5ID4gc2NyaXB0XCIpO24ubGVuZ3RoPjA/bnVsbD09KHM9blswXS5wYXJlbnROb2RlKXx8cy5pbnNlcnRCZWZvcmUocixuWzBdKTpvLmJvZHkuYXBwZW5kQ2hpbGQocil9O251bGwhPW8mJm8uYm9keT9uKCk6bnVsbD09b3x8by5hZGRFdmVudExpc3RlbmVyKFwiRE9NQ29udGVudExvYWRlZFwiLG4pfTtmdW5jdGlvbiBCKCl7cmV0dXJuIEI9T2JqZWN0LmFzc2lnbj9PYmplY3QuYXNzaWduLmJpbmQoKTpmdW5jdGlvbih0KXtmb3IodmFyIGk9MTtpPGFyZ3VtZW50cy5sZW5ndGg7aSsrKXt2YXIgZT1hcmd1bWVudHNbaV07Zm9yKHZhciByIGluIGUpKHt9KS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGUscikmJih0W3JdPWVbcl0pfXJldHVybiB0fSxCLmFwcGx5KG51bGwsYXJndW1lbnRzKX1mdW5jdGlvbiBIKHQsaSl7aWYobnVsbD09dClyZXR1cm57fTt2YXIgZT17fTtmb3IodmFyIHIgaW4gdClpZih7fS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHQscikpe2lmKC0xIT09aS5pbmRleE9mKHIpKWNvbnRpbnVlO2Vbcl09dFtyXX1yZXR1cm4gZX12Ll9fUG9zdGhvZ0V4dGVuc2lvbnNfXz12Ll9fUG9zdGhvZ0V4dGVuc2lvbnNfX3x8e30sdi5fX1Bvc3Rob2dFeHRlbnNpb25zX18ubG9hZEV4dGVybmFsRGVwZW5kZW5jeT0odCxpLGUpPT57dmFyIHI9XCIvc3RhdGljL1wiK2krXCIuanM/dj1cIit0LnZlcnNpb247aWYoXCJyZW1vdGUtY29uZmlnXCI9PT1pJiYocj1cIi9hcnJheS9cIit0LmNvbmZpZy50b2tlbitcIi9jb25maWcuanNcIiksXCJ0b29sYmFyXCI9PT1pKXt2YXIgcz0zZTU7cj1yK1wiJnQ9XCIrTWF0aC5mbG9vcihEYXRlLm5vdygpL3MpKnN9dmFyIG49dC5yZXF1ZXN0Um91dGVyLmVuZHBvaW50Rm9yKFwiYXNzZXRzXCIscik7cSh0LG4sZSl9LHYuX19Qb3N0aG9nRXh0ZW5zaW9uc19fLmxvYWRTaXRlQXBwPSh0LGksZSk9Pnt2YXIgcj10LnJlcXVlc3RSb3V0ZXIuZW5kcG9pbnRGb3IoXCJhcGlcIixpKTtxKHQscixlKX07dmFyIFc9e307ZnVuY3Rpb24gRyh0LGksZSl7aWYoeCh0KSlpZihyJiZ0LmZvckVhY2g9PT1yKXQuZm9yRWFjaChpLGUpO2Vsc2UgaWYoXCJsZW5ndGhcImluIHQmJnQubGVuZ3RoPT09K3QubGVuZ3RoKWZvcih2YXIgcz0wLG49dC5sZW5ndGg7czxuO3MrKylpZihzIGluIHQmJmkuY2FsbChlLHRbc10scyk9PT1XKXJldHVybn1mdW5jdGlvbiBKKHQsaSxlKXtpZighTyh0KSl7aWYoeCh0KSlyZXR1cm4gRyh0LGksZSk7aWYoRCh0KSl7Zm9yKHZhciByIG9mIHQuZW50cmllcygpKWlmKGkuY2FsbChlLHJbMV0sclswXSk9PT1XKXJldHVybn1lbHNlIGZvcih2YXIgcyBpbiB0KWlmKCQuY2FsbCh0LHMpJiZpLmNhbGwoZSx0W3NdLHMpPT09VylyZXR1cm59fXZhciBWPWZ1bmN0aW9uKHQpe2Zvcih2YXIgaT1hcmd1bWVudHMubGVuZ3RoLGU9bmV3IEFycmF5KGk+MT9pLTE6MCkscj0xO3I8aTtyKyspZVtyLTFdPWFyZ3VtZW50c1tyXTtyZXR1cm4gRyhlLChmdW5jdGlvbihpKXtmb3IodmFyIGUgaW4gaSl2b2lkIDAhPT1pW2VdJiYodFtlXT1pW2VdKX0pKSx0fSxLPWZ1bmN0aW9uKHQpe2Zvcih2YXIgaT1hcmd1bWVudHMubGVuZ3RoLGU9bmV3IEFycmF5KGk+MT9pLTE6MCkscj0xO3I8aTtyKyspZVtyLTFdPWFyZ3VtZW50c1tyXTtyZXR1cm4gRyhlLChmdW5jdGlvbihpKXtHKGksKGZ1bmN0aW9uKGkpe3QucHVzaChpKX0pKX0pKSx0fTtmdW5jdGlvbiBZKHQpe2Zvcih2YXIgaT1PYmplY3Qua2V5cyh0KSxlPWkubGVuZ3RoLHI9bmV3IEFycmF5KGUpO2UtLTspcltlXT1baVtlXSx0W2lbZV1dXTtyZXR1cm4gcn12YXIgWD1mdW5jdGlvbih0KXt0cnl7cmV0dXJuIHQoKX1jYXRjaCh0KXtyZXR1cm59fSxRPWZ1bmN0aW9uKHQpe3JldHVybiBmdW5jdGlvbigpe3RyeXtmb3IodmFyIGk9YXJndW1lbnRzLmxlbmd0aCxlPW5ldyBBcnJheShpKSxyPTA7cjxpO3IrKyllW3JdPWFyZ3VtZW50c1tyXTtyZXR1cm4gdC5hcHBseSh0aGlzLGUpfWNhdGNoKHQpe2ouY3JpdGljYWwoXCJJbXBsZW1lbnRhdGlvbiBlcnJvci4gUGxlYXNlIHR1cm4gb24gZGVidWcgbW9kZSBhbmQgb3BlbiBhIHRpY2tldCBvbiBodHRwczovL2FwcC5wb3N0aG9nLmNvbS9ob21lI3BhbmVsPXN1cHBvcnQlM0FzdXBwb3J0JTNBLlwiKSxqLmNyaXRpY2FsKHQpfX19LFo9ZnVuY3Rpb24odCl7dmFyIGk9e307cmV0dXJuIEoodCwoZnVuY3Rpb24odCxlKXsoVCh0KSYmdC5sZW5ndGg+MHx8Rih0KSkmJihpW2VdPXQpfSkpLGl9O2Z1bmN0aW9uIHR0KHQsaSl7cmV0dXJuIGU9dCxyPXQ9PlQodCkmJiFDKGkpP3Quc2xpY2UoMCxpKTp0LHM9bmV3IFNldCxmdW5jdGlvbiB0KGksZSl7cmV0dXJuIGkhPT1PYmplY3QoaSk/cj9yKGksZSk6aTpzLmhhcyhpKT92b2lkIDA6KHMuYWRkKGkpLHgoaSk/KG49W10sRyhpLChpPT57bi5wdXNoKHQoaSkpfSkpKToobj17fSxKKGksKChpLGUpPT57cy5oYXMoaSl8fChuW2VdPXQoaSxlKSl9KSkpLG4pO3ZhciBufShlKTt2YXIgZSxyLHN9dmFyIGl0PVtcImhlcm9rdWFwcC5jb21cIixcInZlcmNlbC5hcHBcIixcIm5ldGxpZnkuYXBwXCJdO2Z1bmN0aW9uIGV0KHQpe3ZhciBpPW51bGw9PXQ/dm9pZCAwOnQuaG9zdG5hbWU7aWYoIVQoaSkpcmV0dXJuITE7dmFyIGU9aS5zcGxpdChcIi5cIikuc2xpY2UoLTIpLmpvaW4oXCIuXCIpO2Zvcih2YXIgciBvZiBpdClpZihlPT09cilyZXR1cm4hMTtyZXR1cm4hMH1mdW5jdGlvbiBydCh0LGkpe2Zvcih2YXIgZT0wO2U8dC5sZW5ndGg7ZSsrKWlmKGkodFtlXSkpcmV0dXJuIHRbZV19ZnVuY3Rpb24gc3QodCxpLGUscil7dmFye2NhcHR1cmU6cz0hMSxwYXNzaXZlOm49ITB9PW51bGwhPXI/cjp7fTtudWxsPT10fHx0LmFkZEV2ZW50TGlzdGVuZXIoaSxlLHtjYXB0dXJlOnMscGFzc2l2ZTpufSl9dmFyIG50PVwiJHBlb3BsZV9kaXN0aW5jdF9pZFwiLG90PVwiX19hbGlhc1wiLGF0PVwiX190aW1lcnNcIixsdD1cIiRhdXRvY2FwdHVyZV9kaXNhYmxlZF9zZXJ2ZXJfc2lkZVwiLHV0PVwiJGhlYXRtYXBzX2VuYWJsZWRfc2VydmVyX3NpZGVcIixodD1cIiRleGNlcHRpb25fY2FwdHVyZV9lbmFibGVkX3NlcnZlcl9zaWRlXCIsZHQ9XCIkZXJyb3JfdHJhY2tpbmdfc3VwcHJlc3Npb25fcnVsZXNcIix2dD1cIiR3ZWJfdml0YWxzX2VuYWJsZWRfc2VydmVyX3NpZGVcIixjdD1cIiRkZWFkX2NsaWNrc19lbmFibGVkX3NlcnZlcl9zaWRlXCIsZnQ9XCIkd2ViX3ZpdGFsc19hbGxvd2VkX21ldHJpY3NcIixwdD1cIiRzZXNzaW9uX3JlY29yZGluZ19lbmFibGVkX3NlcnZlcl9zaWRlXCIsZ3Q9XCIkY29uc29sZV9sb2dfcmVjb3JkaW5nX2VuYWJsZWRfc2VydmVyX3NpZGVcIixfdD1cIiRzZXNzaW9uX3JlY29yZGluZ19uZXR3b3JrX3BheWxvYWRfY2FwdHVyZVwiLG10PVwiJHNlc3Npb25fcmVjb3JkaW5nX21hc2tpbmdcIixidD1cIiRzZXNzaW9uX3JlY29yZGluZ19jYW52YXNfcmVjb3JkaW5nXCIsd3Q9XCIkcmVwbGF5X3NhbXBsZV9yYXRlXCIseXQ9XCIkcmVwbGF5X21pbmltdW1fZHVyYXRpb25cIixTdD1cIiRyZXBsYXlfc2NyaXB0X2NvbmZpZ1wiLCR0PVwiJHNlc2lkXCIsa3Q9XCIkc2Vzc2lvbl9pc19zYW1wbGVkXCIseHQ9XCIkc2Vzc2lvbl9yZWNvcmRpbmdfdXJsX3RyaWdnZXJfYWN0aXZhdGVkX3Nlc3Npb25cIixFdD1cIiRzZXNzaW9uX3JlY29yZGluZ19ldmVudF90cmlnZ2VyX2FjdGl2YXRlZF9zZXNzaW9uXCIsSXQ9XCIkZW5hYmxlZF9mZWF0dXJlX2ZsYWdzXCIsUHQ9XCIkZWFybHlfYWNjZXNzX2ZlYXR1cmVzXCIsUnQ9XCIkZmVhdHVyZV9mbGFnX2RldGFpbHNcIixUdD1cIiRzdG9yZWRfcGVyc29uX3Byb3BlcnRpZXNcIixNdD1cIiRzdG9yZWRfZ3JvdXBfcHJvcGVydGllc1wiLEN0PVwiJHN1cnZleXNcIixPdD1cIiRzdXJ2ZXlzX2FjdGl2YXRlZFwiLEZ0PVwiJGZsYWdfY2FsbF9yZXBvcnRlZFwiLEF0PVwiJHVzZXJfc3RhdGVcIixEdD1cIiRjbGllbnRfc2Vzc2lvbl9wcm9wc1wiLEx0PVwiJGNhcHR1cmVfcmF0ZV9saW1pdFwiLE50PVwiJGluaXRpYWxfY2FtcGFpZ25fcGFyYW1zXCIsanQ9XCIkaW5pdGlhbF9yZWZlcnJlcl9pbmZvXCIsenQ9XCIkaW5pdGlhbF9wZXJzb25faW5mb1wiLFV0PVwiJGVwcFwiLHF0PVwiX19QT1NUSE9HX1RPT0xCQVJfX1wiLEJ0PVwiJHBvc3Rob2dfY29va2llbGVzc1wiLEh0PVtudCxvdCxcIl9fY21wbnNcIixhdCxwdCx1dCwkdCxJdCxkdCxBdCxQdCxSdCxNdCxUdCxDdCxGdCxEdCxMdCxOdCxqdCxVdCx6dF07ZnVuY3Rpb24gV3QodCl7cmV0dXJuIHQgaW5zdGFuY2VvZiBFbGVtZW50JiYodC5pZD09PXF0fHwhKG51bGw9PXQuY2xvc2VzdHx8IXQuY2xvc2VzdChcIi50b29sYmFyLWdsb2JhbC1mYWRlLWNvbnRhaW5lclwiKSkpfWZ1bmN0aW9uIEd0KHQpe3JldHVybiEhdCYmMT09PXQubm9kZVR5cGV9ZnVuY3Rpb24gSnQodCxpKXtyZXR1cm4hIXQmJiEhdC50YWdOYW1lJiZ0LnRhZ05hbWUudG9Mb3dlckNhc2UoKT09PWkudG9Mb3dlckNhc2UoKX1mdW5jdGlvbiBWdCh0KXtyZXR1cm4hIXQmJjM9PT10Lm5vZGVUeXBlfWZ1bmN0aW9uIEt0KHQpe3JldHVybiEhdCYmMTE9PT10Lm5vZGVUeXBlfWZ1bmN0aW9uIFl0KHQpe3JldHVybiB0P2IodCkuc3BsaXQoL1xccysvKTpbXX1mdW5jdGlvbiBYdChpKXt2YXIgZT1udWxsPT10P3ZvaWQgMDp0LmxvY2F0aW9uLmhyZWY7cmV0dXJuISEoZSYmaSYmaS5zb21lKCh0PT5lLm1hdGNoKHQpKSkpfWZ1bmN0aW9uIFF0KHQpe3ZhciBpPVwiXCI7c3dpdGNoKHR5cGVvZiB0LmNsYXNzTmFtZSl7Y2FzZVwic3RyaW5nXCI6aT10LmNsYXNzTmFtZTticmVhaztjYXNlXCJvYmplY3RcIjppPSh0LmNsYXNzTmFtZSYmXCJiYXNlVmFsXCJpbiB0LmNsYXNzTmFtZT90LmNsYXNzTmFtZS5iYXNlVmFsOm51bGwpfHx0LmdldEF0dHJpYnV0ZShcImNsYXNzXCIpfHxcIlwiO2JyZWFrO2RlZmF1bHQ6aT1cIlwifXJldHVybiBZdChpKX1mdW5jdGlvbiBadCh0KXtyZXR1cm4gTyh0KT9udWxsOmIodCkuc3BsaXQoLyhcXHMrKS8pLmZpbHRlcigodD0+Y2kodCkpKS5qb2luKFwiXCIpLnJlcGxhY2UoL1tcXHJcXG5dL2csXCIgXCIpLnJlcGxhY2UoL1sgXSsvZyxcIiBcIikuc3Vic3RyaW5nKDAsMjU1KX1mdW5jdGlvbiB0aSh0KXt2YXIgaT1cIlwiO3JldHVybiBuaSh0KSYmIW9pKHQpJiZ0LmNoaWxkTm9kZXMmJnQuY2hpbGROb2Rlcy5sZW5ndGgmJkoodC5jaGlsZE5vZGVzLChmdW5jdGlvbih0KXt2YXIgZTtWdCh0KSYmdC50ZXh0Q29udGVudCYmKGkrPW51bGwhPT0oZT1adCh0LnRleHRDb250ZW50KSkmJnZvaWQgMCE9PWU/ZTpcIlwiKX0pKSxiKGkpfWZ1bmN0aW9uIGlpKHQpe3JldHVybiBSKHQudGFyZ2V0KT90LnNyY0VsZW1lbnR8fG51bGw6bnVsbCE9KGk9dC50YXJnZXQpJiZpLnNoYWRvd1Jvb3Q/dC5jb21wb3NlZFBhdGgoKVswXXx8bnVsbDp0LnRhcmdldHx8bnVsbDt2YXIgaX12YXIgZWk9W1wiYVwiLFwiYnV0dG9uXCIsXCJmb3JtXCIsXCJpbnB1dFwiLFwic2VsZWN0XCIsXCJ0ZXh0YXJlYVwiLFwibGFiZWxcIl07ZnVuY3Rpb24gcmkodCl7dmFyIGk9dC5wYXJlbnROb2RlO3JldHVybiEoIWl8fCFHdChpKSkmJml9ZnVuY3Rpb24gc2koaSxlLHIscyxuKXt2YXIgbyxhLGw7aWYodm9pZCAwPT09ciYmKHI9dm9pZCAwKSwhdHx8IWl8fEp0KGksXCJodG1sXCIpfHwhR3QoaSkpcmV0dXJuITE7aWYobnVsbCE9KG89cikmJm8udXJsX2FsbG93bGlzdCYmIVh0KHIudXJsX2FsbG93bGlzdCkpcmV0dXJuITE7aWYobnVsbCE9KGE9cikmJmEudXJsX2lnbm9yZWxpc3QmJlh0KHIudXJsX2lnbm9yZWxpc3QpKXJldHVybiExO2lmKG51bGwhPShsPXIpJiZsLmRvbV9ldmVudF9hbGxvd2xpc3Qpe3ZhciB1PXIuZG9tX2V2ZW50X2FsbG93bGlzdDtpZih1JiYhdS5zb21lKCh0PT5lLnR5cGU9PT10KSkpcmV0dXJuITF9Zm9yKHZhciBoPSExLGQ9W2ldLHY9ITAsYz1pO2MucGFyZW50Tm9kZSYmIUp0KGMsXCJib2R5XCIpOylpZihLdChjLnBhcmVudE5vZGUpKWQucHVzaChjLnBhcmVudE5vZGUuaG9zdCksYz1jLnBhcmVudE5vZGUuaG9zdDtlbHNle2lmKCEodj1yaShjKSkpYnJlYWs7aWYoc3x8ZWkuaW5kZXhPZih2LnRhZ05hbWUudG9Mb3dlckNhc2UoKSk+LTEpaD0hMDtlbHNle3ZhciBmPXQuZ2V0Q29tcHV0ZWRTdHlsZSh2KTtmJiZcInBvaW50ZXJcIj09PWYuZ2V0UHJvcGVydHlWYWx1ZShcImN1cnNvclwiKSYmKGg9ITApfWQucHVzaCh2KSxjPXZ9aWYoIWZ1bmN0aW9uKHQsaSl7dmFyIGU9bnVsbD09aT92b2lkIDA6aS5lbGVtZW50X2FsbG93bGlzdDtpZihSKGUpKXJldHVybiEwO3ZhciByLHM9ZnVuY3Rpb24odCl7aWYoZS5zb21lKChpPT50LnRhZ05hbWUudG9Mb3dlckNhc2UoKT09PWkpKSlyZXR1cm57djohMH19O2Zvcih2YXIgbiBvZiB0KWlmKHI9cyhuKSlyZXR1cm4gci52O3JldHVybiExfShkLHIpKXJldHVybiExO2lmKCFmdW5jdGlvbih0LGkpe3ZhciBlPW51bGw9PWk/dm9pZCAwOmkuY3NzX3NlbGVjdG9yX2FsbG93bGlzdDtpZihSKGUpKXJldHVybiEwO3ZhciByLHM9ZnVuY3Rpb24odCl7aWYoZS5zb21lKChpPT50Lm1hdGNoZXMoaSkpKSlyZXR1cm57djohMH19O2Zvcih2YXIgbiBvZiB0KWlmKHI9cyhuKSlyZXR1cm4gci52O3JldHVybiExfShkLHIpKXJldHVybiExO3ZhciBwPXQuZ2V0Q29tcHV0ZWRTdHlsZShpKTtpZihwJiZcInBvaW50ZXJcIj09PXAuZ2V0UHJvcGVydHlWYWx1ZShcImN1cnNvclwiKSYmXCJjbGlja1wiPT09ZS50eXBlKXJldHVybiEwO3ZhciBnPWkudGFnTmFtZS50b0xvd2VyQ2FzZSgpO3N3aXRjaChnKXtjYXNlXCJodG1sXCI6cmV0dXJuITE7Y2FzZVwiZm9ybVwiOnJldHVybihufHxbXCJzdWJtaXRcIl0pLmluZGV4T2YoZS50eXBlKT49MDtjYXNlXCJpbnB1dFwiOmNhc2VcInNlbGVjdFwiOmNhc2VcInRleHRhcmVhXCI6cmV0dXJuKG58fFtcImNoYW5nZVwiLFwiY2xpY2tcIl0pLmluZGV4T2YoZS50eXBlKT49MDtkZWZhdWx0OnJldHVybiBoPyhufHxbXCJjbGlja1wiXSkuaW5kZXhPZihlLnR5cGUpPj0wOihufHxbXCJjbGlja1wiXSkuaW5kZXhPZihlLnR5cGUpPj0wJiYoZWkuaW5kZXhPZihnKT4tMXx8XCJ0cnVlXCI9PT1pLmdldEF0dHJpYnV0ZShcImNvbnRlbnRlZGl0YWJsZVwiKSl9fWZ1bmN0aW9uIG5pKHQpe2Zvcih2YXIgaT10O2kucGFyZW50Tm9kZSYmIUp0KGksXCJib2R5XCIpO2k9aS5wYXJlbnROb2RlKXt2YXIgZT1RdChpKTtpZihtKGUsXCJwaC1zZW5zaXRpdmVcIil8fG0oZSxcInBoLW5vLWNhcHR1cmVcIikpcmV0dXJuITF9aWYobShRdCh0KSxcInBoLWluY2x1ZGVcIikpcmV0dXJuITA7dmFyIHI9dC50eXBlfHxcIlwiO2lmKFQocikpc3dpdGNoKHIudG9Mb3dlckNhc2UoKSl7Y2FzZVwiaGlkZGVuXCI6Y2FzZVwicGFzc3dvcmRcIjpyZXR1cm4hMX12YXIgcz10Lm5hbWV8fHQuaWR8fFwiXCI7aWYoVChzKSl7aWYoL15jY3xjYXJkbnVtfGNjbnVtfGNyZWRpdGNhcmR8Y3NjfGN2Y3xjdnZ8ZXhwfHBhc3N8cHdkfHJvdXRpbmd8c2VjY29kZXxzZWN1cml0eWNvZGV8c2VjdXJpdHludW18c29jaWFsc2VjfHNvY3NlY3xzc24vaS50ZXN0KHMucmVwbGFjZSgvW15hLXpBLVowLTldL2csXCJcIikpKXJldHVybiExfXJldHVybiEwfWZ1bmN0aW9uIG9pKHQpe3JldHVybiEhKEp0KHQsXCJpbnB1dFwiKSYmIVtcImJ1dHRvblwiLFwiY2hlY2tib3hcIixcInN1Ym1pdFwiLFwicmVzZXRcIl0uaW5jbHVkZXModC50eXBlKXx8SnQodCxcInNlbGVjdFwiKXx8SnQodCxcInRleHRhcmVhXCIpfHxcInRydWVcIj09PXQuZ2V0QXR0cmlidXRlKFwiY29udGVudGVkaXRhYmxlXCIpKX12YXIgYWk9XCIoNFswLTldezEyfSg/OlswLTldezN9KT8pfCg1WzEtNV1bMC05XXsxNH0pfCg2KD86MDExfDVbMC05XXsyfSlbMC05XXsxMn0pfCgzWzQ3XVswLTldezEzfSl8KDMoPzowWzAtNV18WzY4XVswLTldKVswLTldezExfSl8KCg/OjIxMzF8MTgwMHwzNVswLTldezN9KVswLTldezExfSlcIixsaT1uZXcgUmVnRXhwKFwiXig/OlwiK2FpK1wiKSRcIiksdWk9bmV3IFJlZ0V4cChhaSksaGk9XCJcXFxcZHszfS0/XFxcXGR7Mn0tP1xcXFxkezR9XCIsZGk9bmV3IFJlZ0V4cChcIl4oXCIraGkrXCIpJFwiKSx2aT1uZXcgUmVnRXhwKFwiKFwiK2hpK1wiKVwiKTtmdW5jdGlvbiBjaSh0LGkpe2lmKHZvaWQgMD09PWkmJihpPSEwKSxPKHQpKXJldHVybiExO2lmKFQodCkpe2lmKHQ9Yih0KSwoaT9saTp1aSkudGVzdCgodHx8XCJcIikucmVwbGFjZSgvWy0gXS9nLFwiXCIpKSlyZXR1cm4hMTtpZigoaT9kaTp2aSkudGVzdCh0KSlyZXR1cm4hMX1yZXR1cm4hMH1mdW5jdGlvbiBmaSh0KXt2YXIgaT10aSh0KTtyZXR1cm4gY2koaT0oaStcIiBcIitwaSh0KSkudHJpbSgpKT9pOlwiXCJ9ZnVuY3Rpb24gcGkodCl7dmFyIGk9XCJcIjtyZXR1cm4gdCYmdC5jaGlsZE5vZGVzJiZ0LmNoaWxkTm9kZXMubGVuZ3RoJiZKKHQuY2hpbGROb2RlcywoZnVuY3Rpb24odCl7dmFyIGU7aWYodCYmXCJzcGFuXCI9PT0obnVsbD09KGU9dC50YWdOYW1lKT92b2lkIDA6ZS50b0xvd2VyQ2FzZSgpKSl0cnl7dmFyIHI9dGkodCk7aT0oaStcIiBcIityKS50cmltKCksdC5jaGlsZE5vZGVzJiZ0LmNoaWxkTm9kZXMubGVuZ3RoJiYoaT0oaStcIiBcIitwaSh0KSkudHJpbSgpKX1jYXRjaCh0KXtqLmVycm9yKFwiW0F1dG9DYXB0dXJlXVwiLHQpfX0pKSxpfWZ1bmN0aW9uIGdpKHQpe3JldHVybiBmdW5jdGlvbih0KXt2YXIgaT10Lm1hcCgodD0+e3ZhciBpLGUscj1cIlwiO2lmKHQudGFnX25hbWUmJihyKz10LnRhZ19uYW1lKSx0LmF0dHJfY2xhc3MpZm9yKHZhciBzIG9mKHQuYXR0cl9jbGFzcy5zb3J0KCksdC5hdHRyX2NsYXNzKSlyKz1cIi5cIitzLnJlcGxhY2UoL1wiL2csXCJcIik7dmFyIG49Qih7fSx0LnRleHQ/e3RleHQ6dC50ZXh0fTp7fSx7XCJudGgtY2hpbGRcIjpudWxsIT09KGk9dC5udGhfY2hpbGQpJiZ2b2lkIDAhPT1pP2k6MCxcIm50aC1vZi10eXBlXCI6bnVsbCE9PShlPXQubnRoX29mX3R5cGUpJiZ2b2lkIDAhPT1lP2U6MH0sdC5ocmVmP3tocmVmOnQuaHJlZn06e30sdC5hdHRyX2lkP3thdHRyX2lkOnQuYXR0cl9pZH06e30sdC5hdHRyaWJ1dGVzKSxvPXt9O3JldHVybiBZKG4pLnNvcnQoKCh0LGkpPT57dmFyW2VdPXQsW3JdPWk7cmV0dXJuIGUubG9jYWxlQ29tcGFyZShyKX0pKS5mb3JFYWNoKCh0PT57dmFyW2ksZV09dDtyZXR1cm4gb1tfaShpLnRvU3RyaW5nKCkpXT1faShlLnRvU3RyaW5nKCkpfSkpLHIrPVwiOlwiLHIrPVkobykubWFwKCh0PT57dmFyW2ksZV09dDtyZXR1cm4gaSsnPVwiJytlKydcIid9KSkuam9pbihcIlwiKX0pKTtyZXR1cm4gaS5qb2luKFwiO1wiKX0oZnVuY3Rpb24odCl7cmV0dXJuIHQubWFwKCh0PT57dmFyIGksZSxyPXt0ZXh0Om51bGw9PShpPXQuJGVsX3RleHQpP3ZvaWQgMDppLnNsaWNlKDAsNDAwKSx0YWdfbmFtZTp0LnRhZ19uYW1lLGhyZWY6bnVsbD09KGU9dC5hdHRyX19ocmVmKT92b2lkIDA6ZS5zbGljZSgwLDIwNDgpLGF0dHJfY2xhc3M6bWkodCksYXR0cl9pZDp0LmF0dHJfX2lkLG50aF9jaGlsZDp0Lm50aF9jaGlsZCxudGhfb2ZfdHlwZTp0Lm50aF9vZl90eXBlLGF0dHJpYnV0ZXM6e319O3JldHVybiBZKHQpLmZpbHRlcigodD0+e3ZhcltpXT10O3JldHVybiAwPT09aS5pbmRleE9mKFwiYXR0cl9fXCIpfSkpLmZvckVhY2goKHQ9Pnt2YXJbaSxlXT10O3JldHVybiByLmF0dHJpYnV0ZXNbaV09ZX0pKSxyfSkpfSh0KSl9ZnVuY3Rpb24gX2kodCl7cmV0dXJuIHQucmVwbGFjZSgvXCJ8XFxcXFwiL2csJ1xcXFxcIicpfWZ1bmN0aW9uIG1pKHQpe3ZhciBpPXQuYXR0cl9fY2xhc3M7cmV0dXJuIGk/eChpKT9pOll0KGkpOnZvaWQgMH1jbGFzcyBiaXtjb25zdHJ1Y3Rvcigpe3RoaXMuY2xpY2tzPVtdfWlzUmFnZUNsaWNrKHQsaSxlKXt2YXIgcj10aGlzLmNsaWNrc1t0aGlzLmNsaWNrcy5sZW5ndGgtMV07aWYociYmTWF0aC5hYnModC1yLngpK01hdGguYWJzKGktci55KTwzMCYmZS1yLnRpbWVzdGFtcDwxZTMpe2lmKHRoaXMuY2xpY2tzLnB1c2goe3g6dCx5OmksdGltZXN0YW1wOmV9KSwzPT09dGhpcy5jbGlja3MubGVuZ3RoKXJldHVybiEwfWVsc2UgdGhpcy5jbGlja3M9W3t4OnQseTppLHRpbWVzdGFtcDplfV07cmV0dXJuITF9fXZhciB3aT1bXCJsb2NhbGhvc3RcIixcIjEyNy4wLjAuMVwiXSx5aT10PT57dmFyIGk9bnVsbD09bz92b2lkIDA6by5jcmVhdGVFbGVtZW50KFwiYVwiKTtyZXR1cm4gUihpKT9udWxsOihpLmhyZWY9dCxpKX0sU2k9ZnVuY3Rpb24odCxpKXt2YXIgZSxyO3ZvaWQgMD09PWkmJihpPVwiJlwiKTt2YXIgcz1bXTtyZXR1cm4gSih0LChmdW5jdGlvbih0LGkpe1IodCl8fFIoaSl8fFwidW5kZWZpbmVkXCI9PT1pfHwoZT1lbmNvZGVVUklDb21wb25lbnQoKHQ9PnQgaW5zdGFuY2VvZiBGaWxlKSh0KT90Lm5hbWU6dC50b1N0cmluZygpKSxyPWVuY29kZVVSSUNvbXBvbmVudChpKSxzW3MubGVuZ3RoXT1yK1wiPVwiK2UpfSkpLHMuam9pbihpKX0sJGk9ZnVuY3Rpb24odCxpKXtmb3IodmFyIGUscj0oKHQuc3BsaXQoXCIjXCIpWzBdfHxcIlwiKS5zcGxpdCgvXFw/KC4qKS8pWzFdfHxcIlwiKS5yZXBsYWNlKC9eXFw/Ky9nLFwiXCIpLnNwbGl0KFwiJlwiKSxzPTA7czxyLmxlbmd0aDtzKyspe3ZhciBuPXJbc10uc3BsaXQoXCI9XCIpO2lmKG5bMF09PT1pKXtlPW47YnJlYWt9fWlmKCF4KGUpfHxlLmxlbmd0aDwyKXJldHVyblwiXCI7dmFyIG89ZVsxXTt0cnl7bz1kZWNvZGVVUklDb21wb25lbnQobyl9Y2F0Y2godCl7ai5lcnJvcihcIlNraXBwaW5nIGRlY29kaW5nIGZvciBtYWxmb3JtZWQgcXVlcnkgcGFyYW06IFwiK28pfXJldHVybiBvLnJlcGxhY2UoL1xcKy9nLFwiIFwiKX0sa2k9ZnVuY3Rpb24odCxpLGUpe2lmKCF0fHwhaXx8IWkubGVuZ3RoKXJldHVybiB0O2Zvcih2YXIgcj10LnNwbGl0KFwiI1wiKSxzPXJbMF18fFwiXCIsbj1yWzFdLG89cy5zcGxpdChcIj9cIiksYT1vWzFdLGw9b1swXSx1PShhfHxcIlwiKS5zcGxpdChcIiZcIiksaD1bXSxkPTA7ZDx1Lmxlbmd0aDtkKyspe3ZhciB2PXVbZF0uc3BsaXQoXCI9XCIpO3godikmJihpLmluY2x1ZGVzKHZbMF0pP2gucHVzaCh2WzBdK1wiPVwiK2UpOmgucHVzaCh1W2RdKSl9dmFyIGM9bDtyZXR1cm4gbnVsbCE9YSYmKGMrPVwiP1wiK2guam9pbihcIiZcIikpLG51bGwhPW4mJihjKz1cIiNcIituKSxjfSx4aT1mdW5jdGlvbih0LGkpe3ZhciBlPXQubWF0Y2gobmV3IFJlZ0V4cChpK1wiPShbXiZdKilcIikpO3JldHVybiBlP2VbMV06bnVsbH0sRWk9eihcIltBdXRvQ2FwdHVyZV1cIik7ZnVuY3Rpb24gSWkodCxpKXtyZXR1cm4gaS5sZW5ndGg+dD9pLnNsaWNlKDAsdCkrXCIuLi5cIjppfWZ1bmN0aW9uIFBpKHQpe2lmKHQucHJldmlvdXNFbGVtZW50U2libGluZylyZXR1cm4gdC5wcmV2aW91c0VsZW1lbnRTaWJsaW5nO3ZhciBpPXQ7ZG97aT1pLnByZXZpb3VzU2libGluZ313aGlsZShpJiYhR3QoaSkpO3JldHVybiBpfWZ1bmN0aW9uIFJpKHQsaSxlLHIpe3ZhciBzPXQudGFnTmFtZS50b0xvd2VyQ2FzZSgpLG49e3RhZ19uYW1lOnN9O2VpLmluZGV4T2Yocyk+LTEmJiFlJiYoXCJhXCI9PT1zLnRvTG93ZXJDYXNlKCl8fFwiYnV0dG9uXCI9PT1zLnRvTG93ZXJDYXNlKCk/bi4kZWxfdGV4dD1JaSgxMDI0LGZpKHQpKTpuLiRlbF90ZXh0PUlpKDEwMjQsdGkodCkpKTt2YXIgbz1RdCh0KTtvLmxlbmd0aD4wJiYobi5jbGFzc2VzPW8uZmlsdGVyKChmdW5jdGlvbih0KXtyZXR1cm5cIlwiIT09dH0pKSksSih0LmF0dHJpYnV0ZXMsKGZ1bmN0aW9uKGUpe3ZhciBzO2lmKCghb2kodCl8fC0xIT09W1wibmFtZVwiLFwiaWRcIixcImNsYXNzXCIsXCJhcmlhLWxhYmVsXCJdLmluZGV4T2YoZS5uYW1lKSkmJigobnVsbD09cnx8IXIuaW5jbHVkZXMoZS5uYW1lKSkmJiFpJiZjaShlLnZhbHVlKSYmKHM9ZS5uYW1lLCFUKHMpfHxcIl9uZ2NvbnRlbnRcIiE9PXMuc3Vic3RyaW5nKDAsMTApJiZcIl9uZ2hvc3RcIiE9PXMuc3Vic3RyaW5nKDAsNykpKSl7dmFyIG89ZS52YWx1ZTtcImNsYXNzXCI9PT1lLm5hbWUmJihvPVl0KG8pLmpvaW4oXCIgXCIpKSxuW1wiYXR0cl9fXCIrZS5uYW1lXT1JaSgxMDI0LG8pfX0pKTtmb3IodmFyIGE9MSxsPTEsdT10O3U9UGkodSk7KWErKyx1LnRhZ05hbWU9PT10LnRhZ05hbWUmJmwrKztyZXR1cm4gbi5udGhfY2hpbGQ9YSxuLm50aF9vZl90eXBlPWwsbn1mdW5jdGlvbiBUaShpLGUpe2Zvcih2YXIgcixzLHtlOm4sbWFza0FsbEVsZW1lbnRBdHRyaWJ1dGVzOm8sbWFza0FsbFRleHQ6YSxlbGVtZW50QXR0cmlidXRlSWdub3JlTGlzdDpsLGVsZW1lbnRzQ2hhaW5Bc1N0cmluZzp1fT1lLGg9W2ldLGQ9aTtkLnBhcmVudE5vZGUmJiFKdChkLFwiYm9keVwiKTspS3QoZC5wYXJlbnROb2RlKT8oaC5wdXNoKGQucGFyZW50Tm9kZS5ob3N0KSxkPWQucGFyZW50Tm9kZS5ob3N0KTooaC5wdXNoKGQucGFyZW50Tm9kZSksZD1kLnBhcmVudE5vZGUpO3ZhciB2LGM9W10sZj17fSxwPSExLGc9ITE7aWYoSihoLCh0PT57dmFyIGk9bmkodCk7XCJhXCI9PT10LnRhZ05hbWUudG9Mb3dlckNhc2UoKSYmKHA9dC5nZXRBdHRyaWJ1dGUoXCJocmVmXCIpLHA9aSYmcCYmY2kocCkmJnApLG0oUXQodCksXCJwaC1uby1jYXB0dXJlXCIpJiYoZz0hMCksYy5wdXNoKFJpKHQsbyxhLGwpKTt2YXIgZT1mdW5jdGlvbih0KXtpZighbmkodCkpcmV0dXJue307dmFyIGk9e307cmV0dXJuIEoodC5hdHRyaWJ1dGVzLChmdW5jdGlvbih0KXtpZih0Lm5hbWUmJjA9PT10Lm5hbWUuaW5kZXhPZihcImRhdGEtcGgtY2FwdHVyZS1hdHRyaWJ1dGVcIikpe3ZhciBlPXQubmFtZS5yZXBsYWNlKFwiZGF0YS1waC1jYXB0dXJlLWF0dHJpYnV0ZS1cIixcIlwiKSxyPXQudmFsdWU7ZSYmciYmY2kocikmJihpW2VdPXIpfX0pKSxpfSh0KTtWKGYsZSl9KSksZylyZXR1cm57cHJvcHM6e30sZXhwbGljaXROb0NhcHR1cmU6Z307aWYoYXx8KFwiYVwiPT09aS50YWdOYW1lLnRvTG93ZXJDYXNlKCl8fFwiYnV0dG9uXCI9PT1pLnRhZ05hbWUudG9Mb3dlckNhc2UoKT9jWzBdLiRlbF90ZXh0PWZpKGkpOmNbMF0uJGVsX3RleHQ9dGkoaSkpLHApe3ZhciBfLGI7Y1swXS5hdHRyX19ocmVmPXA7dmFyIHc9bnVsbD09KF89eWkocCkpP3ZvaWQgMDpfLmhvc3QseT1udWxsPT10fHxudWxsPT0oYj10LmxvY2F0aW9uKT92b2lkIDA6Yi5ob3N0O3cmJnkmJnchPT15JiYodj1wKX1yZXR1cm57cHJvcHM6Vih7JGV2ZW50X3R5cGU6bi50eXBlLCRjZV92ZXJzaW9uOjF9LHU/e306eyRlbGVtZW50czpjfSx7JGVsZW1lbnRzX2NoYWluOmdpKGMpfSxudWxsIT0ocj1jWzBdKSYmci4kZWxfdGV4dD97JGVsX3RleHQ6bnVsbD09KHM9Y1swXSk/dm9pZCAwOnMuJGVsX3RleHR9Ont9LHYmJlwiY2xpY2tcIj09PW4udHlwZT97JGV4dGVybmFsX2NsaWNrX3VybDp2fTp7fSxmKX19Y2xhc3MgTWl7Y29uc3RydWN0b3IodCl7dGhpcy5pPSExLHRoaXMubz1udWxsLHRoaXMucmFnZWNsaWNrcz1uZXcgYmksdGhpcy5oPSExLHRoaXMuaW5zdGFuY2U9dCx0aGlzLm09bnVsbH1nZXQgUygpe3ZhciB0LGksZT1JKHRoaXMuaW5zdGFuY2UuY29uZmlnLmF1dG9jYXB0dXJlKT90aGlzLmluc3RhbmNlLmNvbmZpZy5hdXRvY2FwdHVyZTp7fTtyZXR1cm4gZS51cmxfYWxsb3dsaXN0PW51bGw9PSh0PWUudXJsX2FsbG93bGlzdCk/dm9pZCAwOnQubWFwKCh0PT5uZXcgUmVnRXhwKHQpKSksZS51cmxfaWdub3JlbGlzdD1udWxsPT0oaT1lLnVybF9pZ25vcmVsaXN0KT92b2lkIDA6aS5tYXAoKHQ9Pm5ldyBSZWdFeHAodCkpKSxlfSQoKXtpZih0aGlzLmlzQnJvd3NlclN1cHBvcnRlZCgpKXtpZih0JiZvKXt2YXIgaT1pPT57aT1pfHwobnVsbD09dD92b2lkIDA6dC5ldmVudCk7dHJ5e3RoaXMuayhpKX1jYXRjaCh0KXtFaS5lcnJvcihcIkZhaWxlZCB0byBjYXB0dXJlIGV2ZW50XCIsdCl9fTtpZihzdChvLFwic3VibWl0XCIsaSx7Y2FwdHVyZTohMH0pLHN0KG8sXCJjaGFuZ2VcIixpLHtjYXB0dXJlOiEwfSksc3QobyxcImNsaWNrXCIsaSx7Y2FwdHVyZTohMH0pLHRoaXMuUy5jYXB0dXJlX2NvcGllZF90ZXh0KXt2YXIgZT1pPT57aT1pfHwobnVsbD09dD92b2lkIDA6dC5ldmVudCksdGhpcy5rKGksZil9O3N0KG8sXCJjb3B5XCIsZSx7Y2FwdHVyZTohMH0pLHN0KG8sXCJjdXRcIixlLHtjYXB0dXJlOiEwfSl9fX1lbHNlIEVpLmluZm8oXCJEaXNhYmxpbmcgQXV0b21hdGljIEV2ZW50IENvbGxlY3Rpb24gYmVjYXVzZSB0aGlzIGJyb3dzZXIgaXMgbm90IHN1cHBvcnRlZFwiKX1zdGFydElmRW5hYmxlZCgpe3RoaXMuaXNFbmFibGVkJiYhdGhpcy5pJiYodGhpcy4kKCksdGhpcy5pPSEwKX1vblJlbW90ZUNvbmZpZyh0KXt0LmVsZW1lbnRzQ2hhaW5Bc1N0cmluZyYmKHRoaXMuaD10LmVsZW1lbnRzQ2hhaW5Bc1N0cmluZyksdGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZSYmdGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZS5yZWdpc3Rlcih7W2x0XTohIXQuYXV0b2NhcHR1cmVfb3B0X291dH0pLHRoaXMubz0hIXQuYXV0b2NhcHR1cmVfb3B0X291dCx0aGlzLnN0YXJ0SWZFbmFibGVkKCl9c2V0RWxlbWVudFNlbGVjdG9ycyh0KXt0aGlzLm09dH1nZXRFbGVtZW50U2VsZWN0b3JzKHQpe3ZhciBpLGU9W107cmV0dXJuIG51bGw9PShpPXRoaXMubSl8fGkuZm9yRWFjaCgoaT0+e3ZhciByPW51bGw9PW8/dm9pZCAwOm8ucXVlcnlTZWxlY3RvckFsbChpKTtudWxsPT1yfHxyLmZvckVhY2goKHI9Pnt0PT09ciYmZS5wdXNoKGkpfSkpfSkpLGV9Z2V0IGlzRW5hYmxlZCgpe3ZhciB0LGksZT1udWxsPT0odD10aGlzLmluc3RhbmNlLnBlcnNpc3RlbmNlKT92b2lkIDA6dC5wcm9wc1tsdF0scj10aGlzLm87aWYoQyhyKSYmIUEoZSkmJiF0aGlzLmluc3RhbmNlLmNvbmZpZy5hZHZhbmNlZF9kaXNhYmxlX2RlY2lkZSlyZXR1cm4hMTt2YXIgcz1udWxsIT09KGk9dGhpcy5vKSYmdm9pZCAwIT09aT9pOiEhZTtyZXR1cm4hIXRoaXMuaW5zdGFuY2UuY29uZmlnLmF1dG9jYXB0dXJlJiYhc31rKGksZSl7aWYodm9pZCAwPT09ZSYmKGU9XCIkYXV0b2NhcHR1cmVcIiksdGhpcy5pc0VuYWJsZWQpe3ZhciByLHM9aWkoaSk7aWYoVnQocykmJihzPXMucGFyZW50Tm9kZXx8bnVsbCksXCIkYXV0b2NhcHR1cmVcIj09PWUmJlwiY2xpY2tcIj09PWkudHlwZSYmaSBpbnN0YW5jZW9mIE1vdXNlRXZlbnQpdGhpcy5pbnN0YW5jZS5jb25maWcucmFnZWNsaWNrJiZudWxsIT0ocj10aGlzLnJhZ2VjbGlja3MpJiZyLmlzUmFnZUNsaWNrKGkuY2xpZW50WCxpLmNsaWVudFksKG5ldyBEYXRlKS5nZXRUaW1lKCkpJiZ0aGlzLmsoaSxcIiRyYWdlY2xpY2tcIik7dmFyIG49ZT09PWY7aWYocyYmc2kocyxpLHRoaXMuUyxuLG4/W1wiY29weVwiLFwiY3V0XCJdOnZvaWQgMCkpe3Zhcntwcm9wczpvLGV4cGxpY2l0Tm9DYXB0dXJlOmF9PVRpKHMse2U6aSxtYXNrQWxsRWxlbWVudEF0dHJpYnV0ZXM6dGhpcy5pbnN0YW5jZS5jb25maWcubWFza19hbGxfZWxlbWVudF9hdHRyaWJ1dGVzLG1hc2tBbGxUZXh0OnRoaXMuaW5zdGFuY2UuY29uZmlnLm1hc2tfYWxsX3RleHQsZWxlbWVudEF0dHJpYnV0ZUlnbm9yZUxpc3Q6dGhpcy5TLmVsZW1lbnRfYXR0cmlidXRlX2lnbm9yZWxpc3QsZWxlbWVudHNDaGFpbkFzU3RyaW5nOnRoaXMuaH0pO2lmKGEpcmV0dXJuITE7dmFyIGw9dGhpcy5nZXRFbGVtZW50U2VsZWN0b3JzKHMpO2lmKGwmJmwubGVuZ3RoPjAmJihvLiRlbGVtZW50X3NlbGVjdG9ycz1sKSxlPT09Zil7dmFyIHUsaD1adChudWxsPT10fHxudWxsPT0odT10LmdldFNlbGVjdGlvbigpKT92b2lkIDA6dS50b1N0cmluZygpKSxkPWkudHlwZXx8XCJjbGlwYm9hcmRcIjtpZighaClyZXR1cm4hMTtvLiRzZWxlY3RlZF9jb250ZW50PWgsby4kY29weV90eXBlPWR9cmV0dXJuIHRoaXMuaW5zdGFuY2UuY2FwdHVyZShlLG8pLCEwfX19aXNCcm93c2VyU3VwcG9ydGVkKCl7cmV0dXJuIEUobnVsbD09bz92b2lkIDA6by5xdWVyeVNlbGVjdG9yQWxsKX19TWF0aC50cnVuY3x8KE1hdGgudHJ1bmM9ZnVuY3Rpb24odCl7cmV0dXJuIHQ8MD9NYXRoLmNlaWwodCk6TWF0aC5mbG9vcih0KX0pLE51bWJlci5pc0ludGVnZXJ8fChOdW1iZXIuaXNJbnRlZ2VyPWZ1bmN0aW9uKHQpe3JldHVybiBGKHQpJiZpc0Zpbml0ZSh0KSYmTWF0aC5mbG9vcih0KT09PXR9KTt2YXIgQ2k9XCIwMTIzNDU2Nzg5YWJjZGVmXCI7Y2xhc3MgT2l7Y29uc3RydWN0b3IodCl7aWYodGhpcy5ieXRlcz10LDE2IT09dC5sZW5ndGgpdGhyb3cgbmV3IFR5cGVFcnJvcihcIm5vdCAxMjgtYml0IGxlbmd0aFwiKX1zdGF0aWMgZnJvbUZpZWxkc1Y3KHQsaSxlLHIpe2lmKCFOdW1iZXIuaXNJbnRlZ2VyKHQpfHwhTnVtYmVyLmlzSW50ZWdlcihpKXx8IU51bWJlci5pc0ludGVnZXIoZSl8fCFOdW1iZXIuaXNJbnRlZ2VyKHIpfHx0PDB8fGk8MHx8ZTwwfHxyPDB8fHQ+MHhmZmZmZmZmZmZmZmZ8fGk+NDA5NXx8ZT4xMDczNzQxODIzfHxyPjQyOTQ5NjcyOTUpdGhyb3cgbmV3IFJhbmdlRXJyb3IoXCJpbnZhbGlkIGZpZWxkIHZhbHVlXCIpO3ZhciBzPW5ldyBVaW50OEFycmF5KDE2KTtyZXR1cm4gc1swXT10L01hdGgucG93KDIsNDApLHNbMV09dC9NYXRoLnBvdygyLDMyKSxzWzJdPXQvTWF0aC5wb3coMiwyNCksc1szXT10L01hdGgucG93KDIsMTYpLHNbNF09dC9NYXRoLnBvdygyLDgpLHNbNV09dCxzWzZdPTExMnxpPj4+OCxzWzddPWksc1s4XT0xMjh8ZT4+PjI0LHNbOV09ZT4+PjE2LHNbMTBdPWU+Pj44LHNbMTFdPWUsc1sxMl09cj4+PjI0LHNbMTNdPXI+Pj4xNixzWzE0XT1yPj4+OCxzWzE1XT1yLG5ldyBPaShzKX10b1N0cmluZygpe2Zvcih2YXIgdD1cIlwiLGk9MDtpPHRoaXMuYnl0ZXMubGVuZ3RoO2krKyl0PXQrQ2kuY2hhckF0KHRoaXMuYnl0ZXNbaV0+Pj40KStDaS5jaGFyQXQoMTUmdGhpcy5ieXRlc1tpXSksMyE9PWkmJjUhPT1pJiY3IT09aSYmOSE9PWl8fCh0Kz1cIi1cIik7aWYoMzYhPT10Lmxlbmd0aCl0aHJvdyBuZXcgRXJyb3IoXCJJbnZhbGlkIFVVSUR2NyB3YXMgZ2VuZXJhdGVkXCIpO3JldHVybiB0fWNsb25lKCl7cmV0dXJuIG5ldyBPaSh0aGlzLmJ5dGVzLnNsaWNlKDApKX1lcXVhbHModCl7cmV0dXJuIDA9PT10aGlzLmNvbXBhcmVUbyh0KX1jb21wYXJlVG8odCl7Zm9yKHZhciBpPTA7aTwxNjtpKyspe3ZhciBlPXRoaXMuYnl0ZXNbaV0tdC5ieXRlc1tpXTtpZigwIT09ZSlyZXR1cm4gTWF0aC5zaWduKGUpfXJldHVybiAwfX1jbGFzcyBGaXtjb25zdHJ1Y3Rvcigpe3RoaXMuST0wLHRoaXMuUD0wLHRoaXMuUj1uZXcgTGl9Z2VuZXJhdGUoKXt2YXIgdD10aGlzLmdlbmVyYXRlT3JBYm9ydCgpO2lmKFIodCkpe3RoaXMuST0wO3ZhciBpPXRoaXMuZ2VuZXJhdGVPckFib3J0KCk7aWYoUihpKSl0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZCBub3QgZ2VuZXJhdGUgVVVJRCBhZnRlciB0aW1lc3RhbXAgcmVzZXRcIik7cmV0dXJuIGl9cmV0dXJuIHR9Z2VuZXJhdGVPckFib3J0KCl7dmFyIHQ9RGF0ZS5ub3coKTtpZih0PnRoaXMuSSl0aGlzLkk9dCx0aGlzLlQoKTtlbHNle2lmKCEodCsxZTQ+dGhpcy5JKSlyZXR1cm47dGhpcy5QKyssdGhpcy5QPjQzOTgwNDY1MTExMDMmJih0aGlzLkkrKyx0aGlzLlQoKSl9cmV0dXJuIE9pLmZyb21GaWVsZHNWNyh0aGlzLkksTWF0aC50cnVuYyh0aGlzLlAvTWF0aC5wb3coMiwzMCkpLHRoaXMuUCZNYXRoLnBvdygyLDMwKS0xLHRoaXMuUi5uZXh0VWludDMyKCkpfVQoKXt0aGlzLlA9MTAyNCp0aGlzLlIubmV4dFVpbnQzMigpKygxMDIzJnRoaXMuUi5uZXh0VWludDMyKCkpfX12YXIgQWksRGk9dD0+e2lmKFwidW5kZWZpbmVkXCIhPXR5cGVvZiBVVUlEVjdfREVOWV9XRUFLX1JORyYmVVVJRFY3X0RFTllfV0VBS19STkcpdGhyb3cgbmV3IEVycm9yKFwibm8gY3J5cHRvZ3JhcGhpY2FsbHkgc3Ryb25nIFJORyBhdmFpbGFibGVcIik7Zm9yKHZhciBpPTA7aTx0Lmxlbmd0aDtpKyspdFtpXT02NTUzNipNYXRoLnRydW5jKDY1NTM2Kk1hdGgucmFuZG9tKCkpK01hdGgudHJ1bmMoNjU1MzYqTWF0aC5yYW5kb20oKSk7cmV0dXJuIHR9O3QmJiFSKHQuY3J5cHRvKSYmY3J5cHRvLmdldFJhbmRvbVZhbHVlcyYmKERpPXQ9PmNyeXB0by5nZXRSYW5kb21WYWx1ZXModCkpO2NsYXNzIExpe2NvbnN0cnVjdG9yKCl7dGhpcy5NPW5ldyBVaW50MzJBcnJheSg4KSx0aGlzLkM9MS8wfW5leHRVaW50MzIoKXtyZXR1cm4gdGhpcy5DPj10aGlzLk0ubGVuZ3RoJiYoRGkodGhpcy5NKSx0aGlzLkM9MCksdGhpcy5NW3RoaXMuQysrXX19dmFyIE5pPSgpPT5qaSgpLnRvU3RyaW5nKCksamk9KCk9PihBaXx8KEFpPW5ldyBGaSkpLmdlbmVyYXRlKCksemk9XCJcIjt2YXIgVWk9L1thLXowLTldW2EtejAtOS1dK1xcLlthLXpdezIsfSQvaTtmdW5jdGlvbiBxaSh0LGkpe2lmKGkpe3ZhciBlPWZ1bmN0aW9uKHQsaSl7aWYodm9pZCAwPT09aSYmKGk9byksemkpcmV0dXJuIHppO2lmKCFpKXJldHVyblwiXCI7aWYoW1wibG9jYWxob3N0XCIsXCIxMjcuMC4wLjFcIl0uaW5jbHVkZXModCkpcmV0dXJuXCJcIjtmb3IodmFyIGU9dC5zcGxpdChcIi5cIikscj1NYXRoLm1pbihlLmxlbmd0aCw4KSxzPVwiZG1uX2Noa19cIitOaSgpOyF6aSYmci0tOyl7dmFyIG49ZS5zbGljZShyKS5qb2luKFwiLlwiKSxhPXMrXCI9MTtkb21haW49LlwiK24rXCI7cGF0aD0vXCI7aS5jb29raWU9YStcIjttYXgtYWdlPTNcIixpLmNvb2tpZS5pbmNsdWRlcyhzKSYmKGkuY29va2llPWErXCI7bWF4LWFnZT0wXCIsemk9bil9cmV0dXJuIHppfSh0KTtpZighZSl7dmFyIHI9KHQ9Pnt2YXIgaT10Lm1hdGNoKFVpKTtyZXR1cm4gaT9pWzBdOlwiXCJ9KSh0KTtyIT09ZSYmai5pbmZvKFwiV2FybmluZzogY29va2llIHN1YmRvbWFpbiBkaXNjb3ZlcnkgbWlzbWF0Y2hcIixyLGUpLGU9cn1yZXR1cm4gZT9cIjsgZG9tYWluPS5cIitlOlwiXCJ9cmV0dXJuXCJcIn12YXIgQmk9e086KCk9PiEhbyxGOmZ1bmN0aW9uKHQpe2ouZXJyb3IoXCJjb29raWVTdG9yZSBlcnJvcjogXCIrdCl9LEE6ZnVuY3Rpb24odCl7aWYobyl7dHJ5e2Zvcih2YXIgaT10K1wiPVwiLGU9by5jb29raWUuc3BsaXQoXCI7XCIpLmZpbHRlcigodD0+dC5sZW5ndGgpKSxyPTA7cjxlLmxlbmd0aDtyKyspe2Zvcih2YXIgcz1lW3JdO1wiIFwiPT1zLmNoYXJBdCgwKTspcz1zLnN1YnN0cmluZygxLHMubGVuZ3RoKTtpZigwPT09cy5pbmRleE9mKGkpKXJldHVybiBkZWNvZGVVUklDb21wb25lbnQocy5zdWJzdHJpbmcoaS5sZW5ndGgscy5sZW5ndGgpKX19Y2F0Y2godCl7fXJldHVybiBudWxsfX0sRDpmdW5jdGlvbih0KXt2YXIgaTt0cnl7aT1KU09OLnBhcnNlKEJpLkEodCkpfHx7fX1jYXRjaCh0KXt9cmV0dXJuIGl9LEw6ZnVuY3Rpb24odCxpLGUscixzKXtpZihvKXRyeXt2YXIgbj1cIlwiLGE9XCJcIixsPXFpKG8ubG9jYXRpb24uaG9zdG5hbWUscik7aWYoZSl7dmFyIHU9bmV3IERhdGU7dS5zZXRUaW1lKHUuZ2V0VGltZSgpKzI0KmUqNjAqNjAqMWUzKSxuPVwiOyBleHBpcmVzPVwiK3UudG9VVENTdHJpbmcoKX1zJiYoYT1cIjsgc2VjdXJlXCIpO3ZhciBoPXQrXCI9XCIrZW5jb2RlVVJJQ29tcG9uZW50KEpTT04uc3RyaW5naWZ5KGkpKStuK1wiOyBTYW1lU2l0ZT1MYXg7IHBhdGg9L1wiK2wrYTtyZXR1cm4gaC5sZW5ndGg+MzY4Ni40JiZqLndhcm4oXCJjb29raWVTdG9yZSB3YXJuaW5nOiBsYXJnZSBjb29raWUsIGxlbj1cIitoLmxlbmd0aCksby5jb29raWU9aCxofWNhdGNoKHQpe3JldHVybn19LE46ZnVuY3Rpb24odCxpKXt0cnl7QmkuTCh0LFwiXCIsLTEsaSl9Y2F0Y2godCl7cmV0dXJufX19LEhpPW51bGwsV2k9e086ZnVuY3Rpb24oKXtpZighQyhIaSkpcmV0dXJuIEhpO3ZhciBpPSEwO2lmKFIodCkpaT0hMTtlbHNlIHRyeXt2YXIgZT1cIl9fbXBsc3N1cHBvcnRfX1wiO1dpLkwoZSxcInh5elwiKSwnXCJ4eXpcIichPT1XaS5BKGUpJiYoaT0hMSksV2kuTihlKX1jYXRjaCh0KXtpPSExfXJldHVybiBpfHxqLmVycm9yKFwibG9jYWxTdG9yYWdlIHVuc3VwcG9ydGVkOyBmYWxsaW5nIGJhY2sgdG8gY29va2llIHN0b3JlXCIpLEhpPWksaX0sRjpmdW5jdGlvbih0KXtqLmVycm9yKFwibG9jYWxTdG9yYWdlIGVycm9yOiBcIit0KX0sQTpmdW5jdGlvbihpKXt0cnl7cmV0dXJuIG51bGw9PXQ/dm9pZCAwOnQubG9jYWxTdG9yYWdlLmdldEl0ZW0oaSl9Y2F0Y2godCl7V2kuRih0KX1yZXR1cm4gbnVsbH0sRDpmdW5jdGlvbih0KXt0cnl7cmV0dXJuIEpTT04ucGFyc2UoV2kuQSh0KSl8fHt9fWNhdGNoKHQpe31yZXR1cm4gbnVsbH0sTDpmdW5jdGlvbihpLGUpe3RyeXtudWxsPT10fHx0LmxvY2FsU3RvcmFnZS5zZXRJdGVtKGksSlNPTi5zdHJpbmdpZnkoZSkpfWNhdGNoKHQpe1dpLkYodCl9fSxOOmZ1bmN0aW9uKGkpe3RyeXtudWxsPT10fHx0LmxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGkpfWNhdGNoKHQpe1dpLkYodCl9fX0sR2k9W1wiZGlzdGluY3RfaWRcIiwkdCxrdCxVdCx6dF0sSmk9Qih7fSxXaSx7RDpmdW5jdGlvbih0KXt0cnl7dmFyIGk9e307dHJ5e2k9QmkuRCh0KXx8e319Y2F0Y2godCl7fXZhciBlPVYoaSxKU09OLnBhcnNlKFdpLkEodCl8fFwie31cIikpO3JldHVybiBXaS5MKHQsZSksZX1jYXRjaCh0KXt9cmV0dXJuIG51bGx9LEw6ZnVuY3Rpb24odCxpLGUscixzLG4pe3RyeXtXaS5MKHQsaSx2b2lkIDAsdm9pZCAwLG4pO3ZhciBvPXt9O0dpLmZvckVhY2goKHQ9PntpW3RdJiYob1t0XT1pW3RdKX0pKSxPYmplY3Qua2V5cyhvKS5sZW5ndGgmJkJpLkwodCxvLGUscixzLG4pfWNhdGNoKHQpe1dpLkYodCl9fSxOOmZ1bmN0aW9uKGksZSl7dHJ5e251bGw9PXR8fHQubG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oaSksQmkuTihpLGUpfWNhdGNoKHQpe1dpLkYodCl9fX0pLFZpPXt9LEtpPXtPOmZ1bmN0aW9uKCl7cmV0dXJuITB9LEY6ZnVuY3Rpb24odCl7ai5lcnJvcihcIm1lbW9yeVN0b3JhZ2UgZXJyb3I6IFwiK3QpfSxBOmZ1bmN0aW9uKHQpe3JldHVybiBWaVt0XXx8bnVsbH0sRDpmdW5jdGlvbih0KXtyZXR1cm4gVmlbdF18fG51bGx9LEw6ZnVuY3Rpb24odCxpKXtWaVt0XT1pfSxOOmZ1bmN0aW9uKHQpe2RlbGV0ZSBWaVt0XX19LFlpPW51bGwsWGk9e086ZnVuY3Rpb24oKXtpZighQyhZaSkpcmV0dXJuIFlpO2lmKFlpPSEwLFIodCkpWWk9ITE7ZWxzZSB0cnl7dmFyIGk9XCJfX3N1cHBvcnRfX1wiO1hpLkwoaSxcInh5elwiKSwnXCJ4eXpcIichPT1YaS5BKGkpJiYoWWk9ITEpLFhpLk4oaSl9Y2F0Y2godCl7WWk9ITF9cmV0dXJuIFlpfSxGOmZ1bmN0aW9uKHQpe2ouZXJyb3IoXCJzZXNzaW9uU3RvcmFnZSBlcnJvcjogXCIsdCl9LEE6ZnVuY3Rpb24oaSl7dHJ5e3JldHVybiBudWxsPT10P3ZvaWQgMDp0LnNlc3Npb25TdG9yYWdlLmdldEl0ZW0oaSl9Y2F0Y2godCl7WGkuRih0KX1yZXR1cm4gbnVsbH0sRDpmdW5jdGlvbih0KXt0cnl7cmV0dXJuIEpTT04ucGFyc2UoWGkuQSh0KSl8fG51bGx9Y2F0Y2godCl7fXJldHVybiBudWxsfSxMOmZ1bmN0aW9uKGksZSl7dHJ5e251bGw9PXR8fHQuc2Vzc2lvblN0b3JhZ2Uuc2V0SXRlbShpLEpTT04uc3RyaW5naWZ5KGUpKX1jYXRjaCh0KXtYaS5GKHQpfX0sTjpmdW5jdGlvbihpKXt0cnl7bnVsbD09dHx8dC5zZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKGkpfWNhdGNoKHQpe1hpLkYodCl9fX0sUWk9ZnVuY3Rpb24odCl7cmV0dXJuIHRbdC5QRU5ESU5HPS0xXT1cIlBFTkRJTkdcIix0W3QuREVOSUVEPTBdPVwiREVOSUVEXCIsdFt0LkdSQU5URUQ9MV09XCJHUkFOVEVEXCIsdH0oe30pO2NsYXNzIFppe2NvbnN0cnVjdG9yKHQpe3RoaXMuX2luc3RhbmNlPXR9Z2V0IFMoKXtyZXR1cm4gdGhpcy5faW5zdGFuY2UuY29uZmlnfWdldCBjb25zZW50KCl7cmV0dXJuIHRoaXMuaigpP1FpLkRFTklFRDp0aGlzLlV9aXNPcHRlZE91dCgpe3JldHVybiB0aGlzLmNvbnNlbnQ9PT1RaS5ERU5JRUR8fHRoaXMuY29uc2VudD09PVFpLlBFTkRJTkcmJnRoaXMuUy5vcHRfb3V0X2NhcHR1cmluZ19ieV9kZWZhdWx0fWlzT3B0ZWRJbigpe3JldHVybiF0aGlzLmlzT3B0ZWRPdXQoKX1vcHRJbk91dCh0KXt0aGlzLnEuTCh0aGlzLkIsdD8xOjAsdGhpcy5TLmNvb2tpZV9leHBpcmF0aW9uLHRoaXMuUy5jcm9zc19zdWJkb21haW5fY29va2llLHRoaXMuUy5zZWN1cmVfY29va2llKX1yZXNldCgpe3RoaXMucS5OKHRoaXMuQix0aGlzLlMuY3Jvc3Nfc3ViZG9tYWluX2Nvb2tpZSl9Z2V0IEIoKXt2YXJ7dG9rZW46dCxvcHRfb3V0X2NhcHR1cmluZ19jb29raWVfcHJlZml4Oml9PXRoaXMuX2luc3RhbmNlLmNvbmZpZztyZXR1cm4oaXx8XCJfX3BoX29wdF9pbl9vdXRfXCIpK3R9Z2V0IFUoKXt2YXIgdD10aGlzLnEuQSh0aGlzLkIpO3JldHVyblwiMVwiPT09dD9RaS5HUkFOVEVEOlwiMFwiPT09dD9RaS5ERU5JRUQ6UWkuUEVORElOR31nZXQgcSgpe2lmKCF0aGlzLkgpe3ZhciB0PXRoaXMuUy5vcHRfb3V0X2NhcHR1cmluZ19wZXJzaXN0ZW5jZV90eXBlO3RoaXMuSD1cImxvY2FsU3RvcmFnZVwiPT09dD9XaTpCaTt2YXIgaT1cImxvY2FsU3RvcmFnZVwiPT09dD9CaTpXaTtpLkEodGhpcy5CKSYmKHRoaXMuSC5BKHRoaXMuQil8fHRoaXMub3B0SW5PdXQoXCIxXCI9PT1pLkEodGhpcy5CKSksaS5OKHRoaXMuQix0aGlzLlMuY3Jvc3Nfc3ViZG9tYWluX2Nvb2tpZSkpfXJldHVybiB0aGlzLkh9aigpe3JldHVybiEhdGhpcy5TLnJlc3BlY3RfZG50JiYhIXJ0KFtudWxsPT1uP3ZvaWQgMDpuLmRvTm90VHJhY2ssbnVsbD09bj92b2lkIDA6bi5tc0RvTm90VHJhY2ssdi5kb05vdFRyYWNrXSwodD0+bShbITAsMSxcIjFcIixcInllc1wiXSx0KSkpfX12YXIgdGU9eihcIltEZWFkIENsaWNrc11cIiksaWU9KCk9PiEwLGVlPXQ9Pnt2YXIgaSxlPSEobnVsbD09KGk9dC5pbnN0YW5jZS5wZXJzaXN0ZW5jZSl8fCFpLmdldF9wcm9wZXJ0eShjdCkpLHI9dC5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9kZWFkX2NsaWNrcztyZXR1cm4gQShyKT9yOmV9O2NsYXNzIHJle2dldCBsYXp5TG9hZGVkRGVhZENsaWNrc0F1dG9jYXB0dXJlKCl7cmV0dXJuIHRoaXMuV31jb25zdHJ1Y3Rvcih0LGksZSl7dGhpcy5pbnN0YW5jZT10LHRoaXMuaXNFbmFibGVkPWksdGhpcy5vbkNhcHR1cmU9ZSx0aGlzLnN0YXJ0SWZFbmFibGVkKCl9b25SZW1vdGVDb25maWcodCl7dGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZSYmdGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZS5yZWdpc3Rlcih7W2N0XTpudWxsPT10P3ZvaWQgMDp0LmNhcHR1cmVEZWFkQ2xpY2tzfSksdGhpcy5zdGFydElmRW5hYmxlZCgpfXN0YXJ0SWZFbmFibGVkKCl7dGhpcy5pc0VuYWJsZWQodGhpcykmJnRoaXMuRygoKCk9Pnt0aGlzLkooKX0pKX1HKHQpe3ZhciBpLGU7bnVsbCE9KGk9di5fX1Bvc3Rob2dFeHRlbnNpb25zX18pJiZpLmluaXREZWFkQ2xpY2tzQXV0b2NhcHR1cmUmJnQoKSxudWxsPT0oZT12Ll9fUG9zdGhvZ0V4dGVuc2lvbnNfXyl8fG51bGw9PWUubG9hZEV4dGVybmFsRGVwZW5kZW5jeXx8ZS5sb2FkRXh0ZXJuYWxEZXBlbmRlbmN5KHRoaXMuaW5zdGFuY2UsXCJkZWFkLWNsaWNrcy1hdXRvY2FwdHVyZVwiLChpPT57aT90ZS5lcnJvcihcImZhaWxlZCB0byBsb2FkIHNjcmlwdFwiLGkpOnQoKX0pKX1KKCl7dmFyIHQ7aWYobyl7aWYoIXRoaXMuVyYmbnVsbCE9KHQ9di5fX1Bvc3Rob2dFeHRlbnNpb25zX18pJiZ0LmluaXREZWFkQ2xpY2tzQXV0b2NhcHR1cmUpe3ZhciBpPUkodGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9kZWFkX2NsaWNrcyk/dGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9kZWFkX2NsaWNrczp7fTtpLl9fb25DYXB0dXJlPXRoaXMub25DYXB0dXJlLHRoaXMuVz12Ll9fUG9zdGhvZ0V4dGVuc2lvbnNfXy5pbml0RGVhZENsaWNrc0F1dG9jYXB0dXJlKHRoaXMuaW5zdGFuY2UsaSksdGhpcy5XLnN0YXJ0KG8pLHRlLmluZm8oXCJzdGFydGluZy4uLlwiKX19ZWxzZSB0ZS5lcnJvcihcImBkb2N1bWVudGAgbm90IGZvdW5kLiBDYW5ub3Qgc3RhcnQuXCIpfXN0b3AoKXt0aGlzLlcmJih0aGlzLlcuc3RvcCgpLHRoaXMuVz12b2lkIDAsdGUuaW5mbyhcInN0b3BwaW5nLi4uXCIpKX19dmFyIHNlPXooXCJbRXhjZXB0aW9uQXV0b2NhcHR1cmVdXCIpO2NsYXNzIG5le2NvbnN0cnVjdG9yKGkpe3ZhciBlO3RoaXMuVj0oKT0+e3ZhciBpO2lmKHQmJnRoaXMuaXNFbmFibGVkJiZudWxsIT0oaT12Ll9fUG9zdGhvZ0V4dGVuc2lvbnNfXykmJmkuZXJyb3JXcmFwcGluZ0Z1bmN0aW9ucyl7dmFyIGU9di5fX1Bvc3Rob2dFeHRlbnNpb25zX18uZXJyb3JXcmFwcGluZ0Z1bmN0aW9ucy53cmFwT25FcnJvcixyPXYuX19Qb3N0aG9nRXh0ZW5zaW9uc19fLmVycm9yV3JhcHBpbmdGdW5jdGlvbnMud3JhcFVuaGFuZGxlZFJlamVjdGlvbixzPXYuX19Qb3N0aG9nRXh0ZW5zaW9uc19fLmVycm9yV3JhcHBpbmdGdW5jdGlvbnMud3JhcENvbnNvbGVFcnJvcjt0cnl7IXRoaXMuSyYmdGhpcy5TLmNhcHR1cmVfdW5oYW5kbGVkX2Vycm9ycyYmKHRoaXMuSz1lKHRoaXMuY2FwdHVyZUV4Y2VwdGlvbi5iaW5kKHRoaXMpKSksIXRoaXMuWSYmdGhpcy5TLmNhcHR1cmVfdW5oYW5kbGVkX3JlamVjdGlvbnMmJih0aGlzLlk9cih0aGlzLmNhcHR1cmVFeGNlcHRpb24uYmluZCh0aGlzKSkpLCF0aGlzLlgmJnRoaXMuUy5jYXB0dXJlX2NvbnNvbGVfZXJyb3JzJiYodGhpcy5YPXModGhpcy5jYXB0dXJlRXhjZXB0aW9uLmJpbmQodGhpcykpKX1jYXRjaCh0KXtzZS5lcnJvcihcImZhaWxlZCB0byBzdGFydFwiLHQpLHRoaXMuWigpfX19LHRoaXMuX2luc3RhbmNlPWksdGhpcy50dD0hKG51bGw9PShlPXRoaXMuX2luc3RhbmNlLnBlcnNpc3RlbmNlKXx8IWUucHJvcHNbaHRdKSx0aGlzLlM9dGhpcy5pdCgpLHRoaXMuc3RhcnRJZkVuYWJsZWQoKX1pdCgpe3ZhciB0PXRoaXMuX2luc3RhbmNlLmNvbmZpZy5jYXB0dXJlX2V4Y2VwdGlvbnMsaT17Y2FwdHVyZV91bmhhbmRsZWRfZXJyb3JzOiExLGNhcHR1cmVfdW5oYW5kbGVkX3JlamVjdGlvbnM6ITEsY2FwdHVyZV9jb25zb2xlX2Vycm9yczohMX07cmV0dXJuIEkodCk/aT1CKHt9LGksdCk6KFIodCk/dGhpcy50dDp0KSYmKGk9Qih7fSxpLHtjYXB0dXJlX3VuaGFuZGxlZF9lcnJvcnM6ITAsY2FwdHVyZV91bmhhbmRsZWRfcmVqZWN0aW9uczohMH0pKSxpfWdldCBpc0VuYWJsZWQoKXtyZXR1cm4gdGhpcy5TLmNhcHR1cmVfY29uc29sZV9lcnJvcnN8fHRoaXMuUy5jYXB0dXJlX3VuaGFuZGxlZF9lcnJvcnN8fHRoaXMuUy5jYXB0dXJlX3VuaGFuZGxlZF9yZWplY3Rpb25zfXN0YXJ0SWZFbmFibGVkKCl7dGhpcy5pc0VuYWJsZWQmJihzZS5pbmZvKFwiZW5hYmxlZFwiKSx0aGlzLkcodGhpcy5WKSl9Ryh0KXt2YXIgaSxlO251bGwhPShpPXYuX19Qb3N0aG9nRXh0ZW5zaW9uc19fKSYmaS5lcnJvcldyYXBwaW5nRnVuY3Rpb25zJiZ0KCksbnVsbD09KGU9di5fX1Bvc3Rob2dFeHRlbnNpb25zX18pfHxudWxsPT1lLmxvYWRFeHRlcm5hbERlcGVuZGVuY3l8fGUubG9hZEV4dGVybmFsRGVwZW5kZW5jeSh0aGlzLl9pbnN0YW5jZSxcImV4Y2VwdGlvbi1hdXRvY2FwdHVyZVwiLChpPT57aWYoaSlyZXR1cm4gc2UuZXJyb3IoXCJmYWlsZWQgdG8gbG9hZCBzY3JpcHRcIixpKTt0KCl9KSl9Wigpe3ZhciB0LGksZTtudWxsPT0odD10aGlzLkspfHx0LmNhbGwodGhpcyksdGhpcy5LPXZvaWQgMCxudWxsPT0oaT10aGlzLlkpfHxpLmNhbGwodGhpcyksdGhpcy5ZPXZvaWQgMCxudWxsPT0oZT10aGlzLlgpfHxlLmNhbGwodGhpcyksdGhpcy5YPXZvaWQgMH1vblJlbW90ZUNvbmZpZyh0KXt2YXIgaT10LmF1dG9jYXB0dXJlRXhjZXB0aW9uczt0aGlzLnR0PSEhaXx8ITEsdGhpcy5TPXRoaXMuaXQoKSx0aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZSYmdGhpcy5faW5zdGFuY2UucGVyc2lzdGVuY2UucmVnaXN0ZXIoe1todF06dGhpcy50dH0pLHRoaXMuc3RhcnRJZkVuYWJsZWQoKX1jYXB0dXJlRXhjZXB0aW9uKHQpe3ZhciBpPXRoaXMuX2luc3RhbmNlLnJlcXVlc3RSb3V0ZXIuZW5kcG9pbnRGb3IoXCJ1aVwiKTt0LiRleGNlcHRpb25fcGVyc29uVVJMPWkrXCIvcHJvamVjdC9cIit0aGlzLl9pbnN0YW5jZS5jb25maWcudG9rZW4rXCIvcGVyc29uL1wiK3RoaXMuX2luc3RhbmNlLmdldF9kaXN0aW5jdF9pZCgpLHRoaXMuX2luc3RhbmNlLmV4Y2VwdGlvbnMuc2VuZEV4Y2VwdGlvbkV2ZW50KHQpfX1mdW5jdGlvbiBvZSh0KXtyZXR1cm4hUihFdmVudCkmJmFlKHQsRXZlbnQpfWZ1bmN0aW9uIGFlKHQsaSl7dHJ5e3JldHVybiB0IGluc3RhbmNlb2YgaX1jYXRjaCh0KXtyZXR1cm4hMX19ZnVuY3Rpb24gbGUodCl7c3dpdGNoKE9iamVjdC5wcm90b3R5cGUudG9TdHJpbmcuY2FsbCh0KSl7Y2FzZVwiW29iamVjdCBFcnJvcl1cIjpjYXNlXCJbb2JqZWN0IEV4Y2VwdGlvbl1cIjpjYXNlXCJbb2JqZWN0IERPTUV4Y2VwdGlvbl1cIjpjYXNlXCJbb2JqZWN0IERPTUVycm9yXVwiOnJldHVybiEwO2RlZmF1bHQ6cmV0dXJuIGFlKHQsRXJyb3IpfX1mdW5jdGlvbiB1ZSh0LGkpe3JldHVybiBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwodCk9PT1cIltvYmplY3QgXCIraStcIl1cIn1mdW5jdGlvbiBoZSh0KXtyZXR1cm4gdWUodCxcIkRPTUVycm9yXCIpfXZhciBkZT0vXFwoZXJyb3I6ICguKilcXCkvLHZlPTUwLGNlPVwiP1wiO2Z1bmN0aW9uIGZlKHQsaSxlLHIpe3ZhciBzPXtwbGF0Zm9ybTpcIndlYjpqYXZhc2NyaXB0XCIsZmlsZW5hbWU6dCxmdW5jdGlvbjpcIjxhbm9ueW1vdXM+XCI9PT1pP2NlOmksaW5fYXBwOiEwfTtyZXR1cm4gUihlKXx8KHMubGluZW5vPWUpLFIocil8fChzLmNvbG5vPXIpLHN9dmFyIHBlPS9eXFxzKmF0IChcXFMrPykoPzo6KFxcZCspKSg/OjooXFxkKykpXFxzKiQvaSxnZT0vXlxccyphdCAoPzooLis/XFwpKD86IFxcWy4rXFxdKT98Lio/KSA/XFwoKD86YWRkcmVzcyBhdCApPyk/KD86YXN5bmMgKT8oKD86PGFub255bW91cz58Wy1hLXpdKzp8LipidW5kbGV8XFwvKT8uKj8pKD86OihcXGQrKSk/KD86OihcXGQrKSk/XFwpP1xccyokL2ksX2U9L1xcKChcXFMqKSg/OjooXFxkKykpKD86OihcXGQrKSlcXCkvLG1lPS9eXFxzKiguKj8pKD86XFwoKC4qPylcXCkpPyg/Ol58QCk/KCg/OlstYS16XSspPzpcXC8uKj98XFxbbmF0aXZlIGNvZGVcXF18W15AXSooPzpidW5kbGV8XFxkK1xcLmpzKXxcXC9bXFx3XFwtLiAvPV0rKSg/OjooXFxkKykpPyg/OjooXFxkKykpP1xccyokL2ksYmU9LyhcXFMrKSBsaW5lIChcXGQrKSg/OiA+IGV2YWwgbGluZSBcXGQrKSogPiBldmFsL2ksd2U9ZnVuY3Rpb24oKXtmb3IodmFyIHQ9YXJndW1lbnRzLmxlbmd0aCxpPW5ldyBBcnJheSh0KSxlPTA7ZTx0O2UrKylpW2VdPWFyZ3VtZW50c1tlXTt2YXIgcj1pLnNvcnQoKCh0LGkpPT50WzBdLWlbMF0pKS5tYXAoKHQ9PnRbMV0pKTtyZXR1cm4gZnVuY3Rpb24odCxpKXt2b2lkIDA9PT1pJiYoaT0wKTtmb3IodmFyIGU9W10scz10LnNwbGl0KFwiXFxuXCIpLG49aTtuPHMubGVuZ3RoO24rKyl7dmFyIG89c1tuXTtpZighKG8ubGVuZ3RoPjEwMjQpKXt2YXIgYT1kZS50ZXN0KG8pP28ucmVwbGFjZShkZSxcIiQxXCIpOm87aWYoIWEubWF0Y2goL1xcUypFcnJvcjogLykpe2Zvcih2YXIgbCBvZiByKXt2YXIgdT1sKGEpO2lmKHUpe2UucHVzaCh1KTticmVha319aWYoZS5sZW5ndGg+PXZlKWJyZWFrfX19cmV0dXJuIGZ1bmN0aW9uKHQpe2lmKCF0Lmxlbmd0aClyZXR1cm5bXTt2YXIgaT1BcnJheS5mcm9tKHQpO3JldHVybiBpLnJldmVyc2UoKSxpLnNsaWNlKDAsdmUpLm1hcCgodD0+Qih7fSx0LHtmaWxlbmFtZTp0LmZpbGVuYW1lfHx5ZShpKS5maWxlbmFtZSxmdW5jdGlvbjp0LmZ1bmN0aW9ufHxjZX0pKSl9KGUpfX0oLi4uW1szMCx0PT57dmFyIGk9cGUuZXhlYyh0KTtpZihpKXt2YXJbLGUscixzXT1pO3JldHVybiBmZShlLGNlLCtyLCtzKX12YXIgbj1nZS5leGVjKHQpO2lmKG4pe2lmKG5bMl0mJjA9PT1uWzJdLmluZGV4T2YoXCJldmFsXCIpKXt2YXIgbz1fZS5leGVjKG5bMl0pO28mJihuWzJdPW9bMV0sblszXT1vWzJdLG5bNF09b1szXSl9dmFyW2EsbF09eGUoblsxXXx8Y2UsblsyXSk7cmV0dXJuIGZlKGwsYSxuWzNdPytuWzNdOnZvaWQgMCxuWzRdPytuWzRdOnZvaWQgMCl9fV0sWzUwLHQ9Pnt2YXIgaT1tZS5leGVjKHQpO2lmKGkpe2lmKGlbM10mJmlbM10uaW5kZXhPZihcIiA+IGV2YWxcIik+LTEpe3ZhciBlPWJlLmV4ZWMoaVszXSk7ZSYmKGlbMV09aVsxXXx8XCJldmFsXCIsaVszXT1lWzFdLGlbNF09ZVsyXSxpWzVdPVwiXCIpfXZhciByPWlbM10scz1pWzFdfHxjZTtyZXR1cm5bcyxyXT14ZShzLHIpLGZlKHIscyxpWzRdPytpWzRdOnZvaWQgMCxpWzVdPytpWzVdOnZvaWQgMCl9fV1dKTtmdW5jdGlvbiB5ZSh0KXtyZXR1cm4gdFt0Lmxlbmd0aC0xXXx8e319dmFyIFNlLCRlLGtlLHhlPSh0LGkpPT57dmFyIGU9LTEhPT10LmluZGV4T2YoXCJzYWZhcmktZXh0ZW5zaW9uXCIpLHI9LTEhPT10LmluZGV4T2YoXCJzYWZhcmktd2ViLWV4dGVuc2lvblwiKTtyZXR1cm4gZXx8cj9bLTEhPT10LmluZGV4T2YoXCJAXCIpP3Quc3BsaXQoXCJAXCIpWzBdOmNlLGU/XCJzYWZhcmktZXh0ZW5zaW9uOlwiK2k6XCJzYWZhcmktd2ViLWV4dGVuc2lvbjpcIitpXTpbdCxpXX07dmFyIEVlPS9eKD86W1V1XW5jYXVnaHQgKD86ZXhjZXB0aW9uOiApPyk/KD86KCg/OkV2YWx8SW50ZXJuYWx8UmFuZ2V8UmVmZXJlbmNlfFN5bnRheHxUeXBlfFVSSXwpRXJyb3IpOiApPyguKikkL2k7ZnVuY3Rpb24gSWUodCxpKXt2b2lkIDA9PT1pJiYoaT0wKTt2YXIgZT10LnN0YWNrdHJhY2V8fHQuc3RhY2t8fFwiXCIscj1mdW5jdGlvbih0KXtpZih0JiZQZS50ZXN0KHQubWVzc2FnZSkpcmV0dXJuIDE7cmV0dXJuIDB9KHQpO3RyeXt2YXIgcz13ZSxuPWZ1bmN0aW9uKHQsaSl7dmFyIGU9ZnVuY3Rpb24odCl7dmFyIGk9Z2xvYmFsVGhpcy5fcG9zdGhvZ0NodW5rSWRzO2lmKCFpKXJldHVybnt9O3ZhciBlPU9iamVjdC5rZXlzKGkpO3JldHVybiBrZSYmZS5sZW5ndGg9PT0kZXx8KCRlPWUubGVuZ3RoLGtlPWUucmVkdWNlKCgoZSxyKT0+e1NlfHwoU2U9e30pO3ZhciBzPVNlW3JdO2lmKHMpZVtzWzBdXT1zWzFdO2Vsc2UgZm9yKHZhciBuPXQociksbz1uLmxlbmd0aC0xO28+PTA7by0tKXt2YXIgYT1uW29dLGw9bnVsbD09YT92b2lkIDA6YS5maWxlbmFtZSx1PWlbcl07aWYobCYmdSl7ZVtsXT11LFNlW3JdPVtsLHVdO2JyZWFrfX1yZXR1cm4gZX0pLHt9KSksa2V9KGkpO3JldHVybiB0LmZvckVhY2goKHQ9Pnt0LmZpbGVuYW1lJiYodC5jaHVua19pZD1lW3QuZmlsZW5hbWVdKX0pKSx0fShzKGUscikscyk7cmV0dXJuIG4uc2xpY2UoMCxuLmxlbmd0aC1pKX1jYXRjaCh0KXt9cmV0dXJuW119dmFyIFBlPS9NaW5pZmllZCBSZWFjdCBlcnJvciAjXFxkKzsvaTtmdW5jdGlvbiBSZSh0LGkpe3ZhciBlLHIscz1JZSh0KSxuPW51bGw9PT0oZT1udWxsPT1pP3ZvaWQgMDppLmhhbmRsZWQpfHx2b2lkIDA9PT1lfHxlLG89bnVsbCE9PShyPW51bGw9PWk/dm9pZCAwOmkuc3ludGhldGljKSYmdm9pZCAwIT09ciYmcjtyZXR1cm57dHlwZTpudWxsIT1pJiZpLm92ZXJyaWRlRXhjZXB0aW9uVHlwZT9pLm92ZXJyaWRlRXhjZXB0aW9uVHlwZTp0Lm5hbWUsdmFsdWU6ZnVuY3Rpb24odCl7dmFyIGk9dC5tZXNzYWdlO2lmKGkuZXJyb3ImJlwic3RyaW5nXCI9PXR5cGVvZiBpLmVycm9yLm1lc3NhZ2UpcmV0dXJuIFN0cmluZyhpLmVycm9yLm1lc3NhZ2UpO3JldHVybiBTdHJpbmcoaSl9KHQpLHN0YWNrdHJhY2U6e2ZyYW1lczpzLHR5cGU6XCJyYXdcIn0sbWVjaGFuaXNtOntoYW5kbGVkOm4sc3ludGhldGljOm99fX1mdW5jdGlvbiBUZSh0LGkpe3ZhciBlPVJlKHQsaSk7cmV0dXJuIHQuY2F1c2UmJmxlKHQuY2F1c2UpJiZ0LmNhdXNlIT09dD9bZSwuLi5UZSh0LmNhdXNlLHtoYW5kbGVkOm51bGw9PWk/dm9pZCAwOmkuaGFuZGxlZCxzeW50aGV0aWM6bnVsbD09aT92b2lkIDA6aS5zeW50aGV0aWN9KV06W2VdfWZ1bmN0aW9uIE1lKHQsaSl7cmV0dXJueyRleGNlcHRpb25fbGlzdDpUZSh0LGkpLCRleGNlcHRpb25fbGV2ZWw6XCJlcnJvclwifX1mdW5jdGlvbiBDZSh0LGkpe3ZhciBlLHIscyxuPW51bGw9PT0oZT1udWxsPT1pP3ZvaWQgMDppLmhhbmRsZWQpfHx2b2lkIDA9PT1lfHxlLG89bnVsbD09PShyPW51bGw9PWk/dm9pZCAwOmkuc3ludGhldGljKXx8dm9pZCAwPT09cnx8cixhPXt0eXBlOm51bGwhPWkmJmkub3ZlcnJpZGVFeGNlcHRpb25UeXBlP2kub3ZlcnJpZGVFeGNlcHRpb25UeXBlOm51bGwhPT0ocz1udWxsPT1pP3ZvaWQgMDppLmRlZmF1bHRFeGNlcHRpb25UeXBlKSYmdm9pZCAwIT09cz9zOlwiRXJyb3JcIix2YWx1ZTp0fHwobnVsbD09aT92b2lkIDA6aS5kZWZhdWx0RXhjZXB0aW9uTWVzc2FnZSksbWVjaGFuaXNtOntoYW5kbGVkOm4sc3ludGhldGljOm99fTtpZihudWxsIT1pJiZpLnN5bnRoZXRpY0V4Y2VwdGlvbil7dmFyIGw9SWUoaS5zeW50aGV0aWNFeGNlcHRpb24sMSk7bC5sZW5ndGgmJihhLnN0YWNrdHJhY2U9e2ZyYW1lczpsLHR5cGU6XCJyYXdcIn0pfXJldHVybnskZXhjZXB0aW9uX2xpc3Q6W2FdLCRleGNlcHRpb25fbGV2ZWw6XCJlcnJvclwifX1mdW5jdGlvbiBPZSh0KXtyZXR1cm4gVCh0KSYmIU0odCkmJl8uaW5kZXhPZih0KT49MH1mdW5jdGlvbiBGZSh0LGkpe3ZhciBlLHIscz1udWxsPT09KGU9bnVsbD09aT92b2lkIDA6aS5oYW5kbGVkKXx8dm9pZCAwPT09ZXx8ZSxuPW51bGw9PT0ocj1udWxsPT1pP3ZvaWQgMDppLnN5bnRoZXRpYyl8fHZvaWQgMD09PXJ8fHIsbz1udWxsIT1pJiZpLm92ZXJyaWRlRXhjZXB0aW9uVHlwZT9pLm92ZXJyaWRlRXhjZXB0aW9uVHlwZTpvZSh0KT90LmNvbnN0cnVjdG9yLm5hbWU6XCJFcnJvclwiLGE9XCJOb24tRXJyb3IgJ2V4Y2VwdGlvbicgY2FwdHVyZWQgd2l0aCBrZXlzOiBcIitmdW5jdGlvbih0LGkpe3ZvaWQgMD09PWkmJihpPTQwKTt2YXIgZT1PYmplY3Qua2V5cyh0KTtpZihlLnNvcnQoKSwhZS5sZW5ndGgpcmV0dXJuXCJbb2JqZWN0IGhhcyBubyBrZXlzXVwiO2Zvcih2YXIgcj1lLmxlbmd0aDtyPjA7ci0tKXt2YXIgcz1lLnNsaWNlKDAscikuam9pbihcIiwgXCIpO2lmKCEocy5sZW5ndGg+aSkpcmV0dXJuIHI9PT1lLmxlbmd0aHx8cy5sZW5ndGg8PWk/czpzLnNsaWNlKDAsaSkrXCIuLi5cIn1yZXR1cm5cIlwifSh0KSxsPXt0eXBlOm8sdmFsdWU6YSxtZWNoYW5pc206e2hhbmRsZWQ6cyxzeW50aGV0aWM6bn19O2lmKG51bGwhPWkmJmkuc3ludGhldGljRXhjZXB0aW9uKXt2YXIgdT1JZShudWxsPT1pP3ZvaWQgMDppLnN5bnRoZXRpY0V4Y2VwdGlvbiwxKTt1Lmxlbmd0aCYmKGwuc3RhY2t0cmFjZT17ZnJhbWVzOnUsdHlwZTpcInJhd1wifSl9cmV0dXJueyRleGNlcHRpb25fbGlzdDpbbF0sJGV4Y2VwdGlvbl9sZXZlbDpPZSh0LmxldmVsKT90LmxldmVsOlwiZXJyb3JcIn19ZnVuY3Rpb24gQWUodCxpKXt2YXJ7ZXJyb3I6ZSxldmVudDpyfT10LHM9eyRleGNlcHRpb25fbGlzdDpbXX0sbj1lfHxyO2lmKGhlKG4pfHxmdW5jdGlvbih0KXtyZXR1cm4gdWUodCxcIkRPTUV4Y2VwdGlvblwiKX0obikpe3ZhciBvPW47aWYoZnVuY3Rpb24odCl7cmV0dXJuXCJzdGFja1wiaW4gdH0obikpcz1NZShuLGkpO2Vsc2V7dmFyIGE9by5uYW1lfHwoaGUobyk/XCJET01FcnJvclwiOlwiRE9NRXhjZXB0aW9uXCIpLGw9by5tZXNzYWdlP2ErXCI6IFwiK28ubWVzc2FnZTphO3M9Q2UobCxCKHt9LGkse292ZXJyaWRlRXhjZXB0aW9uVHlwZTpoZShvKT9cIkRPTUVycm9yXCI6XCJET01FeGNlcHRpb25cIixkZWZhdWx0RXhjZXB0aW9uTWVzc2FnZTpsfSkpfXJldHVyblwiY29kZVwiaW4gbyYmKHMuJGV4Y2VwdGlvbl9ET01FeGNlcHRpb25fY29kZT1cIlwiK28uY29kZSksc31pZihmdW5jdGlvbih0KXtyZXR1cm4gdWUodCxcIkVycm9yRXZlbnRcIil9KG4pJiZuLmVycm9yKXJldHVybiBNZShuLmVycm9yLGkpO2lmKGxlKG4pKXJldHVybiBNZShuLGkpO2lmKGZ1bmN0aW9uKHQpe3JldHVybiB1ZSh0LFwiT2JqZWN0XCIpfShuKXx8b2UobikpcmV0dXJuIEZlKG4saSk7aWYoUihlKSYmVChyKSl7dmFyIHU9XCJFcnJvclwiLGg9cixkPXIubWF0Y2goRWUpO3JldHVybiBkJiYodT1kWzFdLGg9ZFsyXSksQ2UoaCxCKHt9LGkse292ZXJyaWRlRXhjZXB0aW9uVHlwZTp1LGRlZmF1bHRFeGNlcHRpb25NZXNzYWdlOmh9KSl9cmV0dXJuIENlKG4saSl9ZnVuY3Rpb24gRGUodCxpLGUpe3RyeXtpZighKGkgaW4gdCkpcmV0dXJuKCk9Pnt9O3ZhciByPXRbaV0scz1lKHIpO3JldHVybiBFKHMpJiYocy5wcm90b3R5cGU9cy5wcm90b3R5cGV8fHt9LE9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKHMse19fcG9zdGhvZ193cmFwcGVkX186e2VudW1lcmFibGU6ITEsdmFsdWU6ITB9fSkpLHRbaV09cywoKT0+e3RbaV09cn19Y2F0Y2godCl7cmV0dXJuKCk9Pnt9fX1jbGFzcyBMZXtjb25zdHJ1Y3RvcihpKXt2YXIgZTt0aGlzLl9pbnN0YW5jZT1pLHRoaXMuZXQ9KG51bGw9PXR8fG51bGw9PShlPXQubG9jYXRpb24pP3ZvaWQgMDplLnBhdGhuYW1lKXx8XCJcIn1nZXQgaXNFbmFibGVkKCl7cmV0dXJuXCJoaXN0b3J5X2NoYW5nZVwiPT09dGhpcy5faW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfcGFnZXZpZXd9c3RhcnRJZkVuYWJsZWQoKXt0aGlzLmlzRW5hYmxlZCYmKGouaW5mbyhcIkhpc3RvcnkgQVBJIG1vbml0b3JpbmcgZW5hYmxlZCwgc3RhcnRpbmcuLi5cIiksdGhpcy5tb25pdG9ySGlzdG9yeUNoYW5nZXMoKSl9c3RvcCgpe3RoaXMucnQmJnRoaXMucnQoKSx0aGlzLnJ0PXZvaWQgMCxqLmluZm8oXCJIaXN0b3J5IEFQSSBtb25pdG9yaW5nIHN0b3BwZWRcIil9bW9uaXRvckhpc3RvcnlDaGFuZ2VzKCl7dmFyIGksZTtpZih0JiZ0Lmhpc3Rvcnkpe3ZhciByPXRoaXM7bnVsbCE9KGk9dC5oaXN0b3J5LnB1c2hTdGF0ZSkmJmkuX19wb3N0aG9nX3dyYXBwZWRfX3x8RGUodC5oaXN0b3J5LFwicHVzaFN0YXRlXCIsKHQ9PmZ1bmN0aW9uKGksZSxzKXt0LmNhbGwodGhpcyxpLGUscyksci5zdChcInB1c2hTdGF0ZVwiKX0pKSxudWxsIT0oZT10Lmhpc3RvcnkucmVwbGFjZVN0YXRlKSYmZS5fX3Bvc3Rob2dfd3JhcHBlZF9ffHxEZSh0Lmhpc3RvcnksXCJyZXBsYWNlU3RhdGVcIiwodD0+ZnVuY3Rpb24oaSxlLHMpe3QuY2FsbCh0aGlzLGksZSxzKSxyLnN0KFwicmVwbGFjZVN0YXRlXCIpfSkpLHRoaXMubnQoKX19c3QoaSl7dHJ5e3ZhciBlLHI9bnVsbD09dHx8bnVsbD09KGU9dC5sb2NhdGlvbik/dm9pZCAwOmUucGF0aG5hbWU7aWYoIXIpcmV0dXJuO3IhPT10aGlzLmV0JiZ0aGlzLmlzRW5hYmxlZCYmdGhpcy5faW5zdGFuY2UuY2FwdHVyZShcIiRwYWdldmlld1wiLHtuYXZpZ2F0aW9uX3R5cGU6aX0pLHRoaXMuZXQ9cn1jYXRjaCh0KXtqLmVycm9yKFwiRXJyb3IgY2FwdHVyaW5nIFwiK2krXCIgcGFnZXZpZXdcIix0KX19bnQoKXtpZighdGhpcy5ydCl7dmFyIGk9KCk9Pnt0aGlzLnN0KFwicG9wc3RhdGVcIil9O3N0KHQsXCJwb3BzdGF0ZVwiLGkpLHRoaXMucnQ9KCk9Pnt0JiZ0LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJwb3BzdGF0ZVwiLGkpfX19fWZ1bmN0aW9uIE5lKHQpe3ZhciBpLGU7cmV0dXJuKG51bGw9PShpPUpTT04uc3RyaW5naWZ5KHQsKGU9W10sZnVuY3Rpb24odCxpKXtpZihJKGkpKXtmb3IoO2UubGVuZ3RoPjAmJmVbZS5sZW5ndGgtMV0hPT10aGlzOyllLnBvcCgpO3JldHVybiBlLmluY2x1ZGVzKGkpP1wiW0NpcmN1bGFyXVwiOihlLnB1c2goaSksaSl9cmV0dXJuIGl9KSkpP3ZvaWQgMDppLmxlbmd0aCl8fDB9ZnVuY3Rpb24gamUodCxpKXtpZih2b2lkIDA9PT1pJiYoaT02NjA2MDI4LjgpLHQuc2l6ZT49aSYmdC5kYXRhLmxlbmd0aD4xKXt2YXIgZT1NYXRoLmZsb29yKHQuZGF0YS5sZW5ndGgvMikscj10LmRhdGEuc2xpY2UoMCxlKSxzPXQuZGF0YS5zbGljZShlKTtyZXR1cm5bamUoe3NpemU6TmUociksZGF0YTpyLHNlc3Npb25JZDp0LnNlc3Npb25JZCx3aW5kb3dJZDp0LndpbmRvd0lkfSksamUoe3NpemU6TmUocyksZGF0YTpzLHNlc3Npb25JZDp0LnNlc3Npb25JZCx3aW5kb3dJZDp0LndpbmRvd0lkfSldLmZsYXRNYXAoKHQ9PnQpKX1yZXR1cm5bdF19dmFyIHplPSh0PT4odFt0LkRvbUNvbnRlbnRMb2FkZWQ9MF09XCJEb21Db250ZW50TG9hZGVkXCIsdFt0LkxvYWQ9MV09XCJMb2FkXCIsdFt0LkZ1bGxTbmFwc2hvdD0yXT1cIkZ1bGxTbmFwc2hvdFwiLHRbdC5JbmNyZW1lbnRhbFNuYXBzaG90PTNdPVwiSW5jcmVtZW50YWxTbmFwc2hvdFwiLHRbdC5NZXRhPTRdPVwiTWV0YVwiLHRbdC5DdXN0b209NV09XCJDdXN0b21cIix0W3QuUGx1Z2luPTZdPVwiUGx1Z2luXCIsdCkpKHplfHx7fSksVWU9KHQ9Pih0W3QuTXV0YXRpb249MF09XCJNdXRhdGlvblwiLHRbdC5Nb3VzZU1vdmU9MV09XCJNb3VzZU1vdmVcIix0W3QuTW91c2VJbnRlcmFjdGlvbj0yXT1cIk1vdXNlSW50ZXJhY3Rpb25cIix0W3QuU2Nyb2xsPTNdPVwiU2Nyb2xsXCIsdFt0LlZpZXdwb3J0UmVzaXplPTRdPVwiVmlld3BvcnRSZXNpemVcIix0W3QuSW5wdXQ9NV09XCJJbnB1dFwiLHRbdC5Ub3VjaE1vdmU9Nl09XCJUb3VjaE1vdmVcIix0W3QuTWVkaWFJbnRlcmFjdGlvbj03XT1cIk1lZGlhSW50ZXJhY3Rpb25cIix0W3QuU3R5bGVTaGVldFJ1bGU9OF09XCJTdHlsZVNoZWV0UnVsZVwiLHRbdC5DYW52YXNNdXRhdGlvbj05XT1cIkNhbnZhc011dGF0aW9uXCIsdFt0LkZvbnQ9MTBdPVwiRm9udFwiLHRbdC5Mb2c9MTFdPVwiTG9nXCIsdFt0LkRyYWc9MTJdPVwiRHJhZ1wiLHRbdC5TdHlsZURlY2xhcmF0aW9uPTEzXT1cIlN0eWxlRGVjbGFyYXRpb25cIix0W3QuU2VsZWN0aW9uPTE0XT1cIlNlbGVjdGlvblwiLHRbdC5BZG9wdGVkU3R5bGVTaGVldD0xNV09XCJBZG9wdGVkU3R5bGVTaGVldFwiLHRbdC5DdXN0b21FbGVtZW50PTE2XT1cIkN1c3RvbUVsZW1lbnRcIix0KSkoVWV8fHt9KSxxZT1cIltTZXNzaW9uUmVjb3JkaW5nXVwiLEJlPVwicmVkYWN0ZWRcIixIZT17aW5pdGlhdG9yVHlwZXM6W1wiYXVkaW9cIixcImJlYWNvblwiLFwiYm9keVwiLFwiY3NzXCIsXCJlYXJseS1oaW50XCIsXCJlbWJlZFwiLFwiZmV0Y2hcIixcImZyYW1lXCIsXCJpZnJhbWVcIixcImljb25cIixcImltYWdlXCIsXCJpbWdcIixcImlucHV0XCIsXCJsaW5rXCIsXCJuYXZpZ2F0aW9uXCIsXCJvYmplY3RcIixcInBpbmdcIixcInNjcmlwdFwiLFwidHJhY2tcIixcInZpZGVvXCIsXCJ4bWxodHRwcmVxdWVzdFwiXSxtYXNrUmVxdWVzdEZuOnQ9PnQscmVjb3JkSGVhZGVyczohMSxyZWNvcmRCb2R5OiExLHJlY29yZEluaXRpYWxSZXF1ZXN0czohMSxyZWNvcmRQZXJmb3JtYW5jZTohMSxwZXJmb3JtYW5jZUVudHJ5VHlwZVRvT2JzZXJ2ZTpbXCJmaXJzdC1pbnB1dFwiLFwibmF2aWdhdGlvblwiLFwicGFpbnRcIixcInJlc291cmNlXCJdLHBheWxvYWRTaXplTGltaXRCeXRlczoxZTYscGF5bG9hZEhvc3REZW55TGlzdDpbXCIubHItaW5nZXN0LmlvXCIsXCIuaW5nZXN0LnNlbnRyeS5pb1wiLFwiLmNsYXJpdHkubXNcIixcImFuYWx5dGljcy5nb29nbGUuY29tXCIsXCJiYW0ubnItZGF0YS5uZXRcIl19LFdlPVtcImF1dGhvcml6YXRpb25cIixcIngtZm9yd2FyZGVkLWZvclwiLFwiYXV0aG9yaXphdGlvblwiLFwiY29va2llXCIsXCJzZXQtY29va2llXCIsXCJ4LWFwaS1rZXlcIixcIngtcmVhbC1pcFwiLFwicmVtb3RlLWFkZHJcIixcImZvcndhcmRlZFwiLFwicHJveHktYXV0aG9yaXphdGlvblwiLFwieC1jc3JmLXRva2VuXCIsXCJ4LWNzcmZ0b2tlblwiLFwieC14c3JmLXRva2VuXCJdLEdlPVtcInBhc3N3b3JkXCIsXCJzZWNyZXRcIixcInBhc3N3ZFwiLFwiYXBpX2tleVwiLFwiYXBpa2V5XCIsXCJhdXRoXCIsXCJjcmVkZW50aWFsc1wiLFwibXlzcWxfcHdkXCIsXCJwcml2YXRla2V5XCIsXCJwcml2YXRlX2tleVwiLFwidG9rZW5cIl0sSmU9W1wiL3MvXCIsXCIvZS9cIixcIi9pL1wiXTtmdW5jdGlvbiBWZSh0LGksZSxyKXtpZihPKHQpKXJldHVybiB0O3ZhciBzPShudWxsPT1pP3ZvaWQgMDppW1wiY29udGVudC1sZW5ndGhcIl0pfHxmdW5jdGlvbih0KXtyZXR1cm4gbmV3IEJsb2IoW3RdKS5zaXplfSh0KTtyZXR1cm4gVChzKSYmKHM9cGFyc2VJbnQocykpLHM+ZT9xZStcIiBcIityK1wiIGJvZHkgdG9vIGxhcmdlIHRvIHJlY29yZCAoXCIrcytcIiBieXRlcylcIjp0fWZ1bmN0aW9uIEtlKHQsaSl7aWYoTyh0KSlyZXR1cm4gdDt2YXIgZT10O3JldHVybiBjaShlLCExKXx8KGU9cWUrXCIgXCIraStcIiBib2R5IFwiK0JlKSxKKEdlLCh0PT57dmFyIHIscztudWxsIT0ocj1lKSYmci5sZW5ndGgmJi0xIT09KG51bGw9PShzPWUpP3ZvaWQgMDpzLmluZGV4T2YodCkpJiYoZT1xZStcIiBcIitpK1wiIGJvZHkgXCIrQmUrXCIgYXMgbWlnaHQgY29udGFpbjogXCIrdCl9KSksZX12YXIgWWU9KHQsaSk9Pnt2YXIgZSxyLHMsbj17cGF5bG9hZFNpemVMaW1pdEJ5dGVzOkhlLnBheWxvYWRTaXplTGltaXRCeXRlcyxwZXJmb3JtYW5jZUVudHJ5VHlwZVRvT2JzZXJ2ZTpbLi4uSGUucGVyZm9ybWFuY2VFbnRyeVR5cGVUb09ic2VydmVdLHBheWxvYWRIb3N0RGVueUxpc3Q6Wy4uLmkucGF5bG9hZEhvc3REZW55TGlzdHx8W10sLi4uSGUucGF5bG9hZEhvc3REZW55TGlzdF19LG89ITEhPT10LnNlc3Npb25fcmVjb3JkaW5nLnJlY29yZEhlYWRlcnMmJmkucmVjb3JkSGVhZGVycyxhPSExIT09dC5zZXNzaW9uX3JlY29yZGluZy5yZWNvcmRCb2R5JiZpLnJlY29yZEJvZHksbD0hMSE9PXQuY2FwdHVyZV9wZXJmb3JtYW5jZSYmaS5yZWNvcmRQZXJmb3JtYW5jZSx1PShlPW4scz1NYXRoLm1pbigxZTYsbnVsbCE9PShyPWUucGF5bG9hZFNpemVMaW1pdEJ5dGVzKSYmdm9pZCAwIT09cj9yOjFlNiksdD0+KG51bGwhPXQmJnQucmVxdWVzdEJvZHkmJih0LnJlcXVlc3RCb2R5PVZlKHQucmVxdWVzdEJvZHksdC5yZXF1ZXN0SGVhZGVycyxzLFwiUmVxdWVzdFwiKSksbnVsbCE9dCYmdC5yZXNwb25zZUJvZHkmJih0LnJlc3BvbnNlQm9keT1WZSh0LnJlc3BvbnNlQm9keSx0LnJlc3BvbnNlSGVhZGVycyxzLFwiUmVzcG9uc2VcIikpLHQpKSxoPWk9PntyZXR1cm4gdSgoKHQsaSk9Pnt2YXIgZSxyPXlpKHQubmFtZSkscz0wPT09aS5pbmRleE9mKFwiaHR0cFwiKT9udWxsPT0oZT15aShpKSk/dm9pZCAwOmUucGF0aG5hbWU6aTtcIi9cIj09PXMmJihzPVwiXCIpO3ZhciBuPW51bGw9PXI/dm9pZCAwOnIucGF0aG5hbWUucmVwbGFjZShzfHxcIlwiLFwiXCIpO2lmKCEociYmbiYmSmUuc29tZSgodD0+MD09PW4uaW5kZXhPZih0KSkpKSlyZXR1cm4gdH0pKChyPShlPWkpLnJlcXVlc3RIZWFkZXJzLE8ocil8fEooT2JqZWN0LmtleXMobnVsbCE9cj9yOnt9KSwodD0+e1dlLmluY2x1ZGVzKHQudG9Mb3dlckNhc2UoKSkmJihyW3RdPUJlKX0pKSxlKSx0LmFwaV9ob3N0KSk7dmFyIGUscn0sZD1FKHQuc2Vzc2lvbl9yZWNvcmRpbmcubWFza05ldHdvcmtSZXF1ZXN0Rm4pO3JldHVybiBkJiZFKHQuc2Vzc2lvbl9yZWNvcmRpbmcubWFza0NhcHR1cmVkTmV0d29ya1JlcXVlc3RGbikmJmoud2FybihcIkJvdGggYG1hc2tOZXR3b3JrUmVxdWVzdEZuYCBhbmQgYG1hc2tDYXB0dXJlZE5ldHdvcmtSZXF1ZXN0Rm5gIGFyZSBkZWZpbmVkLiBgbWFza05ldHdvcmtSZXF1ZXN0Rm5gIHdpbGwgYmUgaWdub3JlZC5cIiksZCYmKHQuc2Vzc2lvbl9yZWNvcmRpbmcubWFza0NhcHR1cmVkTmV0d29ya1JlcXVlc3RGbj1pPT57dmFyIGU9dC5zZXNzaW9uX3JlY29yZGluZy5tYXNrTmV0d29ya1JlcXVlc3RGbih7dXJsOmkubmFtZX0pO3JldHVybiBCKHt9LGkse25hbWU6bnVsbD09ZT92b2lkIDA6ZS51cmx9KX0pLG4ubWFza1JlcXVlc3RGbj1FKHQuc2Vzc2lvbl9yZWNvcmRpbmcubWFza0NhcHR1cmVkTmV0d29ya1JlcXVlc3RGbik/aT0+e3ZhciBlLHI9aChpKTtyZXR1cm4gciYmbnVsbCE9PShlPW51bGw9PXQuc2Vzc2lvbl9yZWNvcmRpbmcubWFza0NhcHR1cmVkTmV0d29ya1JlcXVlc3RGbj92b2lkIDA6dC5zZXNzaW9uX3JlY29yZGluZy5tYXNrQ2FwdHVyZWROZXR3b3JrUmVxdWVzdEZuKHIpKSYmdm9pZCAwIT09ZT9lOnZvaWQgMH06dD0+ZnVuY3Rpb24odCl7aWYoIVIodCkpcmV0dXJuIHQucmVxdWVzdEJvZHk9S2UodC5yZXF1ZXN0Qm9keSxcIlJlcXVlc3RcIiksdC5yZXNwb25zZUJvZHk9S2UodC5yZXNwb25zZUJvZHksXCJSZXNwb25zZVwiKSx0fShoKHQpKSxCKHt9LEhlLG4se3JlY29yZEhlYWRlcnM6byxyZWNvcmRCb2R5OmEscmVjb3JkUGVyZm9ybWFuY2U6bCxyZWNvcmRJbml0aWFsUmVxdWVzdHM6bH0pfTtmdW5jdGlvbiBYZSh0LGksZSxyLHMpe3JldHVybiBpPmUmJihqLndhcm4oXCJtaW4gY2Fubm90IGJlIGdyZWF0ZXIgdGhhbiBtYXguXCIpLGk9ZSksRih0KT90PmU/KHImJmoud2FybihyK1wiIGNhbm5vdCBiZSAgZ3JlYXRlciB0aGFuIG1heDogXCIrZStcIi4gVXNpbmcgbWF4IHZhbHVlIGluc3RlYWQuXCIpLGUpOnQ8aT8ociYmai53YXJuKHIrXCIgY2Fubm90IGJlIGxlc3MgdGhhbiBtaW46IFwiK2krXCIuIFVzaW5nIG1pbiB2YWx1ZSBpbnN0ZWFkLlwiKSxpKTp0OihyJiZqLndhcm4ocitcIiBtdXN0IGJlIGEgbnVtYmVyLiB1c2luZyBtYXggb3IgZmFsbGJhY2suIG1heDogXCIrZStcIiwgZmFsbGJhY2s6IFwiK3MpLFhlKHN8fGUsaSxlLHIpKX1jbGFzcyBRZXtjb25zdHJ1Y3Rvcih0LGkpe3ZhciBlLHI7dm9pZCAwPT09aSYmKGk9e30pLHRoaXMub3Q9MTAwLHRoaXMubHQ9MTAsdGhpcy51dD17fSx0aGlzLmh0PXt9LHRoaXMuZHQ9KCk9PntPYmplY3Qua2V5cyh0aGlzLnV0KS5mb3JFYWNoKCh0PT57dGhpcy51dFt0XT10aGlzLnV0W3RdK3RoaXMubHQsdGhpcy51dFt0XT49dGhpcy5vdCYmZGVsZXRlIHRoaXMudXRbdF19KSl9LHRoaXMudnQ9dD0+e3ZhciBpPXRoaXMuX3Jyd2ViLm1pcnJvci5nZXROb2RlKHQpO2lmKFwic3ZnXCIhPT0obnVsbD09aT92b2lkIDA6aS5ub2RlTmFtZSkmJmkgaW5zdGFuY2VvZiBFbGVtZW50KXt2YXIgZT1pLmNsb3Nlc3QoXCJzdmdcIik7aWYoZSlyZXR1cm5bdGhpcy5fcnJ3ZWIubWlycm9yLmdldElkKGUpLGVdfXJldHVyblt0LGldfSx0aGlzLmN0PXQ9Pnt2YXIgaSxlLHIscyxuLG8sYSxsO3JldHVybihudWxsIT09KGk9bnVsbD09KGU9dC5yZW1vdmVzKT92b2lkIDA6ZS5sZW5ndGgpJiZ2b2lkIDAhPT1pP2k6MCkrKG51bGwhPT0ocj1udWxsPT0ocz10LmF0dHJpYnV0ZXMpP3ZvaWQgMDpzLmxlbmd0aCkmJnZvaWQgMCE9PXI/cjowKSsobnVsbCE9PShuPW51bGw9PShvPXQudGV4dHMpP3ZvaWQgMDpvLmxlbmd0aCkmJnZvaWQgMCE9PW4/bjowKSsobnVsbCE9PShhPW51bGw9PShsPXQuYWRkcyk/dm9pZCAwOmwubGVuZ3RoKSYmdm9pZCAwIT09YT9hOjApfSx0aGlzLnRocm90dGxlTXV0YXRpb25zPXQ9PntpZigzIT09dC50eXBlfHwwIT09dC5kYXRhLnNvdXJjZSlyZXR1cm4gdDt2YXIgaT10LmRhdGEsZT10aGlzLmN0KGkpO2kuYXR0cmlidXRlcyYmKGkuYXR0cmlidXRlcz1pLmF0dHJpYnV0ZXMuZmlsdGVyKCh0PT57dmFyIGksZSxyLFtzLG5dPXRoaXMudnQodC5pZCk7aWYoMD09PXRoaXMudXRbc10pcmV0dXJuITE7KHRoaXMudXRbc109bnVsbCE9PShpPXRoaXMudXRbc10pJiZ2b2lkIDAhPT1pP2k6dGhpcy5vdCx0aGlzLnV0W3NdPU1hdGgubWF4KHRoaXMudXRbc10tMSwwKSwwPT09dGhpcy51dFtzXSkmJih0aGlzLmh0W3NdfHwodGhpcy5odFtzXT0hMCxudWxsPT0oZT0ocj10aGlzLmZ0KS5vbkJsb2NrZWROb2RlKXx8ZS5jYWxsKHIscyxuKSkpO3JldHVybiB0fSkpKTt2YXIgcj10aGlzLmN0KGkpO3JldHVybiAwIT09cnx8ZT09PXI/dDp2b2lkIDB9LHRoaXMuX3Jyd2ViPXQsdGhpcy5mdD1pLHRoaXMubHQ9WGUobnVsbCE9PShlPXRoaXMuZnQucmVmaWxsUmF0ZSkmJnZvaWQgMCE9PWU/ZTp0aGlzLmx0LDAsMTAwLFwibXV0YXRpb24gdGhyb3R0bGluZyByZWZpbGwgcmF0ZVwiKSx0aGlzLm90PVhlKG51bGwhPT0ocj10aGlzLmZ0LmJ1Y2tldFNpemUpJiZ2b2lkIDAhPT1yP3I6dGhpcy5vdCwwLDEwMCxcIm11dGF0aW9uIHRocm90dGxpbmcgYnVja2V0IHNpemVcIiksc2V0SW50ZXJ2YWwoKCgpPT57dGhpcy5kdCgpfSksMWUzKX19dmFyIFplPVVpbnQ4QXJyYXksdHI9VWludDE2QXJyYXksaXI9VWludDMyQXJyYXksZXI9bmV3IFplKFswLDAsMCwwLDAsMCwwLDAsMSwxLDEsMSwyLDIsMiwyLDMsMywzLDMsNCw0LDQsNCw1LDUsNSw1LDAsMCwwLDBdKSxycj1uZXcgWmUoWzAsMCwwLDAsMSwxLDIsMiwzLDMsNCw0LDUsNSw2LDYsNyw3LDgsOCw5LDksMTAsMTAsMTEsMTEsMTIsMTIsMTMsMTMsMCwwXSksc3I9bmV3IFplKFsxNiwxNywxOCwwLDgsNyw5LDYsMTAsNSwxMSw0LDEyLDMsMTMsMiwxNCwxLDE1XSksbnI9ZnVuY3Rpb24odCxpKXtmb3IodmFyIGU9bmV3IHRyKDMxKSxyPTA7cjwzMTsrK3IpZVtyXT1pKz0xPDx0W3ItMV07dmFyIHM9bmV3IGlyKGVbMzBdKTtmb3Iocj0xO3I8MzA7KytyKWZvcih2YXIgbj1lW3JdO248ZVtyKzFdOysrbilzW25dPW4tZVtyXTw8NXxyO3JldHVybltlLHNdfSxvcj1ucihlciwyKSxhcj1vclswXSxscj1vclsxXTthclsyOF09MjU4LGxyWzI1OF09Mjg7Zm9yKHZhciB1cj1ucihyciwwKVsxXSxocj1uZXcgdHIoMzI3NjgpLGRyPTA7ZHI8MzI3Njg7Kytkcil7dmFyIHZyPSg0MzY5MCZkcik+Pj4xfCgyMTg0NSZkcik8PDE7dnI9KDYxNjgwJih2cj0oNTI0MjgmdnIpPj4+MnwoMTMxMDcmdnIpPDwyKSk+Pj40fCgzODU1JnZyKTw8NCxocltkcl09KCg2NTI4MCZ2cik+Pj44fCgyNTUmdnIpPDw4KT4+PjF9dmFyIGNyPWZ1bmN0aW9uKHQsaSxlKXtmb3IodmFyIHI9dC5sZW5ndGgscz0wLG49bmV3IHRyKGkpO3M8cjsrK3MpKytuW3Rbc10tMV07dmFyIG8sYT1uZXcgdHIoaSk7Zm9yKHM9MDtzPGk7KytzKWFbc109YVtzLTFdK25bcy0xXTw8MTtpZihlKXtvPW5ldyB0cigxPDxpKTt2YXIgbD0xNS1pO2ZvcihzPTA7czxyOysrcylpZih0W3NdKWZvcih2YXIgdT1zPDw0fHRbc10saD1pLXRbc10sZD1hW3Rbc10tMV0rKzw8aCx2PWR8KDE8PGgpLTE7ZDw9djsrK2Qpb1tocltkXT4+PmxdPXV9ZWxzZSBmb3Iobz1uZXcgdHIocikscz0wO3M8cjsrK3Mpb1tzXT1oclthW3Rbc10tMV0rK10+Pj4xNS10W3NdO3JldHVybiBvfSxmcj1uZXcgWmUoMjg4KTtmb3IoZHI9MDtkcjwxNDQ7Kytkcilmcltkcl09ODtmb3IoZHI9MTQ0O2RyPDI1NjsrK2RyKWZyW2RyXT05O2Zvcihkcj0yNTY7ZHI8MjgwOysrZHIpZnJbZHJdPTc7Zm9yKGRyPTI4MDtkcjwyODg7Kytkcilmcltkcl09ODt2YXIgcHI9bmV3IFplKDMyKTtmb3IoZHI9MDtkcjwzMjsrK2RyKXByW2RyXT01O3ZhciBncj1jcihmciw5LDApLF9yPWNyKHByLDUsMCksbXI9ZnVuY3Rpb24odCl7cmV0dXJuKHQvOD4+MCkrKDcmdCYmMSl9LGJyPWZ1bmN0aW9uKHQsaSxlKXsobnVsbD09ZXx8ZT50Lmxlbmd0aCkmJihlPXQubGVuZ3RoKTt2YXIgcj1uZXcodCBpbnN0YW5jZW9mIHRyP3RyOnQgaW5zdGFuY2VvZiBpcj9pcjpaZSkoZS1pKTtyZXR1cm4gci5zZXQodC5zdWJhcnJheShpLGUpKSxyfSx3cj1mdW5jdGlvbih0LGksZSl7ZTw8PTcmaTt2YXIgcj1pLzg+PjA7dFtyXXw9ZSx0W3IrMV18PWU+Pj44fSx5cj1mdW5jdGlvbih0LGksZSl7ZTw8PTcmaTt2YXIgcj1pLzg+PjA7dFtyXXw9ZSx0W3IrMV18PWU+Pj44LHRbcisyXXw9ZT4+PjE2fSxTcj1mdW5jdGlvbih0LGkpe2Zvcih2YXIgZT1bXSxyPTA7cjx0Lmxlbmd0aDsrK3IpdFtyXSYmZS5wdXNoKHtzOnIsZjp0W3JdfSk7dmFyIHM9ZS5sZW5ndGgsbj1lLnNsaWNlKCk7aWYoIXMpcmV0dXJuW25ldyBaZSgwKSwwXTtpZigxPT1zKXt2YXIgbz1uZXcgWmUoZVswXS5zKzEpO3JldHVybiBvW2VbMF0uc109MSxbbywxXX1lLnNvcnQoKGZ1bmN0aW9uKHQsaSl7cmV0dXJuIHQuZi1pLmZ9KSksZS5wdXNoKHtzOi0xLGY6MjUwMDF9KTt2YXIgYT1lWzBdLGw9ZVsxXSx1PTAsaD0xLGQ9Mjtmb3IoZVswXT17czotMSxmOmEuZitsLmYsbDphLHI6bH07aCE9cy0xOylhPWVbZVt1XS5mPGVbZF0uZj91Kys6ZCsrXSxsPWVbdSE9aCYmZVt1XS5mPGVbZF0uZj91Kys6ZCsrXSxlW2grK109e3M6LTEsZjphLmYrbC5mLGw6YSxyOmx9O3ZhciB2PW5bMF0ucztmb3Iocj0xO3I8czsrK3IpbltyXS5zPnYmJih2PW5bcl0ucyk7dmFyIGM9bmV3IHRyKHYrMSksZj0kcihlW2gtMV0sYywwKTtpZihmPmkpe3I9MDt2YXIgcD0wLGc9Zi1pLF89MTw8Zztmb3Iobi5zb3J0KChmdW5jdGlvbih0LGkpe3JldHVybiBjW2kuc10tY1t0LnNdfHx0LmYtaS5mfSkpO3I8czsrK3Ipe3ZhciBtPW5bcl0ucztpZighKGNbbV0+aSkpYnJlYWs7cCs9Xy0oMTw8Zi1jW21dKSxjW21dPWl9Zm9yKHA+Pj49ZztwPjA7KXt2YXIgYj1uW3JdLnM7Y1tiXTxpP3AtPTE8PGktY1tiXSsrLTE6KytyfWZvcig7cj49MCYmcDstLXIpe3ZhciB3PW5bcl0ucztjW3ddPT1pJiYoLS1jW3ddLCsrcCl9Zj1pfXJldHVybltuZXcgWmUoYyksZl19LCRyPWZ1bmN0aW9uKHQsaSxlKXtyZXR1cm4tMT09dC5zP01hdGgubWF4KCRyKHQubCxpLGUrMSksJHIodC5yLGksZSsxKSk6aVt0LnNdPWV9LGtyPWZ1bmN0aW9uKHQpe2Zvcih2YXIgaT10Lmxlbmd0aDtpJiYhdFstLWldOyk7Zm9yKHZhciBlPW5ldyB0cigrK2kpLHI9MCxzPXRbMF0sbj0xLG89ZnVuY3Rpb24odCl7ZVtyKytdPXR9LGE9MTthPD1pOysrYSlpZih0W2FdPT1zJiZhIT1pKSsrbjtlbHNle2lmKCFzJiZuPjIpe2Zvcig7bj4xMzg7bi09MTM4KW8oMzI3NTQpO24+MiYmKG8obj4xMD9uLTExPDw1fDI4NjkwOm4tMzw8NXwxMjMwNSksbj0wKX1lbHNlIGlmKG4+Myl7Zm9yKG8ocyksLS1uO24+NjtuLT02KW8oODMwNCk7bj4yJiYobyhuLTM8PDV8ODIwOCksbj0wKX1mb3IoO24tLTspbyhzKTtuPTEscz10W2FdfXJldHVybltlLnN1YmFycmF5KDAsciksaV19LHhyPWZ1bmN0aW9uKHQsaSl7Zm9yKHZhciBlPTAscj0wO3I8aS5sZW5ndGg7KytyKWUrPXRbcl0qaVtyXTtyZXR1cm4gZX0sRXI9ZnVuY3Rpb24odCxpLGUpe3ZhciByPWUubGVuZ3RoLHM9bXIoaSsyKTt0W3NdPTI1NSZyLHRbcysxXT1yPj4+OCx0W3MrMl09MjU1XnRbc10sdFtzKzNdPTI1NV50W3MrMV07Zm9yKHZhciBuPTA7bjxyOysrbil0W3Mrbis0XT1lW25dO3JldHVybiA4KihzKzQrcil9LElyPWZ1bmN0aW9uKHQsaSxlLHIscyxuLG8sYSxsLHUsaCl7d3IoaSxoKyssZSksKytzWzI1Nl07Zm9yKHZhciBkPVNyKHMsMTUpLHY9ZFswXSxjPWRbMV0sZj1TcihuLDE1KSxwPWZbMF0sZz1mWzFdLF89a3IodiksbT1fWzBdLGI9X1sxXSx3PWtyKHApLHk9d1swXSxTPXdbMV0sJD1uZXcgdHIoMTkpLGs9MDtrPG0ubGVuZ3RoOysraykkWzMxJm1ba11dKys7Zm9yKGs9MDtrPHkubGVuZ3RoOysraykkWzMxJnlba11dKys7Zm9yKHZhciB4PVNyKCQsNyksRT14WzBdLEk9eFsxXSxQPTE5O1A+NCYmIUVbc3JbUC0xXV07LS1QKTt2YXIgUixULE0sQyxPPXUrNTw8MyxGPXhyKHMsZnIpK3hyKG4scHIpK28sQT14cihzLHYpK3hyKG4scCkrbysxNCszKlAreHIoJCxFKSsoMiokWzE2XSszKiRbMTddKzcqJFsxOF0pO2lmKE88PUYmJk88PUEpcmV0dXJuIEVyKGksaCx0LnN1YmFycmF5KGwsbCt1KSk7aWYod3IoaSxoLDErKEE8RikpLGgrPTIsQTxGKXtSPWNyKHYsYywwKSxUPXYsTT1jcihwLGcsMCksQz1wO3ZhciBEPWNyKEUsSSwwKTt3cihpLGgsYi0yNTcpLHdyKGksaCs1LFMtMSksd3IoaSxoKzEwLFAtNCksaCs9MTQ7Zm9yKGs9MDtrPFA7KytrKXdyKGksaCszKmssRVtzcltrXV0pO2grPTMqUDtmb3IodmFyIEw9W20seV0sTj0wO048MjsrK04pe3ZhciBqPUxbTl07Zm9yKGs9MDtrPGoubGVuZ3RoOysrayl7dmFyIHo9MzEmaltrXTt3cihpLGgsRFt6XSksaCs9RVt6XSx6PjE1JiYod3IoaSxoLGpba10+Pj41JjEyNyksaCs9altrXT4+PjEyKX19fWVsc2UgUj1ncixUPWZyLE09X3IsQz1wcjtmb3Ioaz0wO2s8YTsrK2spaWYocltrXT4yNTUpe3o9cltrXT4+PjE4JjMxO3lyKGksaCxSW3orMjU3XSksaCs9VFt6KzI1N10sej43JiYod3IoaSxoLHJba10+Pj4yMyYzMSksaCs9ZXJbel0pO3ZhciBVPTMxJnJba107eXIoaSxoLE1bVV0pLGgrPUNbVV0sVT4zJiYoeXIoaSxoLHJba10+Pj41JjgxOTEpLGgrPXJyW1VdKX1lbHNlIHlyKGksaCxSW3Jba11dKSxoKz1UW3Jba11dO3JldHVybiB5cihpLGgsUlsyNTZdKSxoK1RbMjU2XX0sUHI9bmV3IGlyKFs2NTU0MCwxMzEwODAsMTMxMDg4LDEzMTEwNCwyNjIxNzYsMTA0ODcwNCwxMDQ4ODMyLDIxMTQ1NjAsMjExNzYzMl0pLFJyPWZ1bmN0aW9uKCl7Zm9yKHZhciB0PW5ldyBpcigyNTYpLGk9MDtpPDI1NjsrK2kpe2Zvcih2YXIgZT1pLHI9OTstLXI7KWU9KDEmZSYmMzk4ODI5MjM4NCleZT4+PjE7dFtpXT1lfXJldHVybiB0fSgpLFRyPWZ1bmN0aW9uKCl7dmFyIHQ9NDI5NDk2NzI5NTtyZXR1cm57cDpmdW5jdGlvbihpKXtmb3IodmFyIGU9dCxyPTA7cjxpLmxlbmd0aDsrK3IpZT1SclsyNTUmZV5pW3JdXV5lPj4+ODt0PWV9LGQ6ZnVuY3Rpb24oKXtyZXR1cm4gNDI5NDk2NzI5NV50fX19LE1yPWZ1bmN0aW9uKHQsaSxlLHIscyl7cmV0dXJuIGZ1bmN0aW9uKHQsaSxlLHIscyxuKXt2YXIgbz10Lmxlbmd0aCxhPW5ldyBaZShyK28rNSooMStNYXRoLmZsb29yKG8vN2UzKSkrcyksbD1hLnN1YmFycmF5KHIsYS5sZW5ndGgtcyksdT0wO2lmKCFpfHxvPDgpZm9yKHZhciBoPTA7aDw9bztoKz02NTUzNSl7dmFyIGQ9aCs2NTUzNTtkPG8/dT1FcihsLHUsdC5zdWJhcnJheShoLGQpKToobFtoXT1uLHU9RXIobCx1LHQuc3ViYXJyYXkoaCxvKSkpfWVsc2V7Zm9yKHZhciB2PVByW2ktMV0sYz12Pj4+MTMsZj04MTkxJnYscD0oMTw8ZSktMSxnPW5ldyB0cigzMjc2OCksXz1uZXcgdHIocCsxKSxtPU1hdGguY2VpbChlLzMpLGI9MiptLHc9ZnVuY3Rpb24oaSl7cmV0dXJuKHRbaV1edFtpKzFdPDxtXnRbaSsyXTw8YikmcH0seT1uZXcgaXIoMjVlMyksUz1uZXcgdHIoMjg4KSwkPW5ldyB0cigzMiksaz0wLHg9MCxFPShoPTAsMCksST0wLFA9MDtoPG87KytoKXt2YXIgUj13KGgpLFQ9MzI3NjcmaCxNPV9bUl07aWYoZ1tUXT1NLF9bUl09VCxJPD1oKXt2YXIgQz1vLWg7aWYoKGs+N2UzfHxFPjI0NTc2KSYmQz40MjMpe3U9SXIodCxsLDAseSxTLCQseCxFLFAsaC1QLHUpLEU9az14PTAsUD1oO2Zvcih2YXIgTz0wO088Mjg2OysrTylTW09dPTA7Zm9yKE89MDtPPDMwOysrTykkW09dPTB9dmFyIEY9MixBPTAsRD1mLEw9VC1NJjMyNzY3O2lmKEM+MiYmUj09dyhoLUwpKWZvcih2YXIgTj1NYXRoLm1pbihjLEMpLTEsaj1NYXRoLm1pbigzMjc2NyxoKSx6PU1hdGgubWluKDI1OCxDKTtMPD1qJiYtLUQmJlQhPU07KXtpZih0W2grRl09PXRbaCtGLUxdKXtmb3IodmFyIFU9MDtVPHomJnRbaCtVXT09dFtoK1UtTF07KytVKTtpZihVPkYpe2lmKEY9VSxBPUwsVT5OKWJyZWFrO3ZhciBxPU1hdGgubWluKEwsVS0yKSxCPTA7Zm9yKE89MDtPPHE7KytPKXt2YXIgSD1oLUwrTyszMjc2OCYzMjc2NyxXPUgtZ1tIXSszMjc2OCYzMjc2NztXPkImJihCPVcsTT1IKX19fUwrPShUPU0pLShNPWdbVF0pKzMyNzY4JjMyNzY3fWlmKEEpe3lbRSsrXT0yNjg0MzU0NTZ8bHJbRl08PDE4fHVyW0FdO3ZhciBHPTMxJmxyW0ZdLEo9MzEmdXJbQV07eCs9ZXJbR10rcnJbSl0sKytTWzI1NytHXSwrKyRbSl0sST1oK0YsKytrfWVsc2UgeVtFKytdPXRbaF0sKytTW3RbaF1dfX11PUlyKHQsbCxuLHksUywkLHgsRSxQLGgtUCx1KX1yZXR1cm4gYnIoYSwwLHIrbXIodSkrcyl9KHQsbnVsbD09aS5sZXZlbD82OmkubGV2ZWwsbnVsbD09aS5tZW0/TWF0aC5jZWlsKDEuNSpNYXRoLm1heCg4LE1hdGgubWluKDEzLE1hdGgubG9nKHQubGVuZ3RoKSkpKToxMitpLm1lbSxlLHIsIXMpfSxDcj1mdW5jdGlvbih0LGksZSl7Zm9yKDtlOysraSl0W2ldPWUsZT4+Pj04fSxPcj1mdW5jdGlvbih0LGkpe3ZhciBlPWkuZmlsZW5hbWU7aWYodFswXT0zMSx0WzFdPTEzOSx0WzJdPTgsdFs4XT1pLmxldmVsPDI/NDo5PT1pLmxldmVsPzI6MCx0WzldPTMsMCE9aS5tdGltZSYmQ3IodCw0LE1hdGguZmxvb3IobmV3IERhdGUoaS5tdGltZXx8RGF0ZS5ub3coKSkvMWUzKSksZSl7dFszXT04O2Zvcih2YXIgcj0wO3I8PWUubGVuZ3RoOysrcil0W3IrMTBdPWUuY2hhckNvZGVBdChyKX19LEZyPWZ1bmN0aW9uKHQpe3JldHVybiAxMCsodC5maWxlbmFtZSYmdC5maWxlbmFtZS5sZW5ndGgrMXx8MCl9O2Z1bmN0aW9uIEFyKHQsaSl7dm9pZCAwPT09aSYmKGk9e30pO3ZhciBlPVRyKCkscj10Lmxlbmd0aDtlLnAodCk7dmFyIHM9TXIodCxpLEZyKGkpLDgpLG49cy5sZW5ndGg7cmV0dXJuIE9yKHMsaSksQ3IocyxuLTgsZS5kKCkpLENyKHMsbi00LHIpLHN9ZnVuY3Rpb24gRHIodCxpKXt2YXIgZT10Lmxlbmd0aDtpZihcInVuZGVmaW5lZFwiIT10eXBlb2YgVGV4dEVuY29kZXIpcmV0dXJuKG5ldyBUZXh0RW5jb2RlcikuZW5jb2RlKHQpO2Zvcih2YXIgcj1uZXcgWmUodC5sZW5ndGgrKHQubGVuZ3RoPj4+MSkpLHM9MCxuPWZ1bmN0aW9uKHQpe3JbcysrXT10fSxvPTA7bzxlOysrbyl7aWYocys1PnIubGVuZ3RoKXt2YXIgYT1uZXcgWmUocys4KyhlLW88PDEpKTthLnNldChyKSxyPWF9dmFyIGw9dC5jaGFyQ29kZUF0KG8pO2w8MTI4fHxpP24obCk6bDwyMDQ4PyhuKDE5MnxsPj4+NiksbigxMjh8NjMmbCkpOmw+NTUyOTUmJmw8NTczNDQ/KG4oMjQwfChsPTY1NTM2KygxMDQ3NTUyJmwpfDEwMjMmdC5jaGFyQ29kZUF0KCsrbykpPj4+MTgpLG4oMTI4fGw+Pj4xMiY2MyksbigxMjh8bD4+PjYmNjMpLG4oMTI4fDYzJmwpKToobigyMjR8bD4+PjEyKSxuKDEyOHxsPj4+NiY2MyksbigxMjh8NjMmbCkpfXJldHVybiBicihyLDAscyl9ZnVuY3Rpb24gTHIodCxpKXtyZXR1cm4gZnVuY3Rpb24odCl7Zm9yKHZhciBpPTAsZT0wO2U8dC5sZW5ndGg7ZSsrKWk9KGk8PDUpLWkrdC5jaGFyQ29kZUF0KGUpLGl8PTA7cmV0dXJuIE1hdGguYWJzKGkpfSh0KSUxMDA8WGUoMTAwKmksMCwxMDApfXZhciBOcj1cImRpc2FibGVkXCIsanI9XCJzYW1wbGVkXCIsenI9XCJhY3RpdmVcIixVcj1cImJ1ZmZlcmluZ1wiLHFyPVwicGF1c2VkXCIsQnI9XCJ0cmlnZ2VyXCIsSHI9QnIrXCJfYWN0aXZhdGVkXCIsV3I9QnIrXCJfcGVuZGluZ1wiLEdyPUJyK1wiX1wiK05yO2Z1bmN0aW9uIEpyKHQsaSl7cmV0dXJuIGkuc29tZSgoaT0+XCJyZWdleFwiPT09aS5tYXRjaGluZyYmbmV3IFJlZ0V4cChpLnVybCkudGVzdCh0KSkpfWNsYXNzIFZye2NvbnN0cnVjdG9yKHQpe3RoaXMuZ3Q9dH10cmlnZ2VyU3RhdHVzKHQpe3ZhciBpPXRoaXMuZ3QubWFwKChpPT5pLnRyaWdnZXJTdGF0dXModCkpKTtyZXR1cm4gaS5pbmNsdWRlcyhIcik/SHI6aS5pbmNsdWRlcyhXcik/V3I6R3J9c3RvcCgpe3RoaXMuZ3QuZm9yRWFjaCgodD0+dC5zdG9wKCkpKX19Y2xhc3MgS3J7Y29uc3RydWN0b3IodCl7dGhpcy5ndD10fXRyaWdnZXJTdGF0dXModCl7dmFyIGk9bmV3IFNldDtmb3IodmFyIGUgb2YgdGhpcy5ndClpLmFkZChlLnRyaWdnZXJTdGF0dXModCkpO3N3aXRjaChpLmRlbGV0ZShHciksaS5zaXplKXtjYXNlIDA6cmV0dXJuIEdyO2Nhc2UgMTpyZXR1cm4gQXJyYXkuZnJvbShpKVswXTtkZWZhdWx0OnJldHVybiBXcn19c3RvcCgpe3RoaXMuZ3QuZm9yRWFjaCgodD0+dC5zdG9wKCkpKX19Y2xhc3MgWXJ7dHJpZ2dlclN0YXR1cygpe3JldHVybiBXcn1zdG9wKCl7fX1jbGFzcyBYcntjb25zdHJ1Y3Rvcih0KXt0aGlzLl90PVtdLHRoaXMuYnQ9W10sdGhpcy51cmxCbG9ja2VkPSExLHRoaXMuX2luc3RhbmNlPXR9b25SZW1vdGVDb25maWcodCl7dmFyIGksZTt0aGlzLl90PShudWxsPT0oaT10LnNlc3Npb25SZWNvcmRpbmcpP3ZvaWQgMDppLnVybFRyaWdnZXJzKXx8W10sdGhpcy5idD0obnVsbD09KGU9dC5zZXNzaW9uUmVjb3JkaW5nKT92b2lkIDA6ZS51cmxCbG9ja2xpc3QpfHxbXX13dCh0KXt2YXIgaTtyZXR1cm4gMD09PXRoaXMuX3QubGVuZ3RoP0dyOihudWxsPT0oaT10aGlzLl9pbnN0YW5jZSk/dm9pZCAwOmkuZ2V0X3Byb3BlcnR5KHh0KSk9PT10P0hyOldyfXRyaWdnZXJTdGF0dXModCl7dmFyIGk9dGhpcy53dCh0KSxlPWk9PT1Icj9IcjppPT09V3I/V3I6R3I7cmV0dXJuIHRoaXMuX2luc3RhbmNlLnJlZ2lzdGVyX2Zvcl9zZXNzaW9uKHskc2RrX2RlYnVnX3JlcGxheV91cmxfdHJpZ2dlcl9zdGF0dXM6ZX0pLGV9Y2hlY2tVcmxUcmlnZ2VyQ29uZGl0aW9ucyhpLGUscil7aWYodm9pZCAwIT09dCYmdC5sb2NhdGlvbi5ocmVmKXt2YXIgcz10LmxvY2F0aW9uLmhyZWYsbj10aGlzLnVybEJsb2NrZWQsbz1KcihzLHRoaXMuYnQpO24mJm98fChvJiYhbj9pKCk6IW8mJm4mJmUoKSxKcihzLHRoaXMuX3QpJiZyKFwidXJsXCIpKX19c3RvcCgpe319Y2xhc3MgUXJ7Y29uc3RydWN0b3IodCl7dGhpcy5saW5rZWRGbGFnPW51bGwsdGhpcy5saW5rZWRGbGFnU2Vlbj0hMSx0aGlzLnl0PSgpPT57fSx0aGlzLl9pbnN0YW5jZT10fXRyaWdnZXJTdGF0dXMoKXt2YXIgdD1XcjtyZXR1cm4gTyh0aGlzLmxpbmtlZEZsYWcpJiYodD1HciksdGhpcy5saW5rZWRGbGFnU2VlbiYmKHQ9SHIpLHRoaXMuX2luc3RhbmNlLnJlZ2lzdGVyX2Zvcl9zZXNzaW9uKHskc2RrX2RlYnVnX3JlcGxheV9saW5rZWRfZmxhZ190cmlnZ2VyX3N0YXR1czp0fSksdH1vblJlbW90ZUNvbmZpZyh0LGkpe3ZhciBlO2lmKHRoaXMubGlua2VkRmxhZz0obnVsbD09KGU9dC5zZXNzaW9uUmVjb3JkaW5nKT92b2lkIDA6ZS5saW5rZWRGbGFnKXx8bnVsbCwhTyh0aGlzLmxpbmtlZEZsYWcpJiYhdGhpcy5saW5rZWRGbGFnU2Vlbil7dmFyIHI9VCh0aGlzLmxpbmtlZEZsYWcpP3RoaXMubGlua2VkRmxhZzp0aGlzLmxpbmtlZEZsYWcuZmxhZyxzPVQodGhpcy5saW5rZWRGbGFnKT9udWxsOnRoaXMubGlua2VkRmxhZy52YXJpYW50O3RoaXMueXQ9dGhpcy5faW5zdGFuY2Uub25GZWF0dXJlRmxhZ3MoKCh0LGUpPT57dmFyIG49ITE7aWYoSShlKSYmciBpbiBlKXt2YXIgbz1lW3JdO249QShvKT8hMD09PW86cz9vPT09czohIW99dGhpcy5saW5rZWRGbGFnU2Vlbj1uLG4mJmkocixzKX0pKX19c3RvcCgpe3RoaXMueXQoKX19Y2xhc3MgWnJ7Y29uc3RydWN0b3IodCl7dGhpcy5TdD1bXSx0aGlzLl9pbnN0YW5jZT10fW9uUmVtb3RlQ29uZmlnKHQpe3ZhciBpO3RoaXMuU3Q9KG51bGw9PShpPXQuc2Vzc2lvblJlY29yZGluZyk/dm9pZCAwOmkuZXZlbnRUcmlnZ2Vycyl8fFtdfSR0KHQpe3ZhciBpO3JldHVybiAwPT09dGhpcy5TdC5sZW5ndGg/R3I6KG51bGw9PShpPXRoaXMuX2luc3RhbmNlKT92b2lkIDA6aS5nZXRfcHJvcGVydHkoRXQpKT09PXQ/SHI6V3J9dHJpZ2dlclN0YXR1cyh0KXt2YXIgaT10aGlzLiR0KHQpLGU9aT09PUhyP0hyOmk9PT1Xcj9XcjpHcjtyZXR1cm4gdGhpcy5faW5zdGFuY2UucmVnaXN0ZXJfZm9yX3Nlc3Npb24oeyRzZGtfZGVidWdfcmVwbGF5X2V2ZW50X3RyaWdnZXJfc3RhdHVzOmV9KSxlfXN0b3AoKXt9fWZ1bmN0aW9uIHRzKHQpe3JldHVybiB0LmlzUmVjb3JkaW5nRW5hYmxlZD9VcjpOcn1mdW5jdGlvbiBpcyh0KXtpZighdC5yZWNlaXZlZERlY2lkZSlyZXR1cm4gVXI7aWYoIXQuaXNSZWNvcmRpbmdFbmFibGVkKXJldHVybiBOcjtpZih0LnVybFRyaWdnZXJNYXRjaGluZy51cmxCbG9ja2VkKXJldHVybiBxcjt2YXIgaT0hMD09PXQuaXNTYW1wbGVkLGU9bmV3IFZyKFt0LmV2ZW50VHJpZ2dlck1hdGNoaW5nLHQudXJsVHJpZ2dlck1hdGNoaW5nLHQubGlua2VkRmxhZ01hdGNoaW5nXSkudHJpZ2dlclN0YXR1cyh0LnNlc3Npb25JZCk7cmV0dXJuIGk/anI6ZT09PUhyP3pyOmU9PT1Xcj9VcjohMT09PXQuaXNTYW1wbGVkP05yOnpyfWZ1bmN0aW9uIGVzKHQpe2lmKCF0LnJlY2VpdmVkRGVjaWRlKXJldHVybiBVcjtpZighdC5pc1JlY29yZGluZ0VuYWJsZWQpcmV0dXJuIE5yO2lmKHQudXJsVHJpZ2dlck1hdGNoaW5nLnVybEJsb2NrZWQpcmV0dXJuIHFyO3ZhciBpPW5ldyBLcihbdC5ldmVudFRyaWdnZXJNYXRjaGluZyx0LnVybFRyaWdnZXJNYXRjaGluZyx0LmxpbmtlZEZsYWdNYXRjaGluZ10pLnRyaWdnZXJTdGF0dXModC5zZXNzaW9uSWQpLGU9aSE9PUdyLHI9QSh0LmlzU2FtcGxlZCk7cmV0dXJuIGUmJmk9PT1Xcj9VcjplJiZpPT09R3J8fHImJiF0LmlzU2FtcGxlZD9OcjohMD09PXQuaXNTYW1wbGVkP2pyOnpyfXZhciBycz1cIltTZXNzaW9uUmVjb3JkaW5nXVwiLHNzPXoocnMpO2Z1bmN0aW9uIG5zKCl7dmFyIHQ7cmV0dXJuIG51bGw9PXZ8fG51bGw9PSh0PXYuX19Qb3N0aG9nRXh0ZW5zaW9uc19fKXx8bnVsbD09KHQ9dC5ycndlYik/dm9pZCAwOnQucmVjb3JkfXZhciBvcz0zZTUsYXM9W1VlLk1vdXNlTW92ZSxVZS5Nb3VzZUludGVyYWN0aW9uLFVlLlNjcm9sbCxVZS5WaWV3cG9ydFJlc2l6ZSxVZS5JbnB1dCxVZS5Ub3VjaE1vdmUsVWUuTWVkaWFJbnRlcmFjdGlvbixVZS5EcmFnXSxscz10PT4oe3Jyd2ViTWV0aG9kOnQsZW5xdWV1ZWRBdDpEYXRlLm5vdygpLGF0dGVtcHQ6MX0pO2Z1bmN0aW9uIHVzKHQpe3JldHVybiBmdW5jdGlvbih0LGkpe2Zvcih2YXIgZT1cIlwiLHI9MDtyPHQubGVuZ3RoOyl7dmFyIHM9dFtyKytdO3M8MTI4fHxpP2UrPVN0cmluZy5mcm9tQ2hhckNvZGUocyk6czwyMjQ/ZSs9U3RyaW5nLmZyb21DaGFyQ29kZSgoMzEmcyk8PDZ8NjMmdFtyKytdKTpzPDI0MD9lKz1TdHJpbmcuZnJvbUNoYXJDb2RlKCgxNSZzKTw8MTJ8KDYzJnRbcisrXSk8PDZ8NjMmdFtyKytdKToocz0oKDE1JnMpPDwxOHwoNjMmdFtyKytdKTw8MTJ8KDYzJnRbcisrXSk8PDZ8NjMmdFtyKytdKS02NTUzNixlKz1TdHJpbmcuZnJvbUNoYXJDb2RlKDU1Mjk2fHM+PjEwLDU2MzIwfDEwMjMmcykpfXJldHVybiBlfShBcihEcihKU09OLnN0cmluZ2lmeSh0KSkpLCEwKX1mdW5jdGlvbiBocyh0KXtyZXR1cm4gdC50eXBlPT09emUuQ3VzdG9tJiZcInNlc3Npb25JZGxlXCI9PT10LmRhdGEudGFnfWNsYXNzIGRze2dldCBzZXNzaW9uSWQoKXtyZXR1cm4gdGhpcy5rdH1nZXQgeHQoKXtyZXR1cm4gdGhpcy5faW5zdGFuY2UuY29uZmlnLnNlc3Npb25fcmVjb3JkaW5nLnNlc3Npb25faWRsZV90aHJlc2hvbGRfbXN8fDNlNX1nZXQgc3RhcnRlZCgpe3JldHVybiB0aGlzLkV0fWdldCBJdCgpe2lmKCF0aGlzLl9pbnN0YW5jZS5zZXNzaW9uTWFuYWdlcil0aHJvdyBuZXcgRXJyb3IocnMrXCIgbXVzdCBiZSBzdGFydGVkIHdpdGggYSB2YWxpZCBzZXNzaW9uTWFuYWdlci5cIik7cmV0dXJuIHRoaXMuX2luc3RhbmNlLnNlc3Npb25NYW5hZ2VyfWdldCBQdCgpe3ZhciB0LGk7cmV0dXJuIHRoaXMuUnQudHJpZ2dlclN0YXR1cyh0aGlzLnNlc3Npb25JZCk9PT1Xcj82ZTQ6bnVsbCE9PSh0PW51bGw9PShpPXRoaXMuX2luc3RhbmNlLmNvbmZpZy5zZXNzaW9uX3JlY29yZGluZyk/dm9pZCAwOmkuZnVsbF9zbmFwc2hvdF9pbnRlcnZhbF9taWxsaXMpJiZ2b2lkIDAhPT10P3Q6b3N9Z2V0IFR0KCl7dmFyIHQ9dGhpcy5faW5zdGFuY2UuZ2V0X3Byb3BlcnR5KGt0KTtyZXR1cm4gQSh0KT90Om51bGx9Z2V0IE10KCl7dmFyIHQsaSxlPW51bGw9PSh0PXRoaXMuTSk/dm9pZCAwOnQuZGF0YVsobnVsbD09KGk9dGhpcy5NKT92b2lkIDA6aS5kYXRhLmxlbmd0aCktMV0se3Nlc3Npb25TdGFydFRpbWVzdGFtcDpyfT10aGlzLkl0LmNoZWNrQW5kR2V0U2Vzc2lvbkFuZFdpbmRvd0lkKCEwKTtyZXR1cm4gZT9lLnRpbWVzdGFtcC1yOm51bGx9Z2V0IEN0KCl7dmFyIGk9ISF0aGlzLl9pbnN0YW5jZS5nZXRfcHJvcGVydHkocHQpLGU9IXRoaXMuX2luc3RhbmNlLmNvbmZpZy5kaXNhYmxlX3Nlc3Npb25fcmVjb3JkaW5nO3JldHVybiB0JiZpJiZlfWdldCBPdCgpe3ZhciB0PSEhdGhpcy5faW5zdGFuY2UuZ2V0X3Byb3BlcnR5KGd0KSxpPXRoaXMuX2luc3RhbmNlLmNvbmZpZy5lbmFibGVfcmVjb3JkaW5nX2NvbnNvbGVfbG9nO3JldHVybiBudWxsIT1pP2k6dH1nZXQgRnQoKXt2YXIgdCxpLGUscixzLG4sbz10aGlzLl9pbnN0YW5jZS5jb25maWcuc2Vzc2lvbl9yZWNvcmRpbmcuY2FwdHVyZUNhbnZhcyxhPXRoaXMuX2luc3RhbmNlLmdldF9wcm9wZXJ0eShidCksbD1udWxsIT09KHQ9bnVsbCE9PShpPW51bGw9PW8/dm9pZCAwOm8ucmVjb3JkQ2FudmFzKSYmdm9pZCAwIT09aT9pOm51bGw9PWE/dm9pZCAwOmEuZW5hYmxlZCkmJnZvaWQgMCE9PXQmJnQsdT1udWxsIT09KGU9bnVsbCE9PShyPW51bGw9PW8/dm9pZCAwOm8uY2FudmFzRnBzKSYmdm9pZCAwIT09cj9yOm51bGw9PWE/dm9pZCAwOmEuZnBzKSYmdm9pZCAwIT09ZT9lOjQsaD1udWxsIT09KHM9bnVsbCE9PShuPW51bGw9PW8/dm9pZCAwOm8uY2FudmFzUXVhbGl0eSkmJnZvaWQgMCE9PW4/bjpudWxsPT1hP3ZvaWQgMDphLnF1YWxpdHkpJiZ2b2lkIDAhPT1zP3M6LjQ7aWYoXCJzdHJpbmdcIj09dHlwZW9mIGgpe3ZhciBkPXBhcnNlRmxvYXQoaCk7aD1pc05hTihkKT8uNDpkfXJldHVybntlbmFibGVkOmwsZnBzOlhlKHUsMCwxMixcImNhbnZhcyByZWNvcmRpbmcgZnBzXCIsNCkscXVhbGl0eTpYZShoLDAsMSxcImNhbnZhcyByZWNvcmRpbmcgcXVhbGl0eVwiLC40KX19Z2V0IEF0KCl7dmFyIHQsaSxlPXRoaXMuX2luc3RhbmNlLmdldF9wcm9wZXJ0eShfdCkscj17cmVjb3JkSGVhZGVyczpudWxsPT0odD10aGlzLl9pbnN0YW5jZS5jb25maWcuc2Vzc2lvbl9yZWNvcmRpbmcpP3ZvaWQgMDp0LnJlY29yZEhlYWRlcnMscmVjb3JkQm9keTpudWxsPT0oaT10aGlzLl9pbnN0YW5jZS5jb25maWcuc2Vzc2lvbl9yZWNvcmRpbmcpP3ZvaWQgMDppLnJlY29yZEJvZHl9LHM9KG51bGw9PXI/dm9pZCAwOnIucmVjb3JkSGVhZGVycyl8fChudWxsPT1lP3ZvaWQgMDplLnJlY29yZEhlYWRlcnMpLG49KG51bGw9PXI/dm9pZCAwOnIucmVjb3JkQm9keSl8fChudWxsPT1lP3ZvaWQgMDplLnJlY29yZEJvZHkpLG89SSh0aGlzLl9pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9wZXJmb3JtYW5jZSk/dGhpcy5faW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfcGVyZm9ybWFuY2UubmV0d29ya190aW1pbmc6dGhpcy5faW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfcGVyZm9ybWFuY2UsYT0hIShBKG8pP286bnVsbD09ZT92b2lkIDA6ZS5jYXB0dXJlUGVyZm9ybWFuY2UpO3JldHVybiBzfHxufHxhP3tyZWNvcmRIZWFkZXJzOnMscmVjb3JkQm9keTpuLHJlY29yZFBlcmZvcm1hbmNlOmF9OnZvaWQgMH1nZXQgRHQoKXt2YXIgdCxpLGUscixzLG4sbz10aGlzLl9pbnN0YW5jZS5nZXRfcHJvcGVydHkobXQpLGE9e21hc2tBbGxJbnB1dHM6bnVsbD09KHQ9dGhpcy5faW5zdGFuY2UuY29uZmlnLnNlc3Npb25fcmVjb3JkaW5nKT92b2lkIDA6dC5tYXNrQWxsSW5wdXRzLG1hc2tUZXh0U2VsZWN0b3I6bnVsbD09KGk9dGhpcy5faW5zdGFuY2UuY29uZmlnLnNlc3Npb25fcmVjb3JkaW5nKT92b2lkIDA6aS5tYXNrVGV4dFNlbGVjdG9yLGJsb2NrU2VsZWN0b3I6bnVsbD09KGU9dGhpcy5faW5zdGFuY2UuY29uZmlnLnNlc3Npb25fcmVjb3JkaW5nKT92b2lkIDA6ZS5ibG9ja1NlbGVjdG9yfSxsPW51bGwhPT0ocj1udWxsPT1hP3ZvaWQgMDphLm1hc2tBbGxJbnB1dHMpJiZ2b2lkIDAhPT1yP3I6bnVsbD09bz92b2lkIDA6by5tYXNrQWxsSW5wdXRzLHU9bnVsbCE9PShzPW51bGw9PWE/dm9pZCAwOmEubWFza1RleHRTZWxlY3RvcikmJnZvaWQgMCE9PXM/czpudWxsPT1vP3ZvaWQgMDpvLm1hc2tUZXh0U2VsZWN0b3IsaD1udWxsIT09KG49bnVsbD09YT92b2lkIDA6YS5ibG9ja1NlbGVjdG9yKSYmdm9pZCAwIT09bj9uOm51bGw9PW8/dm9pZCAwOm8uYmxvY2tTZWxlY3RvcjtyZXR1cm4gUihsKSYmUih1KSYmUihoKT92b2lkIDA6e21hc2tBbGxJbnB1dHM6bnVsbD09bHx8bCxtYXNrVGV4dFNlbGVjdG9yOnUsYmxvY2tTZWxlY3RvcjpofX1nZXQgTHQoKXt2YXIgdD10aGlzLl9pbnN0YW5jZS5nZXRfcHJvcGVydHkod3QpO3JldHVybiBGKHQpP3Q6bnVsbH1nZXQgTnQoKXt2YXIgdD10aGlzLl9pbnN0YW5jZS5nZXRfcHJvcGVydHkoeXQpO3JldHVybiBGKHQpP3Q6bnVsbH1nZXQgc3RhdHVzKCl7cmV0dXJuIHRoaXMuanQ/dGhpcy56dCh7cmVjZWl2ZWREZWNpZGU6dGhpcy5qdCxpc1JlY29yZGluZ0VuYWJsZWQ6dGhpcy5DdCxpc1NhbXBsZWQ6dGhpcy5UdCx1cmxUcmlnZ2VyTWF0Y2hpbmc6dGhpcy5VdCxldmVudFRyaWdnZXJNYXRjaGluZzp0aGlzLnF0LGxpbmtlZEZsYWdNYXRjaGluZzp0aGlzLkJ0LHNlc3Npb25JZDp0aGlzLnNlc3Npb25JZH0pOlVyfWNvbnN0cnVjdG9yKHQpe2lmKHRoaXMuenQ9dHMsdGhpcy5qdD0hMSx0aGlzLkh0PVtdLHRoaXMuV3Q9XCJ1bmtub3duXCIsdGhpcy5HdD1EYXRlLm5vdygpLHRoaXMuUnQ9bmV3IFlyLHRoaXMuSnQ9dm9pZCAwLHRoaXMuVnQ9dm9pZCAwLHRoaXMuS3Q9dm9pZCAwLHRoaXMuWXQ9dm9pZCAwLHRoaXMuWHQ9dm9pZCAwLHRoaXMuX2ZvcmNlQWxsb3dMb2NhbGhvc3ROZXR3b3JrQ2FwdHVyZT0hMSx0aGlzLlF0PSgpPT57dGhpcy5adCgpfSx0aGlzLnRpPSgpPT57dGhpcy5paShcImJyb3dzZXIgb2ZmbGluZVwiLHt9KX0sdGhpcy5laT0oKT0+e3RoaXMuaWkoXCJicm93c2VyIG9ubGluZVwiLHt9KX0sdGhpcy5yaT0oKT0+e2lmKG51bGwhPW8mJm8udmlzaWJpbGl0eVN0YXRlKXt2YXIgdD1cIndpbmRvdyBcIitvLnZpc2liaWxpdHlTdGF0ZTt0aGlzLmlpKHQse30pfX0sdGhpcy5faW5zdGFuY2U9dCx0aGlzLkV0PSExLHRoaXMuc2k9XCIvcy9cIix0aGlzLm5pPXZvaWQgMCx0aGlzLmp0PSExLCF0aGlzLl9pbnN0YW5jZS5zZXNzaW9uTWFuYWdlcil0aHJvdyBzcy5lcnJvcihcInN0YXJ0ZWQgd2l0aG91dCB2YWxpZCBzZXNzaW9uTWFuYWdlclwiKSxuZXcgRXJyb3IocnMrXCIgc3RhcnRlZCB3aXRob3V0IHZhbGlkIHNlc3Npb25NYW5hZ2VyLiBUaGlzIGlzIGEgYnVnLlwiKTtpZih0aGlzLl9pbnN0YW5jZS5jb25maWcuX19wcmV2aWV3X2V4cGVyaW1lbnRhbF9jb29raWVsZXNzX21vZGUpdGhyb3cgbmV3IEVycm9yKHJzK1wiIGNhbm5vdCBiZSB1c2VkIHdpdGggX19wcmV2aWV3X2V4cGVyaW1lbnRhbF9jb29raWVsZXNzX21vZGUuXCIpO3RoaXMuQnQ9bmV3IFFyKHRoaXMuX2luc3RhbmNlKSx0aGlzLlV0PW5ldyBYcih0aGlzLl9pbnN0YW5jZSksdGhpcy5xdD1uZXcgWnIodGhpcy5faW5zdGFuY2UpO3ZhcntzZXNzaW9uSWQ6aSx3aW5kb3dJZDplfT10aGlzLkl0LmNoZWNrQW5kR2V0U2Vzc2lvbkFuZFdpbmRvd0lkKCk7dGhpcy5rdD1pLHRoaXMub2k9ZSx0aGlzLk09dGhpcy5haSgpLHRoaXMueHQ+PXRoaXMuSXQuc2Vzc2lvblRpbWVvdXRNcyYmc3Mud2FybihcInNlc3Npb25faWRsZV90aHJlc2hvbGRfbXMgKFwiK3RoaXMueHQrXCIpIGlzIGdyZWF0ZXIgdGhhbiB0aGUgc2Vzc2lvbiB0aW1lb3V0IChcIit0aGlzLkl0LnNlc3Npb25UaW1lb3V0TXMrXCIpLiBTZXNzaW9uIHdpbGwgbmV2ZXIgYmUgZGV0ZWN0ZWQgYXMgaWRsZVwiKX1zdGFydElmRW5hYmxlZE9yU3RvcChpKXt0aGlzLkN0Pyh0aGlzLmxpKGkpLHN0KHQsXCJiZWZvcmV1bmxvYWRcIix0aGlzLlF0KSxzdCh0LFwib2ZmbGluZVwiLHRoaXMudGkpLHN0KHQsXCJvbmxpbmVcIix0aGlzLmVpKSxzdCh0LFwidmlzaWJpbGl0eWNoYW5nZVwiLHRoaXMucmkpLHRoaXMudWkoKSx0aGlzLmhpKCksTyh0aGlzLkp0KSYmKHRoaXMuSnQ9dGhpcy5faW5zdGFuY2Uub24oXCJldmVudENhcHR1cmVkXCIsKHQ9Pnt0cnl7aWYoXCIkcGFnZXZpZXdcIj09PXQuZXZlbnQpe3ZhciBpPW51bGwhPXQmJnQucHJvcGVydGllcy4kY3VycmVudF91cmw/dGhpcy5kaShudWxsPT10P3ZvaWQgMDp0LnByb3BlcnRpZXMuJGN1cnJlbnRfdXJsKTpcIlwiO2lmKCFpKXJldHVybjt0aGlzLmlpKFwiJHBhZ2V2aWV3XCIse2hyZWY6aX0pfX1jYXRjaCh0KXtzcy5lcnJvcihcIkNvdWxkIG5vdCBhZGQgJHBhZ2V2aWV3IHRvIHJyd2ViIHNlc3Npb25cIix0KX19KSkpLHRoaXMuVnR8fCh0aGlzLlZ0PXRoaXMuSXQub25TZXNzaW9uSWQoKCh0LGksZSk9Pnt2YXIgcixzO2UmJih0aGlzLmlpKFwiJHNlc3Npb25faWRfY2hhbmdlXCIse3Nlc3Npb25JZDp0LHdpbmRvd0lkOmksY2hhbmdlUmVhc29uOmV9KSxudWxsPT0ocj10aGlzLl9pbnN0YW5jZSl8fG51bGw9PShyPXIucGVyc2lzdGVuY2UpfHxyLnVucmVnaXN0ZXIoRXQpLG51bGw9PShzPXRoaXMuX2luc3RhbmNlKXx8bnVsbD09KHM9cy5wZXJzaXN0ZW5jZSl8fHMudW5yZWdpc3Rlcih4dCkpfSkpKSk6dGhpcy5zdG9wUmVjb3JkaW5nKCl9c3RvcFJlY29yZGluZygpe3ZhciBpLGUscixzO3RoaXMuRXQmJnRoaXMubmkmJih0aGlzLm5pKCksdGhpcy5uaT12b2lkIDAsdGhpcy5FdD0hMSxudWxsPT10fHx0LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJiZWZvcmV1bmxvYWRcIix0aGlzLlF0KSxudWxsPT10fHx0LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJvZmZsaW5lXCIsdGhpcy50aSksbnVsbD09dHx8dC5yZW1vdmVFdmVudExpc3RlbmVyKFwib25saW5lXCIsdGhpcy5laSksbnVsbD09dHx8dC5yZW1vdmVFdmVudExpc3RlbmVyKFwidmlzaWJpbGl0eWNoYW5nZVwiLHRoaXMucmkpLHRoaXMuYWkoKSxjbGVhckludGVydmFsKHRoaXMudmkpLG51bGw9PShpPXRoaXMuSnQpfHxpLmNhbGwodGhpcyksdGhpcy5KdD12b2lkIDAsbnVsbD09KGU9dGhpcy5YdCl8fGUuY2FsbCh0aGlzKSx0aGlzLlh0PXZvaWQgMCxudWxsPT0ocj10aGlzLlZ0KXx8ci5jYWxsKHRoaXMpLHRoaXMuVnQ9dm9pZCAwLG51bGw9PShzPXRoaXMuWXQpfHxzLmNhbGwodGhpcyksdGhpcy5ZdD12b2lkIDAsdGhpcy5xdC5zdG9wKCksdGhpcy5VdC5zdG9wKCksdGhpcy5CdC5zdG9wKCksc3MuaW5mbyhcInN0b3BwZWRcIikpfWNpKCl7dmFyIHQ7bnVsbD09KHQ9dGhpcy5faW5zdGFuY2UucGVyc2lzdGVuY2UpfHx0LnVucmVnaXN0ZXIoa3QpfWZpKHQpe3ZhciBpLGU9dGhpcy5rdCE9PXQscj10aGlzLkx0O2lmKEYocikpe3ZhciBzPXRoaXMuVHQsbj1lfHwhQShzKSxvPW4/THIodCxyKTpzO24mJihvP3RoaXMucGkoanIpOnNzLndhcm4oXCJTYW1wbGUgcmF0ZSAoXCIrcitcIikgaGFzIGRldGVybWluZWQgdGhhdCB0aGlzIHNlc3Npb25JZCAoXCIrdCtcIikgd2lsbCBub3QgYmUgc2VudCB0byB0aGUgc2VydmVyLlwiKSx0aGlzLmlpKFwic2FtcGxpbmdEZWNpc2lvbk1hZGVcIix7c2FtcGxlUmF0ZTpyLGlzU2FtcGxlZDpvfSkpLG51bGw9PShpPXRoaXMuX2luc3RhbmNlLnBlcnNpc3RlbmNlKXx8aS5yZWdpc3Rlcih7W2t0XTpvfSl9ZWxzZSB0aGlzLmNpKCl9b25SZW1vdGVDb25maWcodCl7dmFyIGksZSxyLHM7KHRoaXMuaWkoXCIkcmVtb3RlX2NvbmZpZ19yZWNlaXZlZFwiLHQpLHRoaXMuZ2kodCksbnVsbCE9KGk9dC5zZXNzaW9uUmVjb3JkaW5nKSYmaS5lbmRwb2ludCkmJih0aGlzLnNpPW51bGw9PShzPXQuc2Vzc2lvblJlY29yZGluZyk/dm9pZCAwOnMuZW5kcG9pbnQpO3RoaXMudWkoKSxcImFueVwiPT09KG51bGw9PShlPXQuc2Vzc2lvblJlY29yZGluZyk/dm9pZCAwOmUudHJpZ2dlck1hdGNoVHlwZSk/KHRoaXMuenQ9aXMsdGhpcy5SdD1uZXcgVnIoW3RoaXMucXQsdGhpcy5VdF0pKToodGhpcy56dD1lcyx0aGlzLlJ0PW5ldyBLcihbdGhpcy5xdCx0aGlzLlV0XSkpLHRoaXMuX2luc3RhbmNlLnJlZ2lzdGVyX2Zvcl9zZXNzaW9uKHskc2RrX2RlYnVnX3JlcGxheV9yZW1vdGVfdHJpZ2dlcl9tYXRjaGluZ19jb25maWc6bnVsbD09KHI9dC5zZXNzaW9uUmVjb3JkaW5nKT92b2lkIDA6ci50cmlnZ2VyTWF0Y2hUeXBlfSksdGhpcy5VdC5vblJlbW90ZUNvbmZpZyh0KSx0aGlzLnF0Lm9uUmVtb3RlQ29uZmlnKHQpLHRoaXMuQnQub25SZW1vdGVDb25maWcodCwoKHQsaSk9Pnt0aGlzLnBpKFwibGlua2VkX2ZsYWdfbWF0Y2hlZFwiLHtmbGFnOnQsdmFyaWFudDppfSl9KSksdGhpcy5qdD0hMCx0aGlzLnN0YXJ0SWZFbmFibGVkT3JTdG9wKCl9dWkoKXtGKHRoaXMuTHQpJiZPKHRoaXMuWXQpJiYodGhpcy5ZdD10aGlzLkl0Lm9uU2Vzc2lvbklkKCh0PT57dGhpcy5maSh0KX0pKSl9Z2kodCl7aWYodGhpcy5faW5zdGFuY2UucGVyc2lzdGVuY2Upe3ZhciBpLGU9dGhpcy5faW5zdGFuY2UucGVyc2lzdGVuY2Uscj0oKT0+e3ZhciBpLHIscyxuLG8sYSxsLHUsaCxkPW51bGw9PShpPXQuc2Vzc2lvblJlY29yZGluZyk/dm9pZCAwOmkuc2FtcGxlUmF0ZSx2PU8oZCk/bnVsbDpwYXJzZUZsb2F0KGQpO08odikmJnRoaXMuY2koKTt2YXIgYz1udWxsPT0ocj10LnNlc3Npb25SZWNvcmRpbmcpP3ZvaWQgMDpyLm1pbmltdW1EdXJhdGlvbk1pbGxpc2Vjb25kcztlLnJlZ2lzdGVyKHtbcHRdOiEhdC5zZXNzaW9uUmVjb3JkaW5nLFtndF06bnVsbD09KHM9dC5zZXNzaW9uUmVjb3JkaW5nKT92b2lkIDA6cy5jb25zb2xlTG9nUmVjb3JkaW5nRW5hYmxlZCxbX3RdOkIoe2NhcHR1cmVQZXJmb3JtYW5jZTp0LmNhcHR1cmVQZXJmb3JtYW5jZX0sbnVsbD09KG49dC5zZXNzaW9uUmVjb3JkaW5nKT92b2lkIDA6bi5uZXR3b3JrUGF5bG9hZENhcHR1cmUpLFttdF06bnVsbD09KG89dC5zZXNzaW9uUmVjb3JkaW5nKT92b2lkIDA6by5tYXNraW5nLFtidF06e2VuYWJsZWQ6bnVsbD09KGE9dC5zZXNzaW9uUmVjb3JkaW5nKT92b2lkIDA6YS5yZWNvcmRDYW52YXMsZnBzOm51bGw9PShsPXQuc2Vzc2lvblJlY29yZGluZyk/dm9pZCAwOmwuY2FudmFzRnBzLHF1YWxpdHk6bnVsbD09KHU9dC5zZXNzaW9uUmVjb3JkaW5nKT92b2lkIDA6dS5jYW52YXNRdWFsaXR5fSxbd3RdOnYsW3l0XTpSKGMpP251bGw6YyxbU3RdOm51bGw9PShoPXQuc2Vzc2lvblJlY29yZGluZyk/dm9pZCAwOmguc2NyaXB0Q29uZmlnfSl9O3IoKSxudWxsPT0oaT10aGlzLkt0KXx8aS5jYWxsKHRoaXMpLHRoaXMuS3Q9dGhpcy5JdC5vblNlc3Npb25JZChyKX19bG9nKHQsaSl7dmFyIGU7dm9pZCAwPT09aSYmKGk9XCJsb2dcIiksbnVsbD09KGU9dGhpcy5faW5zdGFuY2Uuc2Vzc2lvblJlY29yZGluZyl8fGUub25SUndlYkVtaXQoe3R5cGU6NixkYXRhOntwbHVnaW46XCJycndlYi9jb25zb2xlQDFcIixwYXlsb2FkOntsZXZlbDppLHRyYWNlOltdLHBheWxvYWQ6W0pTT04uc3RyaW5naWZ5KHQpXX19LHRpbWVzdGFtcDpEYXRlLm5vdygpfSl9bGkodCl7aWYoIVIoT2JqZWN0LmFzc2lnbikmJiFSKEFycmF5LmZyb20pJiYhKHRoaXMuRXR8fHRoaXMuX2luc3RhbmNlLmNvbmZpZy5kaXNhYmxlX3Nlc3Npb25fcmVjb3JkaW5nfHx0aGlzLl9pbnN0YW5jZS5jb25zZW50LmlzT3B0ZWRPdXQoKSkpe3ZhciBpO2lmKHRoaXMuRXQ9ITAsdGhpcy5JdC5jaGVja0FuZEdldFNlc3Npb25BbmRXaW5kb3dJZCgpLG5zKCkpdGhpcy5taSgpO2Vsc2UgbnVsbD09KGk9di5fX1Bvc3Rob2dFeHRlbnNpb25zX18pfHxudWxsPT1pLmxvYWRFeHRlcm5hbERlcGVuZGVuY3l8fGkubG9hZEV4dGVybmFsRGVwZW5kZW5jeSh0aGlzLl9pbnN0YW5jZSx0aGlzLmJpLCh0PT57aWYodClyZXR1cm4gc3MuZXJyb3IoXCJjb3VsZCBub3QgbG9hZCByZWNvcmRlclwiLHQpO3RoaXMubWkoKX0pKTtzcy5pbmZvKFwic3RhcnRpbmdcIiksdGhpcy5zdGF0dXM9PT16ciYmdGhpcy5waSh0fHxcInJlY29yZGluZ19pbml0aWFsaXplZFwiKX19Z2V0IGJpKCl7dmFyIHQ7cmV0dXJuKG51bGw9PSh0PXRoaXMuX2luc3RhbmNlKXx8bnVsbD09KHQ9dC5wZXJzaXN0ZW5jZSl8fG51bGw9PSh0PXQuZ2V0X3Byb3BlcnR5KFN0KSk/dm9pZCAwOnQuc2NyaXB0KXx8XCJyZWNvcmRlclwifXdpKHQpe3ZhciBpO3JldHVybiAzPT09dC50eXBlJiYtMSE9PWFzLmluZGV4T2YobnVsbD09KGk9dC5kYXRhKT92b2lkIDA6aS5zb3VyY2UpfXlpKHQpe3ZhciBpPXRoaXMud2kodCk7aXx8dGhpcy5XdHx8dC50aW1lc3RhbXAtdGhpcy5HdD50aGlzLnh0JiYodGhpcy5XdD0hMCxjbGVhckludGVydmFsKHRoaXMudmkpLHRoaXMuaWkoXCJzZXNzaW9uSWRsZVwiLHtldmVudFRpbWVzdGFtcDp0LnRpbWVzdGFtcCxsYXN0QWN0aXZpdHlUaW1lc3RhbXA6dGhpcy5HdCx0aHJlc2hvbGQ6dGhpcy54dCxidWZmZXJMZW5ndGg6dGhpcy5NLmRhdGEubGVuZ3RoLGJ1ZmZlclNpemU6dGhpcy5NLnNpemV9KSx0aGlzLlp0KCkpO3ZhciBlPSExO2lmKGkmJih0aGlzLkd0PXQudGltZXN0YW1wLHRoaXMuV3QpKXt2YXIgcj1cInVua25vd25cIj09PXRoaXMuV3Q7dGhpcy5XdD0hMSxyfHwodGhpcy5paShcInNlc3Npb25Ob0xvbmdlcklkbGVcIix7cmVhc29uOlwidXNlciBhY3Rpdml0eVwiLHR5cGU6dC50eXBlfSksZT0hMCl9aWYoIXRoaXMuV3Qpe3Zhcnt3aW5kb3dJZDpzLHNlc3Npb25JZDpufT10aGlzLkl0LmNoZWNrQW5kR2V0U2Vzc2lvbkFuZFdpbmRvd0lkKCFpLHQudGltZXN0YW1wKSxvPXRoaXMua3QhPT1uLGE9dGhpcy5vaSE9PXM7dGhpcy5vaT1zLHRoaXMua3Q9bixvfHxhPyh0aGlzLnN0b3BSZWNvcmRpbmcoKSx0aGlzLnN0YXJ0SWZFbmFibGVkT3JTdG9wKFwic2Vzc2lvbl9pZF9jaGFuZ2VkXCIpKTplJiZ0aGlzLlNpKCl9fSRpKHQpe3RyeXtyZXR1cm4gdC5ycndlYk1ldGhvZCgpLCEwfWNhdGNoKGkpe3JldHVybiB0aGlzLkh0Lmxlbmd0aDwxMD90aGlzLkh0LnB1c2goe2VucXVldWVkQXQ6dC5lbnF1ZXVlZEF0fHxEYXRlLm5vdygpLGF0dGVtcHQ6dC5hdHRlbXB0KysscnJ3ZWJNZXRob2Q6dC5ycndlYk1ldGhvZH0pOnNzLndhcm4oXCJjb3VsZCBub3QgZW1pdCBxdWV1ZWQgcnJ3ZWIgZXZlbnQuXCIsaSx0KSwhMX19aWkodCxpKXtyZXR1cm4gdGhpcy4kaShscygoKCk9Pm5zKCkuYWRkQ3VzdG9tRXZlbnQodCxpKSkpKX1raSgpe3JldHVybiB0aGlzLiRpKGxzKCgoKT0+bnMoKS50YWtlRnVsbFNuYXBzaG90KCkpKSl9bWkoKXt2YXIgdCxpLGUscixzPXtibG9ja0NsYXNzOlwicGgtbm8tY2FwdHVyZVwiLGJsb2NrU2VsZWN0b3I6dm9pZCAwLGlnbm9yZUNsYXNzOlwicGgtaWdub3JlLWlucHV0XCIsbWFza1RleHRDbGFzczpcInBoLW1hc2tcIixtYXNrVGV4dFNlbGVjdG9yOnZvaWQgMCxtYXNrVGV4dEZuOnZvaWQgMCxtYXNrQWxsSW5wdXRzOiEwLG1hc2tJbnB1dE9wdGlvbnM6e3Bhc3N3b3JkOiEwfSxtYXNrSW5wdXRGbjp2b2lkIDAsc2xpbURPTU9wdGlvbnM6e30sY29sbGVjdEZvbnRzOiExLGlubGluZVN0eWxlc2hlZXQ6ITAscmVjb3JkQ3Jvc3NPcmlnaW5JZnJhbWVzOiExfSxuPXRoaXMuX2luc3RhbmNlLmNvbmZpZy5zZXNzaW9uX3JlY29yZGluZztmb3IodmFyW28sYV1vZiBPYmplY3QuZW50cmllcyhufHx7fSkpbyBpbiBzJiYoXCJtYXNrSW5wdXRPcHRpb25zXCI9PT1vP3MubWFza0lucHV0T3B0aW9ucz1CKHtwYXNzd29yZDohMH0sYSk6c1tvXT1hKTsodGhpcy5GdCYmdGhpcy5GdC5lbmFibGVkJiYocy5yZWNvcmRDYW52YXM9ITAscy5zYW1wbGluZz17Y2FudmFzOnRoaXMuRnQuZnBzfSxzLmRhdGFVUkxPcHRpb25zPXt0eXBlOlwiaW1hZ2Uvd2VicFwiLHF1YWxpdHk6dGhpcy5GdC5xdWFsaXR5fSksdGhpcy5EdCkmJihzLm1hc2tBbGxJbnB1dHM9bnVsbD09PShpPXRoaXMuRHQubWFza0FsbElucHV0cyl8fHZvaWQgMD09PWl8fGkscy5tYXNrVGV4dFNlbGVjdG9yPW51bGwhPT0oZT10aGlzLkR0Lm1hc2tUZXh0U2VsZWN0b3IpJiZ2b2lkIDAhPT1lP2U6dm9pZCAwLHMuYmxvY2tTZWxlY3Rvcj1udWxsIT09KHI9dGhpcy5EdC5ibG9ja1NlbGVjdG9yKSYmdm9pZCAwIT09cj9yOnZvaWQgMCk7dmFyIGw9bnMoKTtpZihsKXt0aGlzLnhpPW51bGwhPT0odD10aGlzLnhpKSYmdm9pZCAwIT09dD90Om5ldyBRZShsLHtyZWZpbGxSYXRlOnRoaXMuX2luc3RhbmNlLmNvbmZpZy5zZXNzaW9uX3JlY29yZGluZy5fX211dGF0aW9uUmF0ZUxpbWl0ZXJSZWZpbGxSYXRlLGJ1Y2tldFNpemU6dGhpcy5faW5zdGFuY2UuY29uZmlnLnNlc3Npb25fcmVjb3JkaW5nLl9fbXV0YXRpb25SYXRlTGltaXRlckJ1Y2tldFNpemUsb25CbG9ja2VkTm9kZToodCxpKT0+e3ZhciBlPVwiVG9vIG1hbnkgbXV0YXRpb25zIG9uIG5vZGUgJ1wiK3QrXCInLiBSYXRlIGxpbWl0aW5nLiBUaGlzIGNvdWxkIGJlIGR1ZSB0byBTVkcgYW5pbWF0aW9ucyBvciBzb21ldGhpbmcgc2ltaWxhclwiO3NzLmluZm8oZSx7bm9kZTppfSksdGhpcy5sb2cocnMrXCIgXCIrZSxcIndhcm5cIil9fSk7dmFyIHU9dGhpcy5FaSgpO3RoaXMubmk9bChCKHtlbWl0OnQ9Pnt0aGlzLm9uUlJ3ZWJFbWl0KHQpfSxwbHVnaW5zOnV9LHMpKSx0aGlzLkd0PURhdGUubm93KCksdGhpcy5XdD1BKHRoaXMuV3QpP3RoaXMuV3Q6XCJ1bmtub3duXCIsdGhpcy5paShcIiRzZXNzaW9uX29wdGlvbnNcIix7c2Vzc2lvblJlY29yZGluZ09wdGlvbnM6cyxhY3RpdmVQbHVnaW5zOnUubWFwKCh0PT5udWxsPT10P3ZvaWQgMDp0Lm5hbWUpKX0pLHRoaXMuaWkoXCIkcG9zdGhvZ19jb25maWdcIix7Y29uZmlnOnRoaXMuX2luc3RhbmNlLmNvbmZpZ30pfWVsc2Ugc3MuZXJyb3IoXCJvblNjcmlwdExvYWRlZCB3YXMgY2FsbGVkIGJ1dCBycndlYlJlY29yZCBpcyBub3QgYXZhaWxhYmxlLiBUaGlzIGluZGljYXRlcyBzb21ldGhpbmcgaGFzIGdvbmUgd3JvbmcuXCIpfVNpKCl7aWYodGhpcy52aSYmY2xlYXJJbnRlcnZhbCh0aGlzLnZpKSwhMCE9PXRoaXMuV3Qpe3ZhciB0PXRoaXMuUHQ7dCYmKHRoaXMudmk9c2V0SW50ZXJ2YWwoKCgpPT57dGhpcy5raSgpfSksdCkpfX1FaSgpe3ZhciB0LGksZT1bXSxyPW51bGw9PSh0PXYuX19Qb3N0aG9nRXh0ZW5zaW9uc19fKXx8bnVsbD09KHQ9dC5ycndlYlBsdWdpbnMpP3ZvaWQgMDp0LmdldFJlY29yZENvbnNvbGVQbHVnaW47ciYmdGhpcy5PdCYmZS5wdXNoKHIoKSk7dmFyIHM9bnVsbD09KGk9di5fX1Bvc3Rob2dFeHRlbnNpb25zX18pfHxudWxsPT0oaT1pLnJyd2ViUGx1Z2lucyk/dm9pZCAwOmkuZ2V0UmVjb3JkTmV0d29ya1BsdWdpbjt0aGlzLkF0JiZFKHMpJiYoIXdpLmluY2x1ZGVzKGxvY2F0aW9uLmhvc3RuYW1lKXx8dGhpcy5fZm9yY2VBbGxvd0xvY2FsaG9zdE5ldHdvcmtDYXB0dXJlP2UucHVzaChzKFllKHRoaXMuX2luc3RhbmNlLmNvbmZpZyx0aGlzLkF0KSkpOnNzLmluZm8oXCJOZXR3b3JrQ2FwdHVyZSBub3Qgc3RhcnRlZCBiZWNhdXNlIHdlIGFyZSBvbiBsb2NhbGhvc3QuXCIpKTtyZXR1cm4gZX1vblJSd2ViRW1pdCh0KXt2YXIgaTtpZih0aGlzLklpKCksdCYmSSh0KSl7aWYodC50eXBlPT09emUuTWV0YSl7dmFyIGU9dGhpcy5kaSh0LmRhdGEuaHJlZik7aWYodGhpcy5QaT1lLCFlKXJldHVybjt0LmRhdGEuaHJlZj1lfWVsc2UgdGhpcy5SaSgpO2lmKHRoaXMuVXQuY2hlY2tVcmxUcmlnZ2VyQ29uZGl0aW9ucygoKCk9PnRoaXMuVGkoKSksKCgpPT50aGlzLk1pKCkpLCh0PT50aGlzLkNpKHQpKSksIXRoaXMuVXQudXJsQmxvY2tlZHx8KHI9dCkudHlwZT09PXplLkN1c3RvbSYmXCJyZWNvcmRpbmcgcGF1c2VkXCI9PT1yLmRhdGEudGFnKXt2YXIgcjt0LnR5cGU9PT16ZS5GdWxsU25hcHNob3QmJnRoaXMuU2koKSx0LnR5cGU9PT16ZS5GdWxsU25hcHNob3QmJnRoaXMuanQmJnRoaXMuUnQudHJpZ2dlclN0YXR1cyh0aGlzLnNlc3Npb25JZCk9PT1XciYmdGhpcy5haSgpO3ZhciBzPXRoaXMueGk/dGhpcy54aS50aHJvdHRsZU11dGF0aW9ucyh0KTp0O2lmKHMpe3ZhciBuPWZ1bmN0aW9uKHQpe3ZhciBpPXQ7aWYoaSYmSShpKSYmNj09PWkudHlwZSYmSShpLmRhdGEpJiZcInJyd2ViL2NvbnNvbGVAMVwiPT09aS5kYXRhLnBsdWdpbil7aS5kYXRhLnBheWxvYWQucGF5bG9hZC5sZW5ndGg+MTAmJihpLmRhdGEucGF5bG9hZC5wYXlsb2FkPWkuZGF0YS5wYXlsb2FkLnBheWxvYWQuc2xpY2UoMCwxMCksaS5kYXRhLnBheWxvYWQucGF5bG9hZC5wdXNoKFwiLi4uW3RydW5jYXRlZF1cIikpO2Zvcih2YXIgZT1bXSxyPTA7cjxpLmRhdGEucGF5bG9hZC5wYXlsb2FkLmxlbmd0aDtyKyspaS5kYXRhLnBheWxvYWQucGF5bG9hZFtyXSYmaS5kYXRhLnBheWxvYWQucGF5bG9hZFtyXS5sZW5ndGg+MmUzP2UucHVzaChpLmRhdGEucGF5bG9hZC5wYXlsb2FkW3JdLnNsaWNlKDAsMmUzKStcIi4uLlt0cnVuY2F0ZWRdXCIpOmUucHVzaChpLmRhdGEucGF5bG9hZC5wYXlsb2FkW3JdKTtyZXR1cm4gaS5kYXRhLnBheWxvYWQucGF5bG9hZD1lLHR9cmV0dXJuIHR9KHMpO2lmKHRoaXMueWkobiksITAhPT10aGlzLld0fHxocyhuKSl7aWYoaHMobikpe3ZhciBvPW4uZGF0YS5wYXlsb2FkO2lmKG8pe3ZhciBhPW8ubGFzdEFjdGl2aXR5VGltZXN0YW1wLGw9by50aHJlc2hvbGQ7bi50aW1lc3RhbXA9YStsfX12YXIgdT1udWxsPT09KGk9dGhpcy5faW5zdGFuY2UuY29uZmlnLnNlc3Npb25fcmVjb3JkaW5nLmNvbXByZXNzX2V2ZW50cyl8fHZvaWQgMD09PWl8fGk/ZnVuY3Rpb24odCl7aWYoTmUodCk8MTAyNClyZXR1cm4gdDt0cnl7aWYodC50eXBlPT09emUuRnVsbFNuYXBzaG90KXJldHVybiBCKHt9LHQse2RhdGE6dXModC5kYXRhKSxjdjpcIjIwMjQtMTBcIn0pO2lmKHQudHlwZT09PXplLkluY3JlbWVudGFsU25hcHNob3QmJnQuZGF0YS5zb3VyY2U9PT1VZS5NdXRhdGlvbilyZXR1cm4gQih7fSx0LHtjdjpcIjIwMjQtMTBcIixkYXRhOkIoe30sdC5kYXRhLHt0ZXh0czp1cyh0LmRhdGEudGV4dHMpLGF0dHJpYnV0ZXM6dXModC5kYXRhLmF0dHJpYnV0ZXMpLHJlbW92ZXM6dXModC5kYXRhLnJlbW92ZXMpLGFkZHM6dXModC5kYXRhLmFkZHMpfSl9KTtpZih0LnR5cGU9PT16ZS5JbmNyZW1lbnRhbFNuYXBzaG90JiZ0LmRhdGEuc291cmNlPT09VWUuU3R5bGVTaGVldFJ1bGUpcmV0dXJuIEIoe30sdCx7Y3Y6XCIyMDI0LTEwXCIsZGF0YTpCKHt9LHQuZGF0YSx7YWRkczp0LmRhdGEuYWRkcz91cyh0LmRhdGEuYWRkcyk6dm9pZCAwLHJlbW92ZXM6dC5kYXRhLnJlbW92ZXM/dXModC5kYXRhLnJlbW92ZXMpOnZvaWQgMH0pfSl9Y2F0Y2godCl7c3MuZXJyb3IoXCJjb3VsZCBub3QgY29tcHJlc3MgZXZlbnQgLSB3aWxsIHVzZSB1bmNvbXByZXNzZWQgZXZlbnRcIix0KX1yZXR1cm4gdH0obik6bixoPXskc25hcHNob3RfYnl0ZXM6TmUodSksJHNuYXBzaG90X2RhdGE6dSwkc2Vzc2lvbl9pZDp0aGlzLmt0LCR3aW5kb3dfaWQ6dGhpcy5vaX07dGhpcy5zdGF0dXMhPT1Ocj90aGlzLk9pKGgpOnRoaXMuYWkoKX19fX19UmkoKXtpZighdGhpcy5faW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfcGFnZXZpZXcmJnQpe3ZhciBpPXRoaXMuZGkodC5sb2NhdGlvbi5ocmVmKTt0aGlzLlBpIT09aSYmKHRoaXMuaWkoXCIkdXJsX2NoYW5nZWRcIix7aHJlZjppfSksdGhpcy5QaT1pKX19SWkoKXtpZih0aGlzLkh0Lmxlbmd0aCl7dmFyIHQ9Wy4uLnRoaXMuSHRdO3RoaXMuSHQ9W10sdC5mb3JFYWNoKCh0PT57RGF0ZS5ub3coKS10LmVucXVldWVkQXQ8PTJlMyYmdGhpcy4kaSh0KX0pKX19ZGkodCl7dmFyIGk9dGhpcy5faW5zdGFuY2UuY29uZmlnLnNlc3Npb25fcmVjb3JkaW5nO2lmKGkubWFza05ldHdvcmtSZXF1ZXN0Rm4pe3ZhciBlLHI9e3VybDp0fTtyZXR1cm4gbnVsbD09KGU9cj1pLm1hc2tOZXR3b3JrUmVxdWVzdEZuKHIpKT92b2lkIDA6ZS51cmx9cmV0dXJuIHR9YWkoKXtyZXR1cm4gdGhpcy5NPXtzaXplOjAsZGF0YTpbXSxzZXNzaW9uSWQ6dGhpcy5rdCx3aW5kb3dJZDp0aGlzLm9pfSx0aGlzLk19WnQoKXt0aGlzLkZpJiYoY2xlYXJUaW1lb3V0KHRoaXMuRmkpLHRoaXMuRmk9dm9pZCAwKTt2YXIgdD10aGlzLk50LGk9dGhpcy5NdCxlPUYoaSkmJmk+PTAscj1GKHQpJiZlJiZpPHQ7aWYodGhpcy5zdGF0dXM9PT1Vcnx8dGhpcy5zdGF0dXM9PT1xcnx8dGhpcy5zdGF0dXM9PT1Ocnx8cilyZXR1cm4gdGhpcy5GaT1zZXRUaW1lb3V0KCgoKT0+e3RoaXMuWnQoKX0pLDJlMyksdGhpcy5NO3RoaXMuTS5kYXRhLmxlbmd0aD4wJiZqZSh0aGlzLk0pLmZvckVhY2goKHQ9Pnt0aGlzLkFpKHskc25hcHNob3RfYnl0ZXM6dC5zaXplLCRzbmFwc2hvdF9kYXRhOnQuZGF0YSwkc2Vzc2lvbl9pZDp0LnNlc3Npb25JZCwkd2luZG93X2lkOnQud2luZG93SWQsJGxpYjpcIndlYlwiLCRsaWJfdmVyc2lvbjpjLkxJQl9WRVJTSU9OfSl9KSk7cmV0dXJuIHRoaXMuYWkoKX1PaSh0KXt2YXIgaSxlPTIrKChudWxsPT0oaT10aGlzLk0pP3ZvaWQgMDppLmRhdGEubGVuZ3RoKXx8MCk7IXRoaXMuV3QmJih0aGlzLk0uc2l6ZSt0LiRzbmFwc2hvdF9ieXRlcytlPjk0MzcxOC40fHx0aGlzLk0uc2Vzc2lvbklkIT09dGhpcy5rdCkmJih0aGlzLk09dGhpcy5adCgpKSx0aGlzLk0uc2l6ZSs9dC4kc25hcHNob3RfYnl0ZXMsdGhpcy5NLmRhdGEucHVzaCh0LiRzbmFwc2hvdF9kYXRhKSx0aGlzLkZpfHx0aGlzLld0fHwodGhpcy5GaT1zZXRUaW1lb3V0KCgoKT0+e3RoaXMuWnQoKX0pLDJlMykpfUFpKHQpe3RoaXMuX2luc3RhbmNlLmNhcHR1cmUoXCIkc25hcHNob3RcIix0LHtfdXJsOnRoaXMuX2luc3RhbmNlLnJlcXVlc3RSb3V0ZXIuZW5kcG9pbnRGb3IoXCJhcGlcIix0aGlzLnNpKSxfbm9UcnVuY2F0ZTohMCxfYmF0Y2hLZXk6XCJyZWNvcmRpbmdzXCIsc2tpcF9jbGllbnRfcmF0ZV9saW1pdGluZzohMH0pfUNpKHQpe3ZhciBpO3RoaXMuUnQudHJpZ2dlclN0YXR1cyh0aGlzLnNlc3Npb25JZCk9PT1XciYmKG51bGw9PShpPXRoaXMuX2luc3RhbmNlKXx8bnVsbD09KGk9aS5wZXJzaXN0ZW5jZSl8fGkucmVnaXN0ZXIoe1tcInVybFwiPT09dD94dDpFdF06dGhpcy5rdH0pLHRoaXMuWnQoKSx0aGlzLnBpKHQrXCJfdHJpZ2dlcl9tYXRjaGVkXCIpKX1UaSgpe3RoaXMuVXQudXJsQmxvY2tlZHx8KHRoaXMuVXQudXJsQmxvY2tlZD0hMCxjbGVhckludGVydmFsKHRoaXMudmkpLHNzLmluZm8oXCJyZWNvcmRpbmcgcGF1c2VkIGR1ZSB0byBVUkwgYmxvY2tlclwiKSx0aGlzLmlpKFwicmVjb3JkaW5nIHBhdXNlZFwiLHtyZWFzb246XCJ1cmwgYmxvY2tlclwifSkpfU1pKCl7dGhpcy5VdC51cmxCbG9ja2VkJiYodGhpcy5VdC51cmxCbG9ja2VkPSExLHRoaXMua2koKSx0aGlzLlNpKCksdGhpcy5paShcInJlY29yZGluZyByZXN1bWVkXCIse3JlYXNvbjpcImxlZnQgYmxvY2tlZCB1cmxcIn0pLHNzLmluZm8oXCJyZWNvcmRpbmcgcmVzdW1lZFwiKSl9aGkoKXswIT09dGhpcy5xdC5TdC5sZW5ndGgmJk8odGhpcy5YdCkmJih0aGlzLlh0PXRoaXMuX2luc3RhbmNlLm9uKFwiZXZlbnRDYXB0dXJlZFwiLCh0PT57dHJ5e3RoaXMucXQuU3QuaW5jbHVkZXModC5ldmVudCkmJnRoaXMuQ2koXCJldmVudFwiKX1jYXRjaCh0KXtzcy5lcnJvcihcIkNvdWxkIG5vdCBhY3RpdmF0ZSBldmVudCB0cmlnZ2VyXCIsdCl9fSkpKX1vdmVycmlkZUxpbmtlZEZsYWcoKXt0aGlzLkJ0LmxpbmtlZEZsYWdTZWVuPSEwLHRoaXMua2koKSx0aGlzLnBpKFwibGlua2VkX2ZsYWdfb3ZlcnJpZGRlblwiKX1vdmVycmlkZVNhbXBsaW5nKCl7dmFyIHQ7bnVsbD09KHQ9dGhpcy5faW5zdGFuY2UucGVyc2lzdGVuY2UpfHx0LnJlZ2lzdGVyKHtba3RdOiEwfSksdGhpcy5raSgpLHRoaXMucGkoXCJzYW1wbGluZ19vdmVycmlkZGVuXCIpfW92ZXJyaWRlVHJpZ2dlcih0KXt0aGlzLkNpKHQpfXBpKHQsaSl7dGhpcy5faW5zdGFuY2UucmVnaXN0ZXJfZm9yX3Nlc3Npb24oeyRzZXNzaW9uX3JlY29yZGluZ19zdGFydF9yZWFzb246dH0pLHNzLmluZm8odC5yZXBsYWNlKFwiX1wiLFwiIFwiKSxpKSxtKFtcInJlY29yZGluZ19pbml0aWFsaXplZFwiLFwic2Vzc2lvbl9pZF9jaGFuZ2VkXCJdLHQpfHx0aGlzLmlpKHQsaSl9Z2V0IHNka0RlYnVnUHJvcGVydGllcygpe3ZhcntzZXNzaW9uU3RhcnRUaW1lc3RhbXA6dH09dGhpcy5JdC5jaGVja0FuZEdldFNlc3Npb25BbmRXaW5kb3dJZCghMCk7cmV0dXJueyRyZWNvcmRpbmdfc3RhdHVzOnRoaXMuc3RhdHVzLCRzZGtfZGVidWdfcmVwbGF5X2ludGVybmFsX2J1ZmZlcl9sZW5ndGg6dGhpcy5NLmRhdGEubGVuZ3RoLCRzZGtfZGVidWdfcmVwbGF5X2ludGVybmFsX2J1ZmZlcl9zaXplOnRoaXMuTS5zaXplLCRzZGtfZGVidWdfY3VycmVudF9zZXNzaW9uX2R1cmF0aW9uOnRoaXMuTXQsJHNka19kZWJ1Z19zZXNzaW9uX3N0YXJ0OnR9fX12YXIgdnM9eihcIltTZWdtZW50SW50ZWdyYXRpb25dXCIpO2Z1bmN0aW9uIGNzKHQsaSl7dmFyIGU9dC5jb25maWcuc2VnbWVudDtpZighZSlyZXR1cm4gaSgpOyFmdW5jdGlvbih0LGkpe3ZhciBlPXQuY29uZmlnLnNlZ21lbnQ7aWYoIWUpcmV0dXJuIGkoKTt2YXIgcj1lPT57dmFyIHI9KCk9PmUuYW5vbnltb3VzSWQoKXx8TmkoKTt0LmNvbmZpZy5nZXRfZGV2aWNlX2lkPXIsZS5pZCgpJiYodC5yZWdpc3Rlcih7ZGlzdGluY3RfaWQ6ZS5pZCgpLCRkZXZpY2VfaWQ6cigpfSksdC5wZXJzaXN0ZW5jZS5zZXRfcHJvcGVydHkoQXQsXCJpZGVudGlmaWVkXCIpKSxpKCl9LHM9ZS51c2VyKCk7XCJ0aGVuXCJpbiBzJiZFKHMudGhlbik/cy50aGVuKCh0PT5yKHQpKSk6cihzKX0odCwoKCk9PntlLnJlZ2lzdGVyKCh0PT57UHJvbWlzZSYmUHJvbWlzZS5yZXNvbHZlfHx2cy53YXJuKFwiVGhpcyBicm93c2VyIGRvZXMgbm90IGhhdmUgUHJvbWlzZSBzdXBwb3J0LCBhbmQgY2FuIG5vdCB1c2UgdGhlIHNlZ21lbnQgaW50ZWdyYXRpb25cIik7dmFyIGk9KGksZSk9PntpZighZSlyZXR1cm4gaTtpLmV2ZW50LnVzZXJJZHx8aS5ldmVudC5hbm9ueW1vdXNJZD09PXQuZ2V0X2Rpc3RpbmN0X2lkKCl8fCh2cy5pbmZvKFwiTm8gdXNlcklkIHNldCwgcmVzZXR0aW5nIFBvc3RIb2dcIiksdC5yZXNldCgpKSxpLmV2ZW50LnVzZXJJZCYmaS5ldmVudC51c2VySWQhPT10LmdldF9kaXN0aW5jdF9pZCgpJiYodnMuaW5mbyhcIlVzZXJJZCBzZXQsIGlkZW50aWZ5aW5nIHdpdGggUG9zdEhvZ1wiKSx0LmlkZW50aWZ5KGkuZXZlbnQudXNlcklkKSk7dmFyIHI9dC5jYWxjdWxhdGVFdmVudFByb3BlcnRpZXMoZSxpLmV2ZW50LnByb3BlcnRpZXMpO3JldHVybiBpLmV2ZW50LnByb3BlcnRpZXM9T2JqZWN0LmFzc2lnbih7fSxyLGkuZXZlbnQucHJvcGVydGllcyksaX07cmV0dXJue25hbWU6XCJQb3N0SG9nIEpTXCIsdHlwZTpcImVucmljaG1lbnRcIix2ZXJzaW9uOlwiMS4wLjBcIixpc0xvYWRlZDooKT0+ITAsbG9hZDooKT0+UHJvbWlzZS5yZXNvbHZlKCksdHJhY2s6dD0+aSh0LHQuZXZlbnQuZXZlbnQpLHBhZ2U6dD0+aSh0LFwiJHBhZ2V2aWV3XCIpLGlkZW50aWZ5OnQ9PmkodCxcIiRpZGVudGlmeVwiKSxzY3JlZW46dD0+aSh0LFwiJHNjcmVlblwiKX19KSh0KSkudGhlbigoKCk9PntpKCl9KSl9KSl9dmFyIGZzPVwicG9zdGhvZy1qc1wiO2Z1bmN0aW9uIHBzKHQsaSl7dmFye29yZ2FuaXphdGlvbjplLHByb2plY3RJZDpyLHByZWZpeDpzLHNldmVyaXR5QWxsb3dMaXN0Om49W1wiZXJyb3JcIl19PXZvaWQgMD09PWk/e306aTtyZXR1cm4gaT0+e3ZhciBvLGEsbCx1LGg7aWYoIShcIipcIj09PW58fG4uaW5jbHVkZXMoaS5sZXZlbCkpfHwhdC5fX2xvYWRlZClyZXR1cm4gaTtpLnRhZ3N8fChpLnRhZ3M9e30pO3ZhciBkPXQucmVxdWVzdFJvdXRlci5lbmRwb2ludEZvcihcInVpXCIsXCIvcHJvamVjdC9cIit0LmNvbmZpZy50b2tlbitcIi9wZXJzb24vXCIrdC5nZXRfZGlzdGluY3RfaWQoKSk7aS50YWdzW1wiUG9zdEhvZyBQZXJzb24gVVJMXCJdPWQsdC5zZXNzaW9uUmVjb3JkaW5nU3RhcnRlZCgpJiYoaS50YWdzW1wiUG9zdEhvZyBSZWNvcmRpbmcgVVJMXCJdPXQuZ2V0X3Nlc3Npb25fcmVwbGF5X3VybCh7d2l0aFRpbWVzdGFtcDohMH0pKTt2YXIgdj0obnVsbD09KG89aS5leGNlcHRpb24pP3ZvaWQgMDpvLnZhbHVlcyl8fFtdLGM9di5tYXAoKHQ9PkIoe30sdCx7c3RhY2t0cmFjZTp0LnN0YWNrdHJhY2U/Qih7fSx0LnN0YWNrdHJhY2Use3R5cGU6XCJyYXdcIixmcmFtZXM6KHQuc3RhY2t0cmFjZS5mcmFtZXN8fFtdKS5tYXAoKHQ9PkIoe30sdCx7cGxhdGZvcm06XCJ3ZWI6amF2YXNjcmlwdFwifSkpKX0pOnZvaWQgMH0pKSksZj17JGV4Y2VwdGlvbl9tZXNzYWdlOihudWxsPT0oYT12WzBdKT92b2lkIDA6YS52YWx1ZSl8fGkubWVzc2FnZSwkZXhjZXB0aW9uX3R5cGU6bnVsbD09KGw9dlswXSk/dm9pZCAwOmwudHlwZSwkZXhjZXB0aW9uX3BlcnNvblVSTDpkLCRleGNlcHRpb25fbGV2ZWw6aS5sZXZlbCwkZXhjZXB0aW9uX2xpc3Q6Yywkc2VudHJ5X2V2ZW50X2lkOmkuZXZlbnRfaWQsJHNlbnRyeV9leGNlcHRpb246aS5leGNlcHRpb24sJHNlbnRyeV9leGNlcHRpb25fbWVzc2FnZToobnVsbD09KHU9dlswXSk/dm9pZCAwOnUudmFsdWUpfHxpLm1lc3NhZ2UsJHNlbnRyeV9leGNlcHRpb25fdHlwZTpudWxsPT0oaD12WzBdKT92b2lkIDA6aC50eXBlLCRzZW50cnlfdGFnczppLnRhZ3N9O3JldHVybiBlJiZyJiYoZi4kc2VudHJ5X3VybD0oc3x8XCJodHRwczovL3NlbnRyeS5pby9vcmdhbml6YXRpb25zL1wiKStlK1wiL2lzc3Vlcy8/cHJvamVjdD1cIityK1wiJnF1ZXJ5PVwiK2kuZXZlbnRfaWQpLHQuZXhjZXB0aW9ucy5zZW5kRXhjZXB0aW9uRXZlbnQoZiksaX19Y2xhc3MgZ3N7Y29uc3RydWN0b3IodCxpLGUscixzKXt0aGlzLm5hbWU9ZnMsdGhpcy5zZXR1cE9uY2U9ZnVuY3Rpb24obil7bihwcyh0LHtvcmdhbml6YXRpb246aSxwcm9qZWN0SWQ6ZSxwcmVmaXg6cixzZXZlcml0eUFsbG93TGlzdDpzfSkpfX19dmFyIF9zPW51bGwhPXQmJnQubG9jYXRpb24/eGkodC5sb2NhdGlvbi5oYXNoLFwiX19wb3N0aG9nXCIpfHx4aShsb2NhdGlvbi5oYXNoLFwic3RhdGVcIik6bnVsbCxtcz1cIl9wb3N0SG9nVG9vbGJhclBhcmFtc1wiLGJzPXooXCJbVG9vbGJhcl1cIiksd3M9ZnVuY3Rpb24odCl7cmV0dXJuIHRbdC5VTklOSVRJQUxJWkVEPTBdPVwiVU5JTklUSUFMSVpFRFwiLHRbdC5MT0FESU5HPTFdPVwiTE9BRElOR1wiLHRbdC5MT0FERUQ9Ml09XCJMT0FERURcIix0fSh3c3x8e30pO2NsYXNzIHlze2NvbnN0cnVjdG9yKHQpe3RoaXMuaW5zdGFuY2U9dH1EaSh0KXt2LnBoX3Rvb2xiYXJfc3RhdGU9dH1MaSgpe3ZhciB0O3JldHVybiBudWxsIT09KHQ9di5waF90b29sYmFyX3N0YXRlKSYmdm9pZCAwIT09dD90OndzLlVOSU5JVElBTElaRUR9bWF5YmVMb2FkVG9vbGJhcihpLGUscil7aWYodm9pZCAwPT09aSYmKGk9dm9pZCAwKSx2b2lkIDA9PT1lJiYoZT12b2lkIDApLHZvaWQgMD09PXImJihyPXZvaWQgMCksIXR8fCFvKXJldHVybiExO2k9bnVsbCE9aT9pOnQubG9jYXRpb24scj1udWxsIT1yP3I6dC5oaXN0b3J5O3RyeXtpZighZSl7dHJ5e3QubG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJ0ZXN0XCIsXCJ0ZXN0XCIpLHQubG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oXCJ0ZXN0XCIpfWNhdGNoKHQpe3JldHVybiExfWU9bnVsbD09dD92b2lkIDA6dC5sb2NhbFN0b3JhZ2V9dmFyIHMsbj1fc3x8eGkoaS5oYXNoLFwiX19wb3N0aG9nXCIpfHx4aShpLmhhc2gsXCJzdGF0ZVwiKSxhPW4/WCgoKCk9PkpTT04ucGFyc2UoYXRvYihkZWNvZGVVUklDb21wb25lbnQobikpKSkpfHxYKCgoKT0+SlNPTi5wYXJzZShkZWNvZGVVUklDb21wb25lbnQobikpKSk6bnVsbDtyZXR1cm4gYSYmXCJwaF9hdXRob3JpemVcIj09PWEuYWN0aW9uPygocz1hKS5zb3VyY2U9XCJ1cmxcIixzJiZPYmplY3Qua2V5cyhzKS5sZW5ndGg+MCYmKGEuZGVzaXJlZEhhc2g/aS5oYXNoPWEuZGVzaXJlZEhhc2g6cj9yLnJlcGxhY2VTdGF0ZShyLnN0YXRlLFwiXCIsaS5wYXRobmFtZStpLnNlYXJjaCk6aS5oYXNoPVwiXCIpKTooKHM9SlNPTi5wYXJzZShlLmdldEl0ZW0obXMpfHxcInt9XCIpKS5zb3VyY2U9XCJsb2NhbHN0b3JhZ2VcIixkZWxldGUgcy51c2VySW50ZW50KSwhKCFzLnRva2VufHx0aGlzLmluc3RhbmNlLmNvbmZpZy50b2tlbiE9PXMudG9rZW4pJiYodGhpcy5sb2FkVG9vbGJhcihzKSwhMCl9Y2F0Y2godCl7cmV0dXJuITF9fU5pKHQpe3ZhciBpPXYucGhfbG9hZF90b29sYmFyfHx2LnBoX2xvYWRfZWRpdG9yOyFPKGkpJiZFKGkpP2kodCx0aGlzLmluc3RhbmNlKTpicy53YXJuKFwiTm8gdG9vbGJhciBsb2FkIGZ1bmN0aW9uIGZvdW5kXCIpfWxvYWRUb29sYmFyKGkpe3ZhciBlPSEobnVsbD09b3x8IW8uZ2V0RWxlbWVudEJ5SWQocXQpKTtpZighdHx8ZSlyZXR1cm4hMTt2YXIgcj1cImN1c3RvbVwiPT09dGhpcy5pbnN0YW5jZS5yZXF1ZXN0Um91dGVyLnJlZ2lvbiYmdGhpcy5pbnN0YW5jZS5jb25maWcuYWR2YW5jZWRfZGlzYWJsZV90b29sYmFyX21ldHJpY3Mscz1CKHt0b2tlbjp0aGlzLmluc3RhbmNlLmNvbmZpZy50b2tlbn0saSx7YXBpVVJMOnRoaXMuaW5zdGFuY2UucmVxdWVzdFJvdXRlci5lbmRwb2ludEZvcihcInVpXCIpfSxyP3tpbnN0cnVtZW50OiExfTp7fSk7aWYodC5sb2NhbFN0b3JhZ2Uuc2V0SXRlbShtcyxKU09OLnN0cmluZ2lmeShCKHt9LHMse3NvdXJjZTp2b2lkIDB9KSkpLHRoaXMuTGkoKT09PXdzLkxPQURFRCl0aGlzLk5pKHMpO2Vsc2UgaWYodGhpcy5MaSgpPT09d3MuVU5JTklUSUFMSVpFRCl7dmFyIG47dGhpcy5EaSh3cy5MT0FESU5HKSxudWxsPT0obj12Ll9fUG9zdGhvZ0V4dGVuc2lvbnNfXyl8fG51bGw9PW4ubG9hZEV4dGVybmFsRGVwZW5kZW5jeXx8bi5sb2FkRXh0ZXJuYWxEZXBlbmRlbmN5KHRoaXMuaW5zdGFuY2UsXCJ0b29sYmFyXCIsKHQ9PntpZih0KXJldHVybiBicy5lcnJvcihcIltUb29sYmFyXSBGYWlsZWQgdG8gbG9hZFwiLHQpLHZvaWQgdGhpcy5EaSh3cy5VTklOSVRJQUxJWkVEKTt0aGlzLkRpKHdzLkxPQURFRCksdGhpcy5OaShzKX0pKSxzdCh0LFwidHVyYm9saW5rczpsb2FkXCIsKCgpPT57dGhpcy5EaSh3cy5VTklOSVRJQUxJWkVEKSx0aGlzLmxvYWRUb29sYmFyKHMpfSkpfXJldHVybiEwfWppKHQpe3JldHVybiB0aGlzLmxvYWRUb29sYmFyKHQpfW1heWJlTG9hZEVkaXRvcih0LGksZSl7cmV0dXJuIHZvaWQgMD09PXQmJih0PXZvaWQgMCksdm9pZCAwPT09aSYmKGk9dm9pZCAwKSx2b2lkIDA9PT1lJiYoZT12b2lkIDApLHRoaXMubWF5YmVMb2FkVG9vbGJhcih0LGksZSl9fXZhciBTcz16KFwiW1RyYWNpbmdIZWFkZXJzXVwiKTtjbGFzcyAkc3tjb25zdHJ1Y3Rvcih0KXt0aGlzLnppPXZvaWQgMCx0aGlzLlVpPXZvaWQgMCx0aGlzLlY9KCk9Pnt2YXIgdCxpO1IodGhpcy56aSkmJihudWxsPT0odD12Ll9fUG9zdGhvZ0V4dGVuc2lvbnNfXyl8fG51bGw9PSh0PXQudHJhY2luZ0hlYWRlcnNQYXRjaEZucyl8fHQuX3BhdGNoWEhSKHRoaXMuX2luc3RhbmNlLnNlc3Npb25NYW5hZ2VyKSk7Uih0aGlzLlVpKSYmKG51bGw9PShpPXYuX19Qb3N0aG9nRXh0ZW5zaW9uc19fKXx8bnVsbD09KGk9aS50cmFjaW5nSGVhZGVyc1BhdGNoRm5zKXx8aS5fcGF0Y2hGZXRjaCh0aGlzLl9pbnN0YW5jZS5zZXNzaW9uTWFuYWdlcikpfSx0aGlzLl9pbnN0YW5jZT10fUcodCl7dmFyIGksZTtudWxsIT0oaT12Ll9fUG9zdGhvZ0V4dGVuc2lvbnNfXykmJmkudHJhY2luZ0hlYWRlcnNQYXRjaEZucyYmdCgpLG51bGw9PShlPXYuX19Qb3N0aG9nRXh0ZW5zaW9uc19fKXx8bnVsbD09ZS5sb2FkRXh0ZXJuYWxEZXBlbmRlbmN5fHxlLmxvYWRFeHRlcm5hbERlcGVuZGVuY3kodGhpcy5faW5zdGFuY2UsXCJ0cmFjaW5nLWhlYWRlcnNcIiwoaT0+e2lmKGkpcmV0dXJuIFNzLmVycm9yKFwiZmFpbGVkIHRvIGxvYWQgc2NyaXB0XCIsaSk7dCgpfSkpfXN0YXJ0SWZFbmFibGVkT3JTdG9wKCl7dmFyIHQsaTt0aGlzLl9pbnN0YW5jZS5jb25maWcuX19hZGRfdHJhY2luZ19oZWFkZXJzP3RoaXMuRyh0aGlzLlYpOihudWxsPT0odD10aGlzLnppKXx8dC5jYWxsKHRoaXMpLG51bGw9PShpPXRoaXMuVWkpfHxpLmNhbGwodGhpcyksdGhpcy56aT12b2lkIDAsdGhpcy5VaT12b2lkIDApfX12YXIga3M9eihcIltXZWIgVml0YWxzXVwiKSx4cz05ZTU7Y2xhc3MgRXN7Y29uc3RydWN0b3IodCl7dmFyIGk7dGhpcy5xaT0hMSx0aGlzLmk9ITEsdGhpcy5NPXt1cmw6dm9pZCAwLG1ldHJpY3M6W10sZmlyc3RNZXRyaWNUaW1lc3RhbXA6dm9pZCAwfSx0aGlzLkJpPSgpPT57Y2xlYXJUaW1lb3V0KHRoaXMuSGkpLDAhPT10aGlzLk0ubWV0cmljcy5sZW5ndGgmJih0aGlzLl9pbnN0YW5jZS5jYXB0dXJlKFwiJHdlYl92aXRhbHNcIix0aGlzLk0ubWV0cmljcy5yZWR1Y2UoKCh0LGkpPT5CKHt9LHQse1tcIiR3ZWJfdml0YWxzX1wiK2kubmFtZStcIl9ldmVudFwiXTpCKHt9LGkpLFtcIiR3ZWJfdml0YWxzX1wiK2kubmFtZStcIl92YWx1ZVwiXTppLnZhbHVlfSkpLHt9KSksdGhpcy5NPXt1cmw6dm9pZCAwLG1ldHJpY3M6W10sZmlyc3RNZXRyaWNUaW1lc3RhbXA6dm9pZCAwfSl9LHRoaXMuV2k9dD0+e3ZhciBpLGU9bnVsbD09KGk9dGhpcy5faW5zdGFuY2Uuc2Vzc2lvbk1hbmFnZXIpP3ZvaWQgMDppLmNoZWNrQW5kR2V0U2Vzc2lvbkFuZFdpbmRvd0lkKCEwKTtpZihSKGUpKWtzLmVycm9yKFwiQ291bGQgbm90IHJlYWQgc2Vzc2lvbiBJRC4gRHJvcHBpbmcgbWV0cmljcyFcIik7ZWxzZXt0aGlzLk09dGhpcy5NfHx7dXJsOnZvaWQgMCxtZXRyaWNzOltdLGZpcnN0TWV0cmljVGltZXN0YW1wOnZvaWQgMH07dmFyIHI9dGhpcy5HaSgpO2lmKCFSKHIpKWlmKE8obnVsbD09dD92b2lkIDA6dC5uYW1lKXx8TyhudWxsPT10P3ZvaWQgMDp0LnZhbHVlKSlrcy5lcnJvcihcIkludmFsaWQgbWV0cmljIHJlY2VpdmVkXCIsdCk7ZWxzZSBpZih0aGlzLkppJiZ0LnZhbHVlPj10aGlzLkppKWtzLmVycm9yKFwiSWdub3JpbmcgbWV0cmljIHdpdGggdmFsdWUgPj0gXCIrdGhpcy5KaSx0KTtlbHNlIHRoaXMuTS51cmwhPT1yJiYodGhpcy5CaSgpLHRoaXMuSGk9c2V0VGltZW91dCh0aGlzLkJpLHRoaXMuZmx1c2hUb0NhcHR1cmVUaW1lb3V0TXMpKSxSKHRoaXMuTS51cmwpJiYodGhpcy5NLnVybD1yKSx0aGlzLk0uZmlyc3RNZXRyaWNUaW1lc3RhbXA9Uih0aGlzLk0uZmlyc3RNZXRyaWNUaW1lc3RhbXApP0RhdGUubm93KCk6dGhpcy5NLmZpcnN0TWV0cmljVGltZXN0YW1wLHQuYXR0cmlidXRpb24mJnQuYXR0cmlidXRpb24uaW50ZXJhY3Rpb25UYXJnZXRFbGVtZW50JiYodC5hdHRyaWJ1dGlvbi5pbnRlcmFjdGlvblRhcmdldEVsZW1lbnQ9dm9pZCAwKSx0aGlzLk0ubWV0cmljcy5wdXNoKEIoe30sdCx7JGN1cnJlbnRfdXJsOnIsJHNlc3Npb25faWQ6ZS5zZXNzaW9uSWQsJHdpbmRvd19pZDplLndpbmRvd0lkLHRpbWVzdGFtcDpEYXRlLm5vdygpfSkpLHRoaXMuTS5tZXRyaWNzLmxlbmd0aD09PXRoaXMuYWxsb3dlZE1ldHJpY3MubGVuZ3RoJiZ0aGlzLkJpKCl9fSx0aGlzLlY9KCk9Pnt2YXIgdCxpLGUscixzPXYuX19Qb3N0aG9nRXh0ZW5zaW9uc19fO1Iocyl8fFIocy5wb3N0SG9nV2ViVml0YWxzQ2FsbGJhY2tzKXx8KHtvbkxDUDp0LG9uQ0xTOmksb25GQ1A6ZSxvbklOUDpyfT1zLnBvc3RIb2dXZWJWaXRhbHNDYWxsYmFja3MpLHQmJmkmJmUmJnI/KHRoaXMuYWxsb3dlZE1ldHJpY3MuaW5kZXhPZihcIkxDUFwiKT4tMSYmdCh0aGlzLldpLmJpbmQodGhpcykpLHRoaXMuYWxsb3dlZE1ldHJpY3MuaW5kZXhPZihcIkNMU1wiKT4tMSYmaSh0aGlzLldpLmJpbmQodGhpcykpLHRoaXMuYWxsb3dlZE1ldHJpY3MuaW5kZXhPZihcIkZDUFwiKT4tMSYmZSh0aGlzLldpLmJpbmQodGhpcykpLHRoaXMuYWxsb3dlZE1ldHJpY3MuaW5kZXhPZihcIklOUFwiKT4tMSYmcih0aGlzLldpLmJpbmQodGhpcykpLHRoaXMuaT0hMCk6a3MuZXJyb3IoXCJ3ZWIgdml0YWxzIGNhbGxiYWNrcyBub3QgbG9hZGVkIC0gbm90IHN0YXJ0aW5nXCIpfSx0aGlzLl9pbnN0YW5jZT10LHRoaXMucWk9IShudWxsPT0oaT10aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZSl8fCFpLnByb3BzW3Z0XSksdGhpcy5zdGFydElmRW5hYmxlZCgpfWdldCBhbGxvd2VkTWV0cmljcygpe3ZhciB0LGksZT1JKHRoaXMuX2luc3RhbmNlLmNvbmZpZy5jYXB0dXJlX3BlcmZvcm1hbmNlKT9udWxsPT0odD10aGlzLl9pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9wZXJmb3JtYW5jZSk/dm9pZCAwOnQud2ViX3ZpdGFsc19hbGxvd2VkX21ldHJpY3M6dm9pZCAwO3JldHVybiBSKGUpPyhudWxsPT0oaT10aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZSk/dm9pZCAwOmkucHJvcHNbZnRdKXx8W1wiQ0xTXCIsXCJGQ1BcIixcIklOUFwiLFwiTENQXCJdOmV9Z2V0IGZsdXNoVG9DYXB0dXJlVGltZW91dE1zKCl7cmV0dXJuKEkodGhpcy5faW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfcGVyZm9ybWFuY2UpP3RoaXMuX2luc3RhbmNlLmNvbmZpZy5jYXB0dXJlX3BlcmZvcm1hbmNlLndlYl92aXRhbHNfZGVsYXllZF9mbHVzaF9tczp2b2lkIDApfHw1ZTN9Z2V0IEppKCl7dmFyIHQ9SSh0aGlzLl9pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9wZXJmb3JtYW5jZSkmJkYodGhpcy5faW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfcGVyZm9ybWFuY2UuX193ZWJfdml0YWxzX21heF92YWx1ZSk/dGhpcy5faW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfcGVyZm9ybWFuY2UuX193ZWJfdml0YWxzX21heF92YWx1ZTp4cztyZXR1cm4gMDx0JiZ0PD02ZTQ/eHM6dH1nZXQgaXNFbmFibGVkKCl7dmFyIHQ9bnVsbD09YT92b2lkIDA6YS5wcm90b2NvbDtpZihcImh0dHA6XCIhPT10JiZcImh0dHBzOlwiIT09dClyZXR1cm4ga3MuaW5mbyhcIldlYiBWaXRhbHMgYXJlIGRpc2FibGVkIG9uIG5vbi1odHRwL2h0dHBzIHByb3RvY29sc1wiKSwhMTt2YXIgaT1JKHRoaXMuX2luc3RhbmNlLmNvbmZpZy5jYXB0dXJlX3BlcmZvcm1hbmNlKT90aGlzLl9pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9wZXJmb3JtYW5jZS53ZWJfdml0YWxzOkEodGhpcy5faW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfcGVyZm9ybWFuY2UpP3RoaXMuX2luc3RhbmNlLmNvbmZpZy5jYXB0dXJlX3BlcmZvcm1hbmNlOnZvaWQgMDtyZXR1cm4gQShpKT9pOnRoaXMucWl9c3RhcnRJZkVuYWJsZWQoKXt0aGlzLmlzRW5hYmxlZCYmIXRoaXMuaSYmKGtzLmluZm8oXCJlbmFibGVkLCBzdGFydGluZy4uLlwiKSx0aGlzLkcodGhpcy5WKSl9b25SZW1vdGVDb25maWcodCl7dmFyIGk9SSh0LmNhcHR1cmVQZXJmb3JtYW5jZSkmJiEhdC5jYXB0dXJlUGVyZm9ybWFuY2Uud2ViX3ZpdGFscyxlPUkodC5jYXB0dXJlUGVyZm9ybWFuY2UpP3QuY2FwdHVyZVBlcmZvcm1hbmNlLndlYl92aXRhbHNfYWxsb3dlZF9tZXRyaWNzOnZvaWQgMDt0aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZSYmKHRoaXMuX2luc3RhbmNlLnBlcnNpc3RlbmNlLnJlZ2lzdGVyKHtbdnRdOml9KSx0aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZS5yZWdpc3Rlcih7W2Z0XTplfSkpLHRoaXMucWk9aSx0aGlzLnN0YXJ0SWZFbmFibGVkKCl9Ryh0KXt2YXIgaSxlO251bGwhPShpPXYuX19Qb3N0aG9nRXh0ZW5zaW9uc19fKSYmaS5wb3N0SG9nV2ViVml0YWxzQ2FsbGJhY2tzJiZ0KCksbnVsbD09KGU9di5fX1Bvc3Rob2dFeHRlbnNpb25zX18pfHxudWxsPT1lLmxvYWRFeHRlcm5hbERlcGVuZGVuY3l8fGUubG9hZEV4dGVybmFsRGVwZW5kZW5jeSh0aGlzLl9pbnN0YW5jZSxcIndlYi12aXRhbHNcIiwoaT0+e2k/a3MuZXJyb3IoXCJmYWlsZWQgdG8gbG9hZCBzY3JpcHRcIixpKTp0KCl9KSl9R2koKXt2YXIgaT10P3QubG9jYXRpb24uaHJlZjp2b2lkIDA7cmV0dXJuIGl8fGtzLmVycm9yKFwiQ291bGQgbm90IGRldGVybWluZSBjdXJyZW50IFVSTFwiKSxpfX12YXIgSXM9eihcIltIZWF0bWFwc11cIik7ZnVuY3Rpb24gUHModCl7cmV0dXJuIEkodCkmJlwiY2xpZW50WFwiaW4gdCYmXCJjbGllbnRZXCJpbiB0JiZGKHQuY2xpZW50WCkmJkYodC5jbGllbnRZKX1jbGFzcyBSc3tjb25zdHJ1Y3Rvcih0KXt2YXIgaTt0aGlzLnJhZ2VjbGlja3M9bmV3IGJpLHRoaXMucWk9ITEsdGhpcy5pPSExLHRoaXMuVmk9bnVsbCx0aGlzLmluc3RhbmNlPXQsdGhpcy5xaT0hKG51bGw9PShpPXRoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2UpfHwhaS5wcm9wc1t1dF0pfWdldCBmbHVzaEludGVydmFsTWlsbGlzZWNvbmRzKCl7dmFyIHQ9NWUzO3JldHVybiBJKHRoaXMuaW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfaGVhdG1hcHMpJiZ0aGlzLmluc3RhbmNlLmNvbmZpZy5jYXB0dXJlX2hlYXRtYXBzLmZsdXNoX2ludGVydmFsX21pbGxpc2Vjb25kcyYmKHQ9dGhpcy5pbnN0YW5jZS5jb25maWcuY2FwdHVyZV9oZWF0bWFwcy5mbHVzaF9pbnRlcnZhbF9taWxsaXNlY29uZHMpLHR9Z2V0IGlzRW5hYmxlZCgpe3JldHVybiBSKHRoaXMuaW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfaGVhdG1hcHMpP1IodGhpcy5pbnN0YW5jZS5jb25maWcuZW5hYmxlX2hlYXRtYXBzKT90aGlzLnFpOnRoaXMuaW5zdGFuY2UuY29uZmlnLmVuYWJsZV9oZWF0bWFwczohMSE9PXRoaXMuaW5zdGFuY2UuY29uZmlnLmNhcHR1cmVfaGVhdG1hcHN9c3RhcnRJZkVuYWJsZWQoKXtpZih0aGlzLmlzRW5hYmxlZCl7aWYodGhpcy5pKXJldHVybjtJcy5pbmZvKFwic3RhcnRpbmcuLi5cIiksdGhpcy5LaSgpLHRoaXMuVmk9c2V0SW50ZXJ2YWwodGhpcy5ZaS5iaW5kKHRoaXMpLHRoaXMuZmx1c2hJbnRlcnZhbE1pbGxpc2Vjb25kcyl9ZWxzZXt2YXIgdCxpO2NsZWFySW50ZXJ2YWwobnVsbCE9PSh0PXRoaXMuVmkpJiZ2b2lkIDAhPT10P3Q6dm9pZCAwKSxudWxsPT0oaT10aGlzLlhpKXx8aS5zdG9wKCksdGhpcy5nZXRBbmRDbGVhckJ1ZmZlcigpfX1vblJlbW90ZUNvbmZpZyh0KXt2YXIgaT0hIXQuaGVhdG1hcHM7dGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZSYmdGhpcy5pbnN0YW5jZS5wZXJzaXN0ZW5jZS5yZWdpc3Rlcih7W3V0XTppfSksdGhpcy5xaT1pLHRoaXMuc3RhcnRJZkVuYWJsZWQoKX1nZXRBbmRDbGVhckJ1ZmZlcigpe3ZhciB0PXRoaXMuTTtyZXR1cm4gdGhpcy5NPXZvaWQgMCx0fVFpKHQpe3RoaXMuWmkodC5vcmlnaW5hbEV2ZW50LFwiZGVhZGNsaWNrXCIpfUtpKCl7dCYmbyYmKHN0KHQsXCJiZWZvcmV1bmxvYWRcIix0aGlzLllpLmJpbmQodGhpcykpLHN0KG8sXCJjbGlja1wiLChpPT50aGlzLlppKGl8fChudWxsPT10P3ZvaWQgMDp0LmV2ZW50KSkpLHtjYXB0dXJlOiEwfSksc3QobyxcIm1vdXNlbW92ZVwiLChpPT50aGlzLnRlKGl8fChudWxsPT10P3ZvaWQgMDp0LmV2ZW50KSkpLHtjYXB0dXJlOiEwfSksdGhpcy5YaT1uZXcgcmUodGhpcy5pbnN0YW5jZSxpZSx0aGlzLlFpLmJpbmQodGhpcykpLHRoaXMuWGkuc3RhcnRJZkVuYWJsZWQoKSx0aGlzLmk9ITApfWllKGksZSl7dmFyIHI9dGhpcy5pbnN0YW5jZS5zY3JvbGxNYW5hZ2VyLnNjcm9sbFkoKSxzPXRoaXMuaW5zdGFuY2Uuc2Nyb2xsTWFuYWdlci5zY3JvbGxYKCksbj10aGlzLmluc3RhbmNlLnNjcm9sbE1hbmFnZXIuc2Nyb2xsRWxlbWVudCgpLG89ZnVuY3Rpb24oaSxlLHIpe2Zvcih2YXIgcz1pO3MmJkd0KHMpJiYhSnQocyxcImJvZHlcIik7KXtpZihzPT09cilyZXR1cm4hMTtpZihtKGUsbnVsbD09dD92b2lkIDA6dC5nZXRDb21wdXRlZFN0eWxlKHMpLnBvc2l0aW9uKSlyZXR1cm4hMDtzPXJpKHMpfXJldHVybiExfShpaShpKSxbXCJmaXhlZFwiLFwic3RpY2t5XCJdLG4pO3JldHVybnt4OmkuY2xpZW50WCsobz8wOnMpLHk6aS5jbGllbnRZKyhvPzA6ciksdGFyZ2V0X2ZpeGVkOm8sdHlwZTplfX1aaSh0LGkpe3ZhciBlO2lmKHZvaWQgMD09PWkmJihpPVwiY2xpY2tcIiksIVd0KHQudGFyZ2V0KSYmUHModCkpe3ZhciByPXRoaXMuaWUodCxpKTtudWxsIT0oZT10aGlzLnJhZ2VjbGlja3MpJiZlLmlzUmFnZUNsaWNrKHQuY2xpZW50WCx0LmNsaWVudFksKG5ldyBEYXRlKS5nZXRUaW1lKCkpJiZ0aGlzLmVlKEIoe30scix7dHlwZTpcInJhZ2VjbGlja1wifSkpLHRoaXMuZWUocil9fXRlKHQpeyFXdCh0LnRhcmdldCkmJlBzKHQpJiYoY2xlYXJUaW1lb3V0KHRoaXMucmUpLHRoaXMucmU9c2V0VGltZW91dCgoKCk9Pnt0aGlzLmVlKHRoaXMuaWUodCxcIm1vdXNlbW92ZVwiKSl9KSw1MDApKX1lZShpKXtpZih0KXt2YXIgZT10LmxvY2F0aW9uLmhyZWY7dGhpcy5NPXRoaXMuTXx8e30sdGhpcy5NW2VdfHwodGhpcy5NW2VdPVtdKSx0aGlzLk1bZV0ucHVzaChpKX19WWkoKXt0aGlzLk0mJiFQKHRoaXMuTSkmJnRoaXMuaW5zdGFuY2UuY2FwdHVyZShcIiQkaGVhdG1hcFwiLHskaGVhdG1hcF9kYXRhOnRoaXMuZ2V0QW5kQ2xlYXJCdWZmZXIoKX0pfX1jbGFzcyBUc3tjb25zdHJ1Y3Rvcih0KXt0aGlzLl9pbnN0YW5jZT10fWRvUGFnZVZpZXcoaSxlKXt2YXIgcixzPXRoaXMuc2UoaSxlKTtyZXR1cm4gdGhpcy5uZT17cGF0aG5hbWU6bnVsbCE9PShyPW51bGw9PXQ/dm9pZCAwOnQubG9jYXRpb24ucGF0aG5hbWUpJiZ2b2lkIDAhPT1yP3I6XCJcIixwYWdlVmlld0lkOmUsdGltZXN0YW1wOml9LHRoaXMuX2luc3RhbmNlLnNjcm9sbE1hbmFnZXIucmVzZXRDb250ZXh0KCksc31kb1BhZ2VMZWF2ZSh0KXt2YXIgaTtyZXR1cm4gdGhpcy5zZSh0LG51bGw9PShpPXRoaXMubmUpP3ZvaWQgMDppLnBhZ2VWaWV3SWQpfWRvRXZlbnQoKXt2YXIgdDtyZXR1cm57JHBhZ2V2aWV3X2lkOm51bGw9PSh0PXRoaXMubmUpP3ZvaWQgMDp0LnBhZ2VWaWV3SWR9fXNlKHQsaSl7dmFyIGU9dGhpcy5uZTtpZighZSlyZXR1cm57JHBhZ2V2aWV3X2lkOml9O3ZhciByPXskcGFnZXZpZXdfaWQ6aSwkcHJldl9wYWdldmlld19pZDplLnBhZ2VWaWV3SWR9LHM9dGhpcy5faW5zdGFuY2Uuc2Nyb2xsTWFuYWdlci5nZXRDb250ZXh0KCk7aWYocyYmIXRoaXMuX2luc3RhbmNlLmNvbmZpZy5kaXNhYmxlX3Njcm9sbF9wcm9wZXJ0aWVzKXt2YXJ7bWF4U2Nyb2xsSGVpZ2h0Om4sbGFzdFNjcm9sbFk6byxtYXhTY3JvbGxZOmEsbWF4Q29udGVudEhlaWdodDpsLGxhc3RDb250ZW50WTp1LG1heENvbnRlbnRZOmh9PXM7aWYoIShSKG4pfHxSKG8pfHxSKGEpfHxSKGwpfHxSKHUpfHxSKGgpKSl7bj1NYXRoLmNlaWwobiksbz1NYXRoLmNlaWwobyksYT1NYXRoLmNlaWwoYSksbD1NYXRoLmNlaWwobCksdT1NYXRoLmNlaWwodSksaD1NYXRoLmNlaWwoaCk7dmFyIGQ9bjw9MT8xOlhlKG8vbiwwLDEpLHY9bjw9MT8xOlhlKGEvbiwwLDEpLGM9bDw9MT8xOlhlKHUvbCwwLDEpLGY9bDw9MT8xOlhlKGgvbCwwLDEpO3I9VihyLHskcHJldl9wYWdldmlld19sYXN0X3Njcm9sbDpvLCRwcmV2X3BhZ2V2aWV3X2xhc3Rfc2Nyb2xsX3BlcmNlbnRhZ2U6ZCwkcHJldl9wYWdldmlld19tYXhfc2Nyb2xsOmEsJHByZXZfcGFnZXZpZXdfbWF4X3Njcm9sbF9wZXJjZW50YWdlOnYsJHByZXZfcGFnZXZpZXdfbGFzdF9jb250ZW50OnUsJHByZXZfcGFnZXZpZXdfbGFzdF9jb250ZW50X3BlcmNlbnRhZ2U6YywkcHJldl9wYWdldmlld19tYXhfY29udGVudDpoLCRwcmV2X3BhZ2V2aWV3X21heF9jb250ZW50X3BlcmNlbnRhZ2U6Zn0pfX1yZXR1cm4gZS5wYXRobmFtZSYmKHIuJHByZXZfcGFnZXZpZXdfcGF0aG5hbWU9ZS5wYXRobmFtZSksZS50aW1lc3RhbXAmJihyLiRwcmV2X3BhZ2V2aWV3X2R1cmF0aW9uPSh0LmdldFRpbWUoKS1lLnRpbWVzdGFtcC5nZXRUaW1lKCkpLzFlMykscn19dmFyIE1zPWZ1bmN0aW9uKHQpe3ZhciBpLGUscixzLG49XCJcIjtmb3IoaT1lPTAscj0odD0odCtcIlwiKS5yZXBsYWNlKC9cXHJcXG4vZyxcIlxcblwiKS5yZXBsYWNlKC9cXHIvZyxcIlxcblwiKSkubGVuZ3RoLHM9MDtzPHI7cysrKXt2YXIgbz10LmNoYXJDb2RlQXQocyksYT1udWxsO288MTI4P2UrKzphPW8+MTI3JiZvPDIwNDg/U3RyaW5nLmZyb21DaGFyQ29kZShvPj42fDE5Miw2MyZvfDEyOCk6U3RyaW5nLmZyb21DaGFyQ29kZShvPj4xMnwyMjQsbz4+NiY2M3wxMjgsNjMmb3wxMjgpLEMoYSl8fChlPmkmJihuKz10LnN1YnN0cmluZyhpLGUpKSxuKz1hLGk9ZT1zKzEpfXJldHVybiBlPmkmJihuKz10LnN1YnN0cmluZyhpLHQubGVuZ3RoKSksbn0sQ3M9ISF1fHwhIWwsT3M9XCJ0ZXh0L3BsYWluXCIsRnM9KHQsaSk9Pnt2YXJbZSxyXT10LnNwbGl0KFwiP1wiKSxzPUIoe30saSk7bnVsbD09cnx8ci5zcGxpdChcIiZcIikuZm9yRWFjaCgodD0+e3ZhcltpXT10LnNwbGl0KFwiPVwiKTtkZWxldGUgc1tpXX0pKTt2YXIgbj1TaShzKTtyZXR1cm4gZStcIj9cIisobj1uPyhyP3IrXCImXCI6XCJcIikrbjpyKX0sQXM9KHQsaSk9PkpTT04uc3RyaW5naWZ5KHQsKCh0LGkpPT5cImJpZ2ludFwiPT10eXBlb2YgaT9pLnRvU3RyaW5nKCk6aSksaSksRHM9dD0+e3ZhcntkYXRhOmksY29tcHJlc3Npb246ZX09dDtpZihpKXtpZihlPT09Zy5HWmlwSlMpe3ZhciByPUFyKERyKEFzKGkpKSx7bXRpbWU6MH0pLHM9bmV3IEJsb2IoW3JdLHt0eXBlOk9zfSk7cmV0dXJue2NvbnRlbnRUeXBlOk9zLGJvZHk6cyxlc3RpbWF0ZWRTaXplOnMuc2l6ZX19aWYoZT09PWcuQmFzZTY0KXt2YXIgbj1mdW5jdGlvbih0KXt2YXIgaSxlLHIscyxuLG89XCJBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWmFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6MDEyMzQ1Njc4OSsvPVwiLGE9MCxsPTAsdT1cIlwiLGg9W107aWYoIXQpcmV0dXJuIHQ7dD1Ncyh0KTtkb3tpPShuPXQuY2hhckNvZGVBdChhKyspPDwxNnx0LmNoYXJDb2RlQXQoYSsrKTw8OHx0LmNoYXJDb2RlQXQoYSsrKSk+PjE4JjYzLGU9bj4+MTImNjMscj1uPj42JjYzLHM9NjMmbixoW2wrK109by5jaGFyQXQoaSkrby5jaGFyQXQoZSkrby5jaGFyQXQocikrby5jaGFyQXQocyl9d2hpbGUoYTx0Lmxlbmd0aCk7c3dpdGNoKHU9aC5qb2luKFwiXCIpLHQubGVuZ3RoJTMpe2Nhc2UgMTp1PXUuc2xpY2UoMCwtMikrXCI9PVwiO2JyZWFrO2Nhc2UgMjp1PXUuc2xpY2UoMCwtMSkrXCI9XCJ9cmV0dXJuIHV9KEFzKGkpKSxvPSh0PT5cImRhdGE9XCIrZW5jb2RlVVJJQ29tcG9uZW50KFwic3RyaW5nXCI9PXR5cGVvZiB0P3Q6QXModCkpKShuKTtyZXR1cm57Y29udGVudFR5cGU6XCJhcHBsaWNhdGlvbi94LXd3dy1mb3JtLXVybGVuY29kZWRcIixib2R5Om8sZXN0aW1hdGVkU2l6ZTpuZXcgQmxvYihbb10pLnNpemV9fXZhciBhPUFzKGkpO3JldHVybntjb250ZW50VHlwZTpcImFwcGxpY2F0aW9uL2pzb25cIixib2R5OmEsZXN0aW1hdGVkU2l6ZTpuZXcgQmxvYihbYV0pLnNpemV9fX0sTHM9W107bCYmTHMucHVzaCh7dHJhbnNwb3J0OlwiZmV0Y2hcIixtZXRob2Q6dD0+e3ZhciBpLGUse2NvbnRlbnRUeXBlOnIsYm9keTpzLGVzdGltYXRlZFNpemU6bn09bnVsbCE9PShpPURzKHQpKSYmdm9pZCAwIT09aT9pOnt9LG89bmV3IEhlYWRlcnM7Sih0LmhlYWRlcnMsKGZ1bmN0aW9uKHQsaSl7by5hcHBlbmQoaSx0KX0pKSxyJiZvLmFwcGVuZChcIkNvbnRlbnQtVHlwZVwiLHIpO3ZhciBhPXQudXJsLHU9bnVsbDtpZihoKXt2YXIgZD1uZXcgaDt1PXtzaWduYWw6ZC5zaWduYWwsdGltZW91dDpzZXRUaW1lb3V0KCgoKT0+ZC5hYm9ydCgpKSx0LnRpbWVvdXQpfX1sKGEsQih7bWV0aG9kOihudWxsPT10P3ZvaWQgMDp0Lm1ldGhvZCl8fFwiR0VUXCIsaGVhZGVyczpvLGtlZXBhbGl2ZTpcIlBPU1RcIj09PXQubWV0aG9kJiYobnx8MCk8NTI0MjguOCxib2R5OnMsc2lnbmFsOm51bGw9PShlPXUpP3ZvaWQgMDplLnNpZ25hbH0sdC5mZXRjaE9wdGlvbnMpKS50aGVuKChpPT5pLnRleHQoKS50aGVuKChlPT57dmFyIHI9e3N0YXR1c0NvZGU6aS5zdGF0dXMsdGV4dDplfTtpZigyMDA9PT1pLnN0YXR1cyl0cnl7ci5qc29uPUpTT04ucGFyc2UoZSl9Y2F0Y2godCl7ai5lcnJvcih0KX1udWxsPT10LmNhbGxiYWNrfHx0LmNhbGxiYWNrKHIpfSkpKSkuY2F0Y2goKGk9PntqLmVycm9yKGkpLG51bGw9PXQuY2FsbGJhY2t8fHQuY2FsbGJhY2soe3N0YXR1c0NvZGU6MCx0ZXh0Oml9KX0pKS5maW5hbGx5KCgoKT0+dT9jbGVhclRpbWVvdXQodS50aW1lb3V0KTpudWxsKSl9fSksdSYmTHMucHVzaCh7dHJhbnNwb3J0OlwiWEhSXCIsbWV0aG9kOnQ9Pnt2YXIgaSxlPW5ldyB1O2Uub3Blbih0Lm1ldGhvZHx8XCJHRVRcIix0LnVybCwhMCk7dmFye2NvbnRlbnRUeXBlOnIsYm9keTpzfT1udWxsIT09KGk9RHModCkpJiZ2b2lkIDAhPT1pP2k6e307Sih0LmhlYWRlcnMsKGZ1bmN0aW9uKHQsaSl7ZS5zZXRSZXF1ZXN0SGVhZGVyKGksdCl9KSksciYmZS5zZXRSZXF1ZXN0SGVhZGVyKFwiQ29udGVudC1UeXBlXCIsciksdC50aW1lb3V0JiYoZS50aW1lb3V0PXQudGltZW91dCksZS53aXRoQ3JlZGVudGlhbHM9ITAsZS5vbnJlYWR5c3RhdGVjaGFuZ2U9KCk9PntpZig0PT09ZS5yZWFkeVN0YXRlKXt2YXIgaT17c3RhdHVzQ29kZTplLnN0YXR1cyx0ZXh0OmUucmVzcG9uc2VUZXh0fTtpZigyMDA9PT1lLnN0YXR1cyl0cnl7aS5qc29uPUpTT04ucGFyc2UoZS5yZXNwb25zZVRleHQpfWNhdGNoKHQpe31udWxsPT10LmNhbGxiYWNrfHx0LmNhbGxiYWNrKGkpfX0sZS5zZW5kKHMpfX0pLG51bGwhPW4mJm4uc2VuZEJlYWNvbiYmTHMucHVzaCh7dHJhbnNwb3J0Olwic2VuZEJlYWNvblwiLG1ldGhvZDp0PT57dmFyIGk9RnModC51cmwse2JlYWNvbjpcIjFcIn0pO3RyeXt2YXIgZSx7Y29udGVudFR5cGU6cixib2R5OnN9PW51bGwhPT0oZT1Ecyh0KSkmJnZvaWQgMCE9PWU/ZTp7fSxvPVwic3RyaW5nXCI9PXR5cGVvZiBzP25ldyBCbG9iKFtzXSx7dHlwZTpyfSk6cztuLnNlbmRCZWFjb24oaSxvKX1jYXRjaCh0KXt9fX0pO3ZhciBOcz1mdW5jdGlvbih0LGkpe2lmKCFmdW5jdGlvbih0KXt0cnl7bmV3IFJlZ0V4cCh0KX1jYXRjaCh0KXtyZXR1cm4hMX1yZXR1cm4hMH0oaSkpcmV0dXJuITE7dHJ5e3JldHVybiBuZXcgUmVnRXhwKGkpLnRlc3QodCl9Y2F0Y2godCl7cmV0dXJuITF9fTtmdW5jdGlvbiBqcyh0LGksZSl7cmV0dXJuIEFzKHtkaXN0aW5jdF9pZDp0LHVzZXJQcm9wZXJ0aWVzVG9TZXQ6aSx1c2VyUHJvcGVydGllc1RvU2V0T25jZTplfSl9dmFyIHpzPXtleGFjdDoodCxpKT0+aS5zb21lKChpPT50LnNvbWUoKHQ9Pmk9PT10KSkpKSxpc19ub3Q6KHQsaSk9PmkuZXZlcnkoKGk9PnQuZXZlcnkoKHQ9PmkhPT10KSkpKSxyZWdleDoodCxpKT0+aS5zb21lKChpPT50LnNvbWUoKHQ9Pk5zKGksdCkpKSkpLG5vdF9yZWdleDoodCxpKT0+aS5ldmVyeSgoaT0+dC5ldmVyeSgodD0+IU5zKGksdCkpKSkpLGljb250YWluczoodCxpKT0+aS5tYXAoVXMpLnNvbWUoKGk9PnQubWFwKFVzKS5zb21lKCh0PT5pLmluY2x1ZGVzKHQpKSkpKSxub3RfaWNvbnRhaW5zOih0LGkpPT5pLm1hcChVcykuZXZlcnkoKGk9PnQubWFwKFVzKS5ldmVyeSgodD0+IWkuaW5jbHVkZXModCkpKSkpfSxVcz10PT50LnRvTG93ZXJDYXNlKCkscXM9eihcIltFcnJvciB0cmFja2luZ11cIik7Y2xhc3MgQnN7Y29uc3RydWN0b3IodCl7dmFyIGksZTt0aGlzLm9lPVtdLHRoaXMuX2luc3RhbmNlPXQsdGhpcy5vZT1udWxsIT09KGk9bnVsbD09KGU9dGhpcy5faW5zdGFuY2UucGVyc2lzdGVuY2UpP3ZvaWQgMDplLmdldF9wcm9wZXJ0eShkdCkpJiZ2b2lkIDAhPT1pP2k6W119b25SZW1vdGVDb25maWcodCl7dmFyIGksZSxyPW51bGwhPT0oaT1udWxsPT0oZT10LmVycm9yVHJhY2tpbmcpP3ZvaWQgMDplLnN1cHByZXNzaW9uUnVsZXMpJiZ2b2lkIDAhPT1pP2k6W107dGhpcy5vZT1yLHRoaXMuX2luc3RhbmNlLnBlcnNpc3RlbmNlJiZ0aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZS5yZWdpc3Rlcih7W2R0XTp0aGlzLm9lfSl9c2VuZEV4Y2VwdGlvbkV2ZW50KHQpe3RoaXMuYWUodCk/cXMuaW5mbyhcIlNraXBwaW5nIGV4Y2VwdGlvbiBjYXB0dXJlIGJlY2F1c2UgYSBzdXBwcmVzc2lvbiBydWxlIG1hdGNoZWRcIik6dGhpcy5faW5zdGFuY2UuY2FwdHVyZShcIiRleGNlcHRpb25cIix0LHtfbm9UcnVuY2F0ZTohMCxfYmF0Y2hLZXk6XCJleGNlcHRpb25FdmVudFwifSl9YWUodCl7dmFyIGk9dC4kZXhjZXB0aW9uX2xpc3Q7aWYoIWl8fCF4KGkpfHwwPT09aS5sZW5ndGgpcmV0dXJuITE7dmFyIGU9aS5yZWR1Y2UoKCh0LGkpPT57dmFye3R5cGU6ZSx2YWx1ZTpyfT1pO3JldHVybiBUKGUpJiZlLmxlbmd0aD4wJiZ0LiRleGNlcHRpb25fdHlwZXMucHVzaChlKSxUKHIpJiZyLmxlbmd0aD4wJiZ0LiRleGNlcHRpb25fbWVzc2FnZXMucHVzaChyKSx0fSkseyRleGNlcHRpb25fdHlwZXM6W10sJGV4Y2VwdGlvbl9tZXNzYWdlczpbXX0pO3JldHVybiB0aGlzLm9lLnNvbWUoKHQ9Pnt2YXIgaT10LnZhbHVlcy5tYXAoKHQ9Pnt2YXIgaT16c1t0Lm9wZXJhdG9yXSxyPXgodC52YWx1ZSk/dC52YWx1ZTpbdC52YWx1ZV0scz1lW3Qua2V5XTtyZXR1cm4gci5sZW5ndGg+MCYmaShyLHMpfSkpO3JldHVyblwiT1JcIj09PXQudHlwZT9pLnNvbWUoQm9vbGVhbik6aS5ldmVyeShCb29sZWFuKX0pKX19dmFyIEhzPVwiTW9iaWxlXCIsV3M9XCJpT1NcIixHcz1cIkFuZHJvaWRcIixKcz1cIlRhYmxldFwiLFZzPUdzK1wiIFwiK0pzLEtzPVwiaVBhZFwiLFlzPVwiQXBwbGVcIixYcz1ZcytcIiBXYXRjaFwiLFFzPVwiU2FmYXJpXCIsWnM9XCJCbGFja0JlcnJ5XCIsdG49XCJTYW1zdW5nXCIsZW49dG4rXCJCcm93c2VyXCIscm49dG4rXCIgSW50ZXJuZXRcIixzbj1cIkNocm9tZVwiLG5uPXNuK1wiIE9TXCIsb249c24rXCIgXCIrV3MsYW49XCJJbnRlcm5ldCBFeHBsb3JlclwiLGxuPWFuK1wiIFwiK0hzLHVuPVwiT3BlcmFcIixobj11bitcIiBNaW5pXCIsZG49XCJFZGdlXCIsdm49XCJNaWNyb3NvZnQgXCIrZG4sY249XCJGaXJlZm94XCIsZm49Y24rXCIgXCIrV3MscG49XCJOaW50ZW5kb1wiLGduPVwiUGxheVN0YXRpb25cIixfbj1cIlhib3hcIixtbj1HcytcIiBcIitIcyxibj1IcytcIiBcIitRcyx3bj1cIldpbmRvd3NcIix5bj13bitcIiBQaG9uZVwiLFNuPVwiTm9raWFcIiwkbj1cIk91eWFcIixrbj1cIkdlbmVyaWNcIix4bj1rbitcIiBcIitIcy50b0xvd2VyQ2FzZSgpLEVuPWtuK1wiIFwiK0pzLnRvTG93ZXJDYXNlKCksSW49XCJLb25xdWVyb3JcIixQbj1cIihcXFxcZCsoXFxcXC5cXFxcZCspPylcIixSbj1uZXcgUmVnRXhwKFwiVmVyc2lvbi9cIitQbiksVG49bmV3IFJlZ0V4cChfbixcImlcIiksTW49bmV3IFJlZ0V4cChnbitcIiBcXFxcdytcIixcImlcIiksQ249bmV3IFJlZ0V4cChwbitcIiBcXFxcdytcIixcImlcIiksT249bmV3IFJlZ0V4cChacytcInxQbGF5Qm9va3xCQjEwXCIsXCJpXCIpLEZuPXtcIk5UMy41MVwiOlwiTlQgMy4xMVwiLFwiTlQ0LjBcIjpcIk5UIDQuMFwiLFwiNS4wXCI6XCIyMDAwXCIsNS4xOlwiWFBcIiw1LjI6XCJYUFwiLFwiNi4wXCI6XCJWaXN0YVwiLDYuMTpcIjdcIiw2LjI6XCI4XCIsNi4zOlwiOC4xXCIsNi40OlwiMTBcIixcIjEwLjBcIjpcIjEwXCJ9O3ZhciBBbj0odCxpKT0+aSYmbShpLFlzKXx8ZnVuY3Rpb24odCl7cmV0dXJuIG0odCxRcykmJiFtKHQsc24pJiYhbSh0LEdzKX0odCksRG49ZnVuY3Rpb24odCxpKXtyZXR1cm4gaT1pfHxcIlwiLG0odCxcIiBPUFIvXCIpJiZtKHQsXCJNaW5pXCIpP2huOm0odCxcIiBPUFIvXCIpP3VuOk9uLnRlc3QodCk/WnM6bSh0LFwiSUVcIitIcyl8fG0odCxcIldQRGVza3RvcFwiKT9sbjptKHQsZW4pP3JuOm0odCxkbil8fG0odCxcIkVkZy9cIik/dm46bSh0LFwiRkJJT1NcIik/XCJGYWNlYm9vayBcIitIczptKHQsXCJVQ1dFQlwiKXx8bSh0LFwiVUNCcm93c2VyXCIpP1wiVUMgQnJvd3NlclwiOm0odCxcIkNyaU9TXCIpP29uOm0odCxcIkNyTW9cIil8fG0odCxzbik/c246bSh0LEdzKSYmbSh0LFFzKT9tbjptKHQsXCJGeGlPU1wiKT9mbjptKHQudG9Mb3dlckNhc2UoKSxJbi50b0xvd2VyQ2FzZSgpKT9JbjpBbih0LGkpP20odCxIcyk/Ym46UXM6bSh0LGNuKT9jbjptKHQsXCJNU0lFXCIpfHxtKHQsXCJUcmlkZW50L1wiKT9hbjptKHQsXCJHZWNrb1wiKT9jbjpcIlwifSxMbj17W2xuXTpbbmV3IFJlZ0V4cChcInJ2OlwiK1BuKV0sW3ZuXTpbbmV3IFJlZ0V4cChkbitcIj9cXFxcL1wiK1BuKV0sW3NuXTpbbmV3IFJlZ0V4cChcIihcIitzbitcInxDck1vKVxcXFwvXCIrUG4pXSxbb25dOltuZXcgUmVnRXhwKFwiQ3JpT1NcXFxcL1wiK1BuKV0sXCJVQyBCcm93c2VyXCI6W25ldyBSZWdFeHAoXCIoVUNCcm93c2VyfFVDV0VCKVxcXFwvXCIrUG4pXSxbUXNdOltSbl0sW2JuXTpbUm5dLFt1bl06W25ldyBSZWdFeHAoXCIoT3BlcmF8T1BSKVxcXFwvXCIrUG4pXSxbY25dOltuZXcgUmVnRXhwKGNuK1wiXFxcXC9cIitQbildLFtmbl06W25ldyBSZWdFeHAoXCJGeGlPU1xcXFwvXCIrUG4pXSxbSW5dOltuZXcgUmVnRXhwKFwiS29ucXVlcm9yWzovXT9cIitQbixcImlcIildLFtac106W25ldyBSZWdFeHAoWnMrXCIgXCIrUG4pLFJuXSxbbW5dOltuZXcgUmVnRXhwKFwiYW5kcm9pZFxcXFxzXCIrUG4sXCJpXCIpXSxbcm5dOltuZXcgUmVnRXhwKGVuK1wiXFxcXC9cIitQbildLFthbl06W25ldyBSZWdFeHAoXCIocnY6fE1TSUUgKVwiK1BuKV0sTW96aWxsYTpbbmV3IFJlZ0V4cChcInJ2OlwiK1BuKV19LE5uPWZ1bmN0aW9uKHQsaSl7dmFyIGU9RG4odCxpKSxyPUxuW2VdO2lmKFIocikpcmV0dXJuIG51bGw7Zm9yKHZhciBzPTA7czxyLmxlbmd0aDtzKyspe3ZhciBuPXJbc10sbz10Lm1hdGNoKG4pO2lmKG8pcmV0dXJuIHBhcnNlRmxvYXQob1tvLmxlbmd0aC0yXSl9cmV0dXJuIG51bGx9LGpuPVtbbmV3IFJlZ0V4cChfbitcIjsgXCIrX24rXCIgKC4qPylbKTtdXCIsXCJpXCIpLHQ9Pltfbix0JiZ0WzFdfHxcIlwiXV0sW25ldyBSZWdFeHAocG4sXCJpXCIpLFtwbixcIlwiXV0sW25ldyBSZWdFeHAoZ24sXCJpXCIpLFtnbixcIlwiXV0sW09uLFtacyxcIlwiXV0sW25ldyBSZWdFeHAod24sXCJpXCIpLCh0LGkpPT57aWYoL1Bob25lLy50ZXN0KGkpfHwvV1BEZXNrdG9wLy50ZXN0KGkpKXJldHVyblt5bixcIlwiXTtpZihuZXcgUmVnRXhwKEhzKS50ZXN0KGkpJiYhL0lFTW9iaWxlXFxiLy50ZXN0KGkpKXJldHVyblt3bitcIiBcIitIcyxcIlwiXTt2YXIgZT0vV2luZG93cyBOVCAoWzAtOS5dKykvaS5leGVjKGkpO2lmKGUmJmVbMV0pe3ZhciByPWVbMV0scz1GbltyXXx8XCJcIjtyZXR1cm4vYXJtL2kudGVzdChpKSYmKHM9XCJSVFwiKSxbd24sc119cmV0dXJuW3duLFwiXCJdfV0sWy8oKGlQaG9uZXxpUGFkfGlQb2QpLio/T1MgKFxcZCspXyhcXGQrKV8/KFxcZCspP3xpUGhvbmUpLyx0PT57aWYodCYmdFszXSl7dmFyIGk9W3RbM10sdFs0XSx0WzVdfHxcIjBcIl07cmV0dXJuW1dzLGkuam9pbihcIi5cIildfXJldHVybltXcyxcIlwiXX1dLFsvKHdhdGNoLipcXC8oXFxkK1xcLlxcZCtcXC5cXGQrKXx3YXRjaCBvcywoXFxkK1xcLlxcZCspLCkvaSx0PT57dmFyIGk9XCJcIjtyZXR1cm4gdCYmdC5sZW5ndGg+PTMmJihpPVIodFsyXSk/dFszXTp0WzJdKSxbXCJ3YXRjaE9TXCIsaV19XSxbbmV3IFJlZ0V4cChcIihcIitHcytcIiAoXFxcXGQrKVxcXFwuKFxcXFxkKylcXFxcLj8oXFxcXGQrKT98XCIrR3MrXCIpXCIsXCJpXCIpLHQ9PntpZih0JiZ0WzJdKXt2YXIgaT1bdFsyXSx0WzNdLHRbNF18fFwiMFwiXTtyZXR1cm5bR3MsaS5qb2luKFwiLlwiKV19cmV0dXJuW0dzLFwiXCJdfV0sWy9NYWMgT1MgWCAoXFxkKylbXy5dKFxcZCspW18uXT8oXFxkKyk/L2ksdD0+e3ZhciBpPVtcIk1hYyBPUyBYXCIsXCJcIl07aWYodCYmdFsxXSl7dmFyIGU9W3RbMV0sdFsyXSx0WzNdfHxcIjBcIl07aVsxXT1lLmpvaW4oXCIuXCIpfXJldHVybiBpfV0sWy9NYWMvaSxbXCJNYWMgT1MgWFwiLFwiXCJdXSxbL0NyT1MvLFtubixcIlwiXV0sWy9MaW51eHxkZWJpYW4vaSxbXCJMaW51eFwiLFwiXCJdXV0sem49ZnVuY3Rpb24odCl7cmV0dXJuIENuLnRlc3QodCk/cG46TW4udGVzdCh0KT9nbjpUbi50ZXN0KHQpP19uOm5ldyBSZWdFeHAoJG4sXCJpXCIpLnRlc3QodCk/JG46bmV3IFJlZ0V4cChcIihcIit5bitcInxXUERlc2t0b3ApXCIsXCJpXCIpLnRlc3QodCk/eW46L2lQYWQvLnRlc3QodCk/S3M6L2lQb2QvLnRlc3QodCk/XCJpUG9kIFRvdWNoXCI6L2lQaG9uZS8udGVzdCh0KT9cImlQaG9uZVwiOi8od2F0Y2gpKD86ID9vc1ssL118XFxkLFxcZFxcLylbXFxkLl0rL2kudGVzdCh0KT9YczpPbi50ZXN0KHQpP1pzOi8oa29ibylcXHMoZXJlYWRlcnx0b3VjaCkvaS50ZXN0KHQpP1wiS29ib1wiOm5ldyBSZWdFeHAoU24sXCJpXCIpLnRlc3QodCk/U246LyhrZlthLXpdezJ9d2l8YWVvW2Mtcl17Mn0pKCBidWl8XFwpKS9pLnRlc3QodCl8fC8oa2ZbYS16XSspKCBidWl8XFwpKS4rc2lsa1xcLy9pLnRlc3QodCk/XCJLaW5kbGUgRmlyZVwiOi8oQW5kcm9pZHxaVEUpL2kudGVzdCh0KT8hbmV3IFJlZ0V4cChIcykudGVzdCh0KXx8Lyg5MTM4QnxUQjc4MkJ8TmV4dXMgWzk3XXxwaXhlbCBjfEhVQVdFSVNIVHxCVFZ8bm9ibGUgbm9va3xzbWFydCB1bHRyYSA2KS9pLnRlc3QodCk/L3BpeGVsW1xcZGF4bCBdezEsNn0vaS50ZXN0KHQpJiYhL3BpeGVsIGMvaS50ZXN0KHQpfHwvKGh1YXdlaW1lZC1hbDAwfHRhaC18QVBBfFNNLUc5MnxpOTgwfHp0ZXxVMzA0QUEpL2kudGVzdCh0KXx8L2xteTQ3di9pLnRlc3QodCkmJiEvUVRBUVozL2kudGVzdCh0KT9HczpWczpHczpuZXcgUmVnRXhwKFwiKHBkYXxcIitIcytcIilcIixcImlcIikudGVzdCh0KT94bjpuZXcgUmVnRXhwKEpzLFwiaVwiKS50ZXN0KHQpJiYhbmV3IFJlZ0V4cChKcytcIiBwY1wiLFwiaVwiKS50ZXN0KHQpP0VuOlwiXCJ9LFVuPVwiaHR0cHM/Oi8vKC4qKVwiLHFuPVtcImdjbGlkXCIsXCJnY2xzcmNcIixcImRjbGlkXCIsXCJnYnJhaWRcIixcIndicmFpZFwiLFwiZmJjbGlkXCIsXCJtc2Nsa2lkXCIsXCJ0d2NsaWRcIixcImxpX2ZhdF9pZFwiLFwiaWdzaGlkXCIsXCJ0dGNsaWRcIixcInJkdF9jaWRcIixcImVwaWtcIixcInFjbGlkXCIsXCJzY2NpZFwiLFwiaXJjbGlkXCIsXCJfa3hcIl0sQm49SyhbXCJ1dG1fc291cmNlXCIsXCJ1dG1fbWVkaXVtXCIsXCJ1dG1fY2FtcGFpZ25cIixcInV0bV9jb250ZW50XCIsXCJ1dG1fdGVybVwiLFwiZ2FkX3NvdXJjZVwiLFwibWNfY2lkXCJdLHFuKSxIbj1cIjxtYXNrZWQ+XCI7ZnVuY3Rpb24gV24odCxpLGUpe2lmKCFvKXJldHVybnt9O3ZhciByPWk/SyhbXSxxbixlfHxbXSk6W107cmV0dXJuIEduKGtpKG8uVVJMLHIsSG4pLHQpfWZ1bmN0aW9uIEduKHQsaSl7dmFyIGU9Qm4uY29uY2F0KGl8fFtdKSxyPXt9O3JldHVybiBKKGUsKGZ1bmN0aW9uKGkpe3ZhciBlPSRpKHQsaSk7cltpXT1lfHxudWxsfSkpLHJ9ZnVuY3Rpb24gSm4odCl7dmFyIGk9ZnVuY3Rpb24odCl7cmV0dXJuIHQ/MD09PXQuc2VhcmNoKFVuK1wiZ29vZ2xlLihbXi8/XSopXCIpP1wiZ29vZ2xlXCI6MD09PXQuc2VhcmNoKFVuK1wiYmluZy5jb21cIik/XCJiaW5nXCI6MD09PXQuc2VhcmNoKFVuK1wieWFob28uY29tXCIpP1wieWFob29cIjowPT09dC5zZWFyY2goVW4rXCJkdWNrZHVja2dvLmNvbVwiKT9cImR1Y2tkdWNrZ29cIjpudWxsOm51bGx9KHQpLGU9XCJ5YWhvb1wiIT1pP1wicVwiOlwicFwiLHI9e307aWYoIUMoaSkpe3IuJHNlYXJjaF9lbmdpbmU9aTt2YXIgcz1vPyRpKG8ucmVmZXJyZXIsZSk6XCJcIjtzLmxlbmd0aCYmKHIucGhfa2V5d29yZD1zKX1yZXR1cm4gcn1mdW5jdGlvbiBWbigpe3JldHVybiBuYXZpZ2F0b3IubGFuZ3VhZ2V8fG5hdmlnYXRvci51c2VyTGFuZ3VhZ2V9ZnVuY3Rpb24gS24oKXtyZXR1cm4obnVsbD09bz92b2lkIDA6by5yZWZlcnJlcil8fFwiJGRpcmVjdFwifWZ1bmN0aW9uIFluKHQsaSl7dmFyIGU9dD9LKFtdLHFuLGl8fFtdKTpbXSxyPW51bGw9PWE/dm9pZCAwOmEuaHJlZi5zdWJzdHJpbmcoMCwxZTMpO3JldHVybntyOktuKCkuc3Vic3RyaW5nKDAsMWUzKSx1OnI/a2kocixlLEhuKTp2b2lkIDB9fWZ1bmN0aW9uIFhuKHQpe3ZhciBpLHtyOmUsdTpyfT10LHM9eyRyZWZlcnJlcjplLCRyZWZlcnJpbmdfZG9tYWluOm51bGw9PWU/dm9pZCAwOlwiJGRpcmVjdFwiPT1lP1wiJGRpcmVjdFwiOm51bGw9PShpPXlpKGUpKT92b2lkIDA6aS5ob3N0fTtpZihyKXtzLiRjdXJyZW50X3VybD1yO3ZhciBuPXlpKHIpO3MuJGhvc3Q9bnVsbD09bj92b2lkIDA6bi5ob3N0LHMuJHBhdGhuYW1lPW51bGw9PW4/dm9pZCAwOm4ucGF0aG5hbWU7dmFyIG89R24ocik7VihzLG8pfWlmKGUpe3ZhciBhPUpuKGUpO1YocyxhKX1yZXR1cm4gc31mdW5jdGlvbiBRbigpe3RyeXtyZXR1cm4gSW50bC5EYXRlVGltZUZvcm1hdCgpLnJlc29sdmVkT3B0aW9ucygpLnRpbWVab25lfWNhdGNoKHQpe3JldHVybn19ZnVuY3Rpb24gWm4oKXt0cnl7cmV0dXJuKG5ldyBEYXRlKS5nZXRUaW1lem9uZU9mZnNldCgpfWNhdGNoKHQpe3JldHVybn19ZnVuY3Rpb24gdG8oaSxlKXtpZighZClyZXR1cm57fTt2YXIgcixzLG4sbz1pP0soW10scW4sZXx8W10pOltdLFtsLHVdPWZ1bmN0aW9uKHQpe2Zvcih2YXIgaT0wO2k8am4ubGVuZ3RoO2krKyl7dmFyW2Uscl09am5baV0scz1lLmV4ZWModCksbj1zJiYoRShyKT9yKHMsdCk6cik7aWYobilyZXR1cm4gbn1yZXR1cm5bXCJcIixcIlwiXX0oZCk7cmV0dXJuIFYoWih7JG9zOmwsJG9zX3ZlcnNpb246dSwkYnJvd3NlcjpEbihkLG5hdmlnYXRvci52ZW5kb3IpLCRkZXZpY2U6em4oZCksJGRldmljZV90eXBlOihzPWQsbj16bihzKSxuPT09S3N8fG49PT1Wc3x8XCJLb2JvXCI9PT1ufHxcIktpbmRsZSBGaXJlXCI9PT1ufHxuPT09RW4/SnM6bj09PXBufHxuPT09X258fG49PT1nbnx8bj09PSRuP1wiQ29uc29sZVwiOm49PT1Ycz9cIldlYXJhYmxlXCI6bj9IczpcIkRlc2t0b3BcIiksJHRpbWV6b25lOlFuKCksJHRpbWV6b25lX29mZnNldDpabigpfSkseyRjdXJyZW50X3VybDpraShudWxsPT1hP3ZvaWQgMDphLmhyZWYsbyxIbiksJGhvc3Q6bnVsbD09YT92b2lkIDA6YS5ob3N0LCRwYXRobmFtZTpudWxsPT1hP3ZvaWQgMDphLnBhdGhuYW1lLCRyYXdfdXNlcl9hZ2VudDpkLmxlbmd0aD4xZTM/ZC5zdWJzdHJpbmcoMCw5OTcpK1wiLi4uXCI6ZCwkYnJvd3Nlcl92ZXJzaW9uOk5uKGQsbmF2aWdhdG9yLnZlbmRvciksJGJyb3dzZXJfbGFuZ3VhZ2U6Vm4oKSwkYnJvd3Nlcl9sYW5ndWFnZV9wcmVmaXg6KHI9Vm4oKSxcInN0cmluZ1wiPT10eXBlb2Ygcj9yLnNwbGl0KFwiLVwiKVswXTp2b2lkIDApLCRzY3JlZW5faGVpZ2h0Om51bGw9PXQ/dm9pZCAwOnQuc2NyZWVuLmhlaWdodCwkc2NyZWVuX3dpZHRoOm51bGw9PXQ/dm9pZCAwOnQuc2NyZWVuLndpZHRoLCR2aWV3cG9ydF9oZWlnaHQ6bnVsbD09dD92b2lkIDA6dC5pbm5lckhlaWdodCwkdmlld3BvcnRfd2lkdGg6bnVsbD09dD92b2lkIDA6dC5pbm5lcldpZHRoLCRsaWI6XCJ3ZWJcIiwkbGliX3ZlcnNpb246Yy5MSUJfVkVSU0lPTiwkaW5zZXJ0X2lkOk1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cmluZygyLDEwKStNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHJpbmcoMiwxMCksJHRpbWU6RGF0ZS5ub3coKS8xZTN9KX12YXIgaW89eihcIltGZWF0dXJlRmxhZ3NdXCIpLGVvPVwiJGFjdGl2ZV9mZWF0dXJlX2ZsYWdzXCIscm89XCIkb3ZlcnJpZGVfZmVhdHVyZV9mbGFnc1wiLHNvPVwiJGZlYXR1cmVfZmxhZ19wYXlsb2Fkc1wiLG5vPVwiJG92ZXJyaWRlX2ZlYXR1cmVfZmxhZ19wYXlsb2Fkc1wiLG9vPVwiJGZlYXR1cmVfZmxhZ19yZXF1ZXN0X2lkXCIsYW89dD0+e3ZhciBpPXt9O2Zvcih2YXJbZSxyXW9mIFkodHx8e30pKXImJihpW2VdPXIpO3JldHVybiBpfSxsbz10PT57dmFyIGk9dC5mbGFncztyZXR1cm4gaT8odC5mZWF0dXJlRmxhZ3M9T2JqZWN0LmZyb21FbnRyaWVzKE9iamVjdC5rZXlzKGkpLm1hcCgodD0+e3ZhciBlO3JldHVyblt0LG51bGwhPT0oZT1pW3RdLnZhcmlhbnQpJiZ2b2lkIDAhPT1lP2U6aVt0XS5lbmFibGVkXX0pKSksdC5mZWF0dXJlRmxhZ1BheWxvYWRzPU9iamVjdC5mcm9tRW50cmllcyhPYmplY3Qua2V5cyhpKS5maWx0ZXIoKHQ9PmlbdF0uZW5hYmxlZCkpLmZpbHRlcigodD0+e3ZhciBlO3JldHVybiBudWxsPT0oZT1pW3RdLm1ldGFkYXRhKT92b2lkIDA6ZS5wYXlsb2FkfSkpLm1hcCgodD0+e3ZhciBlO3JldHVyblt0LG51bGw9PShlPWlbdF0ubWV0YWRhdGEpP3ZvaWQgMDplLnBheWxvYWRdfSkpKSk6aW8ud2FybihcIlVzaW5nIGFuIG9sZGVyIHZlcnNpb24gb2YgdGhlIGZlYXR1cmUgZmxhZ3MgZW5kcG9pbnQuIFBsZWFzZSB1cGdyYWRlIHlvdXIgUG9zdEhvZyBzZXJ2ZXIgdG8gdGhlIGxhdGVzdCB2ZXJzaW9uXCIpLHR9LHVvPWZ1bmN0aW9uKHQpe3JldHVybiB0LkZlYXR1cmVGbGFncz1cImZlYXR1cmVfZmxhZ3NcIix0LlJlY29yZGluZ3M9XCJyZWNvcmRpbmdzXCIsdH0oe30pO2NsYXNzIGhve2NvbnN0cnVjdG9yKHQpe3RoaXMubGU9ITEsdGhpcy51ZT0hMSx0aGlzLmhlPSExLHRoaXMuZGU9ITEsdGhpcy52ZT0hMSx0aGlzLmNlPSExLHRoaXMuZmU9ITEsdGhpcy5faW5zdGFuY2U9dCx0aGlzLmZlYXR1cmVGbGFnRXZlbnRIYW5kbGVycz1bXX1kZWNpZGUoKXtpZih0aGlzLl9pbnN0YW5jZS5jb25maWcuX19wcmV2aWV3X3JlbW90ZV9jb25maWcpdGhpcy5jZT0hMDtlbHNle3ZhciB0PSF0aGlzLnBlJiYodGhpcy5faW5zdGFuY2UuY29uZmlnLmFkdmFuY2VkX2Rpc2FibGVfZmVhdHVyZV9mbGFnc3x8dGhpcy5faW5zdGFuY2UuY29uZmlnLmFkdmFuY2VkX2Rpc2FibGVfZmVhdHVyZV9mbGFnc19vbl9maXJzdF9sb2FkKTt0aGlzLmdlKHtkaXNhYmxlRmxhZ3M6dH0pfX1nZXQgaGFzTG9hZGVkRmxhZ3MoKXtyZXR1cm4gdGhpcy51ZX1nZXRGbGFncygpe3JldHVybiBPYmplY3Qua2V5cyh0aGlzLmdldEZsYWdWYXJpYW50cygpKX1nZXRGbGFnc1dpdGhEZXRhaWxzKCl7dmFyIHQ9dGhpcy5faW5zdGFuY2UuZ2V0X3Byb3BlcnR5KFJ0KSxpPXRoaXMuX2luc3RhbmNlLmdldF9wcm9wZXJ0eShybyksZT10aGlzLl9pbnN0YW5jZS5nZXRfcHJvcGVydHkobm8pO2lmKCFlJiYhaSlyZXR1cm4gdHx8e307dmFyIHI9Vih7fSx0fHx7fSkscz1bLi4ubmV3IFNldChbLi4uT2JqZWN0LmtleXMoZXx8e30pLC4uLk9iamVjdC5rZXlzKGl8fHt9KV0pXTtmb3IodmFyIG4gb2Ygcyl7dmFyIG8sYSxsPXJbbl0sdT1udWxsPT1pP3ZvaWQgMDppW25dLGg9Uih1KT9udWxsIT09KG89bnVsbD09bD92b2lkIDA6bC5lbmFibGVkKSYmdm9pZCAwIT09byYmbzohIXUsZD1SKHUpP2wudmFyaWFudDpcInN0cmluZ1wiPT10eXBlb2YgdT91OnZvaWQgMCx2PW51bGw9PWU/dm9pZCAwOmVbbl0sYz1CKHt9LGwse2VuYWJsZWQ6aCx2YXJpYW50Omg/bnVsbCE9ZD9kOm51bGw9PWw/dm9pZCAwOmwudmFyaWFudDp2b2lkIDB9KTtpZihoIT09KG51bGw9PWw/dm9pZCAwOmwuZW5hYmxlZCkmJihjLm9yaWdpbmFsX2VuYWJsZWQ9bnVsbD09bD92b2lkIDA6bC5lbmFibGVkKSxkIT09KG51bGw9PWw/dm9pZCAwOmwudmFyaWFudCkmJihjLm9yaWdpbmFsX3ZhcmlhbnQ9bnVsbD09bD92b2lkIDA6bC52YXJpYW50KSx2KWMubWV0YWRhdGE9Qih7fSxudWxsPT1sP3ZvaWQgMDpsLm1ldGFkYXRhLHtwYXlsb2FkOnYsb3JpZ2luYWxfcGF5bG9hZDpudWxsPT1sfHxudWxsPT0oYT1sLm1ldGFkYXRhKT92b2lkIDA6YS5wYXlsb2FkfSk7cltuXT1jfXJldHVybiB0aGlzLmxlfHwoaW8ud2FybihcIiBPdmVycmlkaW5nIGZlYXR1cmUgZmxhZyBkZXRhaWxzIVwiLHtmbGFnRGV0YWlsczp0LG92ZXJyaWRkZW5QYXlsb2FkczplLGZpbmFsRGV0YWlsczpyfSksdGhpcy5sZT0hMCkscn1nZXRGbGFnVmFyaWFudHMoKXt2YXIgdD10aGlzLl9pbnN0YW5jZS5nZXRfcHJvcGVydHkoSXQpLGk9dGhpcy5faW5zdGFuY2UuZ2V0X3Byb3BlcnR5KHJvKTtpZighaSlyZXR1cm4gdHx8e307Zm9yKHZhciBlPVYoe30sdCkscj1PYmplY3Qua2V5cyhpKSxzPTA7czxyLmxlbmd0aDtzKyspZVtyW3NdXT1pW3Jbc11dO3JldHVybiB0aGlzLmxlfHwoaW8ud2FybihcIiBPdmVycmlkaW5nIGZlYXR1cmUgZmxhZ3MhXCIse2VuYWJsZWRGbGFnczp0LG92ZXJyaWRkZW5GbGFnczppLGZpbmFsRmxhZ3M6ZX0pLHRoaXMubGU9ITApLGV9Z2V0RmxhZ1BheWxvYWRzKCl7dmFyIHQ9dGhpcy5faW5zdGFuY2UuZ2V0X3Byb3BlcnR5KHNvKSxpPXRoaXMuX2luc3RhbmNlLmdldF9wcm9wZXJ0eShubyk7aWYoIWkpcmV0dXJuIHR8fHt9O2Zvcih2YXIgZT1WKHt9LHR8fHt9KSxyPU9iamVjdC5rZXlzKGkpLHM9MDtzPHIubGVuZ3RoO3MrKyllW3Jbc11dPWlbcltzXV07cmV0dXJuIHRoaXMubGV8fChpby53YXJuKFwiIE92ZXJyaWRpbmcgZmVhdHVyZSBmbGFnIHBheWxvYWRzIVwiLHtmbGFnUGF5bG9hZHM6dCxvdmVycmlkZGVuUGF5bG9hZHM6aSxmaW5hbFBheWxvYWRzOmV9KSx0aGlzLmxlPSEwKSxlfXJlbG9hZEZlYXR1cmVGbGFncygpe3RoaXMuZGV8fHRoaXMuX2luc3RhbmNlLmNvbmZpZy5hZHZhbmNlZF9kaXNhYmxlX2ZlYXR1cmVfZmxhZ3N8fHRoaXMucGV8fCh0aGlzLnBlPXNldFRpbWVvdXQoKCgpPT57dGhpcy5nZSgpfSksNSkpfV9lKCl7Y2xlYXJUaW1lb3V0KHRoaXMucGUpLHRoaXMucGU9dm9pZCAwfWVuc3VyZUZsYWdzTG9hZGVkKCl7dGhpcy51ZXx8dGhpcy5oZXx8dGhpcy5wZXx8dGhpcy5yZWxvYWRGZWF0dXJlRmxhZ3MoKX1zZXRBbm9ueW1vdXNEaXN0aW5jdElkKHQpe3RoaXMuJGFub25fZGlzdGluY3RfaWQ9dH1zZXRSZWxvYWRpbmdQYXVzZWQodCl7dGhpcy5kZT10fWdlKHQpe3ZhciBpO2lmKHRoaXMuX2UoKSwhdGhpcy5faW5zdGFuY2UuY29uZmlnLmFkdmFuY2VkX2Rpc2FibGVfZGVjaWRlKWlmKHRoaXMuaGUpdGhpcy52ZT0hMDtlbHNle3ZhciBlPXt0b2tlbjp0aGlzLl9pbnN0YW5jZS5jb25maWcudG9rZW4sZGlzdGluY3RfaWQ6dGhpcy5faW5zdGFuY2UuZ2V0X2Rpc3RpbmN0X2lkKCksZ3JvdXBzOnRoaXMuX2luc3RhbmNlLmdldEdyb3VwcygpLCRhbm9uX2Rpc3RpbmN0X2lkOnRoaXMuJGFub25fZGlzdGluY3RfaWQscGVyc29uX3Byb3BlcnRpZXM6Qih7fSwobnVsbD09KGk9dGhpcy5faW5zdGFuY2UucGVyc2lzdGVuY2UpP3ZvaWQgMDppLmdldF9pbml0aWFsX3Byb3BzKCkpfHx7fSx0aGlzLl9pbnN0YW5jZS5nZXRfcHJvcGVydHkoVHQpfHx7fSksZ3JvdXBfcHJvcGVydGllczp0aGlzLl9pbnN0YW5jZS5nZXRfcHJvcGVydHkoTXQpfTsobnVsbCE9dCYmdC5kaXNhYmxlRmxhZ3N8fHRoaXMuX2luc3RhbmNlLmNvbmZpZy5hZHZhbmNlZF9kaXNhYmxlX2ZlYXR1cmVfZmxhZ3MpJiYoZS5kaXNhYmxlX2ZsYWdzPSEwKTt2YXIgcj10aGlzLl9pbnN0YW5jZS5jb25maWcuX19wcmV2aWV3X2ZsYWdzX3YyJiZ0aGlzLl9pbnN0YW5jZS5jb25maWcuX19wcmV2aWV3X3JlbW90ZV9jb25maWc7ciYmKGUudGltZXpvbmU9UW4oKSksdGhpcy5oZT0hMCx0aGlzLl9pbnN0YW5jZS5tZSh7bWV0aG9kOlwiUE9TVFwiLHVybDp0aGlzLl9pbnN0YW5jZS5yZXF1ZXN0Um91dGVyLmVuZHBvaW50Rm9yKFwiYXBpXCIscj9cIi9mbGFncy8/dj0yXCI6XCIvZGVjaWRlLz92PTRcIiksZGF0YTplLGNvbXByZXNzaW9uOnRoaXMuX2luc3RhbmNlLmNvbmZpZy5kaXNhYmxlX2NvbXByZXNzaW9uP3ZvaWQgMDpnLkJhc2U2NCx0aW1lb3V0OnRoaXMuX2luc3RhbmNlLmNvbmZpZy5mZWF0dXJlX2ZsYWdfcmVxdWVzdF90aW1lb3V0X21zLGNhbGxiYWNrOnQ9Pnt2YXIgaSxyLHM9ITA7KDIwMD09PXQuc3RhdHVzQ29kZSYmKHRoaXMudmV8fCh0aGlzLiRhbm9uX2Rpc3RpbmN0X2lkPXZvaWQgMCkscz0hMSksdGhpcy5oZT0hMSx0aGlzLmNlKXx8KHRoaXMuY2U9ITAsdGhpcy5faW5zdGFuY2UuYmUobnVsbCE9PShyPXQuanNvbikmJnZvaWQgMCE9PXI/cjp7fSkpO2lmKCFlLmRpc2FibGVfZmxhZ3N8fHRoaXMudmUpaWYodGhpcy5mZT0hcyx0Lmpzb24mJm51bGwhPShpPXQuanNvbi5xdW90YUxpbWl0ZWQpJiZpLmluY2x1ZGVzKHVvLkZlYXR1cmVGbGFncykpaW8ud2FybihcIllvdSBoYXZlIGhpdCB5b3VyIGZlYXR1cmUgZmxhZ3MgcXVvdGEgbGltaXQsIGFuZCB3aWxsIG5vdCBiZSBhYmxlIHRvIGxvYWQgZmVhdHVyZSBmbGFncyB1bnRpbCB0aGUgcXVvdGEgaXMgcmVzZXQuICBQbGVhc2UgdmlzaXQgaHR0cHM6Ly9wb3N0aG9nLmNvbS9kb2NzL2JpbGxpbmcvbGltaXRzLWFsZXJ0cyB0byBsZWFybiBtb3JlLlwiKTtlbHNle3ZhciBuO2lmKCFlLmRpc2FibGVfZmxhZ3MpdGhpcy5yZWNlaXZlZEZlYXR1cmVGbGFncyhudWxsIT09KG49dC5qc29uKSYmdm9pZCAwIT09bj9uOnt9LHMpO3RoaXMudmUmJih0aGlzLnZlPSExLHRoaXMuZ2UoKSl9fX0pfX1nZXRGZWF0dXJlRmxhZyh0LGkpe2lmKHZvaWQgMD09PWkmJihpPXt9KSx0aGlzLnVlfHx0aGlzLmdldEZsYWdzKCkmJnRoaXMuZ2V0RmxhZ3MoKS5sZW5ndGg+MCl7dmFyIGU9dGhpcy5nZXRGbGFnVmFyaWFudHMoKVt0XSxyPVwiXCIrZSxzPXRoaXMuX2luc3RhbmNlLmdldF9wcm9wZXJ0eShvbyl8fHZvaWQgMCxuPXRoaXMuX2luc3RhbmNlLmdldF9wcm9wZXJ0eShGdCl8fHt9O2lmKChpLnNlbmRfZXZlbnR8fCEoXCJzZW5kX2V2ZW50XCJpbiBpKSkmJighKHQgaW4gbil8fCFuW3RdLmluY2x1ZGVzKHIpKSl7dmFyIG8sYSxsLHUsaCxkLHYsYyxmO3goblt0XSk/blt0XS5wdXNoKHIpOm5bdF09W3JdLG51bGw9PShvPXRoaXMuX2luc3RhbmNlLnBlcnNpc3RlbmNlKXx8by5yZWdpc3Rlcih7W0Z0XTpufSk7dmFyIHA9dGhpcy5nZXRGZWF0dXJlRmxhZ0RldGFpbHModCksZz17JGZlYXR1cmVfZmxhZzp0LCRmZWF0dXJlX2ZsYWdfcmVzcG9uc2U6ZSwkZmVhdHVyZV9mbGFnX3BheWxvYWQ6dGhpcy5nZXRGZWF0dXJlRmxhZ1BheWxvYWQodCl8fG51bGwsJGZlYXR1cmVfZmxhZ19yZXF1ZXN0X2lkOnMsJGZlYXR1cmVfZmxhZ19ib290c3RyYXBwZWRfcmVzcG9uc2U6KG51bGw9PShhPXRoaXMuX2luc3RhbmNlLmNvbmZpZy5ib290c3RyYXApfHxudWxsPT0oYT1hLmZlYXR1cmVGbGFncyk/dm9pZCAwOmFbdF0pfHxudWxsLCRmZWF0dXJlX2ZsYWdfYm9vdHN0cmFwcGVkX3BheWxvYWQ6KG51bGw9PShsPXRoaXMuX2luc3RhbmNlLmNvbmZpZy5ib290c3RyYXApfHxudWxsPT0obD1sLmZlYXR1cmVGbGFnUGF5bG9hZHMpP3ZvaWQgMDpsW3RdKXx8bnVsbCwkdXNlZF9ib290c3RyYXBfdmFsdWU6IXRoaXMuZmV9O1IobnVsbD09cHx8bnVsbD09KHU9cC5tZXRhZGF0YSk/dm9pZCAwOnUudmVyc2lvbil8fChnLiRmZWF0dXJlX2ZsYWdfdmVyc2lvbj1wLm1ldGFkYXRhLnZlcnNpb24pO3ZhciBfLG09bnVsbCE9PShoPW51bGw9PXB8fG51bGw9PShkPXAucmVhc29uKT92b2lkIDA6ZC5kZXNjcmlwdGlvbikmJnZvaWQgMCE9PWg/aDpudWxsPT1wfHxudWxsPT0odj1wLnJlYXNvbik/dm9pZCAwOnYuY29kZTtpZihtJiYoZy4kZmVhdHVyZV9mbGFnX3JlYXNvbj1tKSxudWxsIT1wJiZudWxsIT0oYz1wLm1ldGFkYXRhKSYmYy5pZCYmKGcuJGZlYXR1cmVfZmxhZ19pZD1wLm1ldGFkYXRhLmlkKSxSKG51bGw9PXA/dm9pZCAwOnAub3JpZ2luYWxfdmFyaWFudCkmJlIobnVsbD09cD92b2lkIDA6cC5vcmlnaW5hbF9lbmFibGVkKXx8KGcuJGZlYXR1cmVfZmxhZ19vcmlnaW5hbF9yZXNwb25zZT1SKHAub3JpZ2luYWxfdmFyaWFudCk/cC5vcmlnaW5hbF9lbmFibGVkOnAub3JpZ2luYWxfdmFyaWFudCksbnVsbCE9cCYmbnVsbCE9KGY9cC5tZXRhZGF0YSkmJmYub3JpZ2luYWxfcGF5bG9hZClnLiRmZWF0dXJlX2ZsYWdfb3JpZ2luYWxfcGF5bG9hZD1udWxsPT1wfHxudWxsPT0oXz1wLm1ldGFkYXRhKT92b2lkIDA6Xy5vcmlnaW5hbF9wYXlsb2FkO3RoaXMuX2luc3RhbmNlLmNhcHR1cmUoXCIkZmVhdHVyZV9mbGFnX2NhbGxlZFwiLGcpfXJldHVybiBlfWlvLndhcm4oJ2dldEZlYXR1cmVGbGFnIGZvciBrZXkgXCInK3QrXCJcXFwiIGZhaWxlZC4gRmVhdHVyZSBmbGFncyBkaWRuJ3QgbG9hZCBpbiB0aW1lLlwiKX1nZXRGZWF0dXJlRmxhZ0RldGFpbHModCl7cmV0dXJuIHRoaXMuZ2V0RmxhZ3NXaXRoRGV0YWlscygpW3RdfWdldEZlYXR1cmVGbGFnUGF5bG9hZCh0KXtyZXR1cm4gdGhpcy5nZXRGbGFnUGF5bG9hZHMoKVt0XX1nZXRSZW1vdGVDb25maWdQYXlsb2FkKHQsaSl7dmFyIGU9dGhpcy5faW5zdGFuY2UuY29uZmlnLnRva2VuO3RoaXMuX2luc3RhbmNlLm1lKHttZXRob2Q6XCJQT1NUXCIsdXJsOnRoaXMuX2luc3RhbmNlLnJlcXVlc3RSb3V0ZXIuZW5kcG9pbnRGb3IoXCJhcGlcIixcIi9kZWNpZGUvP3Y9NFwiKSxkYXRhOntkaXN0aW5jdF9pZDp0aGlzLl9pbnN0YW5jZS5nZXRfZGlzdGluY3RfaWQoKSx0b2tlbjplfSxjb21wcmVzc2lvbjp0aGlzLl9pbnN0YW5jZS5jb25maWcuZGlzYWJsZV9jb21wcmVzc2lvbj92b2lkIDA6Zy5CYXNlNjQsdGltZW91dDp0aGlzLl9pbnN0YW5jZS5jb25maWcuZmVhdHVyZV9mbGFnX3JlcXVlc3RfdGltZW91dF9tcyxjYWxsYmFjazplPT57dmFyIHIscz1udWxsPT0ocj1lLmpzb24pP3ZvaWQgMDpyLmZlYXR1cmVGbGFnUGF5bG9hZHM7aSgobnVsbD09cz92b2lkIDA6c1t0XSl8fHZvaWQgMCl9fSl9aXNGZWF0dXJlRW5hYmxlZCh0LGkpe2lmKHZvaWQgMD09PWkmJihpPXt9KSx0aGlzLnVlfHx0aGlzLmdldEZsYWdzKCkmJnRoaXMuZ2V0RmxhZ3MoKS5sZW5ndGg+MClyZXR1cm4hIXRoaXMuZ2V0RmVhdHVyZUZsYWcodCxpKTtpby53YXJuKCdpc0ZlYXR1cmVFbmFibGVkIGZvciBrZXkgXCInK3QrXCJcXFwiIGZhaWxlZC4gRmVhdHVyZSBmbGFncyBkaWRuJ3QgbG9hZCBpbiB0aW1lLlwiKX1hZGRGZWF0dXJlRmxhZ3NIYW5kbGVyKHQpe3RoaXMuZmVhdHVyZUZsYWdFdmVudEhhbmRsZXJzLnB1c2godCl9cmVtb3ZlRmVhdHVyZUZsYWdzSGFuZGxlcih0KXt0aGlzLmZlYXR1cmVGbGFnRXZlbnRIYW5kbGVycz10aGlzLmZlYXR1cmVGbGFnRXZlbnRIYW5kbGVycy5maWx0ZXIoKGk9PmkhPT10KSl9cmVjZWl2ZWRGZWF0dXJlRmxhZ3ModCxpKXtpZih0aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZSl7dGhpcy51ZT0hMDt2YXIgZT10aGlzLmdldEZsYWdWYXJpYW50cygpLHI9dGhpcy5nZXRGbGFnUGF5bG9hZHMoKSxzPXRoaXMuZ2V0RmxhZ3NXaXRoRGV0YWlscygpOyFmdW5jdGlvbih0LGksZSxyLHMpe3ZvaWQgMD09PWUmJihlPXt9KSx2b2lkIDA9PT1yJiYocj17fSksdm9pZCAwPT09cyYmKHM9e30pO3ZhciBuPWxvKHQpLG89bi5mbGFncyxhPW4uZmVhdHVyZUZsYWdzLGw9bi5mZWF0dXJlRmxhZ1BheWxvYWRzO2lmKGEpe3ZhciB1PXQucmVxdWVzdElkO2lmKHgoYSkpe2lvLndhcm4oXCJ2MSBvZiB0aGUgZmVhdHVyZSBmbGFncyBlbmRwb2ludCBpcyBkZXByZWNhdGVkLiBQbGVhc2UgdXNlIHRoZSBsYXRlc3QgdmVyc2lvbi5cIik7dmFyIGg9e307aWYoYSlmb3IodmFyIGQ9MDtkPGEubGVuZ3RoO2QrKyloW2FbZF1dPSEwO2kmJmkucmVnaXN0ZXIoe1tlb106YSxbSXRdOmh9KX1lbHNle3ZhciB2PWEsYz1sLGY9bzt0LmVycm9yc1doaWxlQ29tcHV0aW5nRmxhZ3MmJih2PUIoe30sZSx2KSxjPUIoe30scixjKSxmPUIoe30scyxmKSksaSYmaS5yZWdpc3RlcihCKHtbZW9dOk9iamVjdC5rZXlzKGFvKHYpKSxbSXRdOnZ8fHt9LFtzb106Y3x8e30sW1J0XTpmfHx7fX0sdT97W29vXTp1fTp7fSkpfX19KHQsdGhpcy5faW5zdGFuY2UucGVyc2lzdGVuY2UsZSxyLHMpLHRoaXMud2UoaSl9fW92ZXJyaWRlKHQsaSl7dm9pZCAwPT09aSYmKGk9ITEpLGlvLndhcm4oXCJvdmVycmlkZSBpcyBkZXByZWNhdGVkLiBQbGVhc2UgdXNlIG92ZXJyaWRlRmVhdHVyZUZsYWdzIGluc3RlYWQuXCIpLHRoaXMub3ZlcnJpZGVGZWF0dXJlRmxhZ3Moe2ZsYWdzOnQsc3VwcHJlc3NXYXJuaW5nOml9KX1vdmVycmlkZUZlYXR1cmVGbGFncyh0KXtpZighdGhpcy5faW5zdGFuY2UuX19sb2FkZWR8fCF0aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZSlyZXR1cm4gaW8udW5pbml0aWFsaXplZFdhcm5pbmcoXCJwb3N0aG9nLmZlYXR1cmVGbGFncy5vdmVycmlkZUZlYXR1cmVGbGFnc1wiKTtpZighMT09PXQpcmV0dXJuIHRoaXMuX2luc3RhbmNlLnBlcnNpc3RlbmNlLnVucmVnaXN0ZXIocm8pLHRoaXMuX2luc3RhbmNlLnBlcnNpc3RlbmNlLnVucmVnaXN0ZXIobm8pLHZvaWQgdGhpcy53ZSgpO2lmKHQmJlwib2JqZWN0XCI9PXR5cGVvZiB0JiYoXCJmbGFnc1wiaW4gdHx8XCJwYXlsb2Fkc1wiaW4gdCkpe3ZhciBpLGU9dDtpZih0aGlzLmxlPUJvb2xlYW4obnVsbCE9PShpPWUuc3VwcHJlc3NXYXJuaW5nKSYmdm9pZCAwIT09aSYmaSksXCJmbGFnc1wiaW4gZSlpZighMT09PWUuZmxhZ3MpdGhpcy5faW5zdGFuY2UucGVyc2lzdGVuY2UudW5yZWdpc3Rlcihybyk7ZWxzZSBpZihlLmZsYWdzKWlmKHgoZS5mbGFncykpe2Zvcih2YXIgcj17fSxzPTA7czxlLmZsYWdzLmxlbmd0aDtzKyspcltlLmZsYWdzW3NdXT0hMDt0aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZS5yZWdpc3Rlcih7W3JvXTpyfSl9ZWxzZSB0aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZS5yZWdpc3Rlcih7W3JvXTplLmZsYWdzfSk7cmV0dXJuXCJwYXlsb2Fkc1wiaW4gZSYmKCExPT09ZS5wYXlsb2Fkcz90aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZS51bnJlZ2lzdGVyKG5vKTplLnBheWxvYWRzJiZ0aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZS5yZWdpc3Rlcih7W25vXTplLnBheWxvYWRzfSkpLHZvaWQgdGhpcy53ZSgpfXRoaXMud2UoKX1vbkZlYXR1cmVGbGFncyh0KXtpZih0aGlzLmFkZEZlYXR1cmVGbGFnc0hhbmRsZXIodCksdGhpcy51ZSl7dmFye2ZsYWdzOmksZmxhZ1ZhcmlhbnRzOmV9PXRoaXMueWUoKTt0KGksZSl9cmV0dXJuKCk9PnRoaXMucmVtb3ZlRmVhdHVyZUZsYWdzSGFuZGxlcih0KX11cGRhdGVFYXJseUFjY2Vzc0ZlYXR1cmVFbnJvbGxtZW50KHQsaSl7dmFyIGUscj0odGhpcy5faW5zdGFuY2UuZ2V0X3Byb3BlcnR5KFB0KXx8W10pLmZpbmQoKGk9PmkuZmxhZ0tleT09PXQpKSxzPXtbXCIkZmVhdHVyZV9lbnJvbGxtZW50L1wiK3RdOml9LG49eyRmZWF0dXJlX2ZsYWc6dCwkZmVhdHVyZV9lbnJvbGxtZW50OmksJHNldDpzfTtyJiYobi4kZWFybHlfYWNjZXNzX2ZlYXR1cmVfbmFtZT1yLm5hbWUpLHRoaXMuX2luc3RhbmNlLmNhcHR1cmUoXCIkZmVhdHVyZV9lbnJvbGxtZW50X3VwZGF0ZVwiLG4pLHRoaXMuc2V0UGVyc29uUHJvcGVydGllc0ZvckZsYWdzKHMsITEpO3ZhciBvPUIoe30sdGhpcy5nZXRGbGFnVmFyaWFudHMoKSx7W3RdOml9KTtudWxsPT0oZT10aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZSl8fGUucmVnaXN0ZXIoe1tlb106T2JqZWN0LmtleXMoYW8obykpLFtJdF06b30pLHRoaXMud2UoKX1nZXRFYXJseUFjY2Vzc0ZlYXR1cmVzKHQsaSxlKXt2b2lkIDA9PT1pJiYoaT0hMSk7dmFyIHI9dGhpcy5faW5zdGFuY2UuZ2V0X3Byb3BlcnR5KFB0KSxzPWU/XCImXCIrZS5tYXAoKHQ9Plwic3RhZ2U9XCIrdCkpLmpvaW4oXCImXCIpOlwiXCI7aWYociYmIWkpcmV0dXJuIHQocik7dGhpcy5faW5zdGFuY2UubWUoe3VybDp0aGlzLl9pbnN0YW5jZS5yZXF1ZXN0Um91dGVyLmVuZHBvaW50Rm9yKFwiYXBpXCIsXCIvYXBpL2Vhcmx5X2FjY2Vzc19mZWF0dXJlcy8/dG9rZW49XCIrdGhpcy5faW5zdGFuY2UuY29uZmlnLnRva2VuK3MpLG1ldGhvZDpcIkdFVFwiLGNhbGxiYWNrOmk9Pnt2YXIgZTtpZihpLmpzb24pe3ZhciByPWkuanNvbi5lYXJseUFjY2Vzc0ZlYXR1cmVzO3JldHVybiBudWxsPT0oZT10aGlzLl9pbnN0YW5jZS5wZXJzaXN0ZW5jZSl8fGUucmVnaXN0ZXIoe1tQdF06cn0pLHQocil9fX0pfXllKCl7dmFyIHQ9dGhpcy5nZXRGbGFncygpLGk9dGhpcy5nZXRGbGFnVmFyaWFudHMoKTtyZXR1cm57ZmxhZ3M6dC5maWx0ZXIoKHQ9PmlbdF0pKSxmbGFnVmFyaWFudHM6T2JqZWN0LmtleXMoaSkuZmlsdGVyKCh0PT5pW3RdKSkucmVkdWNlKCgodCxlKT0+KHRbZV09aVtlXSx0KSkse30pfX13ZSh0KXt2YXJ7ZmxhZ3M6aSxmbGFnVmFyaWFudHM6ZX09dGhpcy55ZSgpO3RoaXMuZmVhdHVyZUZsYWdFdmVudEhhbmRsZXJzLmZvckVhY2goKHI9PnIoaSxlLHtlcnJvcnNMb2FkaW5nOnR9KSkpfXNldFBlcnNvblByb3BlcnRpZXNGb3JGbGFncyh0LGkpe3ZvaWQgMD09PWkmJihpPSEwKTt2YXIgZT10aGlzLl9pbnN0YW5jZS5nZXRfcHJvcGVydHkoVHQpfHx7fTt0aGlzLl9pbnN0YW5jZS5yZWdpc3Rlcih7W1R0XTpCKHt9LGUsdCl9KSxpJiZ0aGlzLl9pbnN0YW5jZS5yZWxvYWRGZWF0dXJlRmxhZ3MoKX1yZXNldFBlcnNvblByb3BlcnRpZXNGb3JGbGFncygpe3RoaXMuX2luc3RhbmNlLnVucmVnaXN0ZXIoVHQpfXNldEdyb3VwUHJvcGVydGllc0ZvckZsYWdzKHQsaSl7dm9pZCAwPT09aSYmKGk9ITApO3ZhciBlPXRoaXMuX2luc3RhbmNlLmdldF9wcm9wZXJ0eShNdCl8fHt9OzAhPT1PYmplY3Qua2V5cyhlKS5sZW5ndGgmJk9iamVjdC5rZXlzKGUpLmZvckVhY2goKGk9PntlW2ldPUIoe30sZVtpXSx0W2ldKSxkZWxldGUgdFtpXX0pKSx0aGlzLl9pbnN0YW5jZS5yZWdpc3Rlcih7W010XTpCKHt9LGUsdCl9KSxpJiZ0aGlzLl9pbnN0YW5jZS5yZWxvYWRGZWF0dXJlRmxhZ3MoKX1yZXNldEdyb3VwUHJvcGVydGllc0ZvckZsYWdzKHQpe2lmKHQpe3ZhciBpPXRoaXMuX2luc3RhbmNlLmdldF9wcm9wZXJ0eShNdCl8fHt9O3RoaXMuX2luc3RhbmNlLnJlZ2lzdGVyKHtbTXRdOkIoe30saSx7W3RdOnt9fSl9KX1lbHNlIHRoaXMuX2luc3RhbmNlLnVucmVnaXN0ZXIoTXQpfX12YXIgdm89W1wiY29va2llXCIsXCJsb2NhbHN0b3JhZ2VcIixcImxvY2Fsc3RvcmFnZStjb29raWVcIixcInNlc3Npb25zdG9yYWdlXCIsXCJtZW1vcnlcIl07Y2xhc3MgY297Y29uc3RydWN0b3IodCl7dGhpcy5TPXQsdGhpcy5wcm9wcz17fSx0aGlzLlNlPSExLHRoaXMuJGU9KHQ9Pnt2YXIgaT1cIlwiO3JldHVybiB0LnRva2VuJiYoaT10LnRva2VuLnJlcGxhY2UoL1xcKy9nLFwiUExcIikucmVwbGFjZSgvXFwvL2csXCJTTFwiKS5yZXBsYWNlKC89L2csXCJFUVwiKSksdC5wZXJzaXN0ZW5jZV9uYW1lP1wicGhfXCIrdC5wZXJzaXN0ZW5jZV9uYW1lOlwicGhfXCIraStcIl9wb3N0aG9nXCJ9KSh0KSx0aGlzLnE9dGhpcy5rZSh0KSx0aGlzLmxvYWQoKSx0LmRlYnVnJiZqLmluZm8oXCJQZXJzaXN0ZW5jZSBsb2FkZWRcIix0LnBlcnNpc3RlbmNlLEIoe30sdGhpcy5wcm9wcykpLHRoaXMudXBkYXRlX2NvbmZpZyh0LHQpLHRoaXMuc2F2ZSgpfWtlKHQpey0xPT09dm8uaW5kZXhPZih0LnBlcnNpc3RlbmNlLnRvTG93ZXJDYXNlKCkpJiYoai5jcml0aWNhbChcIlVua25vd24gcGVyc2lzdGVuY2UgdHlwZSBcIit0LnBlcnNpc3RlbmNlK1wiOyBmYWxsaW5nIGJhY2sgdG8gbG9jYWxTdG9yYWdlK2Nvb2tpZVwiKSx0LnBlcnNpc3RlbmNlPVwibG9jYWxTdG9yYWdlK2Nvb2tpZVwiKTt2YXIgaT10LnBlcnNpc3RlbmNlLnRvTG93ZXJDYXNlKCk7cmV0dXJuXCJsb2NhbHN0b3JhZ2VcIj09PWkmJldpLk8oKT9XaTpcImxvY2Fsc3RvcmFnZStjb29raWVcIj09PWkmJkppLk8oKT9KaTpcInNlc3Npb25zdG9yYWdlXCI9PT1pJiZYaS5PKCk/WGk6XCJtZW1vcnlcIj09PWk/S2k6XCJjb29raWVcIj09PWk/Qmk6SmkuTygpP0ppOkJpfXByb3BlcnRpZXMoKXt2YXIgdD17fTtyZXR1cm4gSih0aGlzLnByb3BzLChmdW5jdGlvbihpLGUpe2lmKGU9PT1JdCYmSShpKSlmb3IodmFyIHI9T2JqZWN0LmtleXMoaSksbj0wO248ci5sZW5ndGg7bisrKXRbXCIkZmVhdHVyZS9cIityW25dXT1pW3Jbbl1dO2Vsc2UgYT1lLGw9ITEsKEMobz1IdCk/bDpzJiZvLmluZGV4T2Y9PT1zPy0xIT1vLmluZGV4T2YoYSk6KEoobywoZnVuY3Rpb24odCl7aWYobHx8KGw9dD09PWEpKXJldHVybiBXfSkpLGwpKXx8KHRbZV09aSk7dmFyIG8sYSxsfSkpLHR9bG9hZCgpe2lmKCF0aGlzLnhlKXt2YXIgdD10aGlzLnEuRCh0aGlzLiRlKTt0JiYodGhpcy5wcm9wcz1WKHt9LHQpKX19c2F2ZSgpe3RoaXMueGV8fHRoaXMucS5MKHRoaXMuJGUsdGhpcy5wcm9wcyx0aGlzLkVlLHRoaXMuSWUsdGhpcy5QZSx0aGlzLlMuZGVidWcpfXJlbW92ZSgpe3RoaXMucS5OKHRoaXMuJGUsITEpLHRoaXMucS5OKHRoaXMuJGUsITApfWNsZWFyKCl7dGhpcy5yZW1vdmUoKSx0aGlzLnByb3BzPXt9fXJlZ2lzdGVyX29uY2UodCxpLGUpe2lmKEkodCkpe1IoaSkmJihpPVwiTm9uZVwiKSx0aGlzLkVlPVIoZSk/dGhpcy5SZTplO3ZhciByPSExO2lmKEoodCwoKHQsZSk9Pnt0aGlzLnByb3BzLmhhc093blByb3BlcnR5KGUpJiZ0aGlzLnByb3BzW2VdIT09aXx8KHRoaXMucHJvcHNbZV09dCxyPSEwKX0pKSxyKXJldHVybiB0aGlzLnNhdmUoKSwhMH1yZXR1cm4hMX1yZWdpc3Rlcih0LGkpe2lmKEkodCkpe3RoaXMuRWU9UihpKT90aGlzLlJlOmk7dmFyIGU9ITE7aWYoSih0LCgoaSxyKT0+e3QuaGFzT3duUHJvcGVydHkocikmJnRoaXMucHJvcHNbcl0hPT1pJiYodGhpcy5wcm9wc1tyXT1pLGU9ITApfSkpLGUpcmV0dXJuIHRoaXMuc2F2ZSgpLCEwfXJldHVybiExfXVucmVnaXN0ZXIodCl7dCBpbiB0aGlzLnByb3BzJiYoZGVsZXRlIHRoaXMucHJvcHNbdF0sdGhpcy5zYXZlKCkpfXVwZGF0ZV9jYW1wYWlnbl9wYXJhbXMoKXtpZighdGhpcy5TZSl7dmFyIHQ9V24odGhpcy5TLmN1c3RvbV9jYW1wYWlnbl9wYXJhbXMsdGhpcy5TLm1hc2tfcGVyc29uYWxfZGF0YV9wcm9wZXJ0aWVzLHRoaXMuUy5jdXN0b21fcGVyc29uYWxfZGF0YV9wcm9wZXJ0aWVzKTtQKFoodCkpfHx0aGlzLnJlZ2lzdGVyKHQpLHRoaXMuU2U9ITB9fXVwZGF0ZV9zZWFyY2hfa2V5d29yZCgpe3ZhciB0O3RoaXMucmVnaXN0ZXIoKHQ9bnVsbD09bz92b2lkIDA6by5yZWZlcnJlcik/Sm4odCk6e30pfXVwZGF0ZV9yZWZlcnJlcl9pbmZvKCl7dmFyIHQ7dGhpcy5yZWdpc3Rlcl9vbmNlKHskcmVmZXJyZXI6S24oKSwkcmVmZXJyaW5nX2RvbWFpbjpudWxsIT1vJiZvLnJlZmVycmVyJiYobnVsbD09KHQ9eWkoby5yZWZlcnJlcikpP3ZvaWQgMDp0Lmhvc3QpfHxcIiRkaXJlY3RcIn0sdm9pZCAwKX1zZXRfaW5pdGlhbF9wZXJzb25faW5mbygpe3RoaXMucHJvcHNbTnRdfHx0aGlzLnByb3BzW2p0XXx8dGhpcy5yZWdpc3Rlcl9vbmNlKHtbenRdOlluKHRoaXMuUy5tYXNrX3BlcnNvbmFsX2RhdGFfcHJvcGVydGllcyx0aGlzLlMuY3VzdG9tX3BlcnNvbmFsX2RhdGFfcHJvcGVydGllcyl9LHZvaWQgMCl9Z2V0X2luaXRpYWxfcHJvcHMoKXt2YXIgdD17fTtKKFtqdCxOdF0sKGk9Pnt2YXIgZT10aGlzLnByb3BzW2ldO2UmJkooZSwoZnVuY3Rpb24oaSxlKXt0W1wiJGluaXRpYWxfXCIrdyhlKV09aX0pKX0pKTt2YXIgaSxlLHI9dGhpcy5wcm9wc1t6dF07aWYocil7dmFyIHM9KGk9WG4ociksZT17fSxKKGksKGZ1bmN0aW9uKHQsaSl7ZVtcIiRpbml0aWFsX1wiK3coaSldPXR9KSksZSk7Vih0LHMpfXJldHVybiB0fXNhZmVfbWVyZ2UodCl7cmV0dXJuIEoodGhpcy5wcm9wcywoZnVuY3Rpb24oaSxlKXtlIGluIHR8fCh0W2VdPWkpfSkpLHR9dXBkYXRlX2NvbmZpZyh0LGkpe2lmKHRoaXMuUmU9dGhpcy5FZT10LmNvb2tpZV9leHBpcmF0aW9uLHRoaXMuc2V0X2Rpc2FibGVkKHQuZGlzYWJsZV9wZXJzaXN0ZW5jZSksdGhpcy5zZXRfY3Jvc3Nfc3ViZG9tYWluKHQuY3Jvc3Nfc3ViZG9tYWluX2Nvb2tpZSksdGhpcy5zZXRfc2VjdXJlKHQuc2VjdXJlX2Nvb2tpZSksdC5wZXJzaXN0ZW5jZSE9PWkucGVyc2lzdGVuY2Upe3ZhciBlPXRoaXMua2UodCkscj10aGlzLnByb3BzO3RoaXMuY2xlYXIoKSx0aGlzLnE9ZSx0aGlzLnByb3BzPXIsdGhpcy5zYXZlKCl9fXNldF9kaXNhYmxlZCh0KXt0aGlzLnhlPXQsdGhpcy54ZT90aGlzLnJlbW92ZSgpOnRoaXMuc2F2ZSgpfXNldF9jcm9zc19zdWJkb21haW4odCl7dCE9PXRoaXMuSWUmJih0aGlzLkllPXQsdGhpcy5yZW1vdmUoKSx0aGlzLnNhdmUoKSl9c2V0X3NlY3VyZSh0KXt0IT09dGhpcy5QZSYmKHRoaXMuUGU9dCx0aGlzLnJlbW92ZSgpLHRoaXMuc2F2ZSgpKX1zZXRfZXZlbnRfdGltZXIodCxpKXt2YXIgZT10aGlzLnByb3BzW2F0XXx8e307ZVt0XT1pLHRoaXMucHJvcHNbYXRdPWUsdGhpcy5zYXZlKCl9cmVtb3ZlX2V2ZW50X3RpbWVyKHQpe3ZhciBpPSh0aGlzLnByb3BzW2F0XXx8e30pW3RdO3JldHVybiBSKGkpfHwoZGVsZXRlIHRoaXMucHJvcHNbYXRdW3RdLHRoaXMuc2F2ZSgpKSxpfWdldF9wcm9wZXJ0eSh0KXtyZXR1cm4gdGhpcy5wcm9wc1t0XX1zZXRfcHJvcGVydHkodCxpKXt0aGlzLnByb3BzW3RdPWksdGhpcy5zYXZlKCl9fWNsYXNzIGZve2NvbnN0cnVjdG9yKCl7dGhpcy5UZT17fSx0aGlzLlRlPXt9fW9uKHQsaSl7cmV0dXJuIHRoaXMuVGVbdF18fCh0aGlzLlRlW3RdPVtdKSx0aGlzLlRlW3RdLnB1c2goaSksKCk9Pnt0aGlzLlRlW3RdPXRoaXMuVGVbdF0uZmlsdGVyKCh0PT50IT09aSkpfX1lbWl0KHQsaSl7Zm9yKHZhciBlIG9mIHRoaXMuVGVbdF18fFtdKWUoaSk7Zm9yKHZhciByIG9mIHRoaXMuVGVbXCIqXCJdfHxbXSlyKHQsaSl9fWNsYXNzIHBve2NvbnN0cnVjdG9yKHQpe3RoaXMuTWU9bmV3IGZvLHRoaXMuQ2U9KHQsaSk9PnRoaXMuT2UodCxpKSYmdGhpcy5GZSh0LGkpJiZ0aGlzLkFlKHQsaSksdGhpcy5PZT0odCxpKT0+bnVsbD09aXx8IWkuZXZlbnR8fChudWxsPT10P3ZvaWQgMDp0LmV2ZW50KT09PShudWxsPT1pP3ZvaWQgMDppLmV2ZW50KSx0aGlzLl9pbnN0YW5jZT10LHRoaXMuRGU9bmV3IFNldCx0aGlzLkxlPW5ldyBTZXR9aW5pdCgpe3ZhciB0O2lmKCFSKG51bGw9PSh0PXRoaXMuX2luc3RhbmNlKT92b2lkIDA6dC5OZSkpe3ZhciBpO251bGw9PShpPXRoaXMuX2luc3RhbmNlKXx8aS5OZSgoKHQsaSk9Pnt0aGlzLm9uKHQsaSl9KSl9fXJlZ2lzdGVyKHQpe3ZhciBpLGU7aWYoIVIobnVsbD09KGk9dGhpcy5faW5zdGFuY2UpP3ZvaWQgMDppLk5lKSYmKHQuZm9yRWFjaCgodD0+e3ZhciBpLGU7bnVsbD09KGk9dGhpcy5MZSl8fGkuYWRkKHQpLG51bGw9PShlPXQuc3RlcHMpfHxlLmZvckVhY2goKHQ9Pnt2YXIgaTtudWxsPT0oaT10aGlzLkRlKXx8aS5hZGQoKG51bGw9PXQ/dm9pZCAwOnQuZXZlbnQpfHxcIlwiKX0pKX0pKSxudWxsIT0oZT10aGlzLl9pbnN0YW5jZSkmJmUuYXV0b2NhcHR1cmUpKXt2YXIgcixzPW5ldyBTZXQ7dC5mb3JFYWNoKCh0PT57dmFyIGk7bnVsbD09KGk9dC5zdGVwcyl8fGkuZm9yRWFjaCgodD0+e251bGwhPXQmJnQuc2VsZWN0b3ImJnMuYWRkKG51bGw9PXQ/dm9pZCAwOnQuc2VsZWN0b3IpfSkpfSkpLG51bGw9PShyPXRoaXMuX2luc3RhbmNlKXx8ci5hdXRvY2FwdHVyZS5zZXRFbGVtZW50U2VsZWN0b3JzKHMpfX1vbih0LGkpe3ZhciBlO251bGwhPWkmJjAhPXQubGVuZ3RoJiYodGhpcy5EZS5oYXModCl8fHRoaXMuRGUuaGFzKG51bGw9PWk/dm9pZCAwOmkuZXZlbnQpKSYmdGhpcy5MZSYmKG51bGw9PShlPXRoaXMuTGUpP3ZvaWQgMDplLnNpemUpPjAmJnRoaXMuTGUuZm9yRWFjaCgodD0+e3RoaXMuamUoaSx0KSYmdGhpcy5NZS5lbWl0KFwiYWN0aW9uQ2FwdHVyZWRcIix0Lm5hbWUpfSkpfXplKHQpe3RoaXMub25BY3Rpb24oXCJhY3Rpb25DYXB0dXJlZFwiLChpPT50KGkpKSl9amUodCxpKXtpZihudWxsPT0obnVsbD09aT92b2lkIDA6aS5zdGVwcykpcmV0dXJuITE7Zm9yKHZhciBlIG9mIGkuc3RlcHMpaWYodGhpcy5DZSh0LGUpKXJldHVybiEwO3JldHVybiExfW9uQWN0aW9uKHQsaSl7cmV0dXJuIHRoaXMuTWUub24odCxpKX1GZSh0LGkpe2lmKG51bGwhPWkmJmkudXJsKXt2YXIgZSxyPW51bGw9PXR8fG51bGw9PShlPXQucHJvcGVydGllcyk/dm9pZCAwOmUuJGN1cnJlbnRfdXJsO2lmKCFyfHxcInN0cmluZ1wiIT10eXBlb2YgcilyZXR1cm4hMTtpZighcG8uVWUocixudWxsPT1pP3ZvaWQgMDppLnVybCwobnVsbD09aT92b2lkIDA6aS51cmxfbWF0Y2hpbmcpfHxcImNvbnRhaW5zXCIpKXJldHVybiExfXJldHVybiEwfXN0YXRpYyBVZShpLGUscil7c3dpdGNoKHIpe2Nhc2VcInJlZ2V4XCI6cmV0dXJuISF0JiZOcyhpLGUpO2Nhc2VcImV4YWN0XCI6cmV0dXJuIGU9PT1pO2Nhc2VcImNvbnRhaW5zXCI6dmFyIHM9cG8ucWUoZSkucmVwbGFjZSgvXy9nLFwiLlwiKS5yZXBsYWNlKC8lL2csXCIuKlwiKTtyZXR1cm4gTnMoaSxzKTtkZWZhdWx0OnJldHVybiExfX1zdGF0aWMgcWUodCl7cmV0dXJuIHQucmVwbGFjZSgvW3xcXFxce30oKVtcXF1eJCsqPy5dL2csXCJcXFxcJCZcIikucmVwbGFjZSgvLS9nLFwiXFxcXHgyZFwiKX1BZSh0LGkpe2lmKChudWxsIT1pJiZpLmhyZWZ8fG51bGwhPWkmJmkudGFnX25hbWV8fG51bGwhPWkmJmkudGV4dCkmJiF0aGlzLkJlKHQpLnNvbWUoKHQ9PiEobnVsbCE9aSYmaS5ocmVmJiYhcG8uVWUodC5ocmVmfHxcIlwiLG51bGw9PWk/dm9pZCAwOmkuaHJlZiwobnVsbD09aT92b2lkIDA6aS5ocmVmX21hdGNoaW5nKXx8XCJleGFjdFwiKSkmJigobnVsbD09aXx8IWkudGFnX25hbWV8fHQudGFnX25hbWU9PT0obnVsbD09aT92b2lkIDA6aS50YWdfbmFtZSkpJiYhKG51bGwhPWkmJmkudGV4dCYmIXBvLlVlKHQudGV4dHx8XCJcIixudWxsPT1pP3ZvaWQgMDppLnRleHQsKG51bGw9PWk/dm9pZCAwOmkudGV4dF9tYXRjaGluZyl8fFwiZXhhY3RcIikmJiFwby5VZSh0LiRlbF90ZXh0fHxcIlwiLG51bGw9PWk/dm9pZCAwOmkudGV4dCwobnVsbD09aT92b2lkIDA6aS50ZXh0X21hdGNoaW5nKXx8XCJleGFjdFwiKSkpKSkpcmV0dXJuITE7aWYobnVsbCE9aSYmaS5zZWxlY3Rvcil7dmFyIGUscj1udWxsPT10fHxudWxsPT0oZT10LnByb3BlcnRpZXMpP3ZvaWQgMDplLiRlbGVtZW50X3NlbGVjdG9ycztpZighcilyZXR1cm4hMTtpZighci5pbmNsdWRlcyhudWxsPT1pP3ZvaWQgMDppLnNlbGVjdG9yKSlyZXR1cm4hMX1yZXR1cm4hMH1CZSh0KXtyZXR1cm4gbnVsbD09KG51bGw9PXQ/dm9pZCAwOnQucHJvcGVydGllcy4kZWxlbWVudHMpP1tdOm51bGw9PXQ/dm9pZCAwOnQucHJvcGVydGllcy4kZWxlbWVudHN9fXZhciBnbz16KFwiW1N1cnZleXNdXCIpO2NsYXNzIF9ve2NvbnN0cnVjdG9yKHQpe3RoaXMuX2luc3RhbmNlPXQsdGhpcy5IZT1uZXcgTWFwLHRoaXMuV2U9bmV3IE1hcH1yZWdpc3Rlcih0KXt2YXIgaTtSKG51bGw9PShpPXRoaXMuX2luc3RhbmNlKT92b2lkIDA6aS5OZSl8fCh0aGlzLkdlKHQpLHRoaXMuSmUodCkpfUplKHQpe3ZhciBpPXQuZmlsdGVyKCh0PT57dmFyIGksZTtyZXR1cm4obnVsbD09KGk9dC5jb25kaXRpb25zKT92b2lkIDA6aS5hY3Rpb25zKSYmKG51bGw9PShlPXQuY29uZGl0aW9ucyl8fG51bGw9PShlPWUuYWN0aW9ucyl8fG51bGw9PShlPWUudmFsdWVzKT92b2lkIDA6ZS5sZW5ndGgpPjB9KSk7aWYoMCE9PWkubGVuZ3RoKXtpZihudWxsPT10aGlzLlZlKXt0aGlzLlZlPW5ldyBwbyh0aGlzLl9pbnN0YW5jZSksdGhpcy5WZS5pbml0KCk7dGhpcy5WZS56ZSgodD0+e3RoaXMub25BY3Rpb24odCl9KSl9aS5mb3JFYWNoKCh0PT57dmFyIGksZSxyLHMsbjt0LmNvbmRpdGlvbnMmJm51bGwhPShpPXQuY29uZGl0aW9ucykmJmkuYWN0aW9ucyYmbnVsbCE9KGU9dC5jb25kaXRpb25zKSYmbnVsbCE9KGU9ZS5hY3Rpb25zKSYmZS52YWx1ZXMmJihudWxsPT0ocj10LmNvbmRpdGlvbnMpfHxudWxsPT0ocj1yLmFjdGlvbnMpfHxudWxsPT0ocj1yLnZhbHVlcyk/dm9pZCAwOnIubGVuZ3RoKT4wJiYobnVsbD09KHM9dGhpcy5WZSl8fHMucmVnaXN0ZXIodC5jb25kaXRpb25zLmFjdGlvbnMudmFsdWVzKSxudWxsPT0obj10LmNvbmRpdGlvbnMpfHxudWxsPT0obj1uLmFjdGlvbnMpfHxudWxsPT0obj1uLnZhbHVlcyl8fG4uZm9yRWFjaCgoaT0+e2lmKGkmJmkubmFtZSl7dmFyIGU9dGhpcy5XZS5nZXQoaS5uYW1lKTtlJiZlLnB1c2godC5pZCksdGhpcy5XZS5zZXQoaS5uYW1lLGV8fFt0LmlkXSl9fSkpKX0pKX19R2UodCl7dmFyIGk7aWYoMCE9PXQuZmlsdGVyKCh0PT57dmFyIGksZTtyZXR1cm4obnVsbD09KGk9dC5jb25kaXRpb25zKT92b2lkIDA6aS5ldmVudHMpJiYobnVsbD09KGU9dC5jb25kaXRpb25zKXx8bnVsbD09KGU9ZS5ldmVudHMpfHxudWxsPT0oZT1lLnZhbHVlcyk/dm9pZCAwOmUubGVuZ3RoKT4wfSkpLmxlbmd0aCl7bnVsbD09KGk9dGhpcy5faW5zdGFuY2UpfHxpLk5lKCgodCxpKT0+e3RoaXMub25FdmVudCh0LGkpfSkpLHQuZm9yRWFjaCgodD0+e3ZhciBpO251bGw9PShpPXQuY29uZGl0aW9ucyl8fG51bGw9PShpPWkuZXZlbnRzKXx8bnVsbD09KGk9aS52YWx1ZXMpfHxpLmZvckVhY2goKGk9PntpZihpJiZpLm5hbWUpe3ZhciBlPXRoaXMuSGUuZ2V0KGkubmFtZSk7ZSYmZS5wdXNoKHQuaWQpLHRoaXMuSGUuc2V0KGkubmFtZSxlfHxbdC5pZF0pfX0pKX0pKX19b25FdmVudCh0LGkpe3ZhciBlLHI9KG51bGw9PShlPXRoaXMuX2luc3RhbmNlKXx8bnVsbD09KGU9ZS5wZXJzaXN0ZW5jZSk/dm9pZCAwOmUucHJvcHNbT3RdKXx8W107aWYoXCJzdXJ2ZXkgc2hvd25cIj09PXQmJmkmJnIubGVuZ3RoPjApe3ZhciBzO2dvLmluZm8oXCJzdXJ2ZXkgZXZlbnQgbWF0Y2hlZCwgcmVtb3Zpbmcgc3VydmV5IGZyb20gYWN0aXZhdGVkIHN1cnZleXNcIix7ZXZlbnQ6dCxldmVudFBheWxvYWQ6aSxleGlzdGluZ0FjdGl2YXRlZFN1cnZleXM6cn0pO3ZhciBuPW51bGw9PWl8fG51bGw9PShzPWkucHJvcGVydGllcyk/dm9pZCAwOnMuJHN1cnZleV9pZDtpZihuKXt2YXIgbz1yLmluZGV4T2Yobik7bz49MCYmKHIuc3BsaWNlKG8sMSksdGhpcy5LZShyKSl9fWVsc2UgdGhpcy5IZS5oYXModCkmJihnby5pbmZvKFwic3VydmV5IGV2ZW50IG1hdGNoZWQsIHVwZGF0aW5nIGFjdGl2YXRlZCBzdXJ2ZXlzXCIse2V2ZW50OnQsc3VydmV5czp0aGlzLkhlLmdldCh0KX0pLHRoaXMuS2Uoci5jb25jYXQodGhpcy5IZS5nZXQodCl8fFtdKSkpfW9uQWN0aW9uKHQpe3ZhciBpLGU9KG51bGw9PShpPXRoaXMuX2luc3RhbmNlKXx8bnVsbD09KGk9aS5wZXJzaXN0ZW5jZSk/dm9pZCAwOmkucHJvcHNbT3RdKXx8W107dGhpcy5XZS5oYXModCkmJnRoaXMuS2UoZS5jb25jYXQodGhpcy5XZS5nZXQodCl8fFtdKSl9S2UodCl7dmFyIGk7bnVsbD09KGk9dGhpcy5faW5zdGFuY2UpfHxudWxsPT0oaT1pLnBlcnNpc3RlbmNlKXx8aS5yZWdpc3Rlcih7W090XTpbLi4ubmV3IFNldCh0KV19KX1nZXRTdXJ2ZXlzKCl7dmFyIHQsaT1udWxsPT0odD10aGlzLl9pbnN0YW5jZSl8fG51bGw9PSh0PXQucGVyc2lzdGVuY2UpP3ZvaWQgMDp0LnByb3BzW090XTtyZXR1cm4gaXx8W119Z2V0RXZlbnRUb1N1cnZleXMoKXtyZXR1cm4gdGhpcy5IZX1ZZSgpe3JldHVybiB0aGlzLlZlfX1jbGFzcyBtb3tjb25zdHJ1Y3Rvcih0KXt0aGlzLlhlPW51bGwsdGhpcy5RZT0hMSx0aGlzLlplPSExLHRoaXMudHI9W10sdGhpcy5faW5zdGFuY2U9dCx0aGlzLl9zdXJ2ZXlFdmVudFJlY2VpdmVyPW51bGx9b25SZW1vdGVDb25maWcodCl7dmFyIGk9dC5zdXJ2ZXlzO2lmKE8oaSkpcmV0dXJuIGdvLndhcm4oXCJEZWNpZGUgbm90IGxvYWRlZCB5ZXQuIE5vdCBsb2FkaW5nIHN1cnZleXMuXCIpO3ZhciBlPXgoaSk7dGhpcy5pcj1lP2kubGVuZ3RoPjA6aSxnby5pbmZvKFwiZGVjaWRlIHJlc3BvbnNlIHJlY2VpdmVkLCBoYXNTdXJ2ZXlzOiBcIit0aGlzLmlyKSx0aGlzLmlyJiZ0aGlzLmxvYWRJZkVuYWJsZWQoKX1yZXNldCgpe2xvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFwibGFzdFNlZW5TdXJ2ZXlEYXRlXCIpO2Zvcih2YXIgdD1bXSxpPTA7aTxsb2NhbFN0b3JhZ2UubGVuZ3RoO2krKyl7dmFyIGU9bG9jYWxTdG9yYWdlLmtleShpKTsobnVsbCE9ZSYmZS5zdGFydHNXaXRoKFwic2VlblN1cnZleV9cIil8fG51bGwhPWUmJmUuc3RhcnRzV2l0aChcImluUHJvZ3Jlc3NTdXJ2ZXlfXCIpKSYmdC5wdXNoKGUpfXQuZm9yRWFjaCgodD0+bG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0odCkpKX1sb2FkSWZFbmFibGVkKCl7aWYoIXRoaXMuWGUpaWYodGhpcy5aZSlnby5pbmZvKFwiQWxyZWFkeSBpbml0aWFsaXppbmcgc3VydmV5cywgc2tpcHBpbmcuLi5cIik7ZWxzZSBpZih0aGlzLl9pbnN0YW5jZS5jb25maWcuZGlzYWJsZV9zdXJ2ZXlzKWdvLmluZm8oXCJEaXNhYmxlZC4gTm90IGxvYWRpbmcgc3VydmV5cy5cIik7ZWxzZSBpZih0aGlzLmlyKXt2YXIgdD1udWxsPT12P3ZvaWQgMDp2Ll9fUG9zdGhvZ0V4dGVuc2lvbnNfXztpZih0KXt0aGlzLlplPSEwO3RyeXt2YXIgaT10LmdlbmVyYXRlU3VydmV5cztpZihpKXJldHVybiB2b2lkIHRoaXMuZXIoaSk7dmFyIGU9dC5sb2FkRXh0ZXJuYWxEZXBlbmRlbmN5O2lmKCFlKXJldHVybiB2b2lkIHRoaXMucnIoXCJQb3N0SG9nIGxvYWRFeHRlcm5hbERlcGVuZGVuY3kgZXh0ZW5zaW9uIG5vdCBmb3VuZC5cIik7ZSh0aGlzLl9pbnN0YW5jZSxcInN1cnZleXNcIiwoaT0+e2l8fCF0LmdlbmVyYXRlU3VydmV5cz90aGlzLnJyKFwiQ291bGQgbm90IGxvYWQgc3VydmV5cyBzY3JpcHRcIixpKTp0aGlzLmVyKHQuZ2VuZXJhdGVTdXJ2ZXlzKX0pKX1jYXRjaCh0KXt0aHJvdyB0aGlzLnJyKFwiRXJyb3IgaW5pdGlhbGl6aW5nIHN1cnZleXNcIix0KSx0fWZpbmFsbHl7dGhpcy5aZT0hMX19ZWxzZSBnby5lcnJvcihcIlBvc3RIb2cgRXh0ZW5zaW9ucyBub3QgZm91bmQuXCIpfWVsc2UgZ28uaW5mbyhcIk5vIHN1cnZleXMgdG8gbG9hZC5cIil9ZXIodCl7dGhpcy5YZT10KHRoaXMuX2luc3RhbmNlKSx0aGlzLl9zdXJ2ZXlFdmVudFJlY2VpdmVyPW5ldyBfbyh0aGlzLl9pbnN0YW5jZSksZ28uaW5mbyhcIlN1cnZleXMgbG9hZGVkIHN1Y2Nlc3NmdWxseVwiKSx0aGlzLnNyKHtpc0xvYWRlZDohMH0pfXJyKHQsaSl7Z28uZXJyb3IodCxpKSx0aGlzLnNyKHtpc0xvYWRlZDohMSxlcnJvcjp0fSl9b25TdXJ2ZXlzTG9hZGVkKHQpe3JldHVybiB0aGlzLnRyLnB1c2godCksdGhpcy5YZSYmdGhpcy5zcih7aXNMb2FkZWQ6ITB9KSwoKT0+e3RoaXMudHI9dGhpcy50ci5maWx0ZXIoKGk9PmkhPT10KSl9fWdldFN1cnZleXModCxpKXtpZih2b2lkIDA9PT1pJiYoaT0hMSksdGhpcy5faW5zdGFuY2UuY29uZmlnLmRpc2FibGVfc3VydmV5cylyZXR1cm4gZ28uaW5mbyhcIkRpc2FibGVkLiBOb3QgbG9hZGluZyBzdXJ2ZXlzLlwiKSx0KFtdKTt2YXIgZT10aGlzLl9pbnN0YW5jZS5nZXRfcHJvcGVydHkoQ3QpO2lmKGUmJiFpKXJldHVybiB0KGUse2lzTG9hZGVkOiEwfSk7aWYodGhpcy5RZSlyZXR1cm4gdChbXSx7aXNMb2FkZWQ6ITEsZXJyb3I6XCJTdXJ2ZXlzIGFyZSBhbHJlYWR5IGJlaW5nIGxvYWRlZFwifSk7dHJ5e3RoaXMuUWU9ITAsdGhpcy5faW5zdGFuY2UubWUoe3VybDp0aGlzLl9pbnN0YW5jZS5yZXF1ZXN0Um91dGVyLmVuZHBvaW50Rm9yKFwiYXBpXCIsXCIvYXBpL3N1cnZleXMvP3Rva2VuPVwiK3RoaXMuX2luc3RhbmNlLmNvbmZpZy50b2tlbiksbWV0aG9kOlwiR0VUXCIsdGltZW91dDp0aGlzLl9pbnN0YW5jZS5jb25maWcuc3VydmV5c19yZXF1ZXN0X3RpbWVvdXRfbXMsY2FsbGJhY2s6aT0+e3ZhciBlO3RoaXMuUWU9ITE7dmFyIHI9aS5zdGF0dXNDb2RlO2lmKDIwMCE9PXJ8fCFpLmpzb24pe3ZhciBzPVwiU3VydmV5cyBBUEkgY291bGQgbm90IGJlIGxvYWRlZCwgc3RhdHVzOiBcIityO3JldHVybiBnby5lcnJvcihzKSx0KFtdLHtpc0xvYWRlZDohMSxlcnJvcjpzfSl9dmFyIG4sbz1pLmpzb24uc3VydmV5c3x8W10sYT1vLmZpbHRlcigodD0+ZnVuY3Rpb24odCl7cmV0dXJuISghdC5zdGFydF9kYXRlfHx0LmVuZF9kYXRlKX0odCkmJihmdW5jdGlvbih0KXt2YXIgaTtyZXR1cm4hKG51bGw9PShpPXQuY29uZGl0aW9ucyl8fG51bGw9PShpPWkuZXZlbnRzKXx8bnVsbD09KGk9aS52YWx1ZXMpfHwhaS5sZW5ndGgpfSh0KXx8ZnVuY3Rpb24odCl7dmFyIGk7cmV0dXJuIShudWxsPT0oaT10LmNvbmRpdGlvbnMpfHxudWxsPT0oaT1pLmFjdGlvbnMpfHxudWxsPT0oaT1pLnZhbHVlcyl8fCFpLmxlbmd0aCl9KHQpKSkpO2EubGVuZ3RoPjAmJihudWxsPT0obj10aGlzLl9zdXJ2ZXlFdmVudFJlY2VpdmVyKXx8bi5yZWdpc3RlcihhKSk7cmV0dXJuIG51bGw9PShlPXRoaXMuX2luc3RhbmNlLnBlcnNpc3RlbmNlKXx8ZS5yZWdpc3Rlcih7W0N0XTpvfSksdChvLHtpc0xvYWRlZDohMH0pfX0pfWNhdGNoKHQpe3Rocm93IHRoaXMuUWU9ITEsdH19c3IodCl7Zm9yKHZhciBpIG9mIHRoaXMudHIpdHJ5e3QuaXNMb2FkZWQ/dGhpcy5nZXRTdXJ2ZXlzKGkpOmkoW10sdCl9Y2F0Y2godCl7Z28uZXJyb3IoXCJFcnJvciBpbiBzdXJ2ZXkgY2FsbGJhY2tcIix0KX19Z2V0QWN0aXZlTWF0Y2hpbmdTdXJ2ZXlzKHQsaSl7aWYodm9pZCAwPT09aSYmKGk9ITEpLCFPKHRoaXMuWGUpKXJldHVybiB0aGlzLlhlLmdldEFjdGl2ZU1hdGNoaW5nU3VydmV5cyh0LGkpO2dvLndhcm4oXCJpbml0IHdhcyBub3QgY2FsbGVkXCIpfW5yKHQpe3ZhciBpPW51bGw7cmV0dXJuIHRoaXMuZ2V0U3VydmV5cygoZT0+e3ZhciByO2k9bnVsbCE9PShyPWUuZmluZCgoaT0+aS5pZD09PXQpKSkmJnZvaWQgMCE9PXI/cjpudWxsfSkpLGl9YXIodCl7aWYoTyh0aGlzLlhlKSlyZXR1cm57ZWxpZ2libGU6ITEscmVhc29uOlwiU0RLIGlzIG5vdCBlbmFibGVkIG9yIHN1cnZleSBmdW5jdGlvbmFsaXR5IGlzIG5vdCB5ZXQgbG9hZGVkXCJ9O3ZhciBpPVwic3RyaW5nXCI9PXR5cGVvZiB0P3RoaXMubnIodCk6dDtyZXR1cm4gaT90aGlzLlhlLmNoZWNrU3VydmV5RWxpZ2liaWxpdHkoaSk6e2VsaWdpYmxlOiExLHJlYXNvbjpcIlN1cnZleSBub3QgZm91bmRcIn19Y2FuUmVuZGVyU3VydmV5KHQpe2lmKE8odGhpcy5YZSkpcmV0dXJuIGdvLndhcm4oXCJpbml0IHdhcyBub3QgY2FsbGVkXCIpLHt2aXNpYmxlOiExLGRpc2FibGVkUmVhc29uOlwiU0RLIGlzIG5vdCBlbmFibGVkIG9yIHN1cnZleSBmdW5jdGlvbmFsaXR5IGlzIG5vdCB5ZXQgbG9hZGVkXCJ9O3ZhciBpPXRoaXMuYXIodCk7cmV0dXJue3Zpc2libGU6aS5lbGlnaWJsZSxkaXNhYmxlZFJlYXNvbjppLnJlYXNvbn19Y2FuUmVuZGVyU3VydmV5QXN5bmModCxpKXtyZXR1cm4gTyh0aGlzLlhlKT8oZ28ud2FybihcImluaXQgd2FzIG5vdCBjYWxsZWRcIiksUHJvbWlzZS5yZXNvbHZlKHt2aXNpYmxlOiExLGRpc2FibGVkUmVhc29uOlwiU0RLIGlzIG5vdCBlbmFibGVkIG9yIHN1cnZleSBmdW5jdGlvbmFsaXR5IGlzIG5vdCB5ZXQgbG9hZGVkXCJ9KSk6bmV3IFByb21pc2UoKGU9Pnt0aGlzLmdldFN1cnZleXMoKGk9Pnt2YXIgcixzPW51bGwhPT0ocj1pLmZpbmQoKGk9PmkuaWQ9PT10KSkpJiZ2b2lkIDAhPT1yP3I6bnVsbDtpZihzKXt2YXIgbj10aGlzLmFyKHMpO2Uoe3Zpc2libGU6bi5lbGlnaWJsZSxkaXNhYmxlZFJlYXNvbjpuLnJlYXNvbn0pfWVsc2UgZSh7dmlzaWJsZTohMSxkaXNhYmxlZFJlYXNvbjpcIlN1cnZleSBub3QgZm91bmRcIn0pfSksaSl9KSl9cmVuZGVyU3VydmV5KHQsaSl7aWYoTyh0aGlzLlhlKSlnby53YXJuKFwiaW5pdCB3YXMgbm90IGNhbGxlZFwiKTtlbHNle3ZhciBlPXRoaXMubnIodCkscj1udWxsPT1vP3ZvaWQgMDpvLnF1ZXJ5U2VsZWN0b3IoaSk7ZT9yP3RoaXMuWGUucmVuZGVyU3VydmV5KGUscik6Z28ud2FybihcIlN1cnZleSBlbGVtZW50IG5vdCBmb3VuZFwiKTpnby53YXJuKFwiU3VydmV5IG5vdCBmb3VuZFwiKX19fXZhciBibz16KFwiW1JhdGVMaW1pdGVyXVwiKTtjbGFzcyB3b3tjb25zdHJ1Y3Rvcih0KXt2YXIgaSxlO3RoaXMuc2VydmVyTGltaXRzPXt9LHRoaXMubGFzdEV2ZW50UmF0ZUxpbWl0ZWQ9ITEsdGhpcy5jaGVja0ZvckxpbWl0aW5nPXQ9Pnt2YXIgaT10LnRleHQ7aWYoaSYmaS5sZW5ndGgpdHJ5eyhKU09OLnBhcnNlKGkpLnF1b3RhX2xpbWl0ZWR8fFtdKS5mb3JFYWNoKCh0PT57Ym8uaW5mbygodHx8XCJldmVudHNcIikrXCIgaXMgcXVvdGEgbGltaXRlZC5cIiksdGhpcy5zZXJ2ZXJMaW1pdHNbdF09KG5ldyBEYXRlKS5nZXRUaW1lKCkrNmU0fSkpfWNhdGNoKHQpe3JldHVybiB2b2lkIGJvLndhcm4oJ2NvdWxkIG5vdCByYXRlIGxpbWl0IC0gY29udGludWluZy4gRXJyb3I6IFwiJysobnVsbD09dD92b2lkIDA6dC5tZXNzYWdlKSsnXCInLHt0ZXh0Oml9KX19LHRoaXMuaW5zdGFuY2U9dCx0aGlzLmNhcHR1cmVFdmVudHNQZXJTZWNvbmQ9KG51bGw9PShpPXQuY29uZmlnLnJhdGVfbGltaXRpbmcpP3ZvaWQgMDppLmV2ZW50c19wZXJfc2Vjb25kKXx8MTAsdGhpcy5jYXB0dXJlRXZlbnRzQnVyc3RMaW1pdD1NYXRoLm1heCgobnVsbD09KGU9dC5jb25maWcucmF0ZV9saW1pdGluZyk/dm9pZCAwOmUuZXZlbnRzX2J1cnN0X2xpbWl0KXx8MTAqdGhpcy5jYXB0dXJlRXZlbnRzUGVyU2Vjb25kLHRoaXMuY2FwdHVyZUV2ZW50c1BlclNlY29uZCksdGhpcy5sYXN0RXZlbnRSYXRlTGltaXRlZD10aGlzLmNsaWVudFJhdGVMaW1pdENvbnRleHQoITApLmlzUmF0ZUxpbWl0ZWR9Y2xpZW50UmF0ZUxpbWl0Q29udGV4dCh0KXt2YXIgaSxlLHI7dm9pZCAwPT09dCYmKHQ9ITEpO3ZhciBzPShuZXcgRGF0ZSkuZ2V0VGltZSgpLG49bnVsbCE9PShpPW51bGw9PShlPXRoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2UpP3ZvaWQgMDplLmdldF9wcm9wZXJ0eShMdCkpJiZ2b2lkIDAhPT1pP2k6e3Rva2Vuczp0aGlzLmNhcHR1cmVFdmVudHNCdXJzdExpbWl0LGxhc3Q6c307bi50b2tlbnMrPShzLW4ubGFzdCkvMWUzKnRoaXMuY2FwdHVyZUV2ZW50c1BlclNlY29uZCxuLmxhc3Q9cyxuLnRva2Vucz50aGlzLmNhcHR1cmVFdmVudHNCdXJzdExpbWl0JiYobi50b2tlbnM9dGhpcy5jYXB0dXJlRXZlbnRzQnVyc3RMaW1pdCk7dmFyIG89bi50b2tlbnM8MTtyZXR1cm4gb3x8dHx8KG4udG9rZW5zPU1hdGgubWF4KDAsbi50b2tlbnMtMSkpLCFvfHx0aGlzLmxhc3RFdmVudFJhdGVMaW1pdGVkfHx0fHx0aGlzLmluc3RhbmNlLmNhcHR1cmUoXCIkJGNsaWVudF9pbmdlc3Rpb25fd2FybmluZ1wiLHskJGNsaWVudF9pbmdlc3Rpb25fd2FybmluZ19tZXNzYWdlOlwicG9zdGhvZy1qcyBjbGllbnQgcmF0ZSBsaW1pdGVkLiBDb25maWcgaXMgc2V0IHRvIFwiK3RoaXMuY2FwdHVyZUV2ZW50c1BlclNlY29uZCtcIiBldmVudHMgcGVyIHNlY29uZCBhbmQgXCIrdGhpcy5jYXB0dXJlRXZlbnRzQnVyc3RMaW1pdCtcIiBldmVudHMgYnVyc3QgbGltaXQuXCJ9LHtza2lwX2NsaWVudF9yYXRlX2xpbWl0aW5nOiEwfSksdGhpcy5sYXN0RXZlbnRSYXRlTGltaXRlZD1vLG51bGw9PShyPXRoaXMuaW5zdGFuY2UucGVyc2lzdGVuY2UpfHxyLnNldF9wcm9wZXJ0eShMdCxuKSx7aXNSYXRlTGltaXRlZDpvLHJlbWFpbmluZ1Rva2VuczpuLnRva2Vuc319aXNTZXJ2ZXJSYXRlTGltaXRlZCh0KXt2YXIgaT10aGlzLnNlcnZlckxpbWl0c1t0fHxcImV2ZW50c1wiXXx8ITE7cmV0dXJuITEhPT1pJiYobmV3IERhdGUpLmdldFRpbWUoKTxpfX12YXIgeW89eihcIltSZW1vdGVDb25maWddXCIpO2NsYXNzIFNve2NvbnN0cnVjdG9yKHQpe3RoaXMuX2luc3RhbmNlPXR9Z2V0IHJlbW90ZUNvbmZpZygpe3ZhciB0O3JldHVybiBudWxsPT0odD12Ll9QT1NUSE9HX1JFTU9URV9DT05GSUcpfHxudWxsPT0odD10W3RoaXMuX2luc3RhbmNlLmNvbmZpZy50b2tlbl0pP3ZvaWQgMDp0LmNvbmZpZ31scih0KXt2YXIgaSxlO251bGwhPShpPXYuX19Qb3N0aG9nRXh0ZW5zaW9uc19fKSYmaS5sb2FkRXh0ZXJuYWxEZXBlbmRlbmN5P251bGw9PShlPXYuX19Qb3N0aG9nRXh0ZW5zaW9uc19fKXx8bnVsbD09ZS5sb2FkRXh0ZXJuYWxEZXBlbmRlbmN5fHxlLmxvYWRFeHRlcm5hbERlcGVuZGVuY3kodGhpcy5faW5zdGFuY2UsXCJyZW1vdGUtY29uZmlnXCIsKCgpPT50KHRoaXMucmVtb3RlQ29uZmlnKSkpOih5by5lcnJvcihcIlBvc3RIb2cgRXh0ZW5zaW9ucyBub3QgZm91bmQuIENhbm5vdCBsb2FkIHJlbW90ZSBjb25maWcuXCIpLHQoKSl9dXIodCl7dGhpcy5faW5zdGFuY2UubWUoe21ldGhvZDpcIkdFVFwiLHVybDp0aGlzLl9pbnN0YW5jZS5yZXF1ZXN0Um91dGVyLmVuZHBvaW50Rm9yKFwiYXNzZXRzXCIsXCIvYXJyYXkvXCIrdGhpcy5faW5zdGFuY2UuY29uZmlnLnRva2VuK1wiL2NvbmZpZ1wiKSxjYWxsYmFjazppPT57dChpLmpzb24pfX0pfWxvYWQoKXt0cnl7aWYodGhpcy5yZW1vdGVDb25maWcpcmV0dXJuIHlvLmluZm8oXCJVc2luZyBwcmVsb2FkZWQgcmVtb3RlIGNvbmZpZ1wiLHRoaXMucmVtb3RlQ29uZmlnKSx2b2lkIHRoaXMuYmUodGhpcy5yZW1vdGVDb25maWcpO2lmKHRoaXMuX2luc3RhbmNlLmNvbmZpZy5hZHZhbmNlZF9kaXNhYmxlX2RlY2lkZSlyZXR1cm4gdm9pZCB5by53YXJuKFwiUmVtb3RlIGNvbmZpZyBpcyBkaXNhYmxlZC4gRmFsbGluZyBiYWNrIHRvIGxvY2FsIGNvbmZpZy5cIik7dGhpcy5scigodD0+e2lmKCF0KXJldHVybiB5by5pbmZvKFwiTm8gY29uZmlnIGZvdW5kIGFmdGVyIGxvYWRpbmcgcmVtb3RlIEpTIGNvbmZpZy4gRmFsbGluZyBiYWNrIHRvIEpTT04uXCIpLHZvaWQgdGhpcy51cigodD0+e3RoaXMuYmUodCl9KSk7dGhpcy5iZSh0KX0pKX1jYXRjaCh0KXt5by5lcnJvcihcIkVycm9yIGxvYWRpbmcgcmVtb3RlIGNvbmZpZ1wiLHQpfX1iZSh0KXt0P3RoaXMuX2luc3RhbmNlLmNvbmZpZy5fX3ByZXZpZXdfcmVtb3RlX2NvbmZpZz8odGhpcy5faW5zdGFuY2UuYmUodCksITEhPT10Lmhhc0ZlYXR1cmVGbGFncyYmdGhpcy5faW5zdGFuY2UuZmVhdHVyZUZsYWdzLmVuc3VyZUZsYWdzTG9hZGVkKCkpOnlvLmluZm8oXCJfX3ByZXZpZXdfcmVtb3RlX2NvbmZpZyBpcyBkaXNhYmxlZC4gTG9nZ2luZyBjb25maWcgaW5zdGVhZFwiLHQpOnlvLmVycm9yKFwiRmFpbGVkIHRvIGZldGNoIHJlbW90ZSBjb25maWcgZnJvbSBQb3N0SG9nLlwiKX19dmFyICRvPTNlMztjbGFzcyBrb3tjb25zdHJ1Y3Rvcih0LGkpe3RoaXMuaHI9ITAsdGhpcy5kcj1bXSx0aGlzLnZyPVhlKChudWxsPT1pP3ZvaWQgMDppLmZsdXNoX2ludGVydmFsX21zKXx8JG8sMjUwLDVlMyxcImZsdXNoIGludGVydmFsXCIsJG8pLHRoaXMuY3I9dH1lbnF1ZXVlKHQpe3RoaXMuZHIucHVzaCh0KSx0aGlzLnByfHx0aGlzLmdyKCl9dW5sb2FkKCl7dGhpcy5fcigpO3ZhciB0PXRoaXMuZHIubGVuZ3RoPjA/dGhpcy5tcigpOnt9LGk9T2JqZWN0LnZhbHVlcyh0KTtbLi4uaS5maWx0ZXIoKHQ9PjA9PT10LnVybC5pbmRleE9mKFwiL2VcIikpKSwuLi5pLmZpbHRlcigodD0+MCE9PXQudXJsLmluZGV4T2YoXCIvZVwiKSkpXS5tYXAoKHQ9Pnt0aGlzLmNyKEIoe30sdCx7dHJhbnNwb3J0Olwic2VuZEJlYWNvblwifSkpfSkpfWVuYWJsZSgpe3RoaXMuaHI9ITEsdGhpcy5ncigpfWdyKCl7dmFyIHQ9dGhpczt0aGlzLmhyfHwodGhpcy5wcj1zZXRUaW1lb3V0KCgoKT0+e2lmKHRoaXMuX3IoKSx0aGlzLmRyLmxlbmd0aD4wKXt2YXIgaT10aGlzLm1yKCksZT1mdW5jdGlvbigpe3ZhciBlPWlbcl0scz0obmV3IERhdGUpLmdldFRpbWUoKTtlLmRhdGEmJngoZS5kYXRhKSYmSihlLmRhdGEsKHQ9Pnt0Lm9mZnNldD1NYXRoLmFicyh0LnRpbWVzdGFtcC1zKSxkZWxldGUgdC50aW1lc3RhbXB9KSksdC5jcihlKX07Zm9yKHZhciByIGluIGkpZSgpfX0pLHRoaXMudnIpKX1fcigpe2NsZWFyVGltZW91dCh0aGlzLnByKSx0aGlzLnByPXZvaWQgMH1tcigpe3ZhciB0PXt9O3JldHVybiBKKHRoaXMuZHIsKGk9Pnt2YXIgZSxyPWkscz0ocj9yLmJhdGNoS2V5Om51bGwpfHxyLnVybDtSKHRbc10pJiYodFtzXT1CKHt9LHIse2RhdGE6W119KSksbnVsbD09KGU9dFtzXS5kYXRhKXx8ZS5wdXNoKHIuZGF0YSl9KSksdGhpcy5kcj1bXSx0fX12YXIgeG89W1wicmV0cmllc1BlcmZvcm1lZFNvRmFyXCJdO2NsYXNzIEVve2NvbnN0cnVjdG9yKGkpe3RoaXMuYnI9ITEsdGhpcy53cj0zZTMsdGhpcy5kcj1bXSx0aGlzLl9pbnN0YW5jZT1pLHRoaXMuZHI9W10sdGhpcy55cj0hMCwhUih0KSYmXCJvbkxpbmVcImluIHQubmF2aWdhdG9yJiYodGhpcy55cj10Lm5hdmlnYXRvci5vbkxpbmUsc3QodCxcIm9ubGluZVwiLCgoKT0+e3RoaXMueXI9ITAsdGhpcy5ZaSgpfSkpLHN0KHQsXCJvZmZsaW5lXCIsKCgpPT57dGhpcy55cj0hMX0pKSl9Z2V0IGxlbmd0aCgpe3JldHVybiB0aGlzLmRyLmxlbmd0aH1yZXRyaWFibGVSZXF1ZXN0KHQpe3ZhcntyZXRyaWVzUGVyZm9ybWVkU29GYXI6aX09dCxlPUgodCx4byk7RihpKSYmaT4wJiYoZS51cmw9RnMoZS51cmwse3JldHJ5X2NvdW50Oml9KSksdGhpcy5faW5zdGFuY2UubWUoQih7fSxlLHtjYWxsYmFjazp0PT57MjAwIT09dC5zdGF0dXNDb2RlJiYodC5zdGF0dXNDb2RlPDQwMHx8dC5zdGF0dXNDb2RlPj01MDApJiYobnVsbCE9aT9pOjApPDEwP3RoaXMuU3IoQih7cmV0cmllc1BlcmZvcm1lZFNvRmFyOml9LGUpKTpudWxsPT1lLmNhbGxiYWNrfHxlLmNhbGxiYWNrKHQpfX0pKX1Tcih0KXt2YXIgaT10LnJldHJpZXNQZXJmb3JtZWRTb0Zhcnx8MDt0LnJldHJpZXNQZXJmb3JtZWRTb0Zhcj1pKzE7dmFyIGU9ZnVuY3Rpb24odCl7dmFyIGk9M2UzKk1hdGgucG93KDIsdCksZT1pLzIscj1NYXRoLm1pbigxOGU1LGkpLHM9KE1hdGgucmFuZG9tKCktLjUpKihyLWUpO3JldHVybiBNYXRoLmNlaWwocitzKX0oaSkscj1EYXRlLm5vdygpK2U7dGhpcy5kci5wdXNoKHtyZXRyeUF0OnIscmVxdWVzdE9wdGlvbnM6dH0pO3ZhciBzPVwiRW5xdWV1ZWQgZmFpbGVkIHJlcXVlc3QgZm9yIHJldHJ5IGluIFwiK2U7bmF2aWdhdG9yLm9uTGluZXx8KHMrPVwiIChCcm93c2VyIGlzIG9mZmxpbmUpXCIpLGoud2FybihzKSx0aGlzLmJyfHwodGhpcy5icj0hMCx0aGlzLiRyKCkpfSRyKCl7dGhpcy5rciYmY2xlYXJUaW1lb3V0KHRoaXMua3IpLHRoaXMua3I9c2V0VGltZW91dCgoKCk9Pnt0aGlzLnlyJiZ0aGlzLmRyLmxlbmd0aD4wJiZ0aGlzLllpKCksdGhpcy4kcigpfSksdGhpcy53cil9WWkoKXt2YXIgdD1EYXRlLm5vdygpLGk9W10sZT10aGlzLmRyLmZpbHRlcigoZT0+ZS5yZXRyeUF0PHR8fChpLnB1c2goZSksITEpKSk7aWYodGhpcy5kcj1pLGUubGVuZ3RoPjApZm9yKHZhcntyZXF1ZXN0T3B0aW9uczpyfW9mIGUpdGhpcy5yZXRyaWFibGVSZXF1ZXN0KHIpfXVubG9hZCgpe2Zvcih2YXJ7cmVxdWVzdE9wdGlvbnM6dH1vZih0aGlzLmtyJiYoY2xlYXJUaW1lb3V0KHRoaXMua3IpLHRoaXMua3I9dm9pZCAwKSx0aGlzLmRyKSl0cnl7dGhpcy5faW5zdGFuY2UubWUoQih7fSx0LHt0cmFuc3BvcnQ6XCJzZW5kQmVhY29uXCJ9KSl9Y2F0Y2godCl7ai5lcnJvcih0KX10aGlzLmRyPVtdfX1jbGFzcyBJb3tjb25zdHJ1Y3Rvcih0KXt0aGlzLkVyPSgpPT57dmFyIHQsaSxlLHI7dGhpcy5Jcnx8KHRoaXMuSXI9e30pO3ZhciBzPXRoaXMuc2Nyb2xsRWxlbWVudCgpLG49dGhpcy5zY3JvbGxZKCksbz1zP01hdGgubWF4KDAscy5zY3JvbGxIZWlnaHQtcy5jbGllbnRIZWlnaHQpOjAsYT1uKygobnVsbD09cz92b2lkIDA6cy5jbGllbnRIZWlnaHQpfHwwKSxsPShudWxsPT1zP3ZvaWQgMDpzLnNjcm9sbEhlaWdodCl8fDA7dGhpcy5Jci5sYXN0U2Nyb2xsWT1NYXRoLmNlaWwobiksdGhpcy5Jci5tYXhTY3JvbGxZPU1hdGgubWF4KG4sbnVsbCE9PSh0PXRoaXMuSXIubWF4U2Nyb2xsWSkmJnZvaWQgMCE9PXQ/dDowKSx0aGlzLklyLm1heFNjcm9sbEhlaWdodD1NYXRoLm1heChvLG51bGwhPT0oaT10aGlzLklyLm1heFNjcm9sbEhlaWdodCkmJnZvaWQgMCE9PWk/aTowKSx0aGlzLklyLmxhc3RDb250ZW50WT1hLHRoaXMuSXIubWF4Q29udGVudFk9TWF0aC5tYXgoYSxudWxsIT09KGU9dGhpcy5Jci5tYXhDb250ZW50WSkmJnZvaWQgMCE9PWU/ZTowKSx0aGlzLklyLm1heENvbnRlbnRIZWlnaHQ9TWF0aC5tYXgobCxudWxsIT09KHI9dGhpcy5Jci5tYXhDb250ZW50SGVpZ2h0KSYmdm9pZCAwIT09cj9yOjApfSx0aGlzLl9pbnN0YW5jZT10fWdldENvbnRleHQoKXtyZXR1cm4gdGhpcy5Jcn1yZXNldENvbnRleHQoKXt2YXIgdD10aGlzLklyO3JldHVybiBzZXRUaW1lb3V0KHRoaXMuRXIsMCksdH1zdGFydE1lYXN1cmluZ1Njcm9sbFBvc2l0aW9uKCl7c3QodCxcInNjcm9sbFwiLHRoaXMuRXIse2NhcHR1cmU6ITB9KSxzdCh0LFwic2Nyb2xsZW5kXCIsdGhpcy5Fcix7Y2FwdHVyZTohMH0pLHN0KHQsXCJyZXNpemVcIix0aGlzLkVyKX1zY3JvbGxFbGVtZW50KCl7aWYoIXRoaXMuX2luc3RhbmNlLmNvbmZpZy5zY3JvbGxfcm9vdF9zZWxlY3RvcilyZXR1cm4gbnVsbD09dD92b2lkIDA6dC5kb2N1bWVudC5kb2N1bWVudEVsZW1lbnQ7dmFyIGk9eCh0aGlzLl9pbnN0YW5jZS5jb25maWcuc2Nyb2xsX3Jvb3Rfc2VsZWN0b3IpP3RoaXMuX2luc3RhbmNlLmNvbmZpZy5zY3JvbGxfcm9vdF9zZWxlY3RvcjpbdGhpcy5faW5zdGFuY2UuY29uZmlnLnNjcm9sbF9yb290X3NlbGVjdG9yXTtmb3IodmFyIGUgb2YgaSl7dmFyIHI9bnVsbD09dD92b2lkIDA6dC5kb2N1bWVudC5xdWVyeVNlbGVjdG9yKGUpO2lmKHIpcmV0dXJuIHJ9fXNjcm9sbFkoKXtpZih0aGlzLl9pbnN0YW5jZS5jb25maWcuc2Nyb2xsX3Jvb3Rfc2VsZWN0b3Ipe3ZhciBpPXRoaXMuc2Nyb2xsRWxlbWVudCgpO3JldHVybiBpJiZpLnNjcm9sbFRvcHx8MH1yZXR1cm4gdCYmKHQuc2Nyb2xsWXx8dC5wYWdlWU9mZnNldHx8dC5kb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuc2Nyb2xsVG9wKXx8MH1zY3JvbGxYKCl7aWYodGhpcy5faW5zdGFuY2UuY29uZmlnLnNjcm9sbF9yb290X3NlbGVjdG9yKXt2YXIgaT10aGlzLnNjcm9sbEVsZW1lbnQoKTtyZXR1cm4gaSYmaS5zY3JvbGxMZWZ0fHwwfXJldHVybiB0JiYodC5zY3JvbGxYfHx0LnBhZ2VYT2Zmc2V0fHx0LmRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zY3JvbGxMZWZ0KXx8MH19dmFyIFBvPXQ9PlluKG51bGw9PXQ/dm9pZCAwOnQuY29uZmlnLm1hc2tfcGVyc29uYWxfZGF0YV9wcm9wZXJ0aWVzLG51bGw9PXQ/dm9pZCAwOnQuY29uZmlnLmN1c3RvbV9wZXJzb25hbF9kYXRhX3Byb3BlcnRpZXMpO2NsYXNzIFJve2NvbnN0cnVjdG9yKHQsaSxlLHIpe3RoaXMuUHI9dD0+e3ZhciBpPXRoaXMuUnIoKTtpZighaXx8aS5zZXNzaW9uSWQhPT10KXt2YXIgZT17c2Vzc2lvbklkOnQscHJvcHM6dGhpcy5Ucih0aGlzLl9pbnN0YW5jZSl9O3RoaXMuTXIucmVnaXN0ZXIoe1tEdF06ZX0pfX0sdGhpcy5faW5zdGFuY2U9dCx0aGlzLkNyPWksdGhpcy5Ncj1lLHRoaXMuVHI9cnx8UG8sdGhpcy5Dci5vblNlc3Npb25JZCh0aGlzLlByKX1Scigpe3JldHVybiB0aGlzLk1yLnByb3BzW0R0XX1nZXRTZXRPbmNlUHJvcHMoKXt2YXIgdCxpPW51bGw9PSh0PXRoaXMuUnIoKSk/dm9pZCAwOnQucHJvcHM7cmV0dXJuIGk/XCJyXCJpbiBpP1huKGkpOnskcmVmZXJyaW5nX2RvbWFpbjppLnJlZmVycmluZ0RvbWFpbiwkcGF0aG5hbWU6aS5pbml0aWFsUGF0aE5hbWUsdXRtX3NvdXJjZTppLnV0bV9zb3VyY2UsdXRtX2NhbXBhaWduOmkudXRtX2NhbXBhaWduLHV0bV9tZWRpdW06aS51dG1fbWVkaXVtLHV0bV9jb250ZW50OmkudXRtX2NvbnRlbnQsdXRtX3Rlcm06aS51dG1fdGVybX06e319Z2V0U2Vzc2lvblByb3BzKCl7dmFyIHQ9e307cmV0dXJuIEooWih0aGlzLmdldFNldE9uY2VQcm9wcygpKSwoKGksZSk9PntcIiRjdXJyZW50X3VybFwiPT09ZSYmKGU9XCJ1cmxcIiksdFtcIiRzZXNzaW9uX2VudHJ5X1wiK3coZSldPWl9KSksdH19dmFyIFRvPXooXCJbU2Vzc2lvbklkXVwiKTtjbGFzcyBNb3tjb25zdHJ1Y3Rvcih0LGksZSl7dmFyIHI7aWYodGhpcy5Pcj1bXSwhdC5wZXJzaXN0ZW5jZSl0aHJvdyBuZXcgRXJyb3IoXCJTZXNzaW9uSWRNYW5hZ2VyIHJlcXVpcmVzIGEgUG9zdEhvZ1BlcnNpc3RlbmNlIGluc3RhbmNlXCIpO2lmKHQuY29uZmlnLl9fcHJldmlld19leHBlcmltZW50YWxfY29va2llbGVzc19tb2RlKXRocm93IG5ldyBFcnJvcihcIlNlc3Npb25JZE1hbmFnZXIgY2Fubm90IGJlIHVzZWQgd2l0aCBfX3ByZXZpZXdfZXhwZXJpbWVudGFsX2Nvb2tpZWxlc3NfbW9kZVwiKTt0aGlzLlM9dC5jb25maWcsdGhpcy5Ncj10LnBlcnNpc3RlbmNlLHRoaXMub2k9dm9pZCAwLHRoaXMua3Q9dm9pZCAwLHRoaXMuX3Nlc3Npb25TdGFydFRpbWVzdGFtcD1udWxsLHRoaXMuX3Nlc3Npb25BY3Rpdml0eVRpbWVzdGFtcD1udWxsLHRoaXMuRnI9aXx8TmksdGhpcy5Bcj1lfHxOaTt2YXIgcz10aGlzLlMucGVyc2lzdGVuY2VfbmFtZXx8dGhpcy5TLnRva2VuLG49dGhpcy5TLnNlc3Npb25faWRsZV90aW1lb3V0X3NlY29uZHN8fDE4MDA7aWYodGhpcy5fc2Vzc2lvblRpbWVvdXRNcz0xZTMqWGUobiw2MCwzNmUzLFwic2Vzc2lvbl9pZGxlX3RpbWVvdXRfc2Vjb25kc1wiLDE4MDApLHQucmVnaXN0ZXIoeyRjb25maWd1cmVkX3Nlc3Npb25fdGltZW91dF9tczp0aGlzLl9zZXNzaW9uVGltZW91dE1zfSksdGhpcy5EcigpLHRoaXMuTHI9XCJwaF9cIitzK1wiX3dpbmRvd19pZFwiLHRoaXMuTnI9XCJwaF9cIitzK1wiX3ByaW1hcnlfd2luZG93X2V4aXN0c1wiLHRoaXMuanIoKSl7dmFyIG89WGkuRCh0aGlzLkxyKSxhPVhpLkQodGhpcy5Ocik7byYmIWE/dGhpcy5vaT1vOlhpLk4odGhpcy5MciksWGkuTCh0aGlzLk5yLCEwKX1pZihudWxsIT0ocj10aGlzLlMuYm9vdHN0cmFwKSYmci5zZXNzaW9uSUQpdHJ5e3ZhciBsPSh0PT57dmFyIGk9dC5yZXBsYWNlKC8tL2csXCJcIik7aWYoMzIhPT1pLmxlbmd0aCl0aHJvdyBuZXcgRXJyb3IoXCJOb3QgYSB2YWxpZCBVVUlEXCIpO2lmKFwiN1wiIT09aVsxMl0pdGhyb3cgbmV3IEVycm9yKFwiTm90IGEgVVVJRHY3XCIpO3JldHVybiBwYXJzZUludChpLnN1YnN0cmluZygwLDEyKSwxNil9KSh0aGlzLlMuYm9vdHN0cmFwLnNlc3Npb25JRCk7dGhpcy56cih0aGlzLlMuYm9vdHN0cmFwLnNlc3Npb25JRCwobmV3IERhdGUpLmdldFRpbWUoKSxsKX1jYXRjaCh0KXtUby5lcnJvcihcIkludmFsaWQgc2Vzc2lvbklEIGluIGJvb3RzdHJhcFwiLHQpfXRoaXMuVXIoKX1nZXQgc2Vzc2lvblRpbWVvdXRNcygpe3JldHVybiB0aGlzLl9zZXNzaW9uVGltZW91dE1zfW9uU2Vzc2lvbklkKHQpe3JldHVybiBSKHRoaXMuT3IpJiYodGhpcy5Pcj1bXSksdGhpcy5Pci5wdXNoKHQpLHRoaXMua3QmJnQodGhpcy5rdCx0aGlzLm9pKSwoKT0+e3RoaXMuT3I9dGhpcy5Pci5maWx0ZXIoKGk9PmkhPT10KSl9fWpyKCl7cmV0dXJuXCJtZW1vcnlcIiE9PXRoaXMuUy5wZXJzaXN0ZW5jZSYmIXRoaXMuTXIueGUmJlhpLk8oKX1xcih0KXt0IT09dGhpcy5vaSYmKHRoaXMub2k9dCx0aGlzLmpyKCkmJlhpLkwodGhpcy5Mcix0KSl9QnIoKXtyZXR1cm4gdGhpcy5vaT90aGlzLm9pOnRoaXMuanIoKT9YaS5EKHRoaXMuTHIpOm51bGx9enIodCxpLGUpe3Q9PT10aGlzLmt0JiZpPT09dGhpcy5fc2Vzc2lvbkFjdGl2aXR5VGltZXN0YW1wJiZlPT09dGhpcy5fc2Vzc2lvblN0YXJ0VGltZXN0YW1wfHwodGhpcy5fc2Vzc2lvblN0YXJ0VGltZXN0YW1wPWUsdGhpcy5fc2Vzc2lvbkFjdGl2aXR5VGltZXN0YW1wPWksdGhpcy5rdD10LHRoaXMuTXIucmVnaXN0ZXIoe1skdF06W2ksdCxlXX0pKX1Icigpe2lmKHRoaXMua3QmJnRoaXMuX3Nlc3Npb25BY3Rpdml0eVRpbWVzdGFtcCYmdGhpcy5fc2Vzc2lvblN0YXJ0VGltZXN0YW1wKXJldHVyblt0aGlzLl9zZXNzaW9uQWN0aXZpdHlUaW1lc3RhbXAsdGhpcy5rdCx0aGlzLl9zZXNzaW9uU3RhcnRUaW1lc3RhbXBdO3ZhciB0PXRoaXMuTXIucHJvcHNbJHRdO3JldHVybiB4KHQpJiYyPT09dC5sZW5ndGgmJnQucHVzaCh0WzBdKSx0fHxbMCxudWxsLDBdfXJlc2V0U2Vzc2lvbklkKCl7dGhpcy56cihudWxsLG51bGwsbnVsbCl9VXIoKXtzdCh0LFwiYmVmb3JldW5sb2FkXCIsKCgpPT57dGhpcy5qcigpJiZYaS5OKHRoaXMuTnIpfSkse2NhcHR1cmU6ITF9KX1jaGVja0FuZEdldFNlc3Npb25BbmRXaW5kb3dJZCh0LGkpe2lmKHZvaWQgMD09PXQmJih0PSExKSx2b2lkIDA9PT1pJiYoaT1udWxsKSx0aGlzLlMuX19wcmV2aWV3X2V4cGVyaW1lbnRhbF9jb29raWVsZXNzX21vZGUpdGhyb3cgbmV3IEVycm9yKFwiY2hlY2tBbmRHZXRTZXNzaW9uQW5kV2luZG93SWQgc2hvdWxkIG5vdCBiZSBjYWxsZWQgaW4gX19wcmV2aWV3X2V4cGVyaW1lbnRhbF9jb29raWVsZXNzX21vZGVcIik7dmFyIGU9aXx8KG5ldyBEYXRlKS5nZXRUaW1lKCksW3IscyxuXT10aGlzLkhyKCksbz10aGlzLkJyKCksYT1GKG4pJiZuPjAmJk1hdGguYWJzKGUtbik+ODY0ZTUsbD0hMSx1PSFzLGg9IXQmJk1hdGguYWJzKGUtcik+dGhpcy5zZXNzaW9uVGltZW91dE1zO3V8fGh8fGE/KHM9dGhpcy5GcigpLG89dGhpcy5BcigpLFRvLmluZm8oXCJuZXcgc2Vzc2lvbiBJRCBnZW5lcmF0ZWRcIix7c2Vzc2lvbklkOnMsd2luZG93SWQ6byxjaGFuZ2VSZWFzb246e25vU2Vzc2lvbklkOnUsYWN0aXZpdHlUaW1lb3V0Omgsc2Vzc2lvblBhc3RNYXhpbXVtTGVuZ3RoOmF9fSksbj1lLGw9ITApOm98fChvPXRoaXMuQXIoKSxsPSEwKTt2YXIgZD0wPT09cnx8IXR8fGE/ZTpyLHY9MD09PW4/KG5ldyBEYXRlKS5nZXRUaW1lKCk6bjtyZXR1cm4gdGhpcy5xcihvKSx0aGlzLnpyKHMsZCx2KSx0fHx0aGlzLkRyKCksbCYmdGhpcy5Pci5mb3JFYWNoKCh0PT50KHMsbyxsP3tub1Nlc3Npb25JZDp1LGFjdGl2aXR5VGltZW91dDpoLHNlc3Npb25QYXN0TWF4aW11bUxlbmd0aDphfTp2b2lkIDApKSkse3Nlc3Npb25JZDpzLHdpbmRvd0lkOm8sc2Vzc2lvblN0YXJ0VGltZXN0YW1wOnYsY2hhbmdlUmVhc29uOmw/e25vU2Vzc2lvbklkOnUsYWN0aXZpdHlUaW1lb3V0Omgsc2Vzc2lvblBhc3RNYXhpbXVtTGVuZ3RoOmF9OnZvaWQgMCxsYXN0QWN0aXZpdHlUaW1lc3RhbXA6cn19RHIoKXtjbGVhclRpbWVvdXQodGhpcy5XciksdGhpcy5Xcj1zZXRUaW1lb3V0KCgoKT0+e3RoaXMucmVzZXRTZXNzaW9uSWQoKX0pLDEuMSp0aGlzLnNlc3Npb25UaW1lb3V0TXMpfX12YXIgQ289W1wiJHNldF9vbmNlXCIsXCIkc2V0XCJdLE9vPXooXCJbU2l0ZUFwcHNdXCIpO2NsYXNzIEZve2NvbnN0cnVjdG9yKHQpe3RoaXMuX2luc3RhbmNlPXQsdGhpcy5Hcj1bXSx0aGlzLmFwcHM9e319Z2V0IGlzRW5hYmxlZCgpe3JldHVybiEhdGhpcy5faW5zdGFuY2UuY29uZmlnLm9wdF9pbl9zaXRlX2FwcHN9SnIodCxpKXtpZihpKXt2YXIgZT10aGlzLmdsb2JhbHNGb3JFdmVudChpKTt0aGlzLkdyLnB1c2goZSksdGhpcy5Hci5sZW5ndGg+MWUzJiYodGhpcy5Hcj10aGlzLkdyLnNsaWNlKDEwKSl9fWdldCBzaXRlQXBwTG9hZGVycygpe3ZhciB0O3JldHVybiBudWxsPT0odD12Ll9QT1NUSE9HX1JFTU9URV9DT05GSUcpfHxudWxsPT0odD10W3RoaXMuX2luc3RhbmNlLmNvbmZpZy50b2tlbl0pP3ZvaWQgMDp0LnNpdGVBcHBzfWluaXQoKXtpZih0aGlzLmlzRW5hYmxlZCl7dmFyIHQ9dGhpcy5faW5zdGFuY2UuTmUodGhpcy5Kci5iaW5kKHRoaXMpKTt0aGlzLlZyPSgpPT57dCgpLHRoaXMuR3I9W10sdGhpcy5Wcj12b2lkIDB9fX1nbG9iYWxzRm9yRXZlbnQodCl7dmFyIGksZSxyLHMsbixvLGE7aWYoIXQpdGhyb3cgbmV3IEVycm9yKFwiRXZlbnQgcGF5bG9hZCBpcyByZXF1aXJlZFwiKTt2YXIgbD17fSx1PXRoaXMuX2luc3RhbmNlLmdldF9wcm9wZXJ0eShcIiRncm91cHNcIil8fFtdLGg9dGhpcy5faW5zdGFuY2UuZ2V0X3Byb3BlcnR5KFwiJHN0b3JlZF9ncm91cF9wcm9wZXJ0aWVzXCIpfHx7fTtmb3IodmFyW2Qsdl1vZiBPYmplY3QuZW50cmllcyhoKSlsW2RdPXtpZDp1W2RdLHR5cGU6ZCxwcm9wZXJ0aWVzOnZ9O3Zhcnskc2V0X29uY2U6Yywkc2V0OmZ9PXQ7cmV0dXJue2V2ZW50OkIoe30sSCh0LENvKSx7cHJvcGVydGllczpCKHt9LHQucHJvcGVydGllcyxmP3skc2V0OkIoe30sbnVsbCE9PShpPW51bGw9PShlPXQucHJvcGVydGllcyk/dm9pZCAwOmUuJHNldCkmJnZvaWQgMCE9PWk/aTp7fSxmKX06e30sYz97JHNldF9vbmNlOkIoe30sbnVsbCE9PShyPW51bGw9PShzPXQucHJvcGVydGllcyk/dm9pZCAwOnMuJHNldF9vbmNlKSYmdm9pZCAwIT09cj9yOnt9LGMpfTp7fSksZWxlbWVudHNfY2hhaW46bnVsbCE9PShuPW51bGw9PShvPXQucHJvcGVydGllcyk/dm9pZCAwOm8uJGVsZW1lbnRzX2NoYWluKSYmdm9pZCAwIT09bj9uOlwiXCIsZGlzdGluY3RfaWQ6bnVsbD09KGE9dC5wcm9wZXJ0aWVzKT92b2lkIDA6YS5kaXN0aW5jdF9pZH0pLHBlcnNvbjp7cHJvcGVydGllczp0aGlzLl9pbnN0YW5jZS5nZXRfcHJvcGVydHkoXCIkc3RvcmVkX3BlcnNvbl9wcm9wZXJ0aWVzXCIpfSxncm91cHM6bH19c2V0dXBTaXRlQXBwKHQpe3ZhciBpPXRoaXMuYXBwc1t0LmlkXSxlPSgpPT57dmFyIGU7KCFpLmVycm9yZWQmJnRoaXMuR3IubGVuZ3RoJiYoT28uaW5mbyhcIlByb2Nlc3NpbmcgXCIrdGhpcy5Hci5sZW5ndGgrXCIgZXZlbnRzIGZvciBzaXRlIGFwcCB3aXRoIGlkIFwiK3QuaWQpLHRoaXMuR3IuZm9yRWFjaCgodD0+bnVsbD09aS5wcm9jZXNzRXZlbnQ/dm9pZCAwOmkucHJvY2Vzc0V2ZW50KHQpKSksaS5wcm9jZXNzZWRCdWZmZXI9ITApLE9iamVjdC52YWx1ZXModGhpcy5hcHBzKS5ldmVyeSgodD0+dC5wcm9jZXNzZWRCdWZmZXJ8fHQuZXJyb3JlZCkpKSYmKG51bGw9PShlPXRoaXMuVnIpfHxlLmNhbGwodGhpcykpfSxyPSExLHM9cz0+e2kuZXJyb3JlZD0hcyxpLmxvYWRlZD0hMCxPby5pbmZvKFwiU2l0ZSBhcHAgd2l0aCBpZCBcIit0LmlkK1wiIFwiKyhzP1wibG9hZGVkXCI6XCJlcnJvcmVkXCIpKSxyJiZlKCl9O3RyeXt2YXJ7cHJvY2Vzc0V2ZW50Om59PXQuaW5pdCh7cG9zdGhvZzp0aGlzLl9pbnN0YW5jZSxjYWxsYmFjazp0PT57cyh0KX19KTtuJiYoaS5wcm9jZXNzRXZlbnQ9bikscj0hMH1jYXRjaChpKXtPby5lcnJvcihcIkVycm9yIHdoaWxlIGluaXRpYWxpemluZyBQb3N0SG9nIGFwcCB3aXRoIGNvbmZpZyBpZCBcIit0LmlkLGkpLHMoITEpfWlmKHImJmkubG9hZGVkKXRyeXtlKCl9Y2F0Y2goZSl7T28uZXJyb3IoXCJFcnJvciB3aGlsZSBwcm9jZXNzaW5nIGJ1ZmZlcmVkIGV2ZW50cyBQb3N0SG9nIGFwcCB3aXRoIGNvbmZpZyBpZCBcIit0LmlkLGUpLGkuZXJyb3JlZD0hMH19S3IoKXt2YXIgdD10aGlzLnNpdGVBcHBMb2FkZXJzfHxbXTtmb3IodmFyIGkgb2YgdCl0aGlzLmFwcHNbaS5pZF09e2lkOmkuaWQsbG9hZGVkOiExLGVycm9yZWQ6ITEscHJvY2Vzc2VkQnVmZmVyOiExfTtmb3IodmFyIGUgb2YgdCl0aGlzLnNldHVwU2l0ZUFwcChlKX1Zcih0KXtpZigwIT09T2JqZWN0LmtleXModGhpcy5hcHBzKS5sZW5ndGgpe3ZhciBpPXRoaXMuZ2xvYmFsc0ZvckV2ZW50KHQpO2Zvcih2YXIgZSBvZiBPYmplY3QudmFsdWVzKHRoaXMuYXBwcykpdHJ5e251bGw9PWUucHJvY2Vzc0V2ZW50fHxlLnByb2Nlc3NFdmVudChpKX1jYXRjaChpKXtPby5lcnJvcihcIkVycm9yIHdoaWxlIHByb2Nlc3NpbmcgZXZlbnQgXCIrdC5ldmVudCtcIiBmb3Igc2l0ZSBhcHAgXCIrZS5pZCxpKX19fW9uUmVtb3RlQ29uZmlnKHQpe3ZhciBpLGUscixzPXRoaXM7aWYobnVsbCE9KGk9dGhpcy5zaXRlQXBwTG9hZGVycykmJmkubGVuZ3RoKXJldHVybiB0aGlzLmlzRW5hYmxlZD8odGhpcy5LcigpLHZvaWQgdGhpcy5faW5zdGFuY2Uub24oXCJldmVudENhcHR1cmVkXCIsKHQ9PnRoaXMuWXIodCkpKSk6dm9pZCBPby5lcnJvcignUG9zdEhvZyBzaXRlIGFwcHMgYXJlIGRpc2FibGVkLiBFbmFibGUgdGhlIFwib3B0X2luX3NpdGVfYXBwc1wiIGNvbmZpZyB0byBwcm9jZWVkLicpO2lmKG51bGw9PShlPXRoaXMuVnIpfHxlLmNhbGwodGhpcyksbnVsbCE9KHI9dC5zaXRlQXBwcykmJnIubGVuZ3RoKWlmKHRoaXMuaXNFbmFibGVkKXt2YXIgbj1mdW5jdGlvbih0KXt2YXIgaTt2W1wiX18kJHBoX3NpdGVfYXBwX1wiK3RdPXMuX2luc3RhbmNlLG51bGw9PShpPXYuX19Qb3N0aG9nRXh0ZW5zaW9uc19fKXx8bnVsbD09aS5sb2FkU2l0ZUFwcHx8aS5sb2FkU2l0ZUFwcChzLl9pbnN0YW5jZSxhLChpPT57aWYoaSlyZXR1cm4gT28uZXJyb3IoXCJFcnJvciB3aGlsZSBpbml0aWFsaXppbmcgUG9zdEhvZyBhcHAgd2l0aCBjb25maWcgaWQgXCIrdCxpKX0pKX07Zm9yKHZhcntpZDpvLHVybDphfW9mIHQuc2l0ZUFwcHMpbihvKX1lbHNlIE9vLmVycm9yKCdQb3N0SG9nIHNpdGUgYXBwcyBhcmUgZGlzYWJsZWQuIEVuYWJsZSB0aGUgXCJvcHRfaW5fc2l0ZV9hcHBzXCIgY29uZmlnIHRvIHByb2NlZWQuJyl9fXZhciBBbz1bXCJhbWF6b25ib3RcIixcImFtYXpvbnByb2R1Y3Rib3RcIixcImFwcC5oeXBlZmFjdG9ycy5jb21cIixcImFwcGxlYm90XCIsXCJhcmNoaXZlLm9yZ19ib3RcIixcImF3YXJpb2JvdFwiLFwiYmFja2xpbmtzZXh0ZW5kZWRib3RcIixcImJhaWR1c3BpZGVyXCIsXCJiaW5nYm90XCIsXCJiaW5ncHJldmlld1wiLFwiY2hyb21lLWxpZ2h0aG91c2VcIixcImRhdGFmb3JzZW9ib3RcIixcImRlZXBzY2FuXCIsXCJkdWNrZHVja2JvdFwiLFwiZmFjZWJvb2tleHRlcm5hbFwiLFwiZmFjZWJvb2tjYXRhbG9nXCIsXCJodHRwOi8veWFuZGV4LmNvbS9ib3RzXCIsXCJodWJzcG90XCIsXCJpYV9hcmNoaXZlclwiLFwibGlua2VkaW5ib3RcIixcIm1ldGEtZXh0ZXJuYWxhZ2VudFwiLFwibWoxMmJvdFwiLFwibXNuYm90XCIsXCJuZXNzdXNcIixcInBldGFsYm90XCIsXCJwaW50ZXJlc3RcIixcInByZXJlbmRlclwiLFwicm9nZXJib3RcIixcInNjcmVhbWluZyBmcm9nXCIsXCJzZWJvdC13YVwiLFwic2l0ZWJ1bGJcIixcInNsYWNrYm90XCIsXCJzbHVycFwiLFwidHJlbmRpY3Rpb25ib3RcIixcInR1cm5pdGluXCIsXCJ0d2l0dGVyYm90XCIsXCJ2ZXJjZWxib3RcIixcInlhaG9vISBzbHVycFwiLFwieWFuZGV4Ym90XCIsXCJ6b29tYm90XCIsXCJib3QuaHRtXCIsXCJib3QucGhwXCIsXCIoYm90O1wiLFwiYm90L1wiLFwiY3Jhd2xlclwiLFwiYWhyZWZzYm90XCIsXCJhaHJlZnNzaXRlYXVkaXRcIixcInNlbXJ1c2hib3RcIixcInNpdGVhdWRpdGJvdFwiLFwic3BsaXRzaWduYWxib3RcIixcImdwdGJvdFwiLFwib2FpLXNlYXJjaGJvdFwiLFwiY2hhdGdwdC11c2VyXCIsXCJwZXJwbGV4aXR5Ym90XCIsXCJiZXR0ZXIgdXB0aW1lIGJvdFwiLFwic2VudHJ5dXB0aW1lYm90XCIsXCJ1cHRpbWVyb2JvdFwiLFwiaGVhZGxlc3NjaHJvbWVcIixcImN5cHJlc3NcIixcImdvb2dsZS1ob3RlbGFkc3ZlcmlmaWVyXCIsXCJhZHNib3QtZ29vZ2xlXCIsXCJhcGlzLWdvb2dsZVwiLFwiZHVwbGV4d2ViLWdvb2dsZVwiLFwiZmVlZGZldGNoZXItZ29vZ2xlXCIsXCJnb29nbGUgZmF2aWNvblwiLFwiZ29vZ2xlIHdlYiBwcmV2aWV3XCIsXCJnb29nbGUtcmVhZC1hbG91ZFwiLFwiZ29vZ2xlYm90XCIsXCJnb29nbGVvdGhlclwiLFwiZ29vZ2xlLWNsb3VkdmVydGV4Ym90XCIsXCJnb29nbGV3ZWJsaWdodFwiLFwibWVkaWFwYXJ0bmVycy1nb29nbGVcIixcInN0b3JlYm90LWdvb2dsZVwiLFwiZ29vZ2xlLWluc3BlY3Rpb250b29sXCIsXCJieXRlc3BpZGVyXCJdLERvPWZ1bmN0aW9uKHQsaSl7aWYoIXQpcmV0dXJuITE7dmFyIGU9dC50b0xvd2VyQ2FzZSgpO3JldHVybiBBby5jb25jYXQoaXx8W10pLnNvbWUoKHQ9Pnt2YXIgaT10LnRvTG93ZXJDYXNlKCk7cmV0dXJuLTEhPT1lLmluZGV4T2YoaSl9KSl9LExvPWZ1bmN0aW9uKHQsaSl7aWYoIXQpcmV0dXJuITE7dmFyIGU9dC51c2VyQWdlbnQ7aWYoZSYmRG8oZSxpKSlyZXR1cm4hMDt0cnl7dmFyIHI9bnVsbD09dD92b2lkIDA6dC51c2VyQWdlbnREYXRhO2lmKG51bGwhPXImJnIuYnJhbmRzJiZyLmJyYW5kcy5zb21lKCh0PT5EbyhudWxsPT10P3ZvaWQgMDp0LmJyYW5kLGkpKSkpcmV0dXJuITB9Y2F0Y2godCl7fXJldHVybiEhdC53ZWJkcml2ZXJ9LE5vPWZ1bmN0aW9uKHQpe3JldHVybiB0LlVTPVwidXNcIix0LkVVPVwiZXVcIix0LkNVU1RPTT1cImN1c3RvbVwiLHR9KHt9KSxqbz1cImkucG9zdGhvZy5jb21cIjtjbGFzcyB6b3tjb25zdHJ1Y3Rvcih0KXt0aGlzLlhyPXt9LHRoaXMuaW5zdGFuY2U9dH1nZXQgYXBpSG9zdCgpe3ZhciB0PXRoaXMuaW5zdGFuY2UuY29uZmlnLmFwaV9ob3N0LnRyaW0oKS5yZXBsYWNlKC9cXC8kLyxcIlwiKTtyZXR1cm5cImh0dHBzOi8vYXBwLnBvc3Rob2cuY29tXCI9PT10P1wiaHR0cHM6Ly91cy5pLnBvc3Rob2cuY29tXCI6dH1nZXQgdWlIb3N0KCl7dmFyIHQsaT1udWxsPT0odD10aGlzLmluc3RhbmNlLmNvbmZpZy51aV9ob3N0KT92b2lkIDA6dC5yZXBsYWNlKC9cXC8kLyxcIlwiKTtyZXR1cm4gaXx8KGk9dGhpcy5hcGlIb3N0LnJlcGxhY2UoXCIuXCIram8sXCIucG9zdGhvZy5jb21cIikpLFwiaHR0cHM6Ly9hcHAucG9zdGhvZy5jb21cIj09PWk/XCJodHRwczovL3VzLnBvc3Rob2cuY29tXCI6aX1nZXQgcmVnaW9uKCl7cmV0dXJuIHRoaXMuWHJbdGhpcy5hcGlIb3N0XXx8KC9odHRwczpcXC9cXC8oYXBwfHVzfHVzLWFzc2V0cykoXFwuaSk/XFwucG9zdGhvZ1xcLmNvbS9pLnRlc3QodGhpcy5hcGlIb3N0KT90aGlzLlhyW3RoaXMuYXBpSG9zdF09Tm8uVVM6L2h0dHBzOlxcL1xcLyhldXxldS1hc3NldHMpKFxcLmkpP1xcLnBvc3Rob2dcXC5jb20vaS50ZXN0KHRoaXMuYXBpSG9zdCk/dGhpcy5Yclt0aGlzLmFwaUhvc3RdPU5vLkVVOnRoaXMuWHJbdGhpcy5hcGlIb3N0XT1Oby5DVVNUT00pLHRoaXMuWHJbdGhpcy5hcGlIb3N0XX1lbmRwb2ludEZvcih0LGkpe2lmKHZvaWQgMD09PWkmJihpPVwiXCIpLGkmJihpPVwiL1wiPT09aVswXT9pOlwiL1wiK2kpLFwidWlcIj09PXQpcmV0dXJuIHRoaXMudWlIb3N0K2k7aWYodGhpcy5yZWdpb249PT1Oby5DVVNUT00pcmV0dXJuIHRoaXMuYXBpSG9zdCtpO3ZhciBlPWpvK2k7c3dpdGNoKHQpe2Nhc2VcImFzc2V0c1wiOnJldHVyblwiaHR0cHM6Ly9cIit0aGlzLnJlZ2lvbitcIi1hc3NldHMuXCIrZTtjYXNlXCJhcGlcIjpyZXR1cm5cImh0dHBzOi8vXCIrdGhpcy5yZWdpb24rXCIuXCIrZX19fXZhciBVbz17aWNvbnRhaW5zOihpLGUpPT4hIXQmJmUuaHJlZi50b0xvd2VyQ2FzZSgpLmluZGV4T2YoaS50b0xvd2VyQ2FzZSgpKT4tMSxub3RfaWNvbnRhaW5zOihpLGUpPT4hIXQmJi0xPT09ZS5ocmVmLnRvTG93ZXJDYXNlKCkuaW5kZXhPZihpLnRvTG93ZXJDYXNlKCkpLHJlZ2V4OihpLGUpPT4hIXQmJk5zKGUuaHJlZixpKSxub3RfcmVnZXg6KGksZSk9PiEhdCYmIU5zKGUuaHJlZixpKSxleGFjdDoodCxpKT0+aS5ocmVmPT09dCxpc19ub3Q6KHQsaSk9PmkuaHJlZiE9PXR9O2NsYXNzIHFve2NvbnN0cnVjdG9yKHQpe3ZhciBpPXRoaXM7dGhpcy5nZXRXZWJFeHBlcmltZW50c0FuZEV2YWx1YXRlRGlzcGxheUxvZ2ljPWZ1bmN0aW9uKHQpe3ZvaWQgMD09PXQmJih0PSExKSxpLmdldFdlYkV4cGVyaW1lbnRzKCh0PT57cW8uUXIoXCJyZXRyaWV2ZWQgd2ViIGV4cGVyaW1lbnRzIGZyb20gdGhlIHNlcnZlclwiKSxpLlpyPW5ldyBNYXAsdC5mb3JFYWNoKCh0PT57aWYodC5mZWF0dXJlX2ZsYWdfa2V5KXt2YXIgZTtpZihpLlpyKXFvLlFyKFwic2V0dGluZyBmbGFnIGtleSBcIix0LmZlYXR1cmVfZmxhZ19rZXksXCIgdG8gd2ViIGV4cGVyaW1lbnQgXCIsdCksbnVsbD09KGU9aS5acil8fGUuc2V0KHQuZmVhdHVyZV9mbGFnX2tleSx0KTt2YXIgcj1pLl9pbnN0YW5jZS5nZXRGZWF0dXJlRmxhZyh0LmZlYXR1cmVfZmxhZ19rZXkpO1QocikmJnQudmFyaWFudHNbcl0mJmkudHModC5uYW1lLHIsdC52YXJpYW50c1tyXS50cmFuc2Zvcm1zKX1lbHNlIGlmKHQudmFyaWFudHMpZm9yKHZhciBzIGluIHQudmFyaWFudHMpe3ZhciBuPXQudmFyaWFudHNbc107cW8uZXMobikmJmkudHModC5uYW1lLHMsbi50cmFuc2Zvcm1zKX19KSl9KSx0KX0sdGhpcy5faW5zdGFuY2U9dCx0aGlzLl9pbnN0YW5jZS5vbkZlYXR1cmVGbGFncygodD0+e3RoaXMub25GZWF0dXJlRmxhZ3ModCl9KSl9b25GZWF0dXJlRmxhZ3ModCl7aWYodGhpcy5faXNfYm90KCkpcW8uUXIoXCJSZWZ1c2luZyB0byByZW5kZXIgd2ViIGV4cGVyaW1lbnQgc2luY2UgdGhlIHZpZXdlciBpcyBhIGxpa2VseSBib3RcIik7ZWxzZSBpZighdGhpcy5faW5zdGFuY2UuY29uZmlnLmRpc2FibGVfd2ViX2V4cGVyaW1lbnRzKXtpZihPKHRoaXMuWnIpKXJldHVybiB0aGlzLlpyPW5ldyBNYXAsdGhpcy5sb2FkSWZFbmFibGVkKCksdm9pZCB0aGlzLnByZXZpZXdXZWJFeHBlcmltZW50KCk7cW8uUXIoXCJhcHBseWluZyBmZWF0dXJlIGZsYWdzXCIsdCksdC5mb3JFYWNoKCh0PT57dmFyIGk7aWYodGhpcy5aciYmbnVsbCE9KGk9dGhpcy5acikmJmkuaGFzKHQpKXt2YXIgZSxyPXRoaXMuX2luc3RhbmNlLmdldEZlYXR1cmVGbGFnKHQpLHM9bnVsbD09KGU9dGhpcy5acik/dm9pZCAwOmUuZ2V0KHQpO3ImJm51bGwhPXMmJnMudmFyaWFudHNbcl0mJnRoaXMudHMocy5uYW1lLHIscy52YXJpYW50c1tyXS50cmFuc2Zvcm1zKX19KSl9fXByZXZpZXdXZWJFeHBlcmltZW50KCl7dmFyIHQ9cW8uZ2V0V2luZG93TG9jYXRpb24oKTtpZihudWxsIT10JiZ0LnNlYXJjaCl7dmFyIGk9JGkobnVsbD09dD92b2lkIDA6dC5zZWFyY2gsXCJfX2V4cGVyaW1lbnRfaWRcIiksZT0kaShudWxsPT10P3ZvaWQgMDp0LnNlYXJjaCxcIl9fZXhwZXJpbWVudF92YXJpYW50XCIpO2kmJmUmJihxby5RcihcInByZXZpZXdpbmcgd2ViIGV4cGVyaW1lbnRzIFwiK2krXCIgJiYgXCIrZSksdGhpcy5nZXRXZWJFeHBlcmltZW50cygodD0+e3RoaXMucnMocGFyc2VJbnQoaSksZSx0KX0pLCExLCEwKSl9fWxvYWRJZkVuYWJsZWQoKXt0aGlzLl9pbnN0YW5jZS5jb25maWcuZGlzYWJsZV93ZWJfZXhwZXJpbWVudHN8fHRoaXMuZ2V0V2ViRXhwZXJpbWVudHNBbmRFdmFsdWF0ZURpc3BsYXlMb2dpYygpfWdldFdlYkV4cGVyaW1lbnRzKHQsaSxlKXtpZih0aGlzLl9pbnN0YW5jZS5jb25maWcuZGlzYWJsZV93ZWJfZXhwZXJpbWVudHMmJiFlKXJldHVybiB0KFtdKTt2YXIgcj10aGlzLl9pbnN0YW5jZS5nZXRfcHJvcGVydHkoXCIkd2ViX2V4cGVyaW1lbnRzXCIpO2lmKHImJiFpKXJldHVybiB0KHIpO3RoaXMuX2luc3RhbmNlLm1lKHt1cmw6dGhpcy5faW5zdGFuY2UucmVxdWVzdFJvdXRlci5lbmRwb2ludEZvcihcImFwaVwiLFwiL2FwaS93ZWJfZXhwZXJpbWVudHMvP3Rva2VuPVwiK3RoaXMuX2luc3RhbmNlLmNvbmZpZy50b2tlbiksbWV0aG9kOlwiR0VUXCIsY2FsbGJhY2s6aT0+e2lmKDIwMCE9PWkuc3RhdHVzQ29kZXx8IWkuanNvbilyZXR1cm4gdChbXSk7dmFyIGU9aS5qc29uLmV4cGVyaW1lbnRzfHxbXTtyZXR1cm4gdChlKX19KX1ycyh0LGksZSl7dmFyIHI9ZS5maWx0ZXIoKGk9PmkuaWQ9PT10KSk7ciYmci5sZW5ndGg+MCYmKHFvLlFyKFwiUHJldmlld2luZyB3ZWIgZXhwZXJpbWVudCBbXCIrclswXS5uYW1lK1wiXSB3aXRoIHZhcmlhbnQgW1wiK2krXCJdXCIpLHRoaXMudHMoclswXS5uYW1lLGksclswXS52YXJpYW50c1tpXS50cmFuc2Zvcm1zKSl9c3RhdGljIGVzKHQpe3JldHVybiFPKHQuY29uZGl0aW9ucykmJihxby5zcyh0KSYmcW8ubnModCkpfXN0YXRpYyBzcyh0KXt2YXIgaTtpZihPKHQuY29uZGl0aW9ucyl8fE8obnVsbD09KGk9dC5jb25kaXRpb25zKT92b2lkIDA6aS51cmwpKXJldHVybiEwO3ZhciBlLHIscyxuPXFvLmdldFdpbmRvd0xvY2F0aW9uKCk7cmV0dXJuISFuJiYobnVsbD09KGU9dC5jb25kaXRpb25zKXx8IWUudXJsfHxVb1tudWxsIT09KHI9bnVsbD09KHM9dC5jb25kaXRpb25zKT92b2lkIDA6cy51cmxNYXRjaFR5cGUpJiZ2b2lkIDAhPT1yP3I6XCJpY29udGFpbnNcIl0odC5jb25kaXRpb25zLnVybCxuKSl9c3RhdGljIGdldFdpbmRvd0xvY2F0aW9uKCl7cmV0dXJuIG51bGw9PXQ/dm9pZCAwOnQubG9jYXRpb259c3RhdGljIG5zKHQpe3ZhciBpO2lmKE8odC5jb25kaXRpb25zKXx8TyhudWxsPT0oaT10LmNvbmRpdGlvbnMpP3ZvaWQgMDppLnV0bSkpcmV0dXJuITA7dmFyIGU9V24oKTtpZihlLnV0bV9zb3VyY2Upe3ZhciByLHMsbixvLGEsbCx1LGgsZD1udWxsPT0ocj10LmNvbmRpdGlvbnMpfHxudWxsPT0ocj1yLnV0bSl8fCFyLnV0bV9jYW1wYWlnbnx8KG51bGw9PShzPXQuY29uZGl0aW9ucyl8fG51bGw9PShzPXMudXRtKT92b2lkIDA6cy51dG1fY2FtcGFpZ24pPT1lLnV0bV9jYW1wYWlnbix2PW51bGw9PShuPXQuY29uZGl0aW9ucyl8fG51bGw9PShuPW4udXRtKXx8IW4udXRtX3NvdXJjZXx8KG51bGw9PShvPXQuY29uZGl0aW9ucyl8fG51bGw9PShvPW8udXRtKT92b2lkIDA6by51dG1fc291cmNlKT09ZS51dG1fc291cmNlLGM9bnVsbD09KGE9dC5jb25kaXRpb25zKXx8bnVsbD09KGE9YS51dG0pfHwhYS51dG1fbWVkaXVtfHwobnVsbD09KGw9dC5jb25kaXRpb25zKXx8bnVsbD09KGw9bC51dG0pP3ZvaWQgMDpsLnV0bV9tZWRpdW0pPT1lLnV0bV9tZWRpdW0sZj1udWxsPT0odT10LmNvbmRpdGlvbnMpfHxudWxsPT0odT11LnV0bSl8fCF1LnV0bV90ZXJtfHwobnVsbD09KGg9dC5jb25kaXRpb25zKXx8bnVsbD09KGg9aC51dG0pP3ZvaWQgMDpoLnV0bV90ZXJtKT09ZS51dG1fdGVybTtyZXR1cm4gZCYmYyYmZiYmdn1yZXR1cm4hMX1zdGF0aWMgUXIodCl7Zm9yKHZhciBpPWFyZ3VtZW50cy5sZW5ndGgsZT1uZXcgQXJyYXkoaT4xP2ktMTowKSxyPTE7cjxpO3IrKyllW3ItMV09YXJndW1lbnRzW3JdO2ouaW5mbyhcIltXZWJFeHBlcmltZW50c10gXCIrdCxlKX10cyh0LGksZSl7dGhpcy5faXNfYm90KCk/cW8uUXIoXCJSZWZ1c2luZyB0byByZW5kZXIgd2ViIGV4cGVyaW1lbnQgc2luY2UgdGhlIHZpZXdlciBpcyBhIGxpa2VseSBib3RcIik6XCJjb250cm9sXCIhPT1pP2UuZm9yRWFjaCgoZT0+e2lmKGUuc2VsZWN0b3Ipe3ZhciByO3FvLlFyKFwiYXBwbHlpbmcgdHJhbnNmb3JtIG9mIHZhcmlhbnQgXCIraStcIiBmb3IgZXhwZXJpbWVudCBcIit0K1wiIFwiLGUpO3ZhciBzPW51bGw9PShyPWRvY3VtZW50KT92b2lkIDA6ci5xdWVyeVNlbGVjdG9yQWxsKGUuc2VsZWN0b3IpO251bGw9PXN8fHMuZm9yRWFjaCgodD0+e3ZhciBpPXQ7ZS5odG1sJiYoaS5pbm5lckhUTUw9ZS5odG1sKSxlLmNzcyYmaS5zZXRBdHRyaWJ1dGUoXCJzdHlsZVwiLGUuY3NzKX0pKX19KSk6cW8uUXIoXCJDb250cm9sIHZhcmlhbnRzIGxlYXZlIHRoZSBwYWdlIHVubW9kaWZpZWQuXCIpfV9pc19ib3QoKXtyZXR1cm4gbiYmdGhpcy5faW5zdGFuY2U/TG8obix0aGlzLl9pbnN0YW5jZS5jb25maWcuY3VzdG9tX2Jsb2NrZWRfdXNlcmFnZW50cyk6dm9pZCAwfX12YXIgQm89e30sSG89KCk9Pnt9LFdvPVwicG9zdGhvZ1wiLEdvPSFDcyYmLTE9PT0obnVsbD09ZD92b2lkIDA6ZC5pbmRleE9mKFwiTVNJRVwiKSkmJi0xPT09KG51bGw9PWQ/dm9pZCAwOmQuaW5kZXhPZihcIk1vemlsbGFcIikpLEpvPSgpPT57dmFyIGk7cmV0dXJue2FwaV9ob3N0OlwiaHR0cHM6Ly91cy5pLnBvc3Rob2cuY29tXCIsdWlfaG9zdDpudWxsLHRva2VuOlwiXCIsYXV0b2NhcHR1cmU6ITAscmFnZWNsaWNrOiEwLGNyb3NzX3N1YmRvbWFpbl9jb29raWU6ZXQobnVsbD09bz92b2lkIDA6by5sb2NhdGlvbikscGVyc2lzdGVuY2U6XCJsb2NhbFN0b3JhZ2UrY29va2llXCIscGVyc2lzdGVuY2VfbmFtZTpcIlwiLGxvYWRlZDpIbyxzYXZlX2NhbXBhaWduX3BhcmFtczohMCxjdXN0b21fY2FtcGFpZ25fcGFyYW1zOltdLGN1c3RvbV9ibG9ja2VkX3VzZXJhZ2VudHM6W10sc2F2ZV9yZWZlcnJlcjohMCxjYXB0dXJlX3BhZ2V2aWV3OiEwLGNhcHR1cmVfcGFnZWxlYXZlOlwiaWZfY2FwdHVyZV9wYWdldmlld1wiLGRlYnVnOmEmJlQobnVsbD09YT92b2lkIDA6YS5zZWFyY2gpJiYtMSE9PWEuc2VhcmNoLmluZGV4T2YoXCJfX3Bvc3Rob2dfZGVidWc9dHJ1ZVwiKXx8ITEsY29va2llX2V4cGlyYXRpb246MzY1LHVwZ3JhZGU6ITEsZGlzYWJsZV9zZXNzaW9uX3JlY29yZGluZzohMSxkaXNhYmxlX3BlcnNpc3RlbmNlOiExLGRpc2FibGVfd2ViX2V4cGVyaW1lbnRzOiEwLGRpc2FibGVfc3VydmV5czohMSxkaXNhYmxlX2V4dGVybmFsX2RlcGVuZGVuY3lfbG9hZGluZzohMSxlbmFibGVfcmVjb3JkaW5nX2NvbnNvbGVfbG9nOnZvaWQgMCxzZWN1cmVfY29va2llOlwiaHR0cHM6XCI9PT0obnVsbD09dHx8bnVsbD09KGk9dC5sb2NhdGlvbik/dm9pZCAwOmkucHJvdG9jb2wpLGlwOiEwLG9wdF9vdXRfY2FwdHVyaW5nX2J5X2RlZmF1bHQ6ITEsb3B0X291dF9wZXJzaXN0ZW5jZV9ieV9kZWZhdWx0OiExLG9wdF9vdXRfdXNlcmFnZW50X2ZpbHRlcjohMSxvcHRfb3V0X2NhcHR1cmluZ19wZXJzaXN0ZW5jZV90eXBlOlwibG9jYWxTdG9yYWdlXCIsb3B0X291dF9jYXB0dXJpbmdfY29va2llX3ByZWZpeDpudWxsLG9wdF9pbl9zaXRlX2FwcHM6ITEscHJvcGVydHlfZGVueWxpc3Q6W10scmVzcGVjdF9kbnQ6ITEsc2FuaXRpemVfcHJvcGVydGllczpudWxsLHJlcXVlc3RfaGVhZGVyczp7fSxyZXF1ZXN0X2JhdGNoaW5nOiEwLHByb3BlcnRpZXNfc3RyaW5nX21heF9sZW5ndGg6NjU1MzUsc2Vzc2lvbl9yZWNvcmRpbmc6e30sbWFza19hbGxfZWxlbWVudF9hdHRyaWJ1dGVzOiExLG1hc2tfYWxsX3RleHQ6ITEsbWFza19wZXJzb25hbF9kYXRhX3Byb3BlcnRpZXM6ITEsY3VzdG9tX3BlcnNvbmFsX2RhdGFfcHJvcGVydGllczpbXSxhZHZhbmNlZF9kaXNhYmxlX2RlY2lkZTohMSxhZHZhbmNlZF9kaXNhYmxlX2ZlYXR1cmVfZmxhZ3M6ITEsYWR2YW5jZWRfZGlzYWJsZV9mZWF0dXJlX2ZsYWdzX29uX2ZpcnN0X2xvYWQ6ITEsYWR2YW5jZWRfZGlzYWJsZV90b29sYmFyX21ldHJpY3M6ITEsZmVhdHVyZV9mbGFnX3JlcXVlc3RfdGltZW91dF9tczozZTMsc3VydmV5c19yZXF1ZXN0X3RpbWVvdXRfbXM6MWU0LG9uX3JlcXVlc3RfZXJyb3I6dD0+e3ZhciBpPVwiQmFkIEhUVFAgc3RhdHVzOiBcIit0LnN0YXR1c0NvZGUrXCIgXCIrdC50ZXh0O2ouZXJyb3IoaSl9LGdldF9kZXZpY2VfaWQ6dD0+dCxjYXB0dXJlX3BlcmZvcm1hbmNlOnZvaWQgMCxuYW1lOlwicG9zdGhvZ1wiLGJvb3RzdHJhcDp7fSxkaXNhYmxlX2NvbXByZXNzaW9uOiExLHNlc3Npb25faWRsZV90aW1lb3V0X3NlY29uZHM6MTgwMCxwZXJzb25fcHJvZmlsZXM6XCJpZGVudGlmaWVkX29ubHlcIixiZWZvcmVfc2VuZDp2b2lkIDAscmVxdWVzdF9xdWV1ZV9jb25maWc6e2ZsdXNoX2ludGVydmFsX21zOiRvfSxfb25DYXB0dXJlOkhvfX0sVm89dD0+e3ZhciBpPXt9O1IodC5wcm9jZXNzX3BlcnNvbil8fChpLnBlcnNvbl9wcm9maWxlcz10LnByb2Nlc3NfcGVyc29uKSxSKHQueGhyX2hlYWRlcnMpfHwoaS5yZXF1ZXN0X2hlYWRlcnM9dC54aHJfaGVhZGVycyksUih0LmNvb2tpZV9uYW1lKXx8KGkucGVyc2lzdGVuY2VfbmFtZT10LmNvb2tpZV9uYW1lKSxSKHQuZGlzYWJsZV9jb29raWUpfHwoaS5kaXNhYmxlX3BlcnNpc3RlbmNlPXQuZGlzYWJsZV9jb29raWUpLFIodC5zdG9yZV9nb29nbGUpfHwoaS5zYXZlX2NhbXBhaWduX3BhcmFtcz10LnN0b3JlX2dvb2dsZSksUih0LnZlcmJvc2UpfHwoaS5kZWJ1Zz10LnZlcmJvc2UpO3ZhciBlPVYoe30saSx0KTtyZXR1cm4geCh0LnByb3BlcnR5X2JsYWNrbGlzdCkmJihSKHQucHJvcGVydHlfZGVueWxpc3QpP2UucHJvcGVydHlfZGVueWxpc3Q9dC5wcm9wZXJ0eV9ibGFja2xpc3Q6eCh0LnByb3BlcnR5X2RlbnlsaXN0KT9lLnByb3BlcnR5X2RlbnlsaXN0PVsuLi50LnByb3BlcnR5X2JsYWNrbGlzdCwuLi50LnByb3BlcnR5X2RlbnlsaXN0XTpqLmVycm9yKFwiSW52YWxpZCB2YWx1ZSBmb3IgcHJvcGVydHlfZGVueWxpc3QgY29uZmlnOiBcIit0LnByb3BlcnR5X2RlbnlsaXN0KSksZX07Y2xhc3MgS297Y29uc3RydWN0b3IoKXt0aGlzLl9fZm9yY2VBbGxvd0xvY2FsaG9zdD0hMX1nZXQgb3MoKXtyZXR1cm4gdGhpcy5fX2ZvcmNlQWxsb3dMb2NhbGhvc3R9c2V0IG9zKHQpe2ouZXJyb3IoXCJXZWJQZXJmb3JtYW5jZU9ic2VydmVyIGlzIGRlcHJlY2F0ZWQgYW5kIGhhcyBubyBpbXBhY3Qgb24gbmV0d29yayBjYXB0dXJlLiBVc2UgYF9mb3JjZUFsbG93TG9jYWxob3N0TmV0d29ya0NhcHR1cmVgIG9uIGBwb3N0aG9nLnNlc3Npb25SZWNvcmRpbmdgXCIpLHRoaXMuX19mb3JjZUFsbG93TG9jYWxob3N0PXR9fWNsYXNzIFlve2dldCBkZWNpZGVFbmRwb2ludFdhc0hpdCgpe3ZhciB0LGk7cmV0dXJuIG51bGwhPT0odD1udWxsPT0oaT10aGlzLmZlYXR1cmVGbGFncyk/dm9pZCAwOmkuaGFzTG9hZGVkRmxhZ3MpJiZ2b2lkIDAhPT10JiZ0fWNvbnN0cnVjdG9yKCl7dGhpcy53ZWJQZXJmb3JtYW5jZT1uZXcgS28sdGhpcy5scz0hMSx0aGlzLnZlcnNpb249Yy5MSUJfVkVSU0lPTix0aGlzLnVzPW5ldyBmbyx0aGlzLl9jYWxjdWxhdGVfZXZlbnRfcHJvcGVydGllcz10aGlzLmNhbGN1bGF0ZUV2ZW50UHJvcGVydGllcy5iaW5kKHRoaXMpLHRoaXMuY29uZmlnPUpvKCksdGhpcy5TZW50cnlJbnRlZ3JhdGlvbj1ncyx0aGlzLnNlbnRyeUludGVncmF0aW9uPXQ9PmZ1bmN0aW9uKHQsaSl7dmFyIGU9cHModCxpKTtyZXR1cm57bmFtZTpmcyxwcm9jZXNzRXZlbnQ6dD0+ZSh0KX19KHRoaXMsdCksdGhpcy5fX3JlcXVlc3RfcXVldWU9W10sdGhpcy5fX2xvYWRlZD0hMSx0aGlzLmFuYWx5dGljc0RlZmF1bHRFbmRwb2ludD1cIi9lL1wiLHRoaXMuaHM9ITEsdGhpcy5kcz1udWxsLHRoaXMudnM9bnVsbCx0aGlzLmNzPW51bGwsdGhpcy5mZWF0dXJlRmxhZ3M9bmV3IGhvKHRoaXMpLHRoaXMudG9vbGJhcj1uZXcgeXModGhpcyksdGhpcy5zY3JvbGxNYW5hZ2VyPW5ldyBJbyh0aGlzKSx0aGlzLnBhZ2VWaWV3TWFuYWdlcj1uZXcgVHModGhpcyksdGhpcy5zdXJ2ZXlzPW5ldyBtbyh0aGlzKSx0aGlzLmV4cGVyaW1lbnRzPW5ldyBxbyh0aGlzKSx0aGlzLmV4Y2VwdGlvbnM9bmV3IEJzKHRoaXMpLHRoaXMucmF0ZUxpbWl0ZXI9bmV3IHdvKHRoaXMpLHRoaXMucmVxdWVzdFJvdXRlcj1uZXcgem8odGhpcyksdGhpcy5jb25zZW50PW5ldyBaaSh0aGlzKSx0aGlzLnBlb3BsZT17c2V0Oih0LGksZSk9Pnt2YXIgcj1UKHQpP3tbdF06aX06dDt0aGlzLnNldFBlcnNvblByb3BlcnRpZXMociksbnVsbD09ZXx8ZSh7fSl9LHNldF9vbmNlOih0LGksZSk9Pnt2YXIgcj1UKHQpP3tbdF06aX06dDt0aGlzLnNldFBlcnNvblByb3BlcnRpZXModm9pZCAwLHIpLG51bGw9PWV8fGUoe30pfX0sdGhpcy5vbihcImV2ZW50Q2FwdHVyZWRcIiwodD0+ai5pbmZvKCdzZW5kIFwiJysobnVsbD09dD92b2lkIDA6dC5ldmVudCkrJ1wiJyx0KSkpfWluaXQodCxpLGUpe2lmKGUmJmUhPT1Xbyl7dmFyIHIscz1udWxsIT09KHI9Qm9bZV0pJiZ2b2lkIDAhPT1yP3I6bmV3IFlvO3JldHVybiBzLl9pbml0KHQsaSxlKSxCb1tlXT1zLEJvW1dvXVtlXT1zLHN9cmV0dXJuIHRoaXMuX2luaXQodCxpLGUpfV9pbml0KGksZSxyKXt2YXIgcyxuO2lmKHZvaWQgMD09PWUmJihlPXt9KSxSKGkpfHxNKGkpKXJldHVybiBqLmNyaXRpY2FsKFwiUG9zdEhvZyB3YXMgaW5pdGlhbGl6ZWQgd2l0aG91dCBhIHRva2VuLiBUaGlzIGxpa2VseSBpbmRpY2F0ZXMgYSBtaXNjb25maWd1cmF0aW9uLiBQbGVhc2UgY2hlY2sgdGhlIGZpcnN0IGFyZ3VtZW50IHBhc3NlZCB0byBwb3N0aG9nLmluaXQoKVwiKSx0aGlzO2lmKHRoaXMuX19sb2FkZWQpcmV0dXJuIGoud2FybihcIllvdSBoYXZlIGFscmVhZHkgaW5pdGlhbGl6ZWQgUG9zdEhvZyEgUmUtaW5pdGlhbGl6aW5nIGlzIGEgbm8tb3BcIiksdGhpczt0aGlzLl9fbG9hZGVkPSEwLHRoaXMuY29uZmlnPXt9LHRoaXMuZnM9W10sZS5wZXJzb25fcHJvZmlsZXMmJih0aGlzLnZzPWUucGVyc29uX3Byb2ZpbGVzKSx0aGlzLnNldF9jb25maWcoVih7fSxKbygpLFZvKGUpLHtuYW1lOnIsdG9rZW46aX0pKSx0aGlzLmNvbmZpZy5vbl94aHJfZXJyb3ImJmouZXJyb3IoXCJvbl94aHJfZXJyb3IgaXMgZGVwcmVjYXRlZC4gVXNlIG9uX3JlcXVlc3RfZXJyb3IgaW5zdGVhZFwiKSx0aGlzLmNvbXByZXNzaW9uPWUuZGlzYWJsZV9jb21wcmVzc2lvbj92b2lkIDA6Zy5HWmlwSlMsdGhpcy5wZXJzaXN0ZW5jZT1uZXcgY28odGhpcy5jb25maWcpLHRoaXMuc2Vzc2lvblBlcnNpc3RlbmNlPVwic2Vzc2lvblN0b3JhZ2VcIj09PXRoaXMuY29uZmlnLnBlcnNpc3RlbmNlfHxcIm1lbW9yeVwiPT09dGhpcy5jb25maWcucGVyc2lzdGVuY2U/dGhpcy5wZXJzaXN0ZW5jZTpuZXcgY28oQih7fSx0aGlzLmNvbmZpZyx7cGVyc2lzdGVuY2U6XCJzZXNzaW9uU3RvcmFnZVwifSkpO3ZhciBvPUIoe30sdGhpcy5wZXJzaXN0ZW5jZS5wcm9wcyksYT1CKHt9LHRoaXMuc2Vzc2lvblBlcnNpc3RlbmNlLnByb3BzKTtpZih0aGlzLnBzPW5ldyBrbygodD0+dGhpcy5ncyh0KSksdGhpcy5jb25maWcucmVxdWVzdF9xdWV1ZV9jb25maWcpLHRoaXMuX3M9bmV3IEVvKHRoaXMpLHRoaXMuX19yZXF1ZXN0X3F1ZXVlPVtdLHRoaXMuY29uZmlnLl9fcHJldmlld19leHBlcmltZW50YWxfY29va2llbGVzc19tb2RlfHwodGhpcy5zZXNzaW9uTWFuYWdlcj1uZXcgTW8odGhpcyksdGhpcy5zZXNzaW9uUHJvcHNNYW5hZ2VyPW5ldyBSbyh0aGlzLHRoaXMuc2Vzc2lvbk1hbmFnZXIsdGhpcy5wZXJzaXN0ZW5jZSkpLG5ldyAkcyh0aGlzKS5zdGFydElmRW5hYmxlZE9yU3RvcCgpLHRoaXMuc2l0ZUFwcHM9bmV3IEZvKHRoaXMpLG51bGw9PShzPXRoaXMuc2l0ZUFwcHMpfHxzLmluaXQoKSx0aGlzLmNvbmZpZy5fX3ByZXZpZXdfZXhwZXJpbWVudGFsX2Nvb2tpZWxlc3NfbW9kZXx8KHRoaXMuc2Vzc2lvblJlY29yZGluZz1uZXcgZHModGhpcyksdGhpcy5zZXNzaW9uUmVjb3JkaW5nLnN0YXJ0SWZFbmFibGVkT3JTdG9wKCkpLHRoaXMuY29uZmlnLmRpc2FibGVfc2Nyb2xsX3Byb3BlcnRpZXN8fHRoaXMuc2Nyb2xsTWFuYWdlci5zdGFydE1lYXN1cmluZ1Njcm9sbFBvc2l0aW9uKCksdGhpcy5hdXRvY2FwdHVyZT1uZXcgTWkodGhpcyksdGhpcy5hdXRvY2FwdHVyZS5zdGFydElmRW5hYmxlZCgpLHRoaXMuc3VydmV5cy5sb2FkSWZFbmFibGVkKCksdGhpcy5oZWF0bWFwcz1uZXcgUnModGhpcyksdGhpcy5oZWF0bWFwcy5zdGFydElmRW5hYmxlZCgpLHRoaXMud2ViVml0YWxzQXV0b2NhcHR1cmU9bmV3IEVzKHRoaXMpLHRoaXMuZXhjZXB0aW9uT2JzZXJ2ZXI9bmV3IG5lKHRoaXMpLHRoaXMuZXhjZXB0aW9uT2JzZXJ2ZXIuc3RhcnRJZkVuYWJsZWQoKSx0aGlzLmRlYWRDbGlja3NBdXRvY2FwdHVyZT1uZXcgcmUodGhpcyxlZSksdGhpcy5kZWFkQ2xpY2tzQXV0b2NhcHR1cmUuc3RhcnRJZkVuYWJsZWQoKSx0aGlzLmhpc3RvcnlBdXRvY2FwdHVyZT1uZXcgTGUodGhpcyksdGhpcy5oaXN0b3J5QXV0b2NhcHR1cmUuc3RhcnRJZkVuYWJsZWQoKSxjLkRFQlVHPWMuREVCVUd8fHRoaXMuY29uZmlnLmRlYnVnLGMuREVCVUcmJmouaW5mbyhcIlN0YXJ0aW5nIGluIGRlYnVnIG1vZGVcIix7dGhpczp0aGlzLGNvbmZpZzplLHRoaXNDOkIoe30sdGhpcy5jb25maWcpLHA6byxzOmF9KSx0aGlzLmJzKCksdm9pZCAwIT09KG51bGw9PShuPWUuYm9vdHN0cmFwKT92b2lkIDA6bi5kaXN0aW5jdElEKSl7dmFyIGwsdSxoPXRoaXMuY29uZmlnLmdldF9kZXZpY2VfaWQoTmkoKSksZD1udWxsIT0obD1lLmJvb3RzdHJhcCkmJmwuaXNJZGVudGlmaWVkSUQ/aDplLmJvb3RzdHJhcC5kaXN0aW5jdElEO3RoaXMucGVyc2lzdGVuY2Uuc2V0X3Byb3BlcnR5KEF0LG51bGwhPSh1PWUuYm9vdHN0cmFwKSYmdS5pc0lkZW50aWZpZWRJRD9cImlkZW50aWZpZWRcIjpcImFub255bW91c1wiKSx0aGlzLnJlZ2lzdGVyKHtkaXN0aW5jdF9pZDplLmJvb3RzdHJhcC5kaXN0aW5jdElELCRkZXZpY2VfaWQ6ZH0pfWlmKHRoaXMud3MoKSl7dmFyIHYsZixwPU9iamVjdC5rZXlzKChudWxsPT0odj1lLmJvb3RzdHJhcCk/dm9pZCAwOnYuZmVhdHVyZUZsYWdzKXx8e30pLmZpbHRlcigodD0+e3ZhciBpO3JldHVybiEobnVsbD09KGk9ZS5ib290c3RyYXApfHxudWxsPT0oaT1pLmZlYXR1cmVGbGFncyl8fCFpW3RdKX0pKS5yZWR1Y2UoKCh0LGkpPT57dmFyIHI7cmV0dXJuIHRbaV09KG51bGw9PShyPWUuYm9vdHN0cmFwKXx8bnVsbD09KHI9ci5mZWF0dXJlRmxhZ3MpP3ZvaWQgMDpyW2ldKXx8ITEsdH0pLHt9KSxfPU9iamVjdC5rZXlzKChudWxsPT0oZj1lLmJvb3RzdHJhcCk/dm9pZCAwOmYuZmVhdHVyZUZsYWdQYXlsb2Fkcyl8fHt9KS5maWx0ZXIoKHQ9PnBbdF0pKS5yZWR1Y2UoKCh0LGkpPT57dmFyIHIscztudWxsIT0ocj1lLmJvb3RzdHJhcCkmJm51bGwhPShyPXIuZmVhdHVyZUZsYWdQYXlsb2FkcykmJnJbaV0mJih0W2ldPW51bGw9PShzPWUuYm9vdHN0cmFwKXx8bnVsbD09KHM9cy5mZWF0dXJlRmxhZ1BheWxvYWRzKT92b2lkIDA6c1tpXSk7cmV0dXJuIHR9KSx7fSk7dGhpcy5mZWF0dXJlRmxhZ3MucmVjZWl2ZWRGZWF0dXJlRmxhZ3Moe2ZlYXR1cmVGbGFnczpwLGZlYXR1cmVGbGFnUGF5bG9hZHM6X30pfWlmKHRoaXMuY29uZmlnLl9fcHJldmlld19leHBlcmltZW50YWxfY29va2llbGVzc19tb2RlKXRoaXMucmVnaXN0ZXJfb25jZSh7ZGlzdGluY3RfaWQ6QnQsJGRldmljZV9pZDpudWxsfSxcIlwiKTtlbHNlIGlmKCF0aGlzLmdldF9kaXN0aW5jdF9pZCgpKXt2YXIgbT10aGlzLmNvbmZpZy5nZXRfZGV2aWNlX2lkKE5pKCkpO3RoaXMucmVnaXN0ZXJfb25jZSh7ZGlzdGluY3RfaWQ6bSwkZGV2aWNlX2lkOm19LFwiXCIpLHRoaXMucGVyc2lzdGVuY2Uuc2V0X3Byb3BlcnR5KEF0LFwiYW5vbnltb3VzXCIpfXJldHVybiBzdCh0LFwib25wYWdlaGlkZVwiaW4gc2VsZj9cInBhZ2VoaWRlXCI6XCJ1bmxvYWRcIix0aGlzLl9oYW5kbGVfdW5sb2FkLmJpbmQodGhpcykse3Bhc3NpdmU6ITF9KSx0aGlzLnRvb2xiYXIubWF5YmVMb2FkVG9vbGJhcigpLGUuc2VnbWVudD9jcyh0aGlzLCgoKT0+dGhpcy55cygpKSk6dGhpcy55cygpLEUodGhpcy5jb25maWcuX29uQ2FwdHVyZSkmJnRoaXMuY29uZmlnLl9vbkNhcHR1cmUhPT1IbyYmKGoud2FybihcIm9uQ2FwdHVyZSBpcyBkZXByZWNhdGVkLiBQbGVhc2UgdXNlIGBiZWZvcmVfc2VuZGAgaW5zdGVhZFwiKSx0aGlzLm9uKFwiZXZlbnRDYXB0dXJlZFwiLCh0PT50aGlzLmNvbmZpZy5fb25DYXB0dXJlKHQuZXZlbnQsdCkpKSksdGhpc31iZSh0KXt2YXIgaSxlLHIscyxuLGEsbCx1O2lmKCFvfHwhby5ib2R5KXJldHVybiBqLmluZm8oXCJkb2N1bWVudCBub3QgcmVhZHkgeWV0LCB0cnlpbmcgYWdhaW4gaW4gNTAwIG1pbGxpc2Vjb25kcy4uLlwiKSx2b2lkIHNldFRpbWVvdXQoKCgpPT57dGhpcy5iZSh0KX0pLDUwMCk7dGhpcy5jb21wcmVzc2lvbj12b2lkIDAsdC5zdXBwb3J0ZWRDb21wcmVzc2lvbiYmIXRoaXMuY29uZmlnLmRpc2FibGVfY29tcHJlc3Npb24mJih0aGlzLmNvbXByZXNzaW9uPW0odC5zdXBwb3J0ZWRDb21wcmVzc2lvbixnLkdaaXBKUyk/Zy5HWmlwSlM6bSh0LnN1cHBvcnRlZENvbXByZXNzaW9uLGcuQmFzZTY0KT9nLkJhc2U2NDp2b2lkIDApLG51bGwhPShpPXQuYW5hbHl0aWNzKSYmaS5lbmRwb2ludCYmKHRoaXMuYW5hbHl0aWNzRGVmYXVsdEVuZHBvaW50PXQuYW5hbHl0aWNzLmVuZHBvaW50KSx0aGlzLnNldF9jb25maWcoe3BlcnNvbl9wcm9maWxlczp0aGlzLnZzP3RoaXMudnM6XCJpZGVudGlmaWVkX29ubHlcIn0pLG51bGw9PShlPXRoaXMuc2l0ZUFwcHMpfHxlLm9uUmVtb3RlQ29uZmlnKHQpLG51bGw9PShyPXRoaXMuc2Vzc2lvblJlY29yZGluZyl8fHIub25SZW1vdGVDb25maWcodCksbnVsbD09KHM9dGhpcy5hdXRvY2FwdHVyZSl8fHMub25SZW1vdGVDb25maWcodCksbnVsbD09KG49dGhpcy5oZWF0bWFwcyl8fG4ub25SZW1vdGVDb25maWcodCksdGhpcy5zdXJ2ZXlzLm9uUmVtb3RlQ29uZmlnKHQpLG51bGw9PShhPXRoaXMud2ViVml0YWxzQXV0b2NhcHR1cmUpfHxhLm9uUmVtb3RlQ29uZmlnKHQpLG51bGw9PShsPXRoaXMuZXhjZXB0aW9uT2JzZXJ2ZXIpfHxsLm9uUmVtb3RlQ29uZmlnKHQpLHRoaXMuZXhjZXB0aW9ucy5vblJlbW90ZUNvbmZpZyh0KSxudWxsPT0odT10aGlzLmRlYWRDbGlja3NBdXRvY2FwdHVyZSl8fHUub25SZW1vdGVDb25maWcodCl9eXMoKXt0cnl7dGhpcy5jb25maWcubG9hZGVkKHRoaXMpfWNhdGNoKHQpe2ouY3JpdGljYWwoXCJgbG9hZGVkYCBmdW5jdGlvbiBmYWlsZWRcIix0KX10aGlzLlNzKCksdGhpcy5jb25maWcuY2FwdHVyZV9wYWdldmlldyYmc2V0VGltZW91dCgoKCk9Pnt0aGlzLmNvbnNlbnQuaXNPcHRlZEluKCkmJnRoaXMuJHMoKX0pLDEpLG5ldyBTbyh0aGlzKS5sb2FkKCksdGhpcy5mZWF0dXJlRmxhZ3MuZGVjaWRlKCl9U3MoKXt2YXIgdDt0aGlzLmhhc19vcHRlZF9vdXRfY2FwdHVyaW5nKCl8fHRoaXMuY29uZmlnLnJlcXVlc3RfYmF0Y2hpbmcmJihudWxsPT0odD10aGlzLnBzKXx8dC5lbmFibGUoKSl9X2RvbV9sb2FkZWQoKXt0aGlzLmhhc19vcHRlZF9vdXRfY2FwdHVyaW5nKCl8fEcodGhpcy5fX3JlcXVlc3RfcXVldWUsKHQ9PnRoaXMuZ3ModCkpKSx0aGlzLl9fcmVxdWVzdF9xdWV1ZT1bXSx0aGlzLlNzKCl9X2hhbmRsZV91bmxvYWQoKXt2YXIgdCxpO3RoaXMuY29uZmlnLnJlcXVlc3RfYmF0Y2hpbmc/KHRoaXMua3MoKSYmdGhpcy5jYXB0dXJlKFwiJHBhZ2VsZWF2ZVwiKSxudWxsPT0odD10aGlzLnBzKXx8dC51bmxvYWQoKSxudWxsPT0oaT10aGlzLl9zKXx8aS51bmxvYWQoKSk6dGhpcy5rcygpJiZ0aGlzLmNhcHR1cmUoXCIkcGFnZWxlYXZlXCIsbnVsbCx7dHJhbnNwb3J0Olwic2VuZEJlYWNvblwifSl9bWUodCl7dGhpcy5fX2xvYWRlZCYmKEdvP3RoaXMuX19yZXF1ZXN0X3F1ZXVlLnB1c2godCk6dGhpcy5yYXRlTGltaXRlci5pc1NlcnZlclJhdGVMaW1pdGVkKHQuYmF0Y2hLZXkpfHwodC50cmFuc3BvcnQ9dC50cmFuc3BvcnR8fHRoaXMuY29uZmlnLmFwaV90cmFuc3BvcnQsdC51cmw9RnModC51cmwse2lwOnRoaXMuY29uZmlnLmlwPzE6MH0pLHQuaGVhZGVycz1CKHt9LHRoaXMuY29uZmlnLnJlcXVlc3RfaGVhZGVycyksdC5jb21wcmVzc2lvbj1cImJlc3QtYXZhaWxhYmxlXCI9PT10LmNvbXByZXNzaW9uP3RoaXMuY29tcHJlc3Npb246dC5jb21wcmVzc2lvbix0LmZldGNoT3B0aW9ucz10LmZldGNoT3B0aW9uc3x8dGhpcy5jb25maWcuZmV0Y2hfb3B0aW9ucywodD0+e3ZhciBpLGUscixzPUIoe30sdCk7cy50aW1lb3V0PXMudGltZW91dHx8NmU0LHMudXJsPUZzKHMudXJsLHtfOihuZXcgRGF0ZSkuZ2V0VGltZSgpLnRvU3RyaW5nKCksdmVyOmMuTElCX1ZFUlNJT04sY29tcHJlc3Npb246cy5jb21wcmVzc2lvbn0pO3ZhciBuPW51bGwhPT0oaT1zLnRyYW5zcG9ydCkmJnZvaWQgMCE9PWk/aTpcImZldGNoXCIsbz1udWxsIT09KGU9bnVsbD09KHI9cnQoTHMsKHQ9PnQudHJhbnNwb3J0PT09bikpKT92b2lkIDA6ci5tZXRob2QpJiZ2b2lkIDAhPT1lP2U6THNbMF0ubWV0aG9kO2lmKCFvKXRocm93IG5ldyBFcnJvcihcIk5vIGF2YWlsYWJsZSB0cmFuc3BvcnQgbWV0aG9kXCIpO28ocyl9KShCKHt9LHQse2NhbGxiYWNrOmk9Pnt2YXIgZSxyOyh0aGlzLnJhdGVMaW1pdGVyLmNoZWNrRm9yTGltaXRpbmcoaSksaS5zdGF0dXNDb2RlPj00MDApJiYobnVsbD09KGU9KHI9dGhpcy5jb25maWcpLm9uX3JlcXVlc3RfZXJyb3IpfHxlLmNhbGwocixpKSk7bnVsbD09dC5jYWxsYmFja3x8dC5jYWxsYmFjayhpKX19KSkpKX1ncyh0KXt0aGlzLl9zP3RoaXMuX3MucmV0cmlhYmxlUmVxdWVzdCh0KTp0aGlzLm1lKHQpfV9leGVjdXRlX2FycmF5KHQpe3ZhciBpLGU9W10scj1bXSxzPVtdO0codCwodD0+e3QmJihpPXRbMF0seChpKT9zLnB1c2godCk6RSh0KT90LmNhbGwodGhpcyk6eCh0KSYmXCJhbGlhc1wiPT09aT9lLnB1c2godCk6eCh0KSYmLTEhPT1pLmluZGV4T2YoXCJjYXB0dXJlXCIpJiZFKHRoaXNbaV0pP3MucHVzaCh0KTpyLnB1c2godCkpfSkpO3ZhciBuPWZ1bmN0aW9uKHQsaSl7Ryh0LChmdW5jdGlvbih0KXtpZih4KHRbMF0pKXt2YXIgZT1pO0oodCwoZnVuY3Rpb24odCl7ZT1lW3RbMF1dLmFwcGx5KGUsdC5zbGljZSgxKSl9KSl9ZWxzZSB0aGlzW3RbMF1dLmFwcGx5KHRoaXMsdC5zbGljZSgxKSl9KSxpKX07bihlLHRoaXMpLG4ocix0aGlzKSxuKHMsdGhpcyl9d3MoKXt2YXIgdCxpO3JldHVybihudWxsPT0odD10aGlzLmNvbmZpZy5ib290c3RyYXApP3ZvaWQgMDp0LmZlYXR1cmVGbGFncykmJk9iamVjdC5rZXlzKG51bGw9PShpPXRoaXMuY29uZmlnLmJvb3RzdHJhcCk/dm9pZCAwOmkuZmVhdHVyZUZsYWdzKS5sZW5ndGg+MHx8ITF9cHVzaCh0KXt0aGlzLl9leGVjdXRlX2FycmF5KFt0XSl9Y2FwdHVyZSh0LGksZSl7dmFyIHI7aWYodGhpcy5fX2xvYWRlZCYmdGhpcy5wZXJzaXN0ZW5jZSYmdGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UmJnRoaXMucHMpe2lmKCF0aGlzLmNvbnNlbnQuaXNPcHRlZE91dCgpKWlmKCFSKHQpJiZUKHQpKXtpZih0aGlzLmNvbmZpZy5vcHRfb3V0X3VzZXJhZ2VudF9maWx0ZXJ8fCF0aGlzLl9pc19ib3QoKSl7dmFyIHM9bnVsbCE9ZSYmZS5za2lwX2NsaWVudF9yYXRlX2xpbWl0aW5nP3ZvaWQgMDp0aGlzLnJhdGVMaW1pdGVyLmNsaWVudFJhdGVMaW1pdENvbnRleHQoKTtpZihudWxsPT1zfHwhcy5pc1JhdGVMaW1pdGVkKXtudWxsIT1pJiZpLiRjdXJyZW50X3VybCYmIVQobnVsbD09aT92b2lkIDA6aS4kY3VycmVudF91cmwpJiYoai5lcnJvcihcIkludmFsaWQgYCRjdXJyZW50X3VybGAgcHJvcGVydHkgcHJvdmlkZWQgdG8gYHBvc3Rob2cuY2FwdHVyZWAuIElucHV0IG11c3QgYmUgYSBzdHJpbmcuIElnbm9yaW5nIHByb3ZpZGVkIHZhbHVlLlwiKSxudWxsPT1pfHxkZWxldGUgaS4kY3VycmVudF91cmwpLHRoaXMuc2Vzc2lvblBlcnNpc3RlbmNlLnVwZGF0ZV9zZWFyY2hfa2V5d29yZCgpLHRoaXMuY29uZmlnLnNhdmVfY2FtcGFpZ25fcGFyYW1zJiZ0aGlzLnNlc3Npb25QZXJzaXN0ZW5jZS51cGRhdGVfY2FtcGFpZ25fcGFyYW1zKCksdGhpcy5jb25maWcuc2F2ZV9yZWZlcnJlciYmdGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UudXBkYXRlX3JlZmVycmVyX2luZm8oKSwodGhpcy5jb25maWcuc2F2ZV9jYW1wYWlnbl9wYXJhbXN8fHRoaXMuY29uZmlnLnNhdmVfcmVmZXJyZXIpJiZ0aGlzLnBlcnNpc3RlbmNlLnNldF9pbml0aWFsX3BlcnNvbl9pbmZvKCk7dmFyIG49bmV3IERhdGUsbz0obnVsbD09ZT92b2lkIDA6ZS50aW1lc3RhbXApfHxuLGE9TmkoKSxsPXt1dWlkOmEsZXZlbnQ6dCxwcm9wZXJ0aWVzOnRoaXMuY2FsY3VsYXRlRXZlbnRQcm9wZXJ0aWVzKHQsaXx8e30sbyxhKX07cyYmKGwucHJvcGVydGllcy4kbGliX3JhdGVfbGltaXRfcmVtYWluaW5nX3Rva2Vucz1zLnJlbWFpbmluZ1Rva2VucyksKG51bGw9PWU/dm9pZCAwOmUuJHNldCkmJihsLiRzZXQ9bnVsbD09ZT92b2lkIDA6ZS4kc2V0KTt2YXIgdT10aGlzLnhzKG51bGw9PWU/dm9pZCAwOmUuJHNldF9vbmNlKTt1JiYobC4kc2V0X29uY2U9dSksKGw9dHQobCxudWxsIT1lJiZlLl9ub1RydW5jYXRlP251bGw6dGhpcy5jb25maWcucHJvcGVydGllc19zdHJpbmdfbWF4X2xlbmd0aCkpLnRpbWVzdGFtcD1vLFIobnVsbD09ZT92b2lkIDA6ZS50aW1lc3RhbXApfHwobC5wcm9wZXJ0aWVzLiRldmVudF90aW1lX292ZXJyaWRlX3Byb3ZpZGVkPSEwLGwucHJvcGVydGllcy4kZXZlbnRfdGltZV9vdmVycmlkZV9zeXN0ZW1fdGltZT1uKTt2YXIgaD1CKHt9LGwucHJvcGVydGllcy4kc2V0LGwuJHNldCk7aWYoUChoKXx8dGhpcy5zZXRQZXJzb25Qcm9wZXJ0aWVzRm9yRmxhZ3MoaCksIU8odGhpcy5jb25maWcuYmVmb3JlX3NlbmQpKXt2YXIgZD10aGlzLkVzKGwpO2lmKCFkKXJldHVybjtsPWR9dGhpcy51cy5lbWl0KFwiZXZlbnRDYXB0dXJlZFwiLGwpO3ZhciB2PXttZXRob2Q6XCJQT1NUXCIsdXJsOm51bGwhPT0ocj1udWxsPT1lP3ZvaWQgMDplLl91cmwpJiZ2b2lkIDAhPT1yP3I6dGhpcy5yZXF1ZXN0Um91dGVyLmVuZHBvaW50Rm9yKFwiYXBpXCIsdGhpcy5hbmFseXRpY3NEZWZhdWx0RW5kcG9pbnQpLGRhdGE6bCxjb21wcmVzc2lvbjpcImJlc3QtYXZhaWxhYmxlXCIsYmF0Y2hLZXk6bnVsbD09ZT92b2lkIDA6ZS5fYmF0Y2hLZXl9O3JldHVybiF0aGlzLmNvbmZpZy5yZXF1ZXN0X2JhdGNoaW5nfHxlJiYobnVsbD09ZXx8IWUuX2JhdGNoS2V5KXx8bnVsbCE9ZSYmZS5zZW5kX2luc3RhbnRseT90aGlzLmdzKHYpOnRoaXMucHMuZW5xdWV1ZSh2KSxsfWouY3JpdGljYWwoXCJUaGlzIGNhcHR1cmUgY2FsbCBpcyBpZ25vcmVkIGR1ZSB0byBjbGllbnQgcmF0ZSBsaW1pdGluZy5cIil9fWVsc2Ugai5lcnJvcihcIk5vIGV2ZW50IG5hbWUgcHJvdmlkZWQgdG8gcG9zdGhvZy5jYXB0dXJlXCIpfWVsc2Ugai51bmluaXRpYWxpemVkV2FybmluZyhcInBvc3Rob2cuY2FwdHVyZVwiKX1OZSh0KXtyZXR1cm4gdGhpcy5vbihcImV2ZW50Q2FwdHVyZWRcIiwoaT0+dChpLmV2ZW50LGkpKSl9Y2FsY3VsYXRlRXZlbnRQcm9wZXJ0aWVzKHQsaSxlLHIscyl7aWYoZT1lfHxuZXcgRGF0ZSwhdGhpcy5wZXJzaXN0ZW5jZXx8IXRoaXMuc2Vzc2lvblBlcnNpc3RlbmNlKXJldHVybiBpO3ZhciBuPXM/dm9pZCAwOnRoaXMucGVyc2lzdGVuY2UucmVtb3ZlX2V2ZW50X3RpbWVyKHQpLGE9Qih7fSxpKTtpZihhLnRva2VuPXRoaXMuY29uZmlnLnRva2VuLHRoaXMuY29uZmlnLl9fcHJldmlld19leHBlcmltZW50YWxfY29va2llbGVzc19tb2RlJiYoYS4kY29va2llbGVzc19tb2RlPSEwKSxcIiRzbmFwc2hvdFwiPT09dCl7dmFyIGw9Qih7fSx0aGlzLnBlcnNpc3RlbmNlLnByb3BlcnRpZXMoKSx0aGlzLnNlc3Npb25QZXJzaXN0ZW5jZS5wcm9wZXJ0aWVzKCkpO3JldHVybiBhLmRpc3RpbmN0X2lkPWwuZGlzdGluY3RfaWQsKCFUKGEuZGlzdGluY3RfaWQpJiYhRihhLmRpc3RpbmN0X2lkKXx8TShhLmRpc3RpbmN0X2lkKSkmJmouZXJyb3IoXCJJbnZhbGlkIGRpc3RpbmN0X2lkIGZvciByZXBsYXkgZXZlbnQuIFRoaXMgaW5kaWNhdGVzIGEgYnVnIGluIHlvdXIgaW1wbGVtZW50YXRpb25cIiksYX12YXIgdSxoPXRvKHRoaXMuY29uZmlnLm1hc2tfcGVyc29uYWxfZGF0YV9wcm9wZXJ0aWVzLHRoaXMuY29uZmlnLmN1c3RvbV9wZXJzb25hbF9kYXRhX3Byb3BlcnRpZXMpO2lmKHRoaXMuc2Vzc2lvbk1hbmFnZXIpe3ZhcntzZXNzaW9uSWQ6dix3aW5kb3dJZDpjfT10aGlzLnNlc3Npb25NYW5hZ2VyLmNoZWNrQW5kR2V0U2Vzc2lvbkFuZFdpbmRvd0lkKHMsZS5nZXRUaW1lKCkpO2EuJHNlc3Npb25faWQ9dixhLiR3aW5kb3dfaWQ9Y310aGlzLnNlc3Npb25Qcm9wc01hbmFnZXImJlYoYSx0aGlzLnNlc3Npb25Qcm9wc01hbmFnZXIuZ2V0U2Vzc2lvblByb3BzKCkpO3RyeXt2YXIgZjt0aGlzLnNlc3Npb25SZWNvcmRpbmcmJlYoYSx0aGlzLnNlc3Npb25SZWNvcmRpbmcuc2RrRGVidWdQcm9wZXJ0aWVzKSxhLiRzZGtfZGVidWdfcmV0cnlfcXVldWVfc2l6ZT1udWxsPT0oZj10aGlzLl9zKT92b2lkIDA6Zi5sZW5ndGh9Y2F0Y2godCl7YS4kc2RrX2RlYnVnX2Vycm9yX2NhcHR1cmluZ19wcm9wZXJ0aWVzPVN0cmluZyh0KX1pZih0aGlzLnJlcXVlc3RSb3V0ZXIucmVnaW9uPT09Tm8uQ1VTVE9NJiYoYS4kbGliX2N1c3RvbV9hcGlfaG9zdD10aGlzLmNvbmZpZy5hcGlfaG9zdCksdT1cIiRwYWdldmlld1wiIT09dHx8cz9cIiRwYWdlbGVhdmVcIiE9PXR8fHM/dGhpcy5wYWdlVmlld01hbmFnZXIuZG9FdmVudCgpOnRoaXMucGFnZVZpZXdNYW5hZ2VyLmRvUGFnZUxlYXZlKGUpOnRoaXMucGFnZVZpZXdNYW5hZ2VyLmRvUGFnZVZpZXcoZSxyKSxhPVYoYSx1KSxcIiRwYWdldmlld1wiPT09dCYmbyYmKGEudGl0bGU9by50aXRsZSksIVIobikpe3ZhciBwPWUuZ2V0VGltZSgpLW47YS4kZHVyYXRpb249cGFyc2VGbG9hdCgocC8xZTMpLnRvRml4ZWQoMykpfWQmJnRoaXMuY29uZmlnLm9wdF9vdXRfdXNlcmFnZW50X2ZpbHRlciYmKGEuJGJyb3dzZXJfdHlwZT10aGlzLl9pc19ib3QoKT9cImJvdFwiOlwiYnJvd3NlclwiKSwoYT1WKHt9LGgsdGhpcy5wZXJzaXN0ZW5jZS5wcm9wZXJ0aWVzKCksdGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UucHJvcGVydGllcygpLGEpKS4kaXNfaWRlbnRpZmllZD10aGlzLl9pc0lkZW50aWZpZWQoKSx4KHRoaXMuY29uZmlnLnByb3BlcnR5X2RlbnlsaXN0KT9KKHRoaXMuY29uZmlnLnByb3BlcnR5X2RlbnlsaXN0LChmdW5jdGlvbih0KXtkZWxldGUgYVt0XX0pKTpqLmVycm9yKFwiSW52YWxpZCB2YWx1ZSBmb3IgcHJvcGVydHlfZGVueWxpc3QgY29uZmlnOiBcIit0aGlzLmNvbmZpZy5wcm9wZXJ0eV9kZW55bGlzdCtcIiBvciBwcm9wZXJ0eV9ibGFja2xpc3QgY29uZmlnOiBcIit0aGlzLmNvbmZpZy5wcm9wZXJ0eV9ibGFja2xpc3QpO3ZhciBnPXRoaXMuY29uZmlnLnNhbml0aXplX3Byb3BlcnRpZXM7ZyYmKGouZXJyb3IoXCJzYW5pdGl6ZV9wcm9wZXJ0aWVzIGlzIGRlcHJlY2F0ZWQuIFVzZSBiZWZvcmVfc2VuZCBpbnN0ZWFkXCIpLGE9ZyhhLHQpKTt2YXIgXz10aGlzLklzKCk7cmV0dXJuIGEuJHByb2Nlc3NfcGVyc29uX3Byb2ZpbGU9XyxfJiYhcyYmdGhpcy5QcyhcIl9jYWxjdWxhdGVfZXZlbnRfcHJvcGVydGllc1wiKSxhfXhzKHQpe3ZhciBpO2lmKCF0aGlzLnBlcnNpc3RlbmNlfHwhdGhpcy5JcygpKXJldHVybiB0O2lmKHRoaXMubHMpcmV0dXJuIHQ7dmFyIGU9dGhpcy5wZXJzaXN0ZW5jZS5nZXRfaW5pdGlhbF9wcm9wcygpLHI9bnVsbD09KGk9dGhpcy5zZXNzaW9uUHJvcHNNYW5hZ2VyKT92b2lkIDA6aS5nZXRTZXRPbmNlUHJvcHMoKSxzPVYoe30sZSxyfHx7fSx0fHx7fSksbj10aGlzLmNvbmZpZy5zYW5pdGl6ZV9wcm9wZXJ0aWVzO3JldHVybiBuJiYoai5lcnJvcihcInNhbml0aXplX3Byb3BlcnRpZXMgaXMgZGVwcmVjYXRlZC4gVXNlIGJlZm9yZV9zZW5kIGluc3RlYWRcIikscz1uKHMsXCIkc2V0X29uY2VcIikpLHRoaXMubHM9ITAsUChzKT92b2lkIDA6c31yZWdpc3Rlcih0LGkpe3ZhciBlO251bGw9PShlPXRoaXMucGVyc2lzdGVuY2UpfHxlLnJlZ2lzdGVyKHQsaSl9cmVnaXN0ZXJfb25jZSh0LGksZSl7dmFyIHI7bnVsbD09KHI9dGhpcy5wZXJzaXN0ZW5jZSl8fHIucmVnaXN0ZXJfb25jZSh0LGksZSl9cmVnaXN0ZXJfZm9yX3Nlc3Npb24odCl7dmFyIGk7bnVsbD09KGk9dGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UpfHxpLnJlZ2lzdGVyKHQpfXVucmVnaXN0ZXIodCl7dmFyIGk7bnVsbD09KGk9dGhpcy5wZXJzaXN0ZW5jZSl8fGkudW5yZWdpc3Rlcih0KX11bnJlZ2lzdGVyX2Zvcl9zZXNzaW9uKHQpe3ZhciBpO251bGw9PShpPXRoaXMuc2Vzc2lvblBlcnNpc3RlbmNlKXx8aS51bnJlZ2lzdGVyKHQpfVJzKHQsaSl7dGhpcy5yZWdpc3Rlcih7W3RdOml9KX1nZXRGZWF0dXJlRmxhZyh0LGkpe3JldHVybiB0aGlzLmZlYXR1cmVGbGFncy5nZXRGZWF0dXJlRmxhZyh0LGkpfWdldEZlYXR1cmVGbGFnUGF5bG9hZCh0KXt2YXIgaT10aGlzLmZlYXR1cmVGbGFncy5nZXRGZWF0dXJlRmxhZ1BheWxvYWQodCk7dHJ5e3JldHVybiBKU09OLnBhcnNlKGkpfWNhdGNoKHQpe3JldHVybiBpfX1pc0ZlYXR1cmVFbmFibGVkKHQsaSl7cmV0dXJuIHRoaXMuZmVhdHVyZUZsYWdzLmlzRmVhdHVyZUVuYWJsZWQodCxpKX1yZWxvYWRGZWF0dXJlRmxhZ3MoKXt0aGlzLmZlYXR1cmVGbGFncy5yZWxvYWRGZWF0dXJlRmxhZ3MoKX11cGRhdGVFYXJseUFjY2Vzc0ZlYXR1cmVFbnJvbGxtZW50KHQsaSl7dGhpcy5mZWF0dXJlRmxhZ3MudXBkYXRlRWFybHlBY2Nlc3NGZWF0dXJlRW5yb2xsbWVudCh0LGkpfWdldEVhcmx5QWNjZXNzRmVhdHVyZXModCxpLGUpe3JldHVybiB2b2lkIDA9PT1pJiYoaT0hMSksdGhpcy5mZWF0dXJlRmxhZ3MuZ2V0RWFybHlBY2Nlc3NGZWF0dXJlcyh0LGksZSl9b24odCxpKXtyZXR1cm4gdGhpcy51cy5vbih0LGkpfW9uRmVhdHVyZUZsYWdzKHQpe3JldHVybiB0aGlzLmZlYXR1cmVGbGFncy5vbkZlYXR1cmVGbGFncyh0KX1vblN1cnZleXNMb2FkZWQodCl7cmV0dXJuIHRoaXMuc3VydmV5cy5vblN1cnZleXNMb2FkZWQodCl9b25TZXNzaW9uSWQodCl7dmFyIGksZTtyZXR1cm4gbnVsbCE9PShpPW51bGw9PShlPXRoaXMuc2Vzc2lvbk1hbmFnZXIpP3ZvaWQgMDplLm9uU2Vzc2lvbklkKHQpKSYmdm9pZCAwIT09aT9pOigpPT57fX1nZXRTdXJ2ZXlzKHQsaSl7dm9pZCAwPT09aSYmKGk9ITEpLHRoaXMuc3VydmV5cy5nZXRTdXJ2ZXlzKHQsaSl9Z2V0QWN0aXZlTWF0Y2hpbmdTdXJ2ZXlzKHQsaSl7dm9pZCAwPT09aSYmKGk9ITEpLHRoaXMuc3VydmV5cy5nZXRBY3RpdmVNYXRjaGluZ1N1cnZleXModCxpKX1yZW5kZXJTdXJ2ZXkodCxpKXt0aGlzLnN1cnZleXMucmVuZGVyU3VydmV5KHQsaSl9Y2FuUmVuZGVyU3VydmV5KHQpe3JldHVybiB0aGlzLnN1cnZleXMuY2FuUmVuZGVyU3VydmV5KHQpfWNhblJlbmRlclN1cnZleUFzeW5jKHQsaSl7cmV0dXJuIHZvaWQgMD09PWkmJihpPSExKSx0aGlzLnN1cnZleXMuY2FuUmVuZGVyU3VydmV5QXN5bmModCxpKX1pZGVudGlmeSh0LGksZSl7aWYoIXRoaXMuX19sb2FkZWR8fCF0aGlzLnBlcnNpc3RlbmNlKXJldHVybiBqLnVuaW5pdGlhbGl6ZWRXYXJuaW5nKFwicG9zdGhvZy5pZGVudGlmeVwiKTtpZihGKHQpJiYodD10LnRvU3RyaW5nKCksai53YXJuKFwiVGhlIGZpcnN0IGFyZ3VtZW50IHRvIHBvc3Rob2cuaWRlbnRpZnkgd2FzIGEgbnVtYmVyLCBidXQgaXQgc2hvdWxkIGJlIGEgc3RyaW5nLiBJdCBoYXMgYmVlbiBjb252ZXJ0ZWQgdG8gYSBzdHJpbmcuXCIpKSx0KXtpZihbXCJkaXN0aW5jdF9pZFwiLFwiZGlzdGluY3RpZFwiXS5pbmNsdWRlcyh0LnRvTG93ZXJDYXNlKCkpKWouY3JpdGljYWwoJ1RoZSBzdHJpbmcgXCInK3QrJ1wiIHdhcyBzZXQgaW4gcG9zdGhvZy5pZGVudGlmeSB3aGljaCBpbmRpY2F0ZXMgYW4gZXJyb3IuIFRoaXMgSUQgc2hvdWxkIGJlIHVuaXF1ZSB0byB0aGUgdXNlciBhbmQgbm90IGEgaGFyZGNvZGVkIHN0cmluZy4nKTtlbHNlIGlmKHRoaXMuUHMoXCJwb3N0aG9nLmlkZW50aWZ5XCIpKXt2YXIgcj10aGlzLmdldF9kaXN0aW5jdF9pZCgpO2lmKHRoaXMucmVnaXN0ZXIoeyR1c2VyX2lkOnR9KSwhdGhpcy5nZXRfcHJvcGVydHkoXCIkZGV2aWNlX2lkXCIpKXt2YXIgcz1yO3RoaXMucmVnaXN0ZXJfb25jZSh7JGhhZF9wZXJzaXN0ZWRfZGlzdGluY3RfaWQ6ITAsJGRldmljZV9pZDpzfSxcIlwiKX10IT09ciYmdCE9PXRoaXMuZ2V0X3Byb3BlcnR5KG90KSYmKHRoaXMudW5yZWdpc3RlcihvdCksdGhpcy5yZWdpc3Rlcih7ZGlzdGluY3RfaWQ6dH0pKTt2YXIgbj1cImFub255bW91c1wiPT09KHRoaXMucGVyc2lzdGVuY2UuZ2V0X3Byb3BlcnR5KEF0KXx8XCJhbm9ueW1vdXNcIik7dCE9PXImJm4/KHRoaXMucGVyc2lzdGVuY2Uuc2V0X3Byb3BlcnR5KEF0LFwiaWRlbnRpZmllZFwiKSx0aGlzLnNldFBlcnNvblByb3BlcnRpZXNGb3JGbGFncyhCKHt9LGV8fHt9LGl8fHt9KSwhMSksdGhpcy5jYXB0dXJlKFwiJGlkZW50aWZ5XCIse2Rpc3RpbmN0X2lkOnQsJGFub25fZGlzdGluY3RfaWQ6cn0seyRzZXQ6aXx8e30sJHNldF9vbmNlOmV8fHt9fSksdGhpcy5jcz1qcyh0LGksZSksdGhpcy5mZWF0dXJlRmxhZ3Muc2V0QW5vbnltb3VzRGlzdGluY3RJZChyKSk6KGl8fGUpJiZ0aGlzLnNldFBlcnNvblByb3BlcnRpZXMoaSxlKSx0IT09ciYmKHRoaXMucmVsb2FkRmVhdHVyZUZsYWdzKCksdGhpcy51bnJlZ2lzdGVyKEZ0KSl9fWVsc2Ugai5lcnJvcihcIlVuaXF1ZSB1c2VyIGlkIGhhcyBub3QgYmVlbiBzZXQgaW4gcG9zdGhvZy5pZGVudGlmeVwiKX1zZXRQZXJzb25Qcm9wZXJ0aWVzKHQsaSl7aWYoKHR8fGkpJiZ0aGlzLlBzKFwicG9zdGhvZy5zZXRQZXJzb25Qcm9wZXJ0aWVzXCIpKXt2YXIgZT1qcyh0aGlzLmdldF9kaXN0aW5jdF9pZCgpLHQsaSk7dGhpcy5jcyE9PWU/KHRoaXMuc2V0UGVyc29uUHJvcGVydGllc0ZvckZsYWdzKEIoe30saXx8e30sdHx8e30pKSx0aGlzLmNhcHR1cmUoXCIkc2V0XCIseyRzZXQ6dHx8e30sJHNldF9vbmNlOml8fHt9fSksdGhpcy5jcz1lKTpqLmluZm8oXCJBIGR1cGxpY2F0ZSBzZXRQZXJzb25Qcm9wZXJ0aWVzIGNhbGwgd2FzIG1hZGUgd2l0aCB0aGUgc2FtZSBwcm9wZXJ0aWVzLiBJdCBoYXMgYmVlbiBpZ25vcmVkLlwiKX19Z3JvdXAodCxpLGUpe2lmKHQmJmkpe2lmKHRoaXMuUHMoXCJwb3N0aG9nLmdyb3VwXCIpKXt2YXIgcj10aGlzLmdldEdyb3VwcygpO3JbdF0hPT1pJiZ0aGlzLnJlc2V0R3JvdXBQcm9wZXJ0aWVzRm9yRmxhZ3ModCksdGhpcy5yZWdpc3Rlcih7JGdyb3VwczpCKHt9LHIse1t0XTppfSl9KSxlJiYodGhpcy5jYXB0dXJlKFwiJGdyb3VwaWRlbnRpZnlcIix7JGdyb3VwX3R5cGU6dCwkZ3JvdXBfa2V5OmksJGdyb3VwX3NldDplfSksdGhpcy5zZXRHcm91cFByb3BlcnRpZXNGb3JGbGFncyh7W3RdOmV9KSksclt0XT09PWl8fGV8fHRoaXMucmVsb2FkRmVhdHVyZUZsYWdzKCl9fWVsc2Ugai5lcnJvcihcInBvc3Rob2cuZ3JvdXAgcmVxdWlyZXMgYSBncm91cCB0eXBlIGFuZCBncm91cCBrZXlcIil9cmVzZXRHcm91cHMoKXt0aGlzLnJlZ2lzdGVyKHskZ3JvdXBzOnt9fSksdGhpcy5yZXNldEdyb3VwUHJvcGVydGllc0ZvckZsYWdzKCksdGhpcy5yZWxvYWRGZWF0dXJlRmxhZ3MoKX1zZXRQZXJzb25Qcm9wZXJ0aWVzRm9yRmxhZ3ModCxpKXt2b2lkIDA9PT1pJiYoaT0hMCksdGhpcy5mZWF0dXJlRmxhZ3Muc2V0UGVyc29uUHJvcGVydGllc0ZvckZsYWdzKHQsaSl9cmVzZXRQZXJzb25Qcm9wZXJ0aWVzRm9yRmxhZ3MoKXt0aGlzLmZlYXR1cmVGbGFncy5yZXNldFBlcnNvblByb3BlcnRpZXNGb3JGbGFncygpfXNldEdyb3VwUHJvcGVydGllc0ZvckZsYWdzKHQsaSl7dm9pZCAwPT09aSYmKGk9ITApLHRoaXMuUHMoXCJwb3N0aG9nLnNldEdyb3VwUHJvcGVydGllc0ZvckZsYWdzXCIpJiZ0aGlzLmZlYXR1cmVGbGFncy5zZXRHcm91cFByb3BlcnRpZXNGb3JGbGFncyh0LGkpfXJlc2V0R3JvdXBQcm9wZXJ0aWVzRm9yRmxhZ3ModCl7dGhpcy5mZWF0dXJlRmxhZ3MucmVzZXRHcm91cFByb3BlcnRpZXNGb3JGbGFncyh0KX1yZXNldCh0KXt2YXIgaSxlLHIscztpZihqLmluZm8oXCJyZXNldFwiKSwhdGhpcy5fX2xvYWRlZClyZXR1cm4gai51bmluaXRpYWxpemVkV2FybmluZyhcInBvc3Rob2cucmVzZXRcIik7dmFyIG49dGhpcy5nZXRfcHJvcGVydHkoXCIkZGV2aWNlX2lkXCIpO2lmKHRoaXMuY29uc2VudC5yZXNldCgpLG51bGw9PShpPXRoaXMucGVyc2lzdGVuY2UpfHxpLmNsZWFyKCksbnVsbD09KGU9dGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UpfHxlLmNsZWFyKCksdGhpcy5zdXJ2ZXlzLnJlc2V0KCksbnVsbD09KHI9dGhpcy5wZXJzaXN0ZW5jZSl8fHIuc2V0X3Byb3BlcnR5KEF0LFwiYW5vbnltb3VzXCIpLG51bGw9PShzPXRoaXMuc2Vzc2lvbk1hbmFnZXIpfHxzLnJlc2V0U2Vzc2lvbklkKCksdGhpcy5jcz1udWxsLHRoaXMuY29uZmlnLl9fcHJldmlld19leHBlcmltZW50YWxfY29va2llbGVzc19tb2RlKXRoaXMucmVnaXN0ZXJfb25jZSh7ZGlzdGluY3RfaWQ6QnQsJGRldmljZV9pZDpudWxsfSxcIlwiKTtlbHNle3ZhciBvPXRoaXMuY29uZmlnLmdldF9kZXZpY2VfaWQoTmkoKSk7dGhpcy5yZWdpc3Rlcl9vbmNlKHtkaXN0aW5jdF9pZDpvLCRkZXZpY2VfaWQ6dD9vOm59LFwiXCIpfXRoaXMucmVnaXN0ZXIoeyRsYXN0X3Bvc3Rob2dfcmVzZXQ6KG5ldyBEYXRlKS50b0lTT1N0cmluZygpfSwxKX1nZXRfZGlzdGluY3RfaWQoKXtyZXR1cm4gdGhpcy5nZXRfcHJvcGVydHkoXCJkaXN0aW5jdF9pZFwiKX1nZXRHcm91cHMoKXtyZXR1cm4gdGhpcy5nZXRfcHJvcGVydHkoXCIkZ3JvdXBzXCIpfHx7fX1nZXRfc2Vzc2lvbl9pZCgpe3ZhciB0LGk7cmV0dXJuIG51bGwhPT0odD1udWxsPT0oaT10aGlzLnNlc3Npb25NYW5hZ2VyKT92b2lkIDA6aS5jaGVja0FuZEdldFNlc3Npb25BbmRXaW5kb3dJZCghMCkuc2Vzc2lvbklkKSYmdm9pZCAwIT09dD90OlwiXCJ9Z2V0X3Nlc3Npb25fcmVwbGF5X3VybCh0KXtpZighdGhpcy5zZXNzaW9uTWFuYWdlcilyZXR1cm5cIlwiO3ZhcntzZXNzaW9uSWQ6aSxzZXNzaW9uU3RhcnRUaW1lc3RhbXA6ZX09dGhpcy5zZXNzaW9uTWFuYWdlci5jaGVja0FuZEdldFNlc3Npb25BbmRXaW5kb3dJZCghMCkscj10aGlzLnJlcXVlc3RSb3V0ZXIuZW5kcG9pbnRGb3IoXCJ1aVwiLFwiL3Byb2plY3QvXCIrdGhpcy5jb25maWcudG9rZW4rXCIvcmVwbGF5L1wiK2kpO2lmKG51bGwhPXQmJnQud2l0aFRpbWVzdGFtcCYmZSl7dmFyIHMsbj1udWxsIT09KHM9dC50aW1lc3RhbXBMb29rQmFjaykmJnZvaWQgMCE9PXM/czoxMDtpZighZSlyZXR1cm4gcjtyKz1cIj90PVwiK01hdGgubWF4KE1hdGguZmxvb3IoKChuZXcgRGF0ZSkuZ2V0VGltZSgpLWUpLzFlMyktbiwwKX1yZXR1cm4gcn1hbGlhcyh0LGkpe3JldHVybiB0PT09dGhpcy5nZXRfcHJvcGVydHkobnQpPyhqLmNyaXRpY2FsKFwiQXR0ZW1wdGluZyB0byBjcmVhdGUgYWxpYXMgZm9yIGV4aXN0aW5nIFBlb3BsZSB1c2VyIC0gYWJvcnRpbmcuXCIpLC0yKTp0aGlzLlBzKFwicG9zdGhvZy5hbGlhc1wiKT8oUihpKSYmKGk9dGhpcy5nZXRfZGlzdGluY3RfaWQoKSksdCE9PWk/KHRoaXMuUnMob3QsdCksdGhpcy5jYXB0dXJlKFwiJGNyZWF0ZV9hbGlhc1wiLHthbGlhczp0LGRpc3RpbmN0X2lkOml9KSk6KGoud2FybihcImFsaWFzIG1hdGNoZXMgY3VycmVudCBkaXN0aW5jdF9pZCAtIHNraXBwaW5nIGFwaSBjYWxsLlwiKSx0aGlzLmlkZW50aWZ5KHQpLC0xKSk6dm9pZCAwfXNldF9jb25maWcodCl7dmFyIGksZSxyLHMsbj1CKHt9LHRoaXMuY29uZmlnKTtJKHQpJiYoVih0aGlzLmNvbmZpZyxWbyh0KSksbnVsbD09KGk9dGhpcy5wZXJzaXN0ZW5jZSl8fGkudXBkYXRlX2NvbmZpZyh0aGlzLmNvbmZpZyxuKSx0aGlzLnNlc3Npb25QZXJzaXN0ZW5jZT1cInNlc3Npb25TdG9yYWdlXCI9PT10aGlzLmNvbmZpZy5wZXJzaXN0ZW5jZXx8XCJtZW1vcnlcIj09PXRoaXMuY29uZmlnLnBlcnNpc3RlbmNlP3RoaXMucGVyc2lzdGVuY2U6bmV3IGNvKEIoe30sdGhpcy5jb25maWcse3BlcnNpc3RlbmNlOlwic2Vzc2lvblN0b3JhZ2VcIn0pKSxXaS5PKCkmJlwidHJ1ZVwiPT09V2kuQShcInBoX2RlYnVnXCIpJiYodGhpcy5jb25maWcuZGVidWc9ITApLHRoaXMuY29uZmlnLmRlYnVnJiYoYy5ERUJVRz0hMCxqLmluZm8oXCJzZXRfY29uZmlnXCIsSlNPTi5zdHJpbmdpZnkoe2NvbmZpZzp0LG9sZENvbmZpZzpuLG5ld0NvbmZpZzpCKHt9LHRoaXMuY29uZmlnKX0sbnVsbCwyKSkpLG51bGw9PShlPXRoaXMuc2Vzc2lvblJlY29yZGluZyl8fGUuc3RhcnRJZkVuYWJsZWRPclN0b3AoKSxudWxsPT0ocj10aGlzLmF1dG9jYXB0dXJlKXx8ci5zdGFydElmRW5hYmxlZCgpLG51bGw9PShzPXRoaXMuaGVhdG1hcHMpfHxzLnN0YXJ0SWZFbmFibGVkKCksdGhpcy5zdXJ2ZXlzLmxvYWRJZkVuYWJsZWQoKSx0aGlzLmJzKCkpfXN0YXJ0U2Vzc2lvblJlY29yZGluZyh0KXt2YXIgaT0hMD09PXQsZT17c2FtcGxpbmc6aXx8IShudWxsPT10fHwhdC5zYW1wbGluZyksbGlua2VkX2ZsYWc6aXx8IShudWxsPT10fHwhdC5saW5rZWRfZmxhZyksdXJsX3RyaWdnZXI6aXx8IShudWxsPT10fHwhdC51cmxfdHJpZ2dlciksZXZlbnRfdHJpZ2dlcjppfHwhKG51bGw9PXR8fCF0LmV2ZW50X3RyaWdnZXIpfTtpZihPYmplY3QudmFsdWVzKGUpLnNvbWUoQm9vbGVhbikpe3ZhciByLHMsbixvLGE7aWYobnVsbD09KHI9dGhpcy5zZXNzaW9uTWFuYWdlcil8fHIuY2hlY2tBbmRHZXRTZXNzaW9uQW5kV2luZG93SWQoKSxlLnNhbXBsaW5nKW51bGw9PShzPXRoaXMuc2Vzc2lvblJlY29yZGluZyl8fHMub3ZlcnJpZGVTYW1wbGluZygpO2lmKGUubGlua2VkX2ZsYWcpbnVsbD09KG49dGhpcy5zZXNzaW9uUmVjb3JkaW5nKXx8bi5vdmVycmlkZUxpbmtlZEZsYWcoKTtpZihlLnVybF90cmlnZ2VyKW51bGw9PShvPXRoaXMuc2Vzc2lvblJlY29yZGluZyl8fG8ub3ZlcnJpZGVUcmlnZ2VyKFwidXJsXCIpO2lmKGUuZXZlbnRfdHJpZ2dlciludWxsPT0oYT10aGlzLnNlc3Npb25SZWNvcmRpbmcpfHxhLm92ZXJyaWRlVHJpZ2dlcihcImV2ZW50XCIpfXRoaXMuc2V0X2NvbmZpZyh7ZGlzYWJsZV9zZXNzaW9uX3JlY29yZGluZzohMX0pfXN0b3BTZXNzaW9uUmVjb3JkaW5nKCl7dGhpcy5zZXRfY29uZmlnKHtkaXNhYmxlX3Nlc3Npb25fcmVjb3JkaW5nOiEwfSl9c2Vzc2lvblJlY29yZGluZ1N0YXJ0ZWQoKXt2YXIgdDtyZXR1cm4hKG51bGw9PSh0PXRoaXMuc2Vzc2lvblJlY29yZGluZyl8fCF0LnN0YXJ0ZWQpfWNhcHR1cmVFeGNlcHRpb24odCxpKXt2YXIgZT1uZXcgRXJyb3IoXCJQb3N0SG9nIHN5bnRoZXRpY0V4Y2VwdGlvblwiKTt0aGlzLmV4Y2VwdGlvbnMuc2VuZEV4Y2VwdGlvbkV2ZW50KEIoe30sQWUoKHQ9PnQgaW5zdGFuY2VvZiBFcnJvcikodCk/e2Vycm9yOnQsZXZlbnQ6dC5tZXNzYWdlfTp7ZXZlbnQ6dH0se3N5bnRoZXRpY0V4Y2VwdGlvbjplfSksaSkpfWxvYWRUb29sYmFyKHQpe3JldHVybiB0aGlzLnRvb2xiYXIubG9hZFRvb2xiYXIodCl9Z2V0X3Byb3BlcnR5KHQpe3ZhciBpO3JldHVybiBudWxsPT0oaT10aGlzLnBlcnNpc3RlbmNlKT92b2lkIDA6aS5wcm9wc1t0XX1nZXRTZXNzaW9uUHJvcGVydHkodCl7dmFyIGk7cmV0dXJuIG51bGw9PShpPXRoaXMuc2Vzc2lvblBlcnNpc3RlbmNlKT92b2lkIDA6aS5wcm9wc1t0XX10b1N0cmluZygpe3ZhciB0LGk9bnVsbCE9PSh0PXRoaXMuY29uZmlnLm5hbWUpJiZ2b2lkIDAhPT10P3Q6V287cmV0dXJuIGkhPT1XbyYmKGk9V28rXCIuXCIraSksaX1faXNJZGVudGlmaWVkKCl7dmFyIHQsaTtyZXR1cm5cImlkZW50aWZpZWRcIj09PShudWxsPT0odD10aGlzLnBlcnNpc3RlbmNlKT92b2lkIDA6dC5nZXRfcHJvcGVydHkoQXQpKXx8XCJpZGVudGlmaWVkXCI9PT0obnVsbD09KGk9dGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UpP3ZvaWQgMDppLmdldF9wcm9wZXJ0eShBdCkpfUlzKCl7dmFyIHQsaTtyZXR1cm4hKFwibmV2ZXJcIj09PXRoaXMuY29uZmlnLnBlcnNvbl9wcm9maWxlc3x8XCJpZGVudGlmaWVkX29ubHlcIj09PXRoaXMuY29uZmlnLnBlcnNvbl9wcm9maWxlcyYmIXRoaXMuX2lzSWRlbnRpZmllZCgpJiZQKHRoaXMuZ2V0R3JvdXBzKCkpJiYobnVsbD09KHQ9dGhpcy5wZXJzaXN0ZW5jZSl8fG51bGw9PSh0PXQucHJvcHMpfHwhdFtvdF0pJiYobnVsbD09KGk9dGhpcy5wZXJzaXN0ZW5jZSl8fG51bGw9PShpPWkucHJvcHMpfHwhaVtVdF0pKX1rcygpe3JldHVybiEwPT09dGhpcy5jb25maWcuY2FwdHVyZV9wYWdlbGVhdmV8fFwiaWZfY2FwdHVyZV9wYWdldmlld1wiPT09dGhpcy5jb25maWcuY2FwdHVyZV9wYWdlbGVhdmUmJighMD09PXRoaXMuY29uZmlnLmNhcHR1cmVfcGFnZXZpZXd8fFwiaGlzdG9yeV9jaGFuZ2VcIj09PXRoaXMuY29uZmlnLmNhcHR1cmVfcGFnZXZpZXcpfWNyZWF0ZVBlcnNvblByb2ZpbGUoKXt0aGlzLklzKCl8fHRoaXMuUHMoXCJwb3N0aG9nLmNyZWF0ZVBlcnNvblByb2ZpbGVcIikmJnRoaXMuc2V0UGVyc29uUHJvcGVydGllcyh7fSx7fSl9UHModCl7cmV0dXJuXCJuZXZlclwiPT09dGhpcy5jb25maWcucGVyc29uX3Byb2ZpbGVzPyhqLmVycm9yKHQrJyB3YXMgY2FsbGVkLCBidXQgcHJvY2Vzc19wZXJzb24gaXMgc2V0IHRvIFwibmV2ZXJcIi4gVGhpcyBjYWxsIHdpbGwgYmUgaWdub3JlZC4nKSwhMSk6KHRoaXMuUnMoVXQsITApLCEwKX1icygpe3ZhciB0LGksZSxyLHM9dGhpcy5jb25zZW50LmlzT3B0ZWRPdXQoKSxuPXRoaXMuY29uZmlnLm9wdF9vdXRfcGVyc2lzdGVuY2VfYnlfZGVmYXVsdCxvPXRoaXMuY29uZmlnLmRpc2FibGVfcGVyc2lzdGVuY2V8fHMmJiEhbjsobnVsbD09KHQ9dGhpcy5wZXJzaXN0ZW5jZSk/dm9pZCAwOnQueGUpIT09byYmKG51bGw9PShlPXRoaXMucGVyc2lzdGVuY2UpfHxlLnNldF9kaXNhYmxlZChvKSk7KG51bGw9PShpPXRoaXMuc2Vzc2lvblBlcnNpc3RlbmNlKT92b2lkIDA6aS54ZSkhPT1vJiYobnVsbD09KHI9dGhpcy5zZXNzaW9uUGVyc2lzdGVuY2UpfHxyLnNldF9kaXNhYmxlZChvKSl9b3B0X2luX2NhcHR1cmluZyh0KXt2YXIgaTsodGhpcy5jb25zZW50Lm9wdEluT3V0KCEwKSx0aGlzLmJzKCksUihudWxsPT10P3ZvaWQgMDp0LmNhcHR1cmVFdmVudE5hbWUpfHxudWxsIT10JiZ0LmNhcHR1cmVFdmVudE5hbWUpJiZ0aGlzLmNhcHR1cmUobnVsbCE9PShpPW51bGw9PXQ/dm9pZCAwOnQuY2FwdHVyZUV2ZW50TmFtZSkmJnZvaWQgMCE9PWk/aTpcIiRvcHRfaW5cIixudWxsPT10P3ZvaWQgMDp0LmNhcHR1cmVQcm9wZXJ0aWVzLHtzZW5kX2luc3RhbnRseTohMH0pO3RoaXMuY29uZmlnLmNhcHR1cmVfcGFnZXZpZXcmJnRoaXMuJHMoKX1vcHRfb3V0X2NhcHR1cmluZygpe3RoaXMuY29uc2VudC5vcHRJbk91dCghMSksdGhpcy5icygpfWhhc19vcHRlZF9pbl9jYXB0dXJpbmcoKXtyZXR1cm4gdGhpcy5jb25zZW50LmlzT3B0ZWRJbigpfWhhc19vcHRlZF9vdXRfY2FwdHVyaW5nKCl7cmV0dXJuIHRoaXMuY29uc2VudC5pc09wdGVkT3V0KCl9Y2xlYXJfb3B0X2luX291dF9jYXB0dXJpbmcoKXt0aGlzLmNvbnNlbnQucmVzZXQoKSx0aGlzLmJzKCl9X2lzX2JvdCgpe3JldHVybiBuP0xvKG4sdGhpcy5jb25maWcuY3VzdG9tX2Jsb2NrZWRfdXNlcmFnZW50cyk6dm9pZCAwfSRzKCl7byYmKFwidmlzaWJsZVwiPT09by52aXNpYmlsaXR5U3RhdGU/dGhpcy5oc3x8KHRoaXMuaHM9ITAsdGhpcy5jYXB0dXJlKFwiJHBhZ2V2aWV3XCIse3RpdGxlOm8udGl0bGV9LHtzZW5kX2luc3RhbnRseTohMH0pLHRoaXMuZHMmJihvLnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJ2aXNpYmlsaXR5Y2hhbmdlXCIsdGhpcy5kcyksdGhpcy5kcz1udWxsKSk6dGhpcy5kc3x8KHRoaXMuZHM9dGhpcy4kcy5iaW5kKHRoaXMpLHN0KG8sXCJ2aXNpYmlsaXR5Y2hhbmdlXCIsdGhpcy5kcykpKX1kZWJ1ZyhpKXshMT09PWk/KG51bGw9PXR8fHQuY29uc29sZS5sb2coXCJZb3UndmUgZGlzYWJsZWQgZGVidWcgbW9kZS5cIiksbG9jYWxTdG9yYWdlJiZsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcInBoX2RlYnVnXCIpLHRoaXMuc2V0X2NvbmZpZyh7ZGVidWc6ITF9KSk6KG51bGw9PXR8fHQuY29uc29sZS5sb2coXCJZb3UncmUgbm93IGluIGRlYnVnIG1vZGUuIEFsbCBjYWxscyB0byBQb3N0SG9nIHdpbGwgYmUgbG9nZ2VkIGluIHlvdXIgY29uc29sZS5cXG5Zb3UgY2FuIGRpc2FibGUgdGhpcyB3aXRoIGBwb3N0aG9nLmRlYnVnKGZhbHNlKWAuXCIpLGxvY2FsU3RvcmFnZSYmbG9jYWxTdG9yYWdlLnNldEl0ZW0oXCJwaF9kZWJ1Z1wiLFwidHJ1ZVwiKSx0aGlzLnNldF9jb25maWcoe2RlYnVnOiEwfSkpfUVzKHQpe2lmKE8odGhpcy5jb25maWcuYmVmb3JlX3NlbmQpKXJldHVybiB0O3ZhciBpPXgodGhpcy5jb25maWcuYmVmb3JlX3NlbmQpP3RoaXMuY29uZmlnLmJlZm9yZV9zZW5kOlt0aGlzLmNvbmZpZy5iZWZvcmVfc2VuZF0sZT10O2Zvcih2YXIgciBvZiBpKXtpZihlPXIoZSksTyhlKSl7dmFyIHM9XCJFdmVudCAnXCIrdC5ldmVudCtcIicgd2FzIHJlamVjdGVkIGluIGJlZm9yZVNlbmQgZnVuY3Rpb25cIjtyZXR1cm4gTCh0LmV2ZW50KT9qLndhcm4ocytcIi4gVGhpcyBjYW4gY2F1c2UgdW5leHBlY3RlZCBiZWhhdmlvci5cIik6ai5pbmZvKHMpLG51bGx9ZS5wcm9wZXJ0aWVzJiYhUChlLnByb3BlcnRpZXMpfHxqLndhcm4oXCJFdmVudCAnXCIrdC5ldmVudCtcIicgaGFzIG5vIHByb3BlcnRpZXMgYWZ0ZXIgYmVmb3JlU2VuZCBmdW5jdGlvbiwgdGhpcyBpcyBsaWtlbHkgYW4gZXJyb3IuXCIpfXJldHVybiBlfWdldFBhZ2VWaWV3SWQoKXt2YXIgdDtyZXR1cm4gbnVsbD09KHQ9dGhpcy5wYWdlVmlld01hbmFnZXIubmUpP3ZvaWQgMDp0LnBhZ2VWaWV3SWR9Y2FwdHVyZVRyYWNlRmVlZGJhY2sodCxpKXt0aGlzLmNhcHR1cmUoXCIkYWlfZmVlZGJhY2tcIix7JGFpX3RyYWNlX2lkOlN0cmluZyh0KSwkYWlfZmVlZGJhY2tfdGV4dDppfSl9Y2FwdHVyZVRyYWNlTWV0cmljKHQsaSxlKXt0aGlzLmNhcHR1cmUoXCIkYWlfbWV0cmljXCIseyRhaV90cmFjZV9pZDpTdHJpbmcodCksJGFpX21ldHJpY19uYW1lOmksJGFpX21ldHJpY192YWx1ZTpTdHJpbmcoZSl9KX19IWZ1bmN0aW9uKHQsaSl7Zm9yKHZhciBlPTA7ZTxpLmxlbmd0aDtlKyspdC5wcm90b3R5cGVbaVtlXV09USh0LnByb3RvdHlwZVtpW2VdXSl9KFlvLFtcImlkZW50aWZ5XCJdKTt2YXIgWG8sUW89ZnVuY3Rpb24odCl7cmV0dXJuIHQuQnV0dG9uPVwiYnV0dG9uXCIsdC5UYWI9XCJ0YWJcIix0LlNlbGVjdG9yPVwic2VsZWN0b3JcIix0fSh7fSksWm89ZnVuY3Rpb24odCl7cmV0dXJuIHQuVG9wTGVmdD1cInRvcF9sZWZ0XCIsdC5Ub3BSaWdodD1cInRvcF9yaWdodFwiLHQuVG9wQ2VudGVyPVwidG9wX2NlbnRlclwiLHQuTWlkZGxlTGVmdD1cIm1pZGRsZV9sZWZ0XCIsdC5NaWRkbGVSaWdodD1cIm1pZGRsZV9yaWdodFwiLHQuTWlkZGxlQ2VudGVyPVwibWlkZGxlX2NlbnRlclwiLHQuTGVmdD1cImxlZnRcIix0LkNlbnRlcj1cImNlbnRlclwiLHQuUmlnaHQ9XCJyaWdodFwiLHQuTmV4dFRvVHJpZ2dlcj1cIm5leHRfdG9fdHJpZ2dlclwiLHR9KHt9KSx0YT1mdW5jdGlvbih0KXtyZXR1cm4gdC5Qb3BvdmVyPVwicG9wb3ZlclwiLHQuQVBJPVwiYXBpXCIsdC5XaWRnZXQ9XCJ3aWRnZXRcIix0fSh7fSksaWE9ZnVuY3Rpb24odCl7cmV0dXJuIHQuT3Blbj1cIm9wZW5cIix0Lk11bHRpcGxlQ2hvaWNlPVwibXVsdGlwbGVfY2hvaWNlXCIsdC5TaW5nbGVDaG9pY2U9XCJzaW5nbGVfY2hvaWNlXCIsdC5SYXRpbmc9XCJyYXRpbmdcIix0Lkxpbms9XCJsaW5rXCIsdH0oe30pLGVhPWZ1bmN0aW9uKHQpe3JldHVybiB0Lk5leHRRdWVzdGlvbj1cIm5leHRfcXVlc3Rpb25cIix0LkVuZD1cImVuZFwiLHQuUmVzcG9uc2VCYXNlZD1cInJlc3BvbnNlX2Jhc2VkXCIsdC5TcGVjaWZpY1F1ZXN0aW9uPVwic3BlY2lmaWNfcXVlc3Rpb25cIix0fSh7fSkscmE9ZnVuY3Rpb24odCl7cmV0dXJuIHQuT25jZT1cIm9uY2VcIix0LlJlY3VycmluZz1cInJlY3VycmluZ1wiLHQuQWx3YXlzPVwiYWx3YXlzXCIsdH0oe30pLHNhPShYbz1Cb1tXb109bmV3IFlvLGZ1bmN0aW9uKCl7ZnVuY3Rpb24gaSgpe2kuZG9uZXx8KGkuZG9uZT0hMCxHbz0hMSxKKEJvLChmdW5jdGlvbih0KXt0Ll9kb21fbG9hZGVkKCl9KSkpfW51bGwhPW8mJm8uYWRkRXZlbnRMaXN0ZW5lcj9cImNvbXBsZXRlXCI9PT1vLnJlYWR5U3RhdGU/aSgpOnN0KG8sXCJET01Db250ZW50TG9hZGVkXCIsaSx7Y2FwdHVyZTohMX0pOnQmJmouZXJyb3IoXCJCcm93c2VyIGRvZXNuJ3Qgc3VwcG9ydCBgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcmAgc28gUG9zdEhvZyBjb3VsZG4ndCBiZSBpbml0aWFsaXplZFwiKX0oKSxYbyk7ZXhwb3J0e2YgYXMgQ09QWV9BVVRPQ0FQVFVSRV9FVkVOVCxnIGFzIENvbXByZXNzaW9uLFlvIGFzIFBvc3RIb2csWm8gYXMgU3VydmV5UG9zaXRpb24sZWEgYXMgU3VydmV5UXVlc3Rpb25CcmFuY2hpbmdUeXBlLGlhIGFzIFN1cnZleVF1ZXN0aW9uVHlwZSxyYSBhcyBTdXJ2ZXlTY2hlZHVsZSx0YSBhcyBTdXJ2ZXlUeXBlLFFvIGFzIFN1cnZleVdpZGdldFR5cGUsc2EgYXMgZGVmYXVsdCxwIGFzIGtub3duVW5zYWZlRWRpdGFibGVFdmVudCxzYSBhcyBwb3N0aG9nLF8gYXMgc2V2ZXJpdHlMZXZlbHN9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bW9kdWxlLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/dist/module.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/react/dist/esm/index.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/react/dist/esm/index.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostHogContext: () => (/* binding */ PostHogContext),\n/* harmony export */   PostHogErrorBoundary: () => (/* binding */ PostHogErrorBoundary),\n/* harmony export */   PostHogFeature: () => (/* binding */ PostHogFeature),\n/* harmony export */   PostHogProvider: () => (/* binding */ PostHogProvider),\n/* harmony export */   useActiveFeatureFlags: () => (/* binding */ useActiveFeatureFlags),\n/* harmony export */   useFeatureFlagEnabled: () => (/* binding */ useFeatureFlagEnabled),\n/* harmony export */   useFeatureFlagPayload: () => (/* binding */ useFeatureFlagPayload),\n/* harmony export */   useFeatureFlagVariantKey: () => (/* binding */ useFeatureFlagVariantKey),\n/* harmony export */   usePostHog: () => (/* binding */ usePostHog)\n/* harmony export */ });\n/* harmony import */ var posthog_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! posthog-js */ \"(ssr)/../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/dist/module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nvar PostHogContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({ client: posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] });\n\nfunction isDeepEqual(obj1, obj2, visited) {\r\n    if (visited === void 0) { visited = new WeakMap(); }\r\n    if (obj1 === obj2) {\r\n        return true;\r\n    }\r\n    if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {\r\n        return false;\r\n    }\r\n    if (visited.has(obj1) && visited.get(obj1) === obj2) {\r\n        return true;\r\n    }\r\n    visited.set(obj1, obj2);\r\n    var keys1 = Object.keys(obj1);\r\n    var keys2 = Object.keys(obj2);\r\n    if (keys1.length !== keys2.length) {\r\n        return false;\r\n    }\r\n    for (var _i = 0, keys1_1 = keys1; _i < keys1_1.length; _i++) {\r\n        var key = keys1_1[_i];\r\n        if (!keys2.includes(key)) {\r\n            return false;\r\n        }\r\n        if (!isDeepEqual(obj1[key], obj2[key], visited)) {\r\n            return false;\r\n        }\r\n    }\r\n    return true;\r\n}\n\nfunction PostHogProvider(_a) {\r\n    var children = _a.children, client = _a.client, apiKey = _a.apiKey, options = _a.options;\r\n    var previousInitializationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\r\n    var posthog = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\r\n        if (client) {\r\n            if (apiKey) {\r\n                console.warn('[PostHog.js] You have provided both `client` and `apiKey` to `PostHogProvider`. `apiKey` will be ignored in favour of `client`.');\r\n            }\r\n            if (options) {\r\n                console.warn('[PostHog.js] You have provided both `client` and `options` to `PostHogProvider`. `options` will be ignored in favour of `client`.');\r\n            }\r\n            return client;\r\n        }\r\n        if (apiKey) {\r\n            return posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\r\n        }\r\n        console.warn('[PostHog.js] No `apiKey` or `client` were provided to `PostHogProvider`. Using default global `window.posthog` instance. You must initialize it manually. This is not recommended behavior.');\r\n        return posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\r\n    }, [client, apiKey, JSON.stringify(options)]);\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\r\n        if (client) {\r\n            return;\r\n        }\r\n        var previousInitialization = previousInitializationRef.current;\r\n        if (!previousInitialization) {\r\n            if (posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].__loaded) {\r\n                console.warn('[PostHog.js] `posthog` was already loaded elsewhere. This may cause issues.');\r\n            }\r\n            posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].init(apiKey, options);\r\n            previousInitializationRef.current = {\r\n                apiKey: apiKey,\r\n                options: options !== null && options !== void 0 ? options : {},\r\n            };\r\n        }\r\n        else {\r\n            if (apiKey !== previousInitialization.apiKey) {\r\n                console.warn(\"[PostHog.js] You have provided a different `apiKey` to `PostHogProvider` than the one that was already initialized. This is not supported by our provider and we'll keep using the previous key. If you need to toggle between API Keys you need to control the `client` yourself and pass it in as a prop rather than an `apiKey` prop.\");\r\n            }\r\n            if (options && !isDeepEqual(options, previousInitialization.options)) {\r\n                posthog_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set_config(options);\r\n            }\r\n            previousInitializationRef.current = {\r\n                apiKey: apiKey,\r\n                options: options !== null && options !== void 0 ? options : {},\r\n            };\r\n        }\r\n    }, [client, apiKey, JSON.stringify(options)]);\r\n    return react__WEBPACK_IMPORTED_MODULE_1___default().createElement(PostHogContext.Provider, { value: { client: posthog } }, children);\r\n}\n\nvar usePostHog = function () {\r\n    var client = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PostHogContext).client;\r\n    return client;\r\n};\n\nfunction useFeatureFlagEnabled(flag) {\r\n    var client = usePostHog();\r\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () { return client.isFeatureEnabled(flag); }), featureEnabled = _a[0], setFeatureEnabled = _a[1];\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\r\n        return client.onFeatureFlags(function () {\r\n            setFeatureEnabled(client.isFeatureEnabled(flag));\r\n        });\r\n    }, [client, flag]);\r\n    return featureEnabled;\r\n}\n\nfunction useFeatureFlagPayload(flag) {\r\n    var client = usePostHog();\r\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () { return client.getFeatureFlagPayload(flag); }), featureFlagPayload = _a[0], setFeatureFlagPayload = _a[1];\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\r\n        return client.onFeatureFlags(function () {\r\n            setFeatureFlagPayload(client.getFeatureFlagPayload(flag));\r\n        });\r\n    }, [client, flag]);\r\n    return featureFlagPayload;\r\n}\n\nfunction useActiveFeatureFlags() {\r\n    var client = usePostHog();\r\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () { return client.featureFlags.getFlags(); }), featureFlags = _a[0], setFeatureFlags = _a[1];\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\r\n        return client.onFeatureFlags(function (flags) {\r\n            setFeatureFlags(flags);\r\n        });\r\n    }, [client]);\r\n    return featureFlags;\r\n}\n\nfunction useFeatureFlagVariantKey(flag) {\r\n    var client = usePostHog();\r\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {\r\n        return client.getFeatureFlag(flag);\r\n    }), featureFlagVariantKey = _a[0], setFeatureFlagVariantKey = _a[1];\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\r\n        return client.onFeatureFlags(function () {\r\n            setFeatureFlagVariantKey(client.getFeatureFlag(flag));\r\n        });\r\n    }, [client, flag]);\r\n    return featureFlagVariantKey;\r\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\n\nvar isFunction = function (f) {\r\n    return typeof f === 'function';\r\n};\r\nvar isUndefined = function (x) {\r\n    return x === void 0;\r\n};\r\nvar isNull = function (x) {\r\n    return x === null;\r\n};\n\nfunction PostHogFeature(_a) {\r\n    var flag = _a.flag, match = _a.match, children = _a.children, fallback = _a.fallback, visibilityObserverOptions = _a.visibilityObserverOptions, trackInteraction = _a.trackInteraction, trackView = _a.trackView, props = __rest(_a, [\"flag\", \"match\", \"children\", \"fallback\", \"visibilityObserverOptions\", \"trackInteraction\", \"trackView\"]);\r\n    var payload = useFeatureFlagPayload(flag);\r\n    var variant = useFeatureFlagVariantKey(flag);\r\n    var shouldTrackInteraction = trackInteraction !== null && trackInteraction !== void 0 ? trackInteraction : true;\r\n    var shouldTrackView = trackView !== null && trackView !== void 0 ? trackView : true;\r\n    if (isUndefined(match) || variant === match) {\r\n        var childNode = isFunction(children) ? children(payload) : children;\r\n        return (react__WEBPACK_IMPORTED_MODULE_1___default().createElement(VisibilityAndClickTrackers, __assign({ flag: flag, options: visibilityObserverOptions, trackInteraction: shouldTrackInteraction, trackView: shouldTrackView }, props), childNode));\r\n    }\r\n    return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), null, fallback);\r\n}\r\nfunction captureFeatureInteraction(_a) {\r\n    var _b;\r\n    var flag = _a.flag, posthog = _a.posthog, flagVariant = _a.flagVariant;\r\n    var properties = {\r\n        feature_flag: flag,\r\n        $set: (_b = {}, _b[\"$feature_interaction/\".concat(flag)] = flagVariant !== null && flagVariant !== void 0 ? flagVariant : true, _b),\r\n    };\r\n    if (typeof flagVariant === 'string') {\r\n        properties.feature_flag_variant = flagVariant;\r\n    }\r\n    posthog.capture('$feature_interaction', properties);\r\n}\r\nfunction captureFeatureView(_a) {\r\n    var _b;\r\n    var flag = _a.flag, posthog = _a.posthog, flagVariant = _a.flagVariant;\r\n    var properties = {\r\n        feature_flag: flag,\r\n        $set: (_b = {}, _b[\"$feature_view/\".concat(flag)] = flagVariant !== null && flagVariant !== void 0 ? flagVariant : true, _b),\r\n    };\r\n    if (typeof flagVariant === 'string') {\r\n        properties.feature_flag_variant = flagVariant;\r\n    }\r\n    posthog.capture('$feature_view', properties);\r\n}\r\nfunction VisibilityAndClickTracker(_a) {\r\n    var flag = _a.flag, children = _a.children, onIntersect = _a.onIntersect, onClick = _a.onClick, trackView = _a.trackView, options = _a.options, props = __rest(_a, [\"flag\", \"children\", \"onIntersect\", \"onClick\", \"trackView\", \"options\"]);\r\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\r\n    var posthog = usePostHog();\r\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\r\n        if (isNull(ref.current) || !trackView)\r\n            return;\r\n        var observer = new IntersectionObserver(function (_a) {\r\n            var entry = _a[0];\r\n            return onIntersect(entry);\r\n        }, __assign({ threshold: 0.1 }, options));\r\n        observer.observe(ref.current);\r\n        return function () { return observer.disconnect(); };\r\n    }, [flag, options, posthog, ref, trackView, onIntersect]);\r\n    return (react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", __assign({ ref: ref }, props, { onClick: onClick }), children));\r\n}\r\nfunction VisibilityAndClickTrackers(_a) {\r\n    var flag = _a.flag, children = _a.children, trackInteraction = _a.trackInteraction, trackView = _a.trackView, options = _a.options, props = __rest(_a, [\"flag\", \"children\", \"trackInteraction\", \"trackView\", \"options\"]);\r\n    var clickTrackedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\r\n    var visibilityTrackedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\r\n    var posthog = usePostHog();\r\n    var variant = useFeatureFlagVariantKey(flag);\r\n    var cachedOnClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\r\n        if (!clickTrackedRef.current && trackInteraction) {\r\n            captureFeatureInteraction({ flag: flag, posthog: posthog, flagVariant: variant });\r\n            clickTrackedRef.current = true;\r\n        }\r\n    }, [flag, posthog, trackInteraction, variant]);\r\n    var onIntersect = function (entry) {\r\n        if (!visibilityTrackedRef.current && entry.isIntersecting) {\r\n            captureFeatureView({ flag: flag, posthog: posthog, flagVariant: variant });\r\n            visibilityTrackedRef.current = true;\r\n        }\r\n    };\r\n    var trackedChildren = react__WEBPACK_IMPORTED_MODULE_1__.Children.map(children, function (child) {\r\n        return (react__WEBPACK_IMPORTED_MODULE_1___default().createElement(VisibilityAndClickTracker, __assign({ flag: flag, onClick: cachedOnClick, onIntersect: onIntersect, trackView: trackView, options: options }, props), child));\r\n    });\r\n    return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), null, trackedChildren);\r\n}\n\nvar INITIAL_STATE = {\r\n    componentStack: null,\r\n    error: null,\r\n};\r\nvar __POSTHOG_ERROR_MESSAGES = {\r\n    INVALID_FALLBACK: '[PostHog.js][PostHogErrorBoundary] Invalid fallback prop, provide a valid React element or a function that returns a valid React element.',\r\n};\r\nvar PostHogErrorBoundary = (function (_super) {\r\n    __extends(PostHogErrorBoundary, _super);\r\n    function PostHogErrorBoundary(props) {\r\n        var _this = _super.call(this, props) || this;\r\n        _this.state = INITIAL_STATE;\r\n        return _this;\r\n    }\r\n    PostHogErrorBoundary.prototype.componentDidCatch = function (error, errorInfo) {\r\n        var componentStack = errorInfo.componentStack;\r\n        var additionalProperties = this.props.additionalProperties;\r\n        this.setState({\r\n            error: error,\r\n            componentStack: componentStack,\r\n        });\r\n        var currentProperties;\r\n        if (isFunction(additionalProperties)) {\r\n            currentProperties = additionalProperties(error);\r\n        }\r\n        else if (typeof additionalProperties === 'object') {\r\n            currentProperties = additionalProperties;\r\n        }\r\n        var client = this.context.client;\r\n        client.captureException(error, currentProperties);\r\n    };\r\n    PostHogErrorBoundary.prototype.render = function () {\r\n        var _a = this.props, children = _a.children, fallback = _a.fallback;\r\n        var state = this.state;\r\n        if (state.componentStack == null) {\r\n            return isFunction(children) ? children() : children;\r\n        }\r\n        var element = isFunction(fallback)\r\n            ? react__WEBPACK_IMPORTED_MODULE_1___default().createElement(fallback, {\r\n                error: state.error,\r\n                componentStack: state.componentStack,\r\n            })\r\n            : fallback;\r\n        if (react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(element)) {\r\n            return element;\r\n        }\r\n        console.warn(__POSTHOG_ERROR_MESSAGES.INVALID_FALLBACK);\r\n        return react__WEBPACK_IMPORTED_MODULE_1___default().createElement((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), null);\r\n    };\r\n    PostHogErrorBoundary.contextType = PostHogContext;\r\n    return PostHogErrorBoundary;\r\n}((react__WEBPACK_IMPORTED_MODULE_1___default().Component)));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/posthog-js@1.246.0_@rrweb+types@2.0.0-alpha.17/node_modules/posthog-js/react/dist/esm/index.js\n");

/***/ })

};
;