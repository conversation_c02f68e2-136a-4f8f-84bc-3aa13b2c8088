"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94";
exports.ids = ["vendor-chunks/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94"];
exports.modules = {

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SemanticAttributes = void 0;\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\nexports.SemanticAttributes = {\n    /**\n     * State of the HTTP connection in the HTTP connection pool.\n     */\n    HTTP_CONNECTION_STATE: 'http.connection.state',\n    /**\n    * Describes a class of error the operation ended with.\n    *\n    * Note: The `error.type` SHOULD be predictable and SHOULD have low cardinality.\n  Instrumentations SHOULD document the list of errors they report.\n  \n  The cardinality of `error.type` within one instrumentation library SHOULD be low.\n  Telemetry consumers that aggregate data from multiple instrumentation libraries and applications\n  should be prepared for `error.type` to have high cardinality at query time when no\n  additional filters are applied.\n  \n  If the operation has completed successfully, instrumentations SHOULD NOT set `error.type`.\n  \n  If a specific domain defines its own set of error identifiers (such as HTTP or gRPC status codes),\n  it&#39;s RECOMMENDED to:\n  \n  * Use a domain-specific attribute\n  * Set `error.type` to capture all errors, regardless of whether they are defined within the domain-specific set or not.\n    */\n    ERROR_TYPE: 'error.type',\n    /**\n     * The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n     */\n    HTTP_REQUEST_BODY_SIZE: 'http.request.body.size',\n    /**\n    * HTTP request method.\n    *\n    * Note: HTTP request method value SHOULD be &#34;known&#34; to the instrumentation.\n  By default, this convention defines &#34;known&#34; methods as the ones listed in [RFC9110](https://www.rfc-editor.org/rfc/rfc9110.html#name-methods)\n  and the PATCH method defined in [RFC5789](https://www.rfc-editor.org/rfc/rfc5789.html).\n  \n  If the HTTP request method is not known to instrumentation, it MUST set the `http.request.method` attribute to `_OTHER`.\n  \n  If the HTTP instrumentation could end up converting valid HTTP request methods to `_OTHER`, then it MUST provide a way to override\n  the list of known HTTP methods. If this override is done via environment variable, then the environment variable MUST be named\n  OTEL_INSTRUMENTATION_HTTP_KNOWN_METHODS and support a comma-separated list of case-sensitive known HTTP methods\n  (this list MUST be a full override of the default known method, it is not a list of known methods in addition to the defaults).\n  \n  HTTP method names are case-sensitive and `http.request.method` attribute value MUST match a known HTTP method name exactly.\n  Instrumentations for specific web frameworks that consider HTTP methods to be case insensitive, SHOULD populate a canonical equivalent.\n  Tracing instrumentations that do so, MUST also set `http.request.method_original` to the original value.\n    */\n    HTTP_REQUEST_METHOD: 'http.request.method',\n    /**\n     * Original HTTP method sent by the client in the request line.\n     */\n    HTTP_REQUEST_METHOD_ORIGINAL: 'http.request.method_original',\n    /**\n     * The ordinal number of request resending attempt (for any reason, including redirects).\n     *\n     * Note: The resend count SHOULD be updated each time an HTTP request gets resent by the client, regardless of what was the cause of the resending (e.g. redirection, authorization failure, 503 Server Unavailable, network issues, or any other).\n     */\n    HTTP_REQUEST_RESEND_COUNT: 'http.request.resend_count',\n    /**\n     * The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n     */\n    HTTP_RESPONSE_BODY_SIZE: 'http.response.body.size',\n    /**\n     * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).\n     */\n    HTTP_RESPONSE_STATUS_CODE: 'http.response.status_code',\n    /**\n    * The matched route, that is, the path template in the format used by the respective server framework.\n    *\n    * Note: MUST NOT be populated when this is not supported by the HTTP server framework as the route attribute should have low-cardinality and the URI path can NOT substitute it.\n  SHOULD include the [application root](/docs/http/http-spans.md#http-server-definitions) if there is one.\n    */\n    HTTP_ROUTE: 'http.route',\n    /**\n     * Peer address of the network connection - IP address or Unix domain socket name.\n     */\n    NETWORK_PEER_ADDRESS: 'network.peer.address',\n    /**\n     * Peer port number of the network connection.\n     */\n    NETWORK_PEER_PORT: 'network.peer.port',\n    /**\n     * [OSI application layer](https://osi-model.com/application-layer/) or non-OSI equivalent.\n     *\n     * Note: The value SHOULD be normalized to lowercase.\n     */\n    NETWORK_PROTOCOL_NAME: 'network.protocol.name',\n    /**\n     * Version of the protocol specified in `network.protocol.name`.\n     *\n     * Note: `network.protocol.version` refers to the version of the protocol used and might be different from the protocol client&#39;s version. If the HTTP client has a version of `0.27.2`, but sends HTTP version `1.1`, this attribute should be set to `1.1`.\n     */\n    NETWORK_PROTOCOL_VERSION: 'network.protocol.version',\n    /**\n     * Server domain name if available without reverse DNS lookup; otherwise, IP address or Unix domain socket name.\n     *\n     * Note: When observed from the client side, and when communicating through an intermediary, `server.address` SHOULD represent the server address behind any intermediaries, for example proxies, if it&#39;s available.\n     */\n    SERVER_ADDRESS: 'server.address',\n    /**\n     * Server port number.\n     *\n     * Note: When observed from the client side, and when communicating through an intermediary, `server.port` SHOULD represent the server port behind any intermediaries, for example proxies, if it&#39;s available.\n     */\n    SERVER_PORT: 'server.port',\n    /**\n    * Absolute URL describing a network resource according to [RFC3986](https://www.rfc-editor.org/rfc/rfc3986).\n    *\n    * Note: For network calls, URL usually has `scheme://host[:port][path][?query][#fragment]` format, where the fragment is not transmitted over HTTP, but if it is known, it SHOULD be included nevertheless.\n  `url.full` MUST NOT contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case username and password SHOULD be redacted and attribute&#39;s value SHOULD be `https://REDACTED:<EMAIL>/`.\n  `url.full` SHOULD capture the absolute URL when it is available (or can be reconstructed) and SHOULD NOT be validated or modified except for sanitizing purposes.\n    */\n    URL_FULL: 'url.full',\n    /**\n     * The [URI path](https://www.rfc-editor.org/rfc/rfc3986#section-3.3) component.\n     */\n    URL_PATH: 'url.path',\n    /**\n     * The [URI query](https://www.rfc-editor.org/rfc/rfc3986#section-3.4) component.\n     *\n     * Note: Sensitive content provided in query string SHOULD be scrubbed when instrumentations can identify it.\n     */\n    URL_QUERY: 'url.query',\n    /**\n     * The [URI scheme](https://www.rfc-editor.org/rfc/rfc3986#section-3.1) component identifying the used protocol.\n     */\n    URL_SCHEME: 'url.scheme',\n    /**\n     * Value of the [HTTP User-Agent](https://www.rfc-editor.org/rfc/rfc9110.html#field.user-agent) header sent by the client.\n     */\n    USER_AGENT_ORIGINAL: 'user_agent.original',\n};\n//# sourceMappingURL=SemanticAttributes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./undici */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfZWE0MjAyM2ZlYjYzMGNmODFmOTdiZGNlMThhNDZlOTQvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi11bmRpY2kvYnVpbGQvc3JjL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2VhNDIwMjNmZWI2MzBjZjgxZjk3YmRjZTE4YTQ2ZTk0XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tdW5kaWNpXFxidWlsZFxcc3JjXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.UndiciInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst diagch = __webpack_require__(/*! diagnostics_channel */ \"diagnostics_channel\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js\");\nconst SemanticAttributes_1 = __webpack_require__(/*! ./enums/SemanticAttributes */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(instrument)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\n// A combination of https://github.com/elastic/apm-agent-nodejs and\n// https://github.com/gadget-inc/opentelemetry-instrumentations/blob/main/packages/opentelemetry-instrumentation-undici/src/index.ts\nclass UndiciInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        this._recordFromReq = new WeakMap();\n    }\n    // No need to instrument files/modules\n    init() {\n        return undefined;\n    }\n    disable() {\n        super.disable();\n        this._channelSubs.forEach(sub => sub.unsubscribe());\n        this._channelSubs.length = 0;\n    }\n    enable() {\n        // \"enabled\" handling is currently a bit messy with InstrumentationBase.\n        // If constructed with `{enabled: false}`, this `.enable()` is still called,\n        // and `this.getConfig().enabled !== this.isEnabled()`, creating confusion.\n        //\n        // For now, this class will setup for instrumenting if `.enable()` is\n        // called, but use `this.getConfig().enabled` to determine if\n        // instrumentation should be generated. This covers the more likely common\n        // case of config being given a construction time, rather than later via\n        // `instance.enable()`, `.disable()`, or `.setConfig()` calls.\n        super.enable();\n        // This method is called by the super-class constructor before ours is\n        // called. So we need to ensure the property is initalized.\n        this._channelSubs = this._channelSubs || [];\n        // Avoid to duplicate subscriptions\n        if (this._channelSubs.length > 0) {\n            return;\n        }\n        this.subscribeToChannel('undici:request:create', this.onRequestCreated.bind(this));\n        this.subscribeToChannel('undici:client:sendHeaders', this.onRequestHeaders.bind(this));\n        this.subscribeToChannel('undici:request:headers', this.onResponseHeaders.bind(this));\n        this.subscribeToChannel('undici:request:trailers', this.onDone.bind(this));\n        this.subscribeToChannel('undici:request:error', this.onError.bind(this));\n    }\n    _updateMetricInstruments() {\n        this._httpClientDurationHistogram = this.meter.createHistogram('http.client.request.duration', {\n            description: 'Measures the duration of outbound HTTP requests.',\n            unit: 's',\n            valueType: api_1.ValueType.DOUBLE,\n            advice: {\n                explicitBucketBoundaries: [\n                    0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1, 2.5, 5,\n                    7.5, 10,\n                ],\n            },\n        });\n    }\n    subscribeToChannel(diagnosticChannel, onMessage) {\n        var _a;\n        // `diagnostics_channel` had a ref counting bug until v18.19.0.\n        // https://github.com/nodejs/node/pull/47520\n        const [major, minor] = process.version\n            .replace('v', '')\n            .split('.')\n            .map(n => Number(n));\n        const useNewSubscribe = major > 18 || (major === 18 && minor >= 19);\n        let unsubscribe;\n        if (useNewSubscribe) {\n            (_a = diagch.subscribe) === null || _a === void 0 ? void 0 : _a.call(diagch, diagnosticChannel, onMessage);\n            unsubscribe = () => { var _a; return (_a = diagch.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(diagch, diagnosticChannel, onMessage); };\n        }\n        else {\n            const channel = diagch.channel(diagnosticChannel);\n            channel.subscribe(onMessage);\n            unsubscribe = () => channel.unsubscribe(onMessage);\n        }\n        this._channelSubs.push({\n            name: diagnosticChannel,\n            unsubscribe,\n        });\n    }\n    // This is the 1st message we receive for each request (fired after request creation). Here we will\n    // create the span and populate some atttributes, then link the span to the request for further\n    // span processing\n    onRequestCreated({ request }) {\n        // Ignore if:\n        // - instrumentation is disabled\n        // - ignored by config\n        // - method is 'CONNECT'\n        const config = this.getConfig();\n        const enabled = config.enabled !== false;\n        const shouldIgnoreReq = (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            var _a;\n            return !enabled ||\n                request.method === 'CONNECT' ||\n                ((_a = config.ignoreRequestHook) === null || _a === void 0 ? void 0 : _a.call(config, request));\n        }, e => e && this._diag.error('caught ignoreRequestHook error: ', e), true);\n        if (shouldIgnoreReq) {\n            return;\n        }\n        const startTime = (0, core_1.hrTime)();\n        let requestUrl;\n        try {\n            requestUrl = new url_1.URL(request.path, request.origin);\n        }\n        catch (err) {\n            this._diag.warn('could not determine url.full:', err);\n            // Skip instrumenting this request.\n            return;\n        }\n        const urlScheme = requestUrl.protocol.replace(':', '');\n        const requestMethod = this.getRequestMethod(request.method);\n        const attributes = {\n            [SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD]: requestMethod,\n            [SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD_ORIGINAL]: request.method,\n            [SemanticAttributes_1.SemanticAttributes.URL_FULL]: requestUrl.toString(),\n            [SemanticAttributes_1.SemanticAttributes.URL_PATH]: requestUrl.pathname,\n            [SemanticAttributes_1.SemanticAttributes.URL_QUERY]: requestUrl.search,\n            [SemanticAttributes_1.SemanticAttributes.URL_SCHEME]: urlScheme,\n        };\n        const schemePorts = { https: '443', http: '80' };\n        const serverAddress = requestUrl.hostname;\n        const serverPort = requestUrl.port || schemePorts[urlScheme];\n        attributes[SemanticAttributes_1.SemanticAttributes.SERVER_ADDRESS] = serverAddress;\n        if (serverPort && !isNaN(Number(serverPort))) {\n            attributes[SemanticAttributes_1.SemanticAttributes.SERVER_PORT] = Number(serverPort);\n        }\n        // Get user agent from headers\n        let userAgent;\n        if (Array.isArray(request.headers)) {\n            const idx = request.headers.findIndex(h => h.toLowerCase() === 'user-agent');\n            if (idx >= 0) {\n                userAgent = request.headers[idx + 1];\n            }\n        }\n        else if (typeof request.headers === 'string') {\n            const headers = request.headers.split('\\r\\n');\n            const uaHeader = headers.find(h => h.toLowerCase().startsWith('user-agent'));\n            userAgent =\n                uaHeader && uaHeader.substring(uaHeader.indexOf(':') + 1).trim();\n        }\n        if (userAgent) {\n            attributes[SemanticAttributes_1.SemanticAttributes.USER_AGENT_ORIGINAL] = userAgent;\n        }\n        // Get attributes from the hook if present\n        const hookAttributes = (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.startSpanHook) === null || _a === void 0 ? void 0 : _a.call(config, request); }, e => e && this._diag.error('caught startSpanHook error: ', e), true);\n        if (hookAttributes) {\n            Object.entries(hookAttributes).forEach(([key, val]) => {\n                attributes[key] = val;\n            });\n        }\n        // Check if parent span is required via config and:\n        // - if a parent is required but not present, we use a `NoopSpan` to still\n        //   propagate context without recording it.\n        // - create a span otherwise\n        const activeCtx = api_1.context.active();\n        const currentSpan = api_1.trace.getSpan(activeCtx);\n        let span;\n        if (config.requireParentforSpans &&\n            (!currentSpan || !api_1.trace.isSpanContextValid(currentSpan.spanContext()))) {\n            span = api_1.trace.wrapSpanContext(api_1.INVALID_SPAN_CONTEXT);\n        }\n        else {\n            span = this.tracer.startSpan(requestMethod === '_OTHER' ? 'HTTP' : requestMethod, {\n                kind: api_1.SpanKind.CLIENT,\n                attributes: attributes,\n            }, activeCtx);\n        }\n        // Execute the request hook if defined\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.requestHook) === null || _a === void 0 ? void 0 : _a.call(config, span, request); }, e => e && this._diag.error('caught requestHook error: ', e), true);\n        // Context propagation goes last so no hook can tamper\n        // the propagation headers\n        const requestContext = api_1.trace.setSpan(api_1.context.active(), span);\n        const addedHeaders = {};\n        api_1.propagation.inject(requestContext, addedHeaders);\n        const headerEntries = Object.entries(addedHeaders);\n        for (let i = 0; i < headerEntries.length; i++) {\n            const [k, v] = headerEntries[i];\n            if (typeof request.addHeader === 'function') {\n                request.addHeader(k, v);\n            }\n            else if (typeof request.headers === 'string') {\n                request.headers += `${k}: ${v}\\r\\n`;\n            }\n            else if (Array.isArray(request.headers)) {\n                // undici@6.11.0 accidentally, briefly removed `request.addHeader()`.\n                request.headers.push(k, v);\n            }\n        }\n        this._recordFromReq.set(request, { span, attributes, startTime });\n    }\n    // This is the 2nd message we receive for each request. It is fired when connection with\n    // the remote is established and about to send the first byte. Here we do have info about the\n    // remote address and port so we can populate some `network.*` attributes into the span\n    onRequestHeaders({ request, socket }) {\n        var _a;\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const config = this.getConfig();\n        const { span } = record;\n        const { remoteAddress, remotePort } = socket;\n        const spanAttributes = {\n            [SemanticAttributes_1.SemanticAttributes.NETWORK_PEER_ADDRESS]: remoteAddress,\n            [SemanticAttributes_1.SemanticAttributes.NETWORK_PEER_PORT]: remotePort,\n        };\n        // After hooks have been processed (which may modify request headers)\n        // we can collect the headers based on the configuration\n        if ((_a = config.headersToSpanAttributes) === null || _a === void 0 ? void 0 : _a.requestHeaders) {\n            const headersToAttribs = new Set(config.headersToSpanAttributes.requestHeaders.map(n => n.toLowerCase()));\n            // headers could be in form\n            // ['name: value', ...] for v5\n            // ['name', 'value', ...] for v6\n            const rawHeaders = Array.isArray(request.headers)\n                ? request.headers\n                : request.headers.split('\\r\\n');\n            rawHeaders.forEach((h, idx) => {\n                const sepIndex = h.indexOf(':');\n                const hasSeparator = sepIndex !== -1;\n                const name = (hasSeparator ? h.substring(0, sepIndex) : h).toLowerCase();\n                const value = hasSeparator\n                    ? h.substring(sepIndex + 1)\n                    : rawHeaders[idx + 1];\n                if (headersToAttribs.has(name)) {\n                    spanAttributes[`http.request.header.${name}`] = value.trim();\n                }\n            });\n        }\n        span.setAttributes(spanAttributes);\n    }\n    // This is the 3rd message we get for each request and it's fired when the server\n    // headers are received, body may not be accessible yet.\n    // From the response headers we can set the status and content length\n    onResponseHeaders({ request, response, }) {\n        var _a, _b;\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes } = record;\n        const spanAttributes = {\n            [SemanticAttributes_1.SemanticAttributes.HTTP_RESPONSE_STATUS_CODE]: response.statusCode,\n        };\n        const config = this.getConfig();\n        // Execute the response hook if defined\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.responseHook) === null || _a === void 0 ? void 0 : _a.call(config, span, { request, response }); }, e => e && this._diag.error('caught responseHook error: ', e), true);\n        const headersToAttribs = new Set();\n        if ((_a = config.headersToSpanAttributes) === null || _a === void 0 ? void 0 : _a.responseHeaders) {\n            (_b = config.headersToSpanAttributes) === null || _b === void 0 ? void 0 : _b.responseHeaders.forEach(name => headersToAttribs.add(name.toLowerCase()));\n        }\n        for (let idx = 0; idx < response.headers.length; idx = idx + 2) {\n            const name = response.headers[idx].toString().toLowerCase();\n            const value = response.headers[idx + 1];\n            if (headersToAttribs.has(name)) {\n                spanAttributes[`http.response.header.${name}`] = value.toString();\n            }\n            if (name === 'content-length') {\n                const contentLength = Number(value.toString());\n                if (!isNaN(contentLength)) {\n                    spanAttributes['http.response.header.content-length'] = contentLength;\n                }\n            }\n        }\n        span.setAttributes(spanAttributes);\n        span.setStatus({\n            code: response.statusCode >= 400\n                ? api_1.SpanStatusCode.ERROR\n                : api_1.SpanStatusCode.UNSET,\n        });\n        record.attributes = Object.assign(attributes, spanAttributes);\n    }\n    // This is the last event we receive if the request went without any errors\n    onDone({ request }) {\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes, startTime } = record;\n        // End the span\n        span.end();\n        this._recordFromReq.delete(request);\n        // Record metrics\n        this.recordRequestDuration(attributes, startTime);\n    }\n    // This is the event we get when something is wrong in the request like\n    // - invalid options when calling `fetch` global API or any undici method for request\n    // - connectivity errors such as unreachable host\n    // - requests aborted through an `AbortController.signal`\n    // NOTE: server errors are considered valid responses and it's the lib consumer\n    // who should deal with that.\n    onError({ request, error }) {\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes, startTime } = record;\n        // NOTE: in `undici@6.3.0` when request aborted the error type changes from\n        // a custom error (`RequestAbortedError`) to a built-in `DOMException` carrying\n        // some differences:\n        // - `code` is from DOMEXception (ABORT_ERR: 20)\n        // - `message` changes\n        // - stacktrace is smaller and contains node internal frames\n        span.recordException(error);\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: error.message,\n        });\n        span.end();\n        this._recordFromReq.delete(request);\n        // Record metrics (with the error)\n        attributes[SemanticAttributes_1.SemanticAttributes.ERROR_TYPE] = error.message;\n        this.recordRequestDuration(attributes, startTime);\n    }\n    recordRequestDuration(attributes, startTime) {\n        // Time to record metrics\n        const metricsAttributes = {};\n        // Get the attribs already in span attributes\n        const keysToCopy = [\n            SemanticAttributes_1.SemanticAttributes.HTTP_RESPONSE_STATUS_CODE,\n            SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD,\n            SemanticAttributes_1.SemanticAttributes.SERVER_ADDRESS,\n            SemanticAttributes_1.SemanticAttributes.SERVER_PORT,\n            SemanticAttributes_1.SemanticAttributes.URL_SCHEME,\n            SemanticAttributes_1.SemanticAttributes.ERROR_TYPE,\n        ];\n        keysToCopy.forEach(key => {\n            if (key in attributes) {\n                metricsAttributes[key] = attributes[key];\n            }\n        });\n        // Take the duration and record it\n        const durationSeconds = (0, core_1.hrTimeToMilliseconds)((0, core_1.hrTimeDuration)(startTime, (0, core_1.hrTime)())) / 1000;\n        this._httpClientDurationHistogram.record(durationSeconds, metricsAttributes);\n    }\n    getRequestMethod(original) {\n        const knownMethods = {\n            CONNECT: true,\n            OPTIONS: true,\n            HEAD: true,\n            GET: true,\n            POST: true,\n            PUT: true,\n            PATCH: true,\n            DELETE: true,\n            TRACE: true,\n        };\n        if (original.toUpperCase() in knownMethods) {\n            return original.toUpperCase();\n        }\n        return '_OTHER';\n    }\n}\nexports.UndiciInstrumentation = UndiciInstrumentation;\n//# sourceMappingURL=undici.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGluc3RydW1lbnQpLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ab3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfZWE0MjAyM2ZlYjYzMGNmODFmOTdiZGNlMThhNDZlOTQvbm9kZV9tb2R1bGVzL0BvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvbi11bmRpY2kvYnVpbGQvc3JjL3VuZGljaS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLGdEQUFxQjtBQUM1QyxjQUFjLG1CQUFPLENBQUMsZ0JBQUs7QUFDM0IsMEJBQTBCLG1CQUFPLENBQUMseU1BQWdDO0FBQ2xFLGNBQWMsbUJBQU8sQ0FBQyw2SUFBb0I7QUFDMUM7QUFDQSxrQkFBa0IsbUJBQU8sQ0FBQyw2TEFBVztBQUNyQyw2QkFBNkIsbUJBQU8sQ0FBQywrTkFBNEI7QUFDakUsZUFBZSxtQkFBTyxDQUFDLDBLQUFxQjtBQUM1QztBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQyxlQUFlO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxRQUFRO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixTQUFTO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxRkFBcUYsUUFBUSxtR0FBbUc7QUFDaE07QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSw4REFBOEQsUUFBUSx1R0FBdUc7QUFDN0s7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLDBCQUEwQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLEVBQUUsSUFBSSxFQUFFO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyw2QkFBNkI7QUFDeEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsaUJBQWlCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixPQUFPO0FBQ3ZCLGdCQUFnQiw0QkFBNEI7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwREFBMEQsS0FBSztBQUMvRDtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isb0JBQW9CO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUJBQW1CO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4REFBOEQsUUFBUSwrRkFBK0YsbUJBQW1CLElBQUk7QUFDNUw7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsK0JBQStCO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RCxLQUFLO0FBQzVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLGFBQWEsU0FBUztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiw4QkFBOEI7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxnQkFBZ0I7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsOEJBQThCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFud2FyXFxEb2N1bWVudHNcXDIgRk9MREVSUyBGT1IgQ1VCRU5UXFxDdWJlbnR3ZWJcXGN1YmVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQG9wZW50ZWxlbWV0cnkraW5zdHJ1bWVudGF0X2VhNDIwMjNmZWI2MzBjZjgxZjk3YmRjZTE4YTQ2ZTk0XFxub2RlX21vZHVsZXNcXEBvcGVudGVsZW1ldHJ5XFxpbnN0cnVtZW50YXRpb24tdW5kaWNpXFxidWlsZFxcc3JjXFx1bmRpY2kuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlVuZGljaUluc3RydW1lbnRhdGlvbiA9IHZvaWQgMDtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5jb25zdCBkaWFnY2ggPSByZXF1aXJlKFwiZGlhZ25vc3RpY3NfY2hhbm5lbFwiKTtcbmNvbnN0IHVybF8xID0gcmVxdWlyZShcInVybFwiKTtcbmNvbnN0IGluc3RydW1lbnRhdGlvbl8xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L2luc3RydW1lbnRhdGlvblwiKTtcbmNvbnN0IGFwaV8xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L2FwaVwiKTtcbi8qKiBAa25pcGlnbm9yZSAqL1xuY29uc3QgdmVyc2lvbl8xID0gcmVxdWlyZShcIi4vdmVyc2lvblwiKTtcbmNvbnN0IFNlbWFudGljQXR0cmlidXRlc18xID0gcmVxdWlyZShcIi4vZW51bXMvU2VtYW50aWNBdHRyaWJ1dGVzXCIpO1xuY29uc3QgY29yZV8xID0gcmVxdWlyZShcIkBvcGVudGVsZW1ldHJ5L2NvcmVcIik7XG4vLyBBIGNvbWJpbmF0aW9uIG9mIGh0dHBzOi8vZ2l0aHViLmNvbS9lbGFzdGljL2FwbS1hZ2VudC1ub2RlanMgYW5kXG4vLyBodHRwczovL2dpdGh1Yi5jb20vZ2FkZ2V0LWluYy9vcGVudGVsZW1ldHJ5LWluc3RydW1lbnRhdGlvbnMvYmxvYi9tYWluL3BhY2thZ2VzL29wZW50ZWxlbWV0cnktaW5zdHJ1bWVudGF0aW9uLXVuZGljaS9zcmMvaW5kZXgudHNcbmNsYXNzIFVuZGljaUluc3RydW1lbnRhdGlvbiBleHRlbmRzIGluc3RydW1lbnRhdGlvbl8xLkluc3RydW1lbnRhdGlvbkJhc2Uge1xuICAgIGNvbnN0cnVjdG9yKGNvbmZpZyA9IHt9KSB7XG4gICAgICAgIHN1cGVyKHZlcnNpb25fMS5QQUNLQUdFX05BTUUsIHZlcnNpb25fMS5QQUNLQUdFX1ZFUlNJT04sIGNvbmZpZyk7XG4gICAgICAgIHRoaXMuX3JlY29yZEZyb21SZXEgPSBuZXcgV2Vha01hcCgpO1xuICAgIH1cbiAgICAvLyBObyBuZWVkIHRvIGluc3RydW1lbnQgZmlsZXMvbW9kdWxlc1xuICAgIGluaXQoKSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfVxuICAgIGRpc2FibGUoKSB7XG4gICAgICAgIHN1cGVyLmRpc2FibGUoKTtcbiAgICAgICAgdGhpcy5fY2hhbm5lbFN1YnMuZm9yRWFjaChzdWIgPT4gc3ViLnVuc3Vic2NyaWJlKCkpO1xuICAgICAgICB0aGlzLl9jaGFubmVsU3Vicy5sZW5ndGggPSAwO1xuICAgIH1cbiAgICBlbmFibGUoKSB7XG4gICAgICAgIC8vIFwiZW5hYmxlZFwiIGhhbmRsaW5nIGlzIGN1cnJlbnRseSBhIGJpdCBtZXNzeSB3aXRoIEluc3RydW1lbnRhdGlvbkJhc2UuXG4gICAgICAgIC8vIElmIGNvbnN0cnVjdGVkIHdpdGggYHtlbmFibGVkOiBmYWxzZX1gLCB0aGlzIGAuZW5hYmxlKClgIGlzIHN0aWxsIGNhbGxlZCxcbiAgICAgICAgLy8gYW5kIGB0aGlzLmdldENvbmZpZygpLmVuYWJsZWQgIT09IHRoaXMuaXNFbmFibGVkKClgLCBjcmVhdGluZyBjb25mdXNpb24uXG4gICAgICAgIC8vXG4gICAgICAgIC8vIEZvciBub3csIHRoaXMgY2xhc3Mgd2lsbCBzZXR1cCBmb3IgaW5zdHJ1bWVudGluZyBpZiBgLmVuYWJsZSgpYCBpc1xuICAgICAgICAvLyBjYWxsZWQsIGJ1dCB1c2UgYHRoaXMuZ2V0Q29uZmlnKCkuZW5hYmxlZGAgdG8gZGV0ZXJtaW5lIGlmXG4gICAgICAgIC8vIGluc3RydW1lbnRhdGlvbiBzaG91bGQgYmUgZ2VuZXJhdGVkLiBUaGlzIGNvdmVycyB0aGUgbW9yZSBsaWtlbHkgY29tbW9uXG4gICAgICAgIC8vIGNhc2Ugb2YgY29uZmlnIGJlaW5nIGdpdmVuIGEgY29uc3RydWN0aW9uIHRpbWUsIHJhdGhlciB0aGFuIGxhdGVyIHZpYVxuICAgICAgICAvLyBgaW5zdGFuY2UuZW5hYmxlKClgLCBgLmRpc2FibGUoKWAsIG9yIGAuc2V0Q29uZmlnKClgIGNhbGxzLlxuICAgICAgICBzdXBlci5lbmFibGUoKTtcbiAgICAgICAgLy8gVGhpcyBtZXRob2QgaXMgY2FsbGVkIGJ5IHRoZSBzdXBlci1jbGFzcyBjb25zdHJ1Y3RvciBiZWZvcmUgb3VycyBpc1xuICAgICAgICAvLyBjYWxsZWQuIFNvIHdlIG5lZWQgdG8gZW5zdXJlIHRoZSBwcm9wZXJ0eSBpcyBpbml0YWxpemVkLlxuICAgICAgICB0aGlzLl9jaGFubmVsU3VicyA9IHRoaXMuX2NoYW5uZWxTdWJzIHx8IFtdO1xuICAgICAgICAvLyBBdm9pZCB0byBkdXBsaWNhdGUgc3Vic2NyaXB0aW9uc1xuICAgICAgICBpZiAodGhpcy5fY2hhbm5lbFN1YnMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuc3Vic2NyaWJlVG9DaGFubmVsKCd1bmRpY2k6cmVxdWVzdDpjcmVhdGUnLCB0aGlzLm9uUmVxdWVzdENyZWF0ZWQuYmluZCh0aGlzKSk7XG4gICAgICAgIHRoaXMuc3Vic2NyaWJlVG9DaGFubmVsKCd1bmRpY2k6Y2xpZW50OnNlbmRIZWFkZXJzJywgdGhpcy5vblJlcXVlc3RIZWFkZXJzLmJpbmQodGhpcykpO1xuICAgICAgICB0aGlzLnN1YnNjcmliZVRvQ2hhbm5lbCgndW5kaWNpOnJlcXVlc3Q6aGVhZGVycycsIHRoaXMub25SZXNwb25zZUhlYWRlcnMuYmluZCh0aGlzKSk7XG4gICAgICAgIHRoaXMuc3Vic2NyaWJlVG9DaGFubmVsKCd1bmRpY2k6cmVxdWVzdDp0cmFpbGVycycsIHRoaXMub25Eb25lLmJpbmQodGhpcykpO1xuICAgICAgICB0aGlzLnN1YnNjcmliZVRvQ2hhbm5lbCgndW5kaWNpOnJlcXVlc3Q6ZXJyb3InLCB0aGlzLm9uRXJyb3IuYmluZCh0aGlzKSk7XG4gICAgfVxuICAgIF91cGRhdGVNZXRyaWNJbnN0cnVtZW50cygpIHtcbiAgICAgICAgdGhpcy5faHR0cENsaWVudER1cmF0aW9uSGlzdG9ncmFtID0gdGhpcy5tZXRlci5jcmVhdGVIaXN0b2dyYW0oJ2h0dHAuY2xpZW50LnJlcXVlc3QuZHVyYXRpb24nLCB7XG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogJ01lYXN1cmVzIHRoZSBkdXJhdGlvbiBvZiBvdXRib3VuZCBIVFRQIHJlcXVlc3RzLicsXG4gICAgICAgICAgICB1bml0OiAncycsXG4gICAgICAgICAgICB2YWx1ZVR5cGU6IGFwaV8xLlZhbHVlVHlwZS5ET1VCTEUsXG4gICAgICAgICAgICBhZHZpY2U6IHtcbiAgICAgICAgICAgICAgICBleHBsaWNpdEJ1Y2tldEJvdW5kYXJpZXM6IFtcbiAgICAgICAgICAgICAgICAgICAgMC4wMDUsIDAuMDEsIDAuMDI1LCAwLjA1LCAwLjA3NSwgMC4xLCAwLjI1LCAwLjUsIDAuNzUsIDEsIDIuNSwgNSxcbiAgICAgICAgICAgICAgICAgICAgNy41LCAxMCxcbiAgICAgICAgICAgICAgICBdLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHN1YnNjcmliZVRvQ2hhbm5lbChkaWFnbm9zdGljQ2hhbm5lbCwgb25NZXNzYWdlKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgLy8gYGRpYWdub3N0aWNzX2NoYW5uZWxgIGhhZCBhIHJlZiBjb3VudGluZyBidWcgdW50aWwgdjE4LjE5LjAuXG4gICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9ub2RlanMvbm9kZS9wdWxsLzQ3NTIwXG4gICAgICAgIGNvbnN0IFttYWpvciwgbWlub3JdID0gcHJvY2Vzcy52ZXJzaW9uXG4gICAgICAgICAgICAucmVwbGFjZSgndicsICcnKVxuICAgICAgICAgICAgLnNwbGl0KCcuJylcbiAgICAgICAgICAgIC5tYXAobiA9PiBOdW1iZXIobikpO1xuICAgICAgICBjb25zdCB1c2VOZXdTdWJzY3JpYmUgPSBtYWpvciA+IDE4IHx8IChtYWpvciA9PT0gMTggJiYgbWlub3IgPj0gMTkpO1xuICAgICAgICBsZXQgdW5zdWJzY3JpYmU7XG4gICAgICAgIGlmICh1c2VOZXdTdWJzY3JpYmUpIHtcbiAgICAgICAgICAgIChfYSA9IGRpYWdjaC5zdWJzY3JpYmUpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYWxsKGRpYWdjaCwgZGlhZ25vc3RpY0NoYW5uZWwsIG9uTWVzc2FnZSk7XG4gICAgICAgICAgICB1bnN1YnNjcmliZSA9ICgpID0+IHsgdmFyIF9hOyByZXR1cm4gKF9hID0gZGlhZ2NoLnVuc3Vic2NyaWJlKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbChkaWFnY2gsIGRpYWdub3N0aWNDaGFubmVsLCBvbk1lc3NhZ2UpOyB9O1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgY29uc3QgY2hhbm5lbCA9IGRpYWdjaC5jaGFubmVsKGRpYWdub3N0aWNDaGFubmVsKTtcbiAgICAgICAgICAgIGNoYW5uZWwuc3Vic2NyaWJlKG9uTWVzc2FnZSk7XG4gICAgICAgICAgICB1bnN1YnNjcmliZSA9ICgpID0+IGNoYW5uZWwudW5zdWJzY3JpYmUob25NZXNzYWdlKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9jaGFubmVsU3Vicy5wdXNoKHtcbiAgICAgICAgICAgIG5hbWU6IGRpYWdub3N0aWNDaGFubmVsLFxuICAgICAgICAgICAgdW5zdWJzY3JpYmUsXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICAvLyBUaGlzIGlzIHRoZSAxc3QgbWVzc2FnZSB3ZSByZWNlaXZlIGZvciBlYWNoIHJlcXVlc3QgKGZpcmVkIGFmdGVyIHJlcXVlc3QgY3JlYXRpb24pLiBIZXJlIHdlIHdpbGxcbiAgICAvLyBjcmVhdGUgdGhlIHNwYW4gYW5kIHBvcHVsYXRlIHNvbWUgYXR0dHJpYnV0ZXMsIHRoZW4gbGluayB0aGUgc3BhbiB0byB0aGUgcmVxdWVzdCBmb3IgZnVydGhlclxuICAgIC8vIHNwYW4gcHJvY2Vzc2luZ1xuICAgIG9uUmVxdWVzdENyZWF0ZWQoeyByZXF1ZXN0IH0pIHtcbiAgICAgICAgLy8gSWdub3JlIGlmOlxuICAgICAgICAvLyAtIGluc3RydW1lbnRhdGlvbiBpcyBkaXNhYmxlZFxuICAgICAgICAvLyAtIGlnbm9yZWQgYnkgY29uZmlnXG4gICAgICAgIC8vIC0gbWV0aG9kIGlzICdDT05ORUNUJ1xuICAgICAgICBjb25zdCBjb25maWcgPSB0aGlzLmdldENvbmZpZygpO1xuICAgICAgICBjb25zdCBlbmFibGVkID0gY29uZmlnLmVuYWJsZWQgIT09IGZhbHNlO1xuICAgICAgICBjb25zdCBzaG91bGRJZ25vcmVSZXEgPSAoMCwgaW5zdHJ1bWVudGF0aW9uXzEuc2FmZUV4ZWN1dGVJblRoZU1pZGRsZSkoKCkgPT4ge1xuICAgICAgICAgICAgdmFyIF9hO1xuICAgICAgICAgICAgcmV0dXJuICFlbmFibGVkIHx8XG4gICAgICAgICAgICAgICAgcmVxdWVzdC5tZXRob2QgPT09ICdDT05ORUNUJyB8fFxuICAgICAgICAgICAgICAgICgoX2EgPSBjb25maWcuaWdub3JlUmVxdWVzdEhvb2spID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYWxsKGNvbmZpZywgcmVxdWVzdCkpO1xuICAgICAgICB9LCBlID0+IGUgJiYgdGhpcy5fZGlhZy5lcnJvcignY2F1Z2h0IGlnbm9yZVJlcXVlc3RIb29rIGVycm9yOiAnLCBlKSwgdHJ1ZSk7XG4gICAgICAgIGlmIChzaG91bGRJZ25vcmVSZXEpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBzdGFydFRpbWUgPSAoMCwgY29yZV8xLmhyVGltZSkoKTtcbiAgICAgICAgbGV0IHJlcXVlc3RVcmw7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXF1ZXN0VXJsID0gbmV3IHVybF8xLlVSTChyZXF1ZXN0LnBhdGgsIHJlcXVlc3Qub3JpZ2luKTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZXJyKSB7XG4gICAgICAgICAgICB0aGlzLl9kaWFnLndhcm4oJ2NvdWxkIG5vdCBkZXRlcm1pbmUgdXJsLmZ1bGw6JywgZXJyKTtcbiAgICAgICAgICAgIC8vIFNraXAgaW5zdHJ1bWVudGluZyB0aGlzIHJlcXVlc3QuXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgdXJsU2NoZW1lID0gcmVxdWVzdFVybC5wcm90b2NvbC5yZXBsYWNlKCc6JywgJycpO1xuICAgICAgICBjb25zdCByZXF1ZXN0TWV0aG9kID0gdGhpcy5nZXRSZXF1ZXN0TWV0aG9kKHJlcXVlc3QubWV0aG9kKTtcbiAgICAgICAgY29uc3QgYXR0cmlidXRlcyA9IHtcbiAgICAgICAgICAgIFtTZW1hbnRpY0F0dHJpYnV0ZXNfMS5TZW1hbnRpY0F0dHJpYnV0ZXMuSFRUUF9SRVFVRVNUX01FVEhPRF06IHJlcXVlc3RNZXRob2QsXG4gICAgICAgICAgICBbU2VtYW50aWNBdHRyaWJ1dGVzXzEuU2VtYW50aWNBdHRyaWJ1dGVzLkhUVFBfUkVRVUVTVF9NRVRIT0RfT1JJR0lOQUxdOiByZXF1ZXN0Lm1ldGhvZCxcbiAgICAgICAgICAgIFtTZW1hbnRpY0F0dHJpYnV0ZXNfMS5TZW1hbnRpY0F0dHJpYnV0ZXMuVVJMX0ZVTExdOiByZXF1ZXN0VXJsLnRvU3RyaW5nKCksXG4gICAgICAgICAgICBbU2VtYW50aWNBdHRyaWJ1dGVzXzEuU2VtYW50aWNBdHRyaWJ1dGVzLlVSTF9QQVRIXTogcmVxdWVzdFVybC5wYXRobmFtZSxcbiAgICAgICAgICAgIFtTZW1hbnRpY0F0dHJpYnV0ZXNfMS5TZW1hbnRpY0F0dHJpYnV0ZXMuVVJMX1FVRVJZXTogcmVxdWVzdFVybC5zZWFyY2gsXG4gICAgICAgICAgICBbU2VtYW50aWNBdHRyaWJ1dGVzXzEuU2VtYW50aWNBdHRyaWJ1dGVzLlVSTF9TQ0hFTUVdOiB1cmxTY2hlbWUsXG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IHNjaGVtZVBvcnRzID0geyBodHRwczogJzQ0MycsIGh0dHA6ICc4MCcgfTtcbiAgICAgICAgY29uc3Qgc2VydmVyQWRkcmVzcyA9IHJlcXVlc3RVcmwuaG9zdG5hbWU7XG4gICAgICAgIGNvbnN0IHNlcnZlclBvcnQgPSByZXF1ZXN0VXJsLnBvcnQgfHwgc2NoZW1lUG9ydHNbdXJsU2NoZW1lXTtcbiAgICAgICAgYXR0cmlidXRlc1tTZW1hbnRpY0F0dHJpYnV0ZXNfMS5TZW1hbnRpY0F0dHJpYnV0ZXMuU0VSVkVSX0FERFJFU1NdID0gc2VydmVyQWRkcmVzcztcbiAgICAgICAgaWYgKHNlcnZlclBvcnQgJiYgIWlzTmFOKE51bWJlcihzZXJ2ZXJQb3J0KSkpIHtcbiAgICAgICAgICAgIGF0dHJpYnV0ZXNbU2VtYW50aWNBdHRyaWJ1dGVzXzEuU2VtYW50aWNBdHRyaWJ1dGVzLlNFUlZFUl9QT1JUXSA9IE51bWJlcihzZXJ2ZXJQb3J0KTtcbiAgICAgICAgfVxuICAgICAgICAvLyBHZXQgdXNlciBhZ2VudCBmcm9tIGhlYWRlcnNcbiAgICAgICAgbGV0IHVzZXJBZ2VudDtcbiAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocmVxdWVzdC5oZWFkZXJzKSkge1xuICAgICAgICAgICAgY29uc3QgaWR4ID0gcmVxdWVzdC5oZWFkZXJzLmZpbmRJbmRleChoID0+IGgudG9Mb3dlckNhc2UoKSA9PT0gJ3VzZXItYWdlbnQnKTtcbiAgICAgICAgICAgIGlmIChpZHggPj0gMCkge1xuICAgICAgICAgICAgICAgIHVzZXJBZ2VudCA9IHJlcXVlc3QuaGVhZGVyc1tpZHggKyAxXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmICh0eXBlb2YgcmVxdWVzdC5oZWFkZXJzID09PSAnc3RyaW5nJykge1xuICAgICAgICAgICAgY29uc3QgaGVhZGVycyA9IHJlcXVlc3QuaGVhZGVycy5zcGxpdCgnXFxyXFxuJyk7XG4gICAgICAgICAgICBjb25zdCB1YUhlYWRlciA9IGhlYWRlcnMuZmluZChoID0+IGgudG9Mb3dlckNhc2UoKS5zdGFydHNXaXRoKCd1c2VyLWFnZW50JykpO1xuICAgICAgICAgICAgdXNlckFnZW50ID1cbiAgICAgICAgICAgICAgICB1YUhlYWRlciAmJiB1YUhlYWRlci5zdWJzdHJpbmcodWFIZWFkZXIuaW5kZXhPZignOicpICsgMSkudHJpbSgpO1xuICAgICAgICB9XG4gICAgICAgIGlmICh1c2VyQWdlbnQpIHtcbiAgICAgICAgICAgIGF0dHJpYnV0ZXNbU2VtYW50aWNBdHRyaWJ1dGVzXzEuU2VtYW50aWNBdHRyaWJ1dGVzLlVTRVJfQUdFTlRfT1JJR0lOQUxdID0gdXNlckFnZW50O1xuICAgICAgICB9XG4gICAgICAgIC8vIEdldCBhdHRyaWJ1dGVzIGZyb20gdGhlIGhvb2sgaWYgcHJlc2VudFxuICAgICAgICBjb25zdCBob29rQXR0cmlidXRlcyA9ICgwLCBpbnN0cnVtZW50YXRpb25fMS5zYWZlRXhlY3V0ZUluVGhlTWlkZGxlKSgoKSA9PiB7IHZhciBfYTsgcmV0dXJuIChfYSA9IGNvbmZpZy5zdGFydFNwYW5Ib29rKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbChjb25maWcsIHJlcXVlc3QpOyB9LCBlID0+IGUgJiYgdGhpcy5fZGlhZy5lcnJvcignY2F1Z2h0IHN0YXJ0U3Bhbkhvb2sgZXJyb3I6ICcsIGUpLCB0cnVlKTtcbiAgICAgICAgaWYgKGhvb2tBdHRyaWJ1dGVzKSB7XG4gICAgICAgICAgICBPYmplY3QuZW50cmllcyhob29rQXR0cmlidXRlcykuZm9yRWFjaCgoW2tleSwgdmFsXSkgPT4ge1xuICAgICAgICAgICAgICAgIGF0dHJpYnV0ZXNba2V5XSA9IHZhbDtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIC8vIENoZWNrIGlmIHBhcmVudCBzcGFuIGlzIHJlcXVpcmVkIHZpYSBjb25maWcgYW5kOlxuICAgICAgICAvLyAtIGlmIGEgcGFyZW50IGlzIHJlcXVpcmVkIGJ1dCBub3QgcHJlc2VudCwgd2UgdXNlIGEgYE5vb3BTcGFuYCB0byBzdGlsbFxuICAgICAgICAvLyAgIHByb3BhZ2F0ZSBjb250ZXh0IHdpdGhvdXQgcmVjb3JkaW5nIGl0LlxuICAgICAgICAvLyAtIGNyZWF0ZSBhIHNwYW4gb3RoZXJ3aXNlXG4gICAgICAgIGNvbnN0IGFjdGl2ZUN0eCA9IGFwaV8xLmNvbnRleHQuYWN0aXZlKCk7XG4gICAgICAgIGNvbnN0IGN1cnJlbnRTcGFuID0gYXBpXzEudHJhY2UuZ2V0U3BhbihhY3RpdmVDdHgpO1xuICAgICAgICBsZXQgc3BhbjtcbiAgICAgICAgaWYgKGNvbmZpZy5yZXF1aXJlUGFyZW50Zm9yU3BhbnMgJiZcbiAgICAgICAgICAgICghY3VycmVudFNwYW4gfHwgIWFwaV8xLnRyYWNlLmlzU3BhbkNvbnRleHRWYWxpZChjdXJyZW50U3Bhbi5zcGFuQ29udGV4dCgpKSkpIHtcbiAgICAgICAgICAgIHNwYW4gPSBhcGlfMS50cmFjZS53cmFwU3BhbkNvbnRleHQoYXBpXzEuSU5WQUxJRF9TUEFOX0NPTlRFWFQpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgc3BhbiA9IHRoaXMudHJhY2VyLnN0YXJ0U3BhbihyZXF1ZXN0TWV0aG9kID09PSAnX09USEVSJyA/ICdIVFRQJyA6IHJlcXVlc3RNZXRob2QsIHtcbiAgICAgICAgICAgICAgICBraW5kOiBhcGlfMS5TcGFuS2luZC5DTElFTlQsXG4gICAgICAgICAgICAgICAgYXR0cmlidXRlczogYXR0cmlidXRlcyxcbiAgICAgICAgICAgIH0sIGFjdGl2ZUN0eCk7XG4gICAgICAgIH1cbiAgICAgICAgLy8gRXhlY3V0ZSB0aGUgcmVxdWVzdCBob29rIGlmIGRlZmluZWRcbiAgICAgICAgKDAsIGluc3RydW1lbnRhdGlvbl8xLnNhZmVFeGVjdXRlSW5UaGVNaWRkbGUpKCgpID0+IHsgdmFyIF9hOyByZXR1cm4gKF9hID0gY29uZmlnLnJlcXVlc3RIb29rKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbChjb25maWcsIHNwYW4sIHJlcXVlc3QpOyB9LCBlID0+IGUgJiYgdGhpcy5fZGlhZy5lcnJvcignY2F1Z2h0IHJlcXVlc3RIb29rIGVycm9yOiAnLCBlKSwgdHJ1ZSk7XG4gICAgICAgIC8vIENvbnRleHQgcHJvcGFnYXRpb24gZ29lcyBsYXN0IHNvIG5vIGhvb2sgY2FuIHRhbXBlclxuICAgICAgICAvLyB0aGUgcHJvcGFnYXRpb24gaGVhZGVyc1xuICAgICAgICBjb25zdCByZXF1ZXN0Q29udGV4dCA9IGFwaV8xLnRyYWNlLnNldFNwYW4oYXBpXzEuY29udGV4dC5hY3RpdmUoKSwgc3Bhbik7XG4gICAgICAgIGNvbnN0IGFkZGVkSGVhZGVycyA9IHt9O1xuICAgICAgICBhcGlfMS5wcm9wYWdhdGlvbi5pbmplY3QocmVxdWVzdENvbnRleHQsIGFkZGVkSGVhZGVycyk7XG4gICAgICAgIGNvbnN0IGhlYWRlckVudHJpZXMgPSBPYmplY3QuZW50cmllcyhhZGRlZEhlYWRlcnMpO1xuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGhlYWRlckVudHJpZXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGNvbnN0IFtrLCB2XSA9IGhlYWRlckVudHJpZXNbaV07XG4gICAgICAgICAgICBpZiAodHlwZW9mIHJlcXVlc3QuYWRkSGVhZGVyID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgICAgICAgcmVxdWVzdC5hZGRIZWFkZXIoaywgdik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICh0eXBlb2YgcmVxdWVzdC5oZWFkZXJzID09PSAnc3RyaW5nJykge1xuICAgICAgICAgICAgICAgIHJlcXVlc3QuaGVhZGVycyArPSBgJHtrfTogJHt2fVxcclxcbmA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChBcnJheS5pc0FycmF5KHJlcXVlc3QuaGVhZGVycykpIHtcbiAgICAgICAgICAgICAgICAvLyB1bmRpY2lANi4xMS4wIGFjY2lkZW50YWxseSwgYnJpZWZseSByZW1vdmVkIGByZXF1ZXN0LmFkZEhlYWRlcigpYC5cbiAgICAgICAgICAgICAgICByZXF1ZXN0LmhlYWRlcnMucHVzaChrLCB2KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9yZWNvcmRGcm9tUmVxLnNldChyZXF1ZXN0LCB7IHNwYW4sIGF0dHJpYnV0ZXMsIHN0YXJ0VGltZSB9KTtcbiAgICB9XG4gICAgLy8gVGhpcyBpcyB0aGUgMm5kIG1lc3NhZ2Ugd2UgcmVjZWl2ZSBmb3IgZWFjaCByZXF1ZXN0LiBJdCBpcyBmaXJlZCB3aGVuIGNvbm5lY3Rpb24gd2l0aFxuICAgIC8vIHRoZSByZW1vdGUgaXMgZXN0YWJsaXNoZWQgYW5kIGFib3V0IHRvIHNlbmQgdGhlIGZpcnN0IGJ5dGUuIEhlcmUgd2UgZG8gaGF2ZSBpbmZvIGFib3V0IHRoZVxuICAgIC8vIHJlbW90ZSBhZGRyZXNzIGFuZCBwb3J0IHNvIHdlIGNhbiBwb3B1bGF0ZSBzb21lIGBuZXR3b3JrLipgIGF0dHJpYnV0ZXMgaW50byB0aGUgc3BhblxuICAgIG9uUmVxdWVzdEhlYWRlcnMoeyByZXF1ZXN0LCBzb2NrZXQgfSkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIGNvbnN0IHJlY29yZCA9IHRoaXMuX3JlY29yZEZyb21SZXEuZ2V0KHJlcXVlc3QpO1xuICAgICAgICBpZiAoIXJlY29yZCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGNvbmZpZyA9IHRoaXMuZ2V0Q29uZmlnKCk7XG4gICAgICAgIGNvbnN0IHsgc3BhbiB9ID0gcmVjb3JkO1xuICAgICAgICBjb25zdCB7IHJlbW90ZUFkZHJlc3MsIHJlbW90ZVBvcnQgfSA9IHNvY2tldDtcbiAgICAgICAgY29uc3Qgc3BhbkF0dHJpYnV0ZXMgPSB7XG4gICAgICAgICAgICBbU2VtYW50aWNBdHRyaWJ1dGVzXzEuU2VtYW50aWNBdHRyaWJ1dGVzLk5FVFdPUktfUEVFUl9BRERSRVNTXTogcmVtb3RlQWRkcmVzcyxcbiAgICAgICAgICAgIFtTZW1hbnRpY0F0dHJpYnV0ZXNfMS5TZW1hbnRpY0F0dHJpYnV0ZXMuTkVUV09SS19QRUVSX1BPUlRdOiByZW1vdGVQb3J0LFxuICAgICAgICB9O1xuICAgICAgICAvLyBBZnRlciBob29rcyBoYXZlIGJlZW4gcHJvY2Vzc2VkICh3aGljaCBtYXkgbW9kaWZ5IHJlcXVlc3QgaGVhZGVycylcbiAgICAgICAgLy8gd2UgY2FuIGNvbGxlY3QgdGhlIGhlYWRlcnMgYmFzZWQgb24gdGhlIGNvbmZpZ3VyYXRpb25cbiAgICAgICAgaWYgKChfYSA9IGNvbmZpZy5oZWFkZXJzVG9TcGFuQXR0cmlidXRlcykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnJlcXVlc3RIZWFkZXJzKSB7XG4gICAgICAgICAgICBjb25zdCBoZWFkZXJzVG9BdHRyaWJzID0gbmV3IFNldChjb25maWcuaGVhZGVyc1RvU3BhbkF0dHJpYnV0ZXMucmVxdWVzdEhlYWRlcnMubWFwKG4gPT4gbi50b0xvd2VyQ2FzZSgpKSk7XG4gICAgICAgICAgICAvLyBoZWFkZXJzIGNvdWxkIGJlIGluIGZvcm1cbiAgICAgICAgICAgIC8vIFsnbmFtZTogdmFsdWUnLCAuLi5dIGZvciB2NVxuICAgICAgICAgICAgLy8gWyduYW1lJywgJ3ZhbHVlJywgLi4uXSBmb3IgdjZcbiAgICAgICAgICAgIGNvbnN0IHJhd0hlYWRlcnMgPSBBcnJheS5pc0FycmF5KHJlcXVlc3QuaGVhZGVycylcbiAgICAgICAgICAgICAgICA/IHJlcXVlc3QuaGVhZGVyc1xuICAgICAgICAgICAgICAgIDogcmVxdWVzdC5oZWFkZXJzLnNwbGl0KCdcXHJcXG4nKTtcbiAgICAgICAgICAgIHJhd0hlYWRlcnMuZm9yRWFjaCgoaCwgaWR4KSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3Qgc2VwSW5kZXggPSBoLmluZGV4T2YoJzonKTtcbiAgICAgICAgICAgICAgICBjb25zdCBoYXNTZXBhcmF0b3IgPSBzZXBJbmRleCAhPT0gLTE7XG4gICAgICAgICAgICAgICAgY29uc3QgbmFtZSA9IChoYXNTZXBhcmF0b3IgPyBoLnN1YnN0cmluZygwLCBzZXBJbmRleCkgOiBoKS50b0xvd2VyQ2FzZSgpO1xuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gaGFzU2VwYXJhdG9yXG4gICAgICAgICAgICAgICAgICAgID8gaC5zdWJzdHJpbmcoc2VwSW5kZXggKyAxKVxuICAgICAgICAgICAgICAgICAgICA6IHJhd0hlYWRlcnNbaWR4ICsgMV07XG4gICAgICAgICAgICAgICAgaWYgKGhlYWRlcnNUb0F0dHJpYnMuaGFzKG5hbWUpKSB7XG4gICAgICAgICAgICAgICAgICAgIHNwYW5BdHRyaWJ1dGVzW2BodHRwLnJlcXVlc3QuaGVhZGVyLiR7bmFtZX1gXSA9IHZhbHVlLnRyaW0oKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBzcGFuLnNldEF0dHJpYnV0ZXMoc3BhbkF0dHJpYnV0ZXMpO1xuICAgIH1cbiAgICAvLyBUaGlzIGlzIHRoZSAzcmQgbWVzc2FnZSB3ZSBnZXQgZm9yIGVhY2ggcmVxdWVzdCBhbmQgaXQncyBmaXJlZCB3aGVuIHRoZSBzZXJ2ZXJcbiAgICAvLyBoZWFkZXJzIGFyZSByZWNlaXZlZCwgYm9keSBtYXkgbm90IGJlIGFjY2Vzc2libGUgeWV0LlxuICAgIC8vIEZyb20gdGhlIHJlc3BvbnNlIGhlYWRlcnMgd2UgY2FuIHNldCB0aGUgc3RhdHVzIGFuZCBjb250ZW50IGxlbmd0aFxuICAgIG9uUmVzcG9uc2VIZWFkZXJzKHsgcmVxdWVzdCwgcmVzcG9uc2UsIH0pIHtcbiAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgY29uc3QgcmVjb3JkID0gdGhpcy5fcmVjb3JkRnJvbVJlcS5nZXQocmVxdWVzdCk7XG4gICAgICAgIGlmICghcmVjb3JkKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgeyBzcGFuLCBhdHRyaWJ1dGVzIH0gPSByZWNvcmQ7XG4gICAgICAgIGNvbnN0IHNwYW5BdHRyaWJ1dGVzID0ge1xuICAgICAgICAgICAgW1NlbWFudGljQXR0cmlidXRlc18xLlNlbWFudGljQXR0cmlidXRlcy5IVFRQX1JFU1BPTlNFX1NUQVRVU19DT0RFXTogcmVzcG9uc2Uuc3RhdHVzQ29kZSxcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgY29uZmlnID0gdGhpcy5nZXRDb25maWcoKTtcbiAgICAgICAgLy8gRXhlY3V0ZSB0aGUgcmVzcG9uc2UgaG9vayBpZiBkZWZpbmVkXG4gICAgICAgICgwLCBpbnN0cnVtZW50YXRpb25fMS5zYWZlRXhlY3V0ZUluVGhlTWlkZGxlKSgoKSA9PiB7IHZhciBfYTsgcmV0dXJuIChfYSA9IGNvbmZpZy5yZXNwb25zZUhvb2spID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYWxsKGNvbmZpZywgc3BhbiwgeyByZXF1ZXN0LCByZXNwb25zZSB9KTsgfSwgZSA9PiBlICYmIHRoaXMuX2RpYWcuZXJyb3IoJ2NhdWdodCByZXNwb25zZUhvb2sgZXJyb3I6ICcsIGUpLCB0cnVlKTtcbiAgICAgICAgY29uc3QgaGVhZGVyc1RvQXR0cmlicyA9IG5ldyBTZXQoKTtcbiAgICAgICAgaWYgKChfYSA9IGNvbmZpZy5oZWFkZXJzVG9TcGFuQXR0cmlidXRlcykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnJlc3BvbnNlSGVhZGVycykge1xuICAgICAgICAgICAgKF9iID0gY29uZmlnLmhlYWRlcnNUb1NwYW5BdHRyaWJ1dGVzKSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2IucmVzcG9uc2VIZWFkZXJzLmZvckVhY2gobmFtZSA9PiBoZWFkZXJzVG9BdHRyaWJzLmFkZChuYW1lLnRvTG93ZXJDYXNlKCkpKTtcbiAgICAgICAgfVxuICAgICAgICBmb3IgKGxldCBpZHggPSAwOyBpZHggPCByZXNwb25zZS5oZWFkZXJzLmxlbmd0aDsgaWR4ID0gaWR4ICsgMikge1xuICAgICAgICAgICAgY29uc3QgbmFtZSA9IHJlc3BvbnNlLmhlYWRlcnNbaWR4XS50b1N0cmluZygpLnRvTG93ZXJDYXNlKCk7XG4gICAgICAgICAgICBjb25zdCB2YWx1ZSA9IHJlc3BvbnNlLmhlYWRlcnNbaWR4ICsgMV07XG4gICAgICAgICAgICBpZiAoaGVhZGVyc1RvQXR0cmlicy5oYXMobmFtZSkpIHtcbiAgICAgICAgICAgICAgICBzcGFuQXR0cmlidXRlc1tgaHR0cC5yZXNwb25zZS5oZWFkZXIuJHtuYW1lfWBdID0gdmFsdWUudG9TdHJpbmcoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChuYW1lID09PSAnY29udGVudC1sZW5ndGgnKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgY29udGVudExlbmd0aCA9IE51bWJlcih2YWx1ZS50b1N0cmluZygpKTtcbiAgICAgICAgICAgICAgICBpZiAoIWlzTmFOKGNvbnRlbnRMZW5ndGgpKSB7XG4gICAgICAgICAgICAgICAgICAgIHNwYW5BdHRyaWJ1dGVzWydodHRwLnJlc3BvbnNlLmhlYWRlci5jb250ZW50LWxlbmd0aCddID0gY29udGVudExlbmd0aDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgc3Bhbi5zZXRBdHRyaWJ1dGVzKHNwYW5BdHRyaWJ1dGVzKTtcbiAgICAgICAgc3Bhbi5zZXRTdGF0dXMoe1xuICAgICAgICAgICAgY29kZTogcmVzcG9uc2Uuc3RhdHVzQ29kZSA+PSA0MDBcbiAgICAgICAgICAgICAgICA/IGFwaV8xLlNwYW5TdGF0dXNDb2RlLkVSUk9SXG4gICAgICAgICAgICAgICAgOiBhcGlfMS5TcGFuU3RhdHVzQ29kZS5VTlNFVCxcbiAgICAgICAgfSk7XG4gICAgICAgIHJlY29yZC5hdHRyaWJ1dGVzID0gT2JqZWN0LmFzc2lnbihhdHRyaWJ1dGVzLCBzcGFuQXR0cmlidXRlcyk7XG4gICAgfVxuICAgIC8vIFRoaXMgaXMgdGhlIGxhc3QgZXZlbnQgd2UgcmVjZWl2ZSBpZiB0aGUgcmVxdWVzdCB3ZW50IHdpdGhvdXQgYW55IGVycm9yc1xuICAgIG9uRG9uZSh7IHJlcXVlc3QgfSkge1xuICAgICAgICBjb25zdCByZWNvcmQgPSB0aGlzLl9yZWNvcmRGcm9tUmVxLmdldChyZXF1ZXN0KTtcbiAgICAgICAgaWYgKCFyZWNvcmQpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB7IHNwYW4sIGF0dHJpYnV0ZXMsIHN0YXJ0VGltZSB9ID0gcmVjb3JkO1xuICAgICAgICAvLyBFbmQgdGhlIHNwYW5cbiAgICAgICAgc3Bhbi5lbmQoKTtcbiAgICAgICAgdGhpcy5fcmVjb3JkRnJvbVJlcS5kZWxldGUocmVxdWVzdCk7XG4gICAgICAgIC8vIFJlY29yZCBtZXRyaWNzXG4gICAgICAgIHRoaXMucmVjb3JkUmVxdWVzdER1cmF0aW9uKGF0dHJpYnV0ZXMsIHN0YXJ0VGltZSk7XG4gICAgfVxuICAgIC8vIFRoaXMgaXMgdGhlIGV2ZW50IHdlIGdldCB3aGVuIHNvbWV0aGluZyBpcyB3cm9uZyBpbiB0aGUgcmVxdWVzdCBsaWtlXG4gICAgLy8gLSBpbnZhbGlkIG9wdGlvbnMgd2hlbiBjYWxsaW5nIGBmZXRjaGAgZ2xvYmFsIEFQSSBvciBhbnkgdW5kaWNpIG1ldGhvZCBmb3IgcmVxdWVzdFxuICAgIC8vIC0gY29ubmVjdGl2aXR5IGVycm9ycyBzdWNoIGFzIHVucmVhY2hhYmxlIGhvc3RcbiAgICAvLyAtIHJlcXVlc3RzIGFib3J0ZWQgdGhyb3VnaCBhbiBgQWJvcnRDb250cm9sbGVyLnNpZ25hbGBcbiAgICAvLyBOT1RFOiBzZXJ2ZXIgZXJyb3JzIGFyZSBjb25zaWRlcmVkIHZhbGlkIHJlc3BvbnNlcyBhbmQgaXQncyB0aGUgbGliIGNvbnN1bWVyXG4gICAgLy8gd2hvIHNob3VsZCBkZWFsIHdpdGggdGhhdC5cbiAgICBvbkVycm9yKHsgcmVxdWVzdCwgZXJyb3IgfSkge1xuICAgICAgICBjb25zdCByZWNvcmQgPSB0aGlzLl9yZWNvcmRGcm9tUmVxLmdldChyZXF1ZXN0KTtcbiAgICAgICAgaWYgKCFyZWNvcmQpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCB7IHNwYW4sIGF0dHJpYnV0ZXMsIHN0YXJ0VGltZSB9ID0gcmVjb3JkO1xuICAgICAgICAvLyBOT1RFOiBpbiBgdW5kaWNpQDYuMy4wYCB3aGVuIHJlcXVlc3QgYWJvcnRlZCB0aGUgZXJyb3IgdHlwZSBjaGFuZ2VzIGZyb21cbiAgICAgICAgLy8gYSBjdXN0b20gZXJyb3IgKGBSZXF1ZXN0QWJvcnRlZEVycm9yYCkgdG8gYSBidWlsdC1pbiBgRE9NRXhjZXB0aW9uYCBjYXJyeWluZ1xuICAgICAgICAvLyBzb21lIGRpZmZlcmVuY2VzOlxuICAgICAgICAvLyAtIGBjb2RlYCBpcyBmcm9tIERPTUVYY2VwdGlvbiAoQUJPUlRfRVJSOiAyMClcbiAgICAgICAgLy8gLSBgbWVzc2FnZWAgY2hhbmdlc1xuICAgICAgICAvLyAtIHN0YWNrdHJhY2UgaXMgc21hbGxlciBhbmQgY29udGFpbnMgbm9kZSBpbnRlcm5hbCBmcmFtZXNcbiAgICAgICAgc3Bhbi5yZWNvcmRFeGNlcHRpb24oZXJyb3IpO1xuICAgICAgICBzcGFuLnNldFN0YXR1cyh7XG4gICAgICAgICAgICBjb2RlOiBhcGlfMS5TcGFuU3RhdHVzQ29kZS5FUlJPUixcbiAgICAgICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgIH0pO1xuICAgICAgICBzcGFuLmVuZCgpO1xuICAgICAgICB0aGlzLl9yZWNvcmRGcm9tUmVxLmRlbGV0ZShyZXF1ZXN0KTtcbiAgICAgICAgLy8gUmVjb3JkIG1ldHJpY3MgKHdpdGggdGhlIGVycm9yKVxuICAgICAgICBhdHRyaWJ1dGVzW1NlbWFudGljQXR0cmlidXRlc18xLlNlbWFudGljQXR0cmlidXRlcy5FUlJPUl9UWVBFXSA9IGVycm9yLm1lc3NhZ2U7XG4gICAgICAgIHRoaXMucmVjb3JkUmVxdWVzdER1cmF0aW9uKGF0dHJpYnV0ZXMsIHN0YXJ0VGltZSk7XG4gICAgfVxuICAgIHJlY29yZFJlcXVlc3REdXJhdGlvbihhdHRyaWJ1dGVzLCBzdGFydFRpbWUpIHtcbiAgICAgICAgLy8gVGltZSB0byByZWNvcmQgbWV0cmljc1xuICAgICAgICBjb25zdCBtZXRyaWNzQXR0cmlidXRlcyA9IHt9O1xuICAgICAgICAvLyBHZXQgdGhlIGF0dHJpYnMgYWxyZWFkeSBpbiBzcGFuIGF0dHJpYnV0ZXNcbiAgICAgICAgY29uc3Qga2V5c1RvQ29weSA9IFtcbiAgICAgICAgICAgIFNlbWFudGljQXR0cmlidXRlc18xLlNlbWFudGljQXR0cmlidXRlcy5IVFRQX1JFU1BPTlNFX1NUQVRVU19DT0RFLFxuICAgICAgICAgICAgU2VtYW50aWNBdHRyaWJ1dGVzXzEuU2VtYW50aWNBdHRyaWJ1dGVzLkhUVFBfUkVRVUVTVF9NRVRIT0QsXG4gICAgICAgICAgICBTZW1hbnRpY0F0dHJpYnV0ZXNfMS5TZW1hbnRpY0F0dHJpYnV0ZXMuU0VSVkVSX0FERFJFU1MsXG4gICAgICAgICAgICBTZW1hbnRpY0F0dHJpYnV0ZXNfMS5TZW1hbnRpY0F0dHJpYnV0ZXMuU0VSVkVSX1BPUlQsXG4gICAgICAgICAgICBTZW1hbnRpY0F0dHJpYnV0ZXNfMS5TZW1hbnRpY0F0dHJpYnV0ZXMuVVJMX1NDSEVNRSxcbiAgICAgICAgICAgIFNlbWFudGljQXR0cmlidXRlc18xLlNlbWFudGljQXR0cmlidXRlcy5FUlJPUl9UWVBFLFxuICAgICAgICBdO1xuICAgICAgICBrZXlzVG9Db3B5LmZvckVhY2goa2V5ID0+IHtcbiAgICAgICAgICAgIGlmIChrZXkgaW4gYXR0cmlidXRlcykge1xuICAgICAgICAgICAgICAgIG1ldHJpY3NBdHRyaWJ1dGVzW2tleV0gPSBhdHRyaWJ1dGVzW2tleV07XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgICAvLyBUYWtlIHRoZSBkdXJhdGlvbiBhbmQgcmVjb3JkIGl0XG4gICAgICAgIGNvbnN0IGR1cmF0aW9uU2Vjb25kcyA9ICgwLCBjb3JlXzEuaHJUaW1lVG9NaWxsaXNlY29uZHMpKCgwLCBjb3JlXzEuaHJUaW1lRHVyYXRpb24pKHN0YXJ0VGltZSwgKDAsIGNvcmVfMS5oclRpbWUpKCkpKSAvIDEwMDA7XG4gICAgICAgIHRoaXMuX2h0dHBDbGllbnREdXJhdGlvbkhpc3RvZ3JhbS5yZWNvcmQoZHVyYXRpb25TZWNvbmRzLCBtZXRyaWNzQXR0cmlidXRlcyk7XG4gICAgfVxuICAgIGdldFJlcXVlc3RNZXRob2Qob3JpZ2luYWwpIHtcbiAgICAgICAgY29uc3Qga25vd25NZXRob2RzID0ge1xuICAgICAgICAgICAgQ09OTkVDVDogdHJ1ZSxcbiAgICAgICAgICAgIE9QVElPTlM6IHRydWUsXG4gICAgICAgICAgICBIRUFEOiB0cnVlLFxuICAgICAgICAgICAgR0VUOiB0cnVlLFxuICAgICAgICAgICAgUE9TVDogdHJ1ZSxcbiAgICAgICAgICAgIFBVVDogdHJ1ZSxcbiAgICAgICAgICAgIFBBVENIOiB0cnVlLFxuICAgICAgICAgICAgREVMRVRFOiB0cnVlLFxuICAgICAgICAgICAgVFJBQ0U6IHRydWUsXG4gICAgICAgIH07XG4gICAgICAgIGlmIChvcmlnaW5hbC50b1VwcGVyQ2FzZSgpIGluIGtub3duTWV0aG9kcykge1xuICAgICAgICAgICAgcmV0dXJuIG9yaWdpbmFsLnRvVXBwZXJDYXNlKCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuICdfT1RIRVInO1xuICAgIH1cbn1cbmV4cG9ydHMuVW5kaWNpSW5zdHJ1bWVudGF0aW9uID0gVW5kaWNpSW5zdHJ1bWVudGF0aW9uO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dW5kaWNpLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js\n");

/***/ }),

/***/ "(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.10.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-undici';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(instrument)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SemanticAttributes = void 0;\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\nexports.SemanticAttributes = {\n    /**\n     * State of the HTTP connection in the HTTP connection pool.\n     */\n    HTTP_CONNECTION_STATE: 'http.connection.state',\n    /**\n    * Describes a class of error the operation ended with.\n    *\n    * Note: The `error.type` SHOULD be predictable and SHOULD have low cardinality.\n  Instrumentations SHOULD document the list of errors they report.\n  \n  The cardinality of `error.type` within one instrumentation library SHOULD be low.\n  Telemetry consumers that aggregate data from multiple instrumentation libraries and applications\n  should be prepared for `error.type` to have high cardinality at query time when no\n  additional filters are applied.\n  \n  If the operation has completed successfully, instrumentations SHOULD NOT set `error.type`.\n  \n  If a specific domain defines its own set of error identifiers (such as HTTP or gRPC status codes),\n  it&#39;s RECOMMENDED to:\n  \n  * Use a domain-specific attribute\n  * Set `error.type` to capture all errors, regardless of whether they are defined within the domain-specific set or not.\n    */\n    ERROR_TYPE: 'error.type',\n    /**\n     * The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n     */\n    HTTP_REQUEST_BODY_SIZE: 'http.request.body.size',\n    /**\n    * HTTP request method.\n    *\n    * Note: HTTP request method value SHOULD be &#34;known&#34; to the instrumentation.\n  By default, this convention defines &#34;known&#34; methods as the ones listed in [RFC9110](https://www.rfc-editor.org/rfc/rfc9110.html#name-methods)\n  and the PATCH method defined in [RFC5789](https://www.rfc-editor.org/rfc/rfc5789.html).\n  \n  If the HTTP request method is not known to instrumentation, it MUST set the `http.request.method` attribute to `_OTHER`.\n  \n  If the HTTP instrumentation could end up converting valid HTTP request methods to `_OTHER`, then it MUST provide a way to override\n  the list of known HTTP methods. If this override is done via environment variable, then the environment variable MUST be named\n  OTEL_INSTRUMENTATION_HTTP_KNOWN_METHODS and support a comma-separated list of case-sensitive known HTTP methods\n  (this list MUST be a full override of the default known method, it is not a list of known methods in addition to the defaults).\n  \n  HTTP method names are case-sensitive and `http.request.method` attribute value MUST match a known HTTP method name exactly.\n  Instrumentations for specific web frameworks that consider HTTP methods to be case insensitive, SHOULD populate a canonical equivalent.\n  Tracing instrumentations that do so, MUST also set `http.request.method_original` to the original value.\n    */\n    HTTP_REQUEST_METHOD: 'http.request.method',\n    /**\n     * Original HTTP method sent by the client in the request line.\n     */\n    HTTP_REQUEST_METHOD_ORIGINAL: 'http.request.method_original',\n    /**\n     * The ordinal number of request resending attempt (for any reason, including redirects).\n     *\n     * Note: The resend count SHOULD be updated each time an HTTP request gets resent by the client, regardless of what was the cause of the resending (e.g. redirection, authorization failure, 503 Server Unavailable, network issues, or any other).\n     */\n    HTTP_REQUEST_RESEND_COUNT: 'http.request.resend_count',\n    /**\n     * The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n     */\n    HTTP_RESPONSE_BODY_SIZE: 'http.response.body.size',\n    /**\n     * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).\n     */\n    HTTP_RESPONSE_STATUS_CODE: 'http.response.status_code',\n    /**\n    * The matched route, that is, the path template in the format used by the respective server framework.\n    *\n    * Note: MUST NOT be populated when this is not supported by the HTTP server framework as the route attribute should have low-cardinality and the URI path can NOT substitute it.\n  SHOULD include the [application root](/docs/http/http-spans.md#http-server-definitions) if there is one.\n    */\n    HTTP_ROUTE: 'http.route',\n    /**\n     * Peer address of the network connection - IP address or Unix domain socket name.\n     */\n    NETWORK_PEER_ADDRESS: 'network.peer.address',\n    /**\n     * Peer port number of the network connection.\n     */\n    NETWORK_PEER_PORT: 'network.peer.port',\n    /**\n     * [OSI application layer](https://osi-model.com/application-layer/) or non-OSI equivalent.\n     *\n     * Note: The value SHOULD be normalized to lowercase.\n     */\n    NETWORK_PROTOCOL_NAME: 'network.protocol.name',\n    /**\n     * Version of the protocol specified in `network.protocol.name`.\n     *\n     * Note: `network.protocol.version` refers to the version of the protocol used and might be different from the protocol client&#39;s version. If the HTTP client has a version of `0.27.2`, but sends HTTP version `1.1`, this attribute should be set to `1.1`.\n     */\n    NETWORK_PROTOCOL_VERSION: 'network.protocol.version',\n    /**\n     * Server domain name if available without reverse DNS lookup; otherwise, IP address or Unix domain socket name.\n     *\n     * Note: When observed from the client side, and when communicating through an intermediary, `server.address` SHOULD represent the server address behind any intermediaries, for example proxies, if it&#39;s available.\n     */\n    SERVER_ADDRESS: 'server.address',\n    /**\n     * Server port number.\n     *\n     * Note: When observed from the client side, and when communicating through an intermediary, `server.port` SHOULD represent the server port behind any intermediaries, for example proxies, if it&#39;s available.\n     */\n    SERVER_PORT: 'server.port',\n    /**\n    * Absolute URL describing a network resource according to [RFC3986](https://www.rfc-editor.org/rfc/rfc3986).\n    *\n    * Note: For network calls, URL usually has `scheme://host[:port][path][?query][#fragment]` format, where the fragment is not transmitted over HTTP, but if it is known, it SHOULD be included nevertheless.\n  `url.full` MUST NOT contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case username and password SHOULD be redacted and attribute&#39;s value SHOULD be `https://REDACTED:<EMAIL>/`.\n  `url.full` SHOULD capture the absolute URL when it is available (or can be reconstructed) and SHOULD NOT be validated or modified except for sanitizing purposes.\n    */\n    URL_FULL: 'url.full',\n    /**\n     * The [URI path](https://www.rfc-editor.org/rfc/rfc3986#section-3.3) component.\n     */\n    URL_PATH: 'url.path',\n    /**\n     * The [URI query](https://www.rfc-editor.org/rfc/rfc3986#section-3.4) component.\n     *\n     * Note: Sensitive content provided in query string SHOULD be scrubbed when instrumentations can identify it.\n     */\n    URL_QUERY: 'url.query',\n    /**\n     * The [URI scheme](https://www.rfc-editor.org/rfc/rfc3986#section-3.1) component identifying the used protocol.\n     */\n    URL_SCHEME: 'url.scheme',\n    /**\n     * Value of the [HTTP User-Agent](https://www.rfc-editor.org/rfc/rfc9110.html#field.user-agent) header sent by the client.\n     */\n    USER_AGENT_ORIGINAL: 'user_agent.original',\n};\n//# sourceMappingURL=SemanticAttributes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9lYTQyMDIzZmViNjMwY2Y4MWY5N2JkY2UxOGE0NmU5NC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXVuZGljaS9idWlsZC9zcmMvZW51bXMvU2VtYW50aWNBdHRyaWJ1dGVzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDBCQUEwQjtBQUMxQjtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELFdBQVc7QUFDaEUsMkNBQTJDLFdBQVc7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVJQUF1STtBQUN2STtBQUNBO0FBQ0E7QUFDQSxtRUFBbUU7QUFDbkU7QUFDQSxnTkFBZ047QUFDaE47QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBNQUEwTTtBQUMxTTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwTEFBMEw7QUFDMUw7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbndhclxcRG9jdW1lbnRzXFwyIEZPTERFUlMgRk9SIENVQkVOVFxcQ3ViZW50d2ViXFxjdWJlbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9lYTQyMDIzZmViNjMwY2Y4MWY5N2JkY2UxOGE0NmU5NFxcbm9kZV9tb2R1bGVzXFxAb3BlbnRlbGVtZXRyeVxcaW5zdHJ1bWVudGF0aW9uLXVuZGljaVxcYnVpbGRcXHNyY1xcZW51bXNcXFNlbWFudGljQXR0cmlidXRlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qXG4gKiBDb3B5cmlnaHQgVGhlIE9wZW5UZWxlbWV0cnkgQXV0aG9yc1xuICpcbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4gKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAqXG4gKiAgICAgIGh0dHBzOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLlNlbWFudGljQXR0cmlidXRlcyA9IHZvaWQgMDtcbi8vIERPIE5PVCBFRElULCB0aGlzIGlzIGFuIEF1dG8tZ2VuZXJhdGVkIGZpbGUgZnJvbSBzY3JpcHRzL3NlbWNvbnYvdGVtcGxhdGVzLy90ZW1wbGF0ZXMvU2VtYW50aWNBdHRyaWJ1dGVzLnRzLmoyXG5leHBvcnRzLlNlbWFudGljQXR0cmlidXRlcyA9IHtcbiAgICAvKipcbiAgICAgKiBTdGF0ZSBvZiB0aGUgSFRUUCBjb25uZWN0aW9uIGluIHRoZSBIVFRQIGNvbm5lY3Rpb24gcG9vbC5cbiAgICAgKi9cbiAgICBIVFRQX0NPTk5FQ1RJT05fU1RBVEU6ICdodHRwLmNvbm5lY3Rpb24uc3RhdGUnLFxuICAgIC8qKlxuICAgICogRGVzY3JpYmVzIGEgY2xhc3Mgb2YgZXJyb3IgdGhlIG9wZXJhdGlvbiBlbmRlZCB3aXRoLlxuICAgICpcbiAgICAqIE5vdGU6IFRoZSBgZXJyb3IudHlwZWAgU0hPVUxEIGJlIHByZWRpY3RhYmxlIGFuZCBTSE9VTEQgaGF2ZSBsb3cgY2FyZGluYWxpdHkuXG4gIEluc3RydW1lbnRhdGlvbnMgU0hPVUxEIGRvY3VtZW50IHRoZSBsaXN0IG9mIGVycm9ycyB0aGV5IHJlcG9ydC5cbiAgXG4gIFRoZSBjYXJkaW5hbGl0eSBvZiBgZXJyb3IudHlwZWAgd2l0aGluIG9uZSBpbnN0cnVtZW50YXRpb24gbGlicmFyeSBTSE9VTEQgYmUgbG93LlxuICBUZWxlbWV0cnkgY29uc3VtZXJzIHRoYXQgYWdncmVnYXRlIGRhdGEgZnJvbSBtdWx0aXBsZSBpbnN0cnVtZW50YXRpb24gbGlicmFyaWVzIGFuZCBhcHBsaWNhdGlvbnNcbiAgc2hvdWxkIGJlIHByZXBhcmVkIGZvciBgZXJyb3IudHlwZWAgdG8gaGF2ZSBoaWdoIGNhcmRpbmFsaXR5IGF0IHF1ZXJ5IHRpbWUgd2hlbiBub1xuICBhZGRpdGlvbmFsIGZpbHRlcnMgYXJlIGFwcGxpZWQuXG4gIFxuICBJZiB0aGUgb3BlcmF0aW9uIGhhcyBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5LCBpbnN0cnVtZW50YXRpb25zIFNIT1VMRCBOT1Qgc2V0IGBlcnJvci50eXBlYC5cbiAgXG4gIElmIGEgc3BlY2lmaWMgZG9tYWluIGRlZmluZXMgaXRzIG93biBzZXQgb2YgZXJyb3IgaWRlbnRpZmllcnMgKHN1Y2ggYXMgSFRUUCBvciBnUlBDIHN0YXR1cyBjb2RlcyksXG4gIGl0JiMzOTtzIFJFQ09NTUVOREVEIHRvOlxuICBcbiAgKiBVc2UgYSBkb21haW4tc3BlY2lmaWMgYXR0cmlidXRlXG4gICogU2V0IGBlcnJvci50eXBlYCB0byBjYXB0dXJlIGFsbCBlcnJvcnMsIHJlZ2FyZGxlc3Mgb2Ygd2hldGhlciB0aGV5IGFyZSBkZWZpbmVkIHdpdGhpbiB0aGUgZG9tYWluLXNwZWNpZmljIHNldCBvciBub3QuXG4gICAgKi9cbiAgICBFUlJPUl9UWVBFOiAnZXJyb3IudHlwZScsXG4gICAgLyoqXG4gICAgICogVGhlIHNpemUgb2YgdGhlIHJlcXVlc3QgcGF5bG9hZCBib2R5IGluIGJ5dGVzLiBUaGlzIGlzIHRoZSBudW1iZXIgb2YgYnl0ZXMgdHJhbnNmZXJyZWQgZXhjbHVkaW5nIGhlYWRlcnMgYW5kIGlzIG9mdGVuLCBidXQgbm90IGFsd2F5cywgcHJlc2VudCBhcyB0aGUgW0NvbnRlbnQtTGVuZ3RoXShodHRwczovL3d3dy5yZmMtZWRpdG9yLm9yZy9yZmMvcmZjOTExMC5odG1sI2ZpZWxkLmNvbnRlbnQtbGVuZ3RoKSBoZWFkZXIuIEZvciByZXF1ZXN0cyB1c2luZyB0cmFuc3BvcnQgZW5jb2RpbmcsIHRoaXMgc2hvdWxkIGJlIHRoZSBjb21wcmVzc2VkIHNpemUuXG4gICAgICovXG4gICAgSFRUUF9SRVFVRVNUX0JPRFlfU0laRTogJ2h0dHAucmVxdWVzdC5ib2R5LnNpemUnLFxuICAgIC8qKlxuICAgICogSFRUUCByZXF1ZXN0IG1ldGhvZC5cbiAgICAqXG4gICAgKiBOb3RlOiBIVFRQIHJlcXVlc3QgbWV0aG9kIHZhbHVlIFNIT1VMRCBiZSAmIzM0O2tub3duJiMzNDsgdG8gdGhlIGluc3RydW1lbnRhdGlvbi5cbiAgQnkgZGVmYXVsdCwgdGhpcyBjb252ZW50aW9uIGRlZmluZXMgJiMzNDtrbm93biYjMzQ7IG1ldGhvZHMgYXMgdGhlIG9uZXMgbGlzdGVkIGluIFtSRkM5MTEwXShodHRwczovL3d3dy5yZmMtZWRpdG9yLm9yZy9yZmMvcmZjOTExMC5odG1sI25hbWUtbWV0aG9kcylcbiAgYW5kIHRoZSBQQVRDSCBtZXRob2QgZGVmaW5lZCBpbiBbUkZDNTc4OV0oaHR0cHM6Ly93d3cucmZjLWVkaXRvci5vcmcvcmZjL3JmYzU3ODkuaHRtbCkuXG4gIFxuICBJZiB0aGUgSFRUUCByZXF1ZXN0IG1ldGhvZCBpcyBub3Qga25vd24gdG8gaW5zdHJ1bWVudGF0aW9uLCBpdCBNVVNUIHNldCB0aGUgYGh0dHAucmVxdWVzdC5tZXRob2RgIGF0dHJpYnV0ZSB0byBgX09USEVSYC5cbiAgXG4gIElmIHRoZSBIVFRQIGluc3RydW1lbnRhdGlvbiBjb3VsZCBlbmQgdXAgY29udmVydGluZyB2YWxpZCBIVFRQIHJlcXVlc3QgbWV0aG9kcyB0byBgX09USEVSYCwgdGhlbiBpdCBNVVNUIHByb3ZpZGUgYSB3YXkgdG8gb3ZlcnJpZGVcbiAgdGhlIGxpc3Qgb2Yga25vd24gSFRUUCBtZXRob2RzLiBJZiB0aGlzIG92ZXJyaWRlIGlzIGRvbmUgdmlhIGVudmlyb25tZW50IHZhcmlhYmxlLCB0aGVuIHRoZSBlbnZpcm9ubWVudCB2YXJpYWJsZSBNVVNUIGJlIG5hbWVkXG4gIE9URUxfSU5TVFJVTUVOVEFUSU9OX0hUVFBfS05PV05fTUVUSE9EUyBhbmQgc3VwcG9ydCBhIGNvbW1hLXNlcGFyYXRlZCBsaXN0IG9mIGNhc2Utc2Vuc2l0aXZlIGtub3duIEhUVFAgbWV0aG9kc1xuICAodGhpcyBsaXN0IE1VU1QgYmUgYSBmdWxsIG92ZXJyaWRlIG9mIHRoZSBkZWZhdWx0IGtub3duIG1ldGhvZCwgaXQgaXMgbm90IGEgbGlzdCBvZiBrbm93biBtZXRob2RzIGluIGFkZGl0aW9uIHRvIHRoZSBkZWZhdWx0cykuXG4gIFxuICBIVFRQIG1ldGhvZCBuYW1lcyBhcmUgY2FzZS1zZW5zaXRpdmUgYW5kIGBodHRwLnJlcXVlc3QubWV0aG9kYCBhdHRyaWJ1dGUgdmFsdWUgTVVTVCBtYXRjaCBhIGtub3duIEhUVFAgbWV0aG9kIG5hbWUgZXhhY3RseS5cbiAgSW5zdHJ1bWVudGF0aW9ucyBmb3Igc3BlY2lmaWMgd2ViIGZyYW1ld29ya3MgdGhhdCBjb25zaWRlciBIVFRQIG1ldGhvZHMgdG8gYmUgY2FzZSBpbnNlbnNpdGl2ZSwgU0hPVUxEIHBvcHVsYXRlIGEgY2Fub25pY2FsIGVxdWl2YWxlbnQuXG4gIFRyYWNpbmcgaW5zdHJ1bWVudGF0aW9ucyB0aGF0IGRvIHNvLCBNVVNUIGFsc28gc2V0IGBodHRwLnJlcXVlc3QubWV0aG9kX29yaWdpbmFsYCB0byB0aGUgb3JpZ2luYWwgdmFsdWUuXG4gICAgKi9cbiAgICBIVFRQX1JFUVVFU1RfTUVUSE9EOiAnaHR0cC5yZXF1ZXN0Lm1ldGhvZCcsXG4gICAgLyoqXG4gICAgICogT3JpZ2luYWwgSFRUUCBtZXRob2Qgc2VudCBieSB0aGUgY2xpZW50IGluIHRoZSByZXF1ZXN0IGxpbmUuXG4gICAgICovXG4gICAgSFRUUF9SRVFVRVNUX01FVEhPRF9PUklHSU5BTDogJ2h0dHAucmVxdWVzdC5tZXRob2Rfb3JpZ2luYWwnLFxuICAgIC8qKlxuICAgICAqIFRoZSBvcmRpbmFsIG51bWJlciBvZiByZXF1ZXN0IHJlc2VuZGluZyBhdHRlbXB0IChmb3IgYW55IHJlYXNvbiwgaW5jbHVkaW5nIHJlZGlyZWN0cykuXG4gICAgICpcbiAgICAgKiBOb3RlOiBUaGUgcmVzZW5kIGNvdW50IFNIT1VMRCBiZSB1cGRhdGVkIGVhY2ggdGltZSBhbiBIVFRQIHJlcXVlc3QgZ2V0cyByZXNlbnQgYnkgdGhlIGNsaWVudCwgcmVnYXJkbGVzcyBvZiB3aGF0IHdhcyB0aGUgY2F1c2Ugb2YgdGhlIHJlc2VuZGluZyAoZS5nLiByZWRpcmVjdGlvbiwgYXV0aG9yaXphdGlvbiBmYWlsdXJlLCA1MDMgU2VydmVyIFVuYXZhaWxhYmxlLCBuZXR3b3JrIGlzc3Vlcywgb3IgYW55IG90aGVyKS5cbiAgICAgKi9cbiAgICBIVFRQX1JFUVVFU1RfUkVTRU5EX0NPVU5UOiAnaHR0cC5yZXF1ZXN0LnJlc2VuZF9jb3VudCcsXG4gICAgLyoqXG4gICAgICogVGhlIHNpemUgb2YgdGhlIHJlc3BvbnNlIHBheWxvYWQgYm9keSBpbiBieXRlcy4gVGhpcyBpcyB0aGUgbnVtYmVyIG9mIGJ5dGVzIHRyYW5zZmVycmVkIGV4Y2x1ZGluZyBoZWFkZXJzIGFuZCBpcyBvZnRlbiwgYnV0IG5vdCBhbHdheXMsIHByZXNlbnQgYXMgdGhlIFtDb250ZW50LUxlbmd0aF0oaHR0cHM6Ly93d3cucmZjLWVkaXRvci5vcmcvcmZjL3JmYzkxMTAuaHRtbCNmaWVsZC5jb250ZW50LWxlbmd0aCkgaGVhZGVyLiBGb3IgcmVxdWVzdHMgdXNpbmcgdHJhbnNwb3J0IGVuY29kaW5nLCB0aGlzIHNob3VsZCBiZSB0aGUgY29tcHJlc3NlZCBzaXplLlxuICAgICAqL1xuICAgIEhUVFBfUkVTUE9OU0VfQk9EWV9TSVpFOiAnaHR0cC5yZXNwb25zZS5ib2R5LnNpemUnLFxuICAgIC8qKlxuICAgICAqIFtIVFRQIHJlc3BvbnNlIHN0YXR1cyBjb2RlXShodHRwczovL3Rvb2xzLmlldGYub3JnL2h0bWwvcmZjNzIzMSNzZWN0aW9uLTYpLlxuICAgICAqL1xuICAgIEhUVFBfUkVTUE9OU0VfU1RBVFVTX0NPREU6ICdodHRwLnJlc3BvbnNlLnN0YXR1c19jb2RlJyxcbiAgICAvKipcbiAgICAqIFRoZSBtYXRjaGVkIHJvdXRlLCB0aGF0IGlzLCB0aGUgcGF0aCB0ZW1wbGF0ZSBpbiB0aGUgZm9ybWF0IHVzZWQgYnkgdGhlIHJlc3BlY3RpdmUgc2VydmVyIGZyYW1ld29yay5cbiAgICAqXG4gICAgKiBOb3RlOiBNVVNUIE5PVCBiZSBwb3B1bGF0ZWQgd2hlbiB0aGlzIGlzIG5vdCBzdXBwb3J0ZWQgYnkgdGhlIEhUVFAgc2VydmVyIGZyYW1ld29yayBhcyB0aGUgcm91dGUgYXR0cmlidXRlIHNob3VsZCBoYXZlIGxvdy1jYXJkaW5hbGl0eSBhbmQgdGhlIFVSSSBwYXRoIGNhbiBOT1Qgc3Vic3RpdHV0ZSBpdC5cbiAgU0hPVUxEIGluY2x1ZGUgdGhlIFthcHBsaWNhdGlvbiByb290XSgvZG9jcy9odHRwL2h0dHAtc3BhbnMubWQjaHR0cC1zZXJ2ZXItZGVmaW5pdGlvbnMpIGlmIHRoZXJlIGlzIG9uZS5cbiAgICAqL1xuICAgIEhUVFBfUk9VVEU6ICdodHRwLnJvdXRlJyxcbiAgICAvKipcbiAgICAgKiBQZWVyIGFkZHJlc3Mgb2YgdGhlIG5ldHdvcmsgY29ubmVjdGlvbiAtIElQIGFkZHJlc3Mgb3IgVW5peCBkb21haW4gc29ja2V0IG5hbWUuXG4gICAgICovXG4gICAgTkVUV09SS19QRUVSX0FERFJFU1M6ICduZXR3b3JrLnBlZXIuYWRkcmVzcycsXG4gICAgLyoqXG4gICAgICogUGVlciBwb3J0IG51bWJlciBvZiB0aGUgbmV0d29yayBjb25uZWN0aW9uLlxuICAgICAqL1xuICAgIE5FVFdPUktfUEVFUl9QT1JUOiAnbmV0d29yay5wZWVyLnBvcnQnLFxuICAgIC8qKlxuICAgICAqIFtPU0kgYXBwbGljYXRpb24gbGF5ZXJdKGh0dHBzOi8vb3NpLW1vZGVsLmNvbS9hcHBsaWNhdGlvbi1sYXllci8pIG9yIG5vbi1PU0kgZXF1aXZhbGVudC5cbiAgICAgKlxuICAgICAqIE5vdGU6IFRoZSB2YWx1ZSBTSE9VTEQgYmUgbm9ybWFsaXplZCB0byBsb3dlcmNhc2UuXG4gICAgICovXG4gICAgTkVUV09SS19QUk9UT0NPTF9OQU1FOiAnbmV0d29yay5wcm90b2NvbC5uYW1lJyxcbiAgICAvKipcbiAgICAgKiBWZXJzaW9uIG9mIHRoZSBwcm90b2NvbCBzcGVjaWZpZWQgaW4gYG5ldHdvcmsucHJvdG9jb2wubmFtZWAuXG4gICAgICpcbiAgICAgKiBOb3RlOiBgbmV0d29yay5wcm90b2NvbC52ZXJzaW9uYCByZWZlcnMgdG8gdGhlIHZlcnNpb24gb2YgdGhlIHByb3RvY29sIHVzZWQgYW5kIG1pZ2h0IGJlIGRpZmZlcmVudCBmcm9tIHRoZSBwcm90b2NvbCBjbGllbnQmIzM5O3MgdmVyc2lvbi4gSWYgdGhlIEhUVFAgY2xpZW50IGhhcyBhIHZlcnNpb24gb2YgYDAuMjcuMmAsIGJ1dCBzZW5kcyBIVFRQIHZlcnNpb24gYDEuMWAsIHRoaXMgYXR0cmlidXRlIHNob3VsZCBiZSBzZXQgdG8gYDEuMWAuXG4gICAgICovXG4gICAgTkVUV09SS19QUk9UT0NPTF9WRVJTSU9OOiAnbmV0d29yay5wcm90b2NvbC52ZXJzaW9uJyxcbiAgICAvKipcbiAgICAgKiBTZXJ2ZXIgZG9tYWluIG5hbWUgaWYgYXZhaWxhYmxlIHdpdGhvdXQgcmV2ZXJzZSBETlMgbG9va3VwOyBvdGhlcndpc2UsIElQIGFkZHJlc3Mgb3IgVW5peCBkb21haW4gc29ja2V0IG5hbWUuXG4gICAgICpcbiAgICAgKiBOb3RlOiBXaGVuIG9ic2VydmVkIGZyb20gdGhlIGNsaWVudCBzaWRlLCBhbmQgd2hlbiBjb21tdW5pY2F0aW5nIHRocm91Z2ggYW4gaW50ZXJtZWRpYXJ5LCBgc2VydmVyLmFkZHJlc3NgIFNIT1VMRCByZXByZXNlbnQgdGhlIHNlcnZlciBhZGRyZXNzIGJlaGluZCBhbnkgaW50ZXJtZWRpYXJpZXMsIGZvciBleGFtcGxlIHByb3hpZXMsIGlmIGl0JiMzOTtzIGF2YWlsYWJsZS5cbiAgICAgKi9cbiAgICBTRVJWRVJfQUREUkVTUzogJ3NlcnZlci5hZGRyZXNzJyxcbiAgICAvKipcbiAgICAgKiBTZXJ2ZXIgcG9ydCBudW1iZXIuXG4gICAgICpcbiAgICAgKiBOb3RlOiBXaGVuIG9ic2VydmVkIGZyb20gdGhlIGNsaWVudCBzaWRlLCBhbmQgd2hlbiBjb21tdW5pY2F0aW5nIHRocm91Z2ggYW4gaW50ZXJtZWRpYXJ5LCBgc2VydmVyLnBvcnRgIFNIT1VMRCByZXByZXNlbnQgdGhlIHNlcnZlciBwb3J0IGJlaGluZCBhbnkgaW50ZXJtZWRpYXJpZXMsIGZvciBleGFtcGxlIHByb3hpZXMsIGlmIGl0JiMzOTtzIGF2YWlsYWJsZS5cbiAgICAgKi9cbiAgICBTRVJWRVJfUE9SVDogJ3NlcnZlci5wb3J0JyxcbiAgICAvKipcbiAgICAqIEFic29sdXRlIFVSTCBkZXNjcmliaW5nIGEgbmV0d29yayByZXNvdXJjZSBhY2NvcmRpbmcgdG8gW1JGQzM5ODZdKGh0dHBzOi8vd3d3LnJmYy1lZGl0b3Iub3JnL3JmYy9yZmMzOTg2KS5cbiAgICAqXG4gICAgKiBOb3RlOiBGb3IgbmV0d29yayBjYWxscywgVVJMIHVzdWFsbHkgaGFzIGBzY2hlbWU6Ly9ob3N0Wzpwb3J0XVtwYXRoXVs/cXVlcnldWyNmcmFnbWVudF1gIGZvcm1hdCwgd2hlcmUgdGhlIGZyYWdtZW50IGlzIG5vdCB0cmFuc21pdHRlZCBvdmVyIEhUVFAsIGJ1dCBpZiBpdCBpcyBrbm93biwgaXQgU0hPVUxEIGJlIGluY2x1ZGVkIG5ldmVydGhlbGVzcy5cbiAgYHVybC5mdWxsYCBNVVNUIE5PVCBjb250YWluIGNyZWRlbnRpYWxzIHBhc3NlZCB2aWEgVVJMIGluIGZvcm0gb2YgYGh0dHBzOi8vdXNlcm5hbWU6cGFzc3dvcmRAd3d3LmV4YW1wbGUuY29tL2AuIEluIHN1Y2ggY2FzZSB1c2VybmFtZSBhbmQgcGFzc3dvcmQgU0hPVUxEIGJlIHJlZGFjdGVkIGFuZCBhdHRyaWJ1dGUmIzM5O3MgdmFsdWUgU0hPVUxEIGJlIGBodHRwczovL1JFREFDVEVEOlJFREFDVEVEQHd3dy5leGFtcGxlLmNvbS9gLlxuICBgdXJsLmZ1bGxgIFNIT1VMRCBjYXB0dXJlIHRoZSBhYnNvbHV0ZSBVUkwgd2hlbiBpdCBpcyBhdmFpbGFibGUgKG9yIGNhbiBiZSByZWNvbnN0cnVjdGVkKSBhbmQgU0hPVUxEIE5PVCBiZSB2YWxpZGF0ZWQgb3IgbW9kaWZpZWQgZXhjZXB0IGZvciBzYW5pdGl6aW5nIHB1cnBvc2VzLlxuICAgICovXG4gICAgVVJMX0ZVTEw6ICd1cmwuZnVsbCcsXG4gICAgLyoqXG4gICAgICogVGhlIFtVUkkgcGF0aF0oaHR0cHM6Ly93d3cucmZjLWVkaXRvci5vcmcvcmZjL3JmYzM5ODYjc2VjdGlvbi0zLjMpIGNvbXBvbmVudC5cbiAgICAgKi9cbiAgICBVUkxfUEFUSDogJ3VybC5wYXRoJyxcbiAgICAvKipcbiAgICAgKiBUaGUgW1VSSSBxdWVyeV0oaHR0cHM6Ly93d3cucmZjLWVkaXRvci5vcmcvcmZjL3JmYzM5ODYjc2VjdGlvbi0zLjQpIGNvbXBvbmVudC5cbiAgICAgKlxuICAgICAqIE5vdGU6IFNlbnNpdGl2ZSBjb250ZW50IHByb3ZpZGVkIGluIHF1ZXJ5IHN0cmluZyBTSE9VTEQgYmUgc2NydWJiZWQgd2hlbiBpbnN0cnVtZW50YXRpb25zIGNhbiBpZGVudGlmeSBpdC5cbiAgICAgKi9cbiAgICBVUkxfUVVFUlk6ICd1cmwucXVlcnknLFxuICAgIC8qKlxuICAgICAqIFRoZSBbVVJJIHNjaGVtZV0oaHR0cHM6Ly93d3cucmZjLWVkaXRvci5vcmcvcmZjL3JmYzM5ODYjc2VjdGlvbi0zLjEpIGNvbXBvbmVudCBpZGVudGlmeWluZyB0aGUgdXNlZCBwcm90b2NvbC5cbiAgICAgKi9cbiAgICBVUkxfU0NIRU1FOiAndXJsLnNjaGVtZScsXG4gICAgLyoqXG4gICAgICogVmFsdWUgb2YgdGhlIFtIVFRQIFVzZXItQWdlbnRdKGh0dHBzOi8vd3d3LnJmYy1lZGl0b3Iub3JnL3JmYy9yZmM5MTEwLmh0bWwjZmllbGQudXNlci1hZ2VudCkgaGVhZGVyIHNlbnQgYnkgdGhlIGNsaWVudC5cbiAgICAgKi9cbiAgICBVU0VSX0FHRU5UX09SSUdJTkFMOiAndXNlcl9hZ2VudC5vcmlnaW5hbCcsXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9U2VtYW50aWNBdHRyaWJ1dGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./undici */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9lYTQyMDIzZmViNjMwY2Y4MWY5N2JkY2UxOGE0NmU5NC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXVuZGljaS9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfZWE0MjAyM2ZlYjYzMGNmODFmOTdiZGNlMThhNDZlOTRcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi11bmRpY2lcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.UndiciInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst diagch = __webpack_require__(/*! diagnostics_channel */ \"diagnostics_channel\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js\");\nconst SemanticAttributes_1 = __webpack_require__(/*! ./enums/SemanticAttributes */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(rsc)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\n// A combination of https://github.com/elastic/apm-agent-nodejs and\n// https://github.com/gadget-inc/opentelemetry-instrumentations/blob/main/packages/opentelemetry-instrumentation-undici/src/index.ts\nclass UndiciInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        this._recordFromReq = new WeakMap();\n    }\n    // No need to instrument files/modules\n    init() {\n        return undefined;\n    }\n    disable() {\n        super.disable();\n        this._channelSubs.forEach(sub => sub.unsubscribe());\n        this._channelSubs.length = 0;\n    }\n    enable() {\n        // \"enabled\" handling is currently a bit messy with InstrumentationBase.\n        // If constructed with `{enabled: false}`, this `.enable()` is still called,\n        // and `this.getConfig().enabled !== this.isEnabled()`, creating confusion.\n        //\n        // For now, this class will setup for instrumenting if `.enable()` is\n        // called, but use `this.getConfig().enabled` to determine if\n        // instrumentation should be generated. This covers the more likely common\n        // case of config being given a construction time, rather than later via\n        // `instance.enable()`, `.disable()`, or `.setConfig()` calls.\n        super.enable();\n        // This method is called by the super-class constructor before ours is\n        // called. So we need to ensure the property is initalized.\n        this._channelSubs = this._channelSubs || [];\n        // Avoid to duplicate subscriptions\n        if (this._channelSubs.length > 0) {\n            return;\n        }\n        this.subscribeToChannel('undici:request:create', this.onRequestCreated.bind(this));\n        this.subscribeToChannel('undici:client:sendHeaders', this.onRequestHeaders.bind(this));\n        this.subscribeToChannel('undici:request:headers', this.onResponseHeaders.bind(this));\n        this.subscribeToChannel('undici:request:trailers', this.onDone.bind(this));\n        this.subscribeToChannel('undici:request:error', this.onError.bind(this));\n    }\n    _updateMetricInstruments() {\n        this._httpClientDurationHistogram = this.meter.createHistogram('http.client.request.duration', {\n            description: 'Measures the duration of outbound HTTP requests.',\n            unit: 's',\n            valueType: api_1.ValueType.DOUBLE,\n            advice: {\n                explicitBucketBoundaries: [\n                    0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1, 2.5, 5,\n                    7.5, 10,\n                ],\n            },\n        });\n    }\n    subscribeToChannel(diagnosticChannel, onMessage) {\n        var _a;\n        // `diagnostics_channel` had a ref counting bug until v18.19.0.\n        // https://github.com/nodejs/node/pull/47520\n        const [major, minor] = process.version\n            .replace('v', '')\n            .split('.')\n            .map(n => Number(n));\n        const useNewSubscribe = major > 18 || (major === 18 && minor >= 19);\n        let unsubscribe;\n        if (useNewSubscribe) {\n            (_a = diagch.subscribe) === null || _a === void 0 ? void 0 : _a.call(diagch, diagnosticChannel, onMessage);\n            unsubscribe = () => { var _a; return (_a = diagch.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(diagch, diagnosticChannel, onMessage); };\n        }\n        else {\n            const channel = diagch.channel(diagnosticChannel);\n            channel.subscribe(onMessage);\n            unsubscribe = () => channel.unsubscribe(onMessage);\n        }\n        this._channelSubs.push({\n            name: diagnosticChannel,\n            unsubscribe,\n        });\n    }\n    // This is the 1st message we receive for each request (fired after request creation). Here we will\n    // create the span and populate some atttributes, then link the span to the request for further\n    // span processing\n    onRequestCreated({ request }) {\n        // Ignore if:\n        // - instrumentation is disabled\n        // - ignored by config\n        // - method is 'CONNECT'\n        const config = this.getConfig();\n        const enabled = config.enabled !== false;\n        const shouldIgnoreReq = (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            var _a;\n            return !enabled ||\n                request.method === 'CONNECT' ||\n                ((_a = config.ignoreRequestHook) === null || _a === void 0 ? void 0 : _a.call(config, request));\n        }, e => e && this._diag.error('caught ignoreRequestHook error: ', e), true);\n        if (shouldIgnoreReq) {\n            return;\n        }\n        const startTime = (0, core_1.hrTime)();\n        let requestUrl;\n        try {\n            requestUrl = new url_1.URL(request.path, request.origin);\n        }\n        catch (err) {\n            this._diag.warn('could not determine url.full:', err);\n            // Skip instrumenting this request.\n            return;\n        }\n        const urlScheme = requestUrl.protocol.replace(':', '');\n        const requestMethod = this.getRequestMethod(request.method);\n        const attributes = {\n            [SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD]: requestMethod,\n            [SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD_ORIGINAL]: request.method,\n            [SemanticAttributes_1.SemanticAttributes.URL_FULL]: requestUrl.toString(),\n            [SemanticAttributes_1.SemanticAttributes.URL_PATH]: requestUrl.pathname,\n            [SemanticAttributes_1.SemanticAttributes.URL_QUERY]: requestUrl.search,\n            [SemanticAttributes_1.SemanticAttributes.URL_SCHEME]: urlScheme,\n        };\n        const schemePorts = { https: '443', http: '80' };\n        const serverAddress = requestUrl.hostname;\n        const serverPort = requestUrl.port || schemePorts[urlScheme];\n        attributes[SemanticAttributes_1.SemanticAttributes.SERVER_ADDRESS] = serverAddress;\n        if (serverPort && !isNaN(Number(serverPort))) {\n            attributes[SemanticAttributes_1.SemanticAttributes.SERVER_PORT] = Number(serverPort);\n        }\n        // Get user agent from headers\n        let userAgent;\n        if (Array.isArray(request.headers)) {\n            const idx = request.headers.findIndex(h => h.toLowerCase() === 'user-agent');\n            if (idx >= 0) {\n                userAgent = request.headers[idx + 1];\n            }\n        }\n        else if (typeof request.headers === 'string') {\n            const headers = request.headers.split('\\r\\n');\n            const uaHeader = headers.find(h => h.toLowerCase().startsWith('user-agent'));\n            userAgent =\n                uaHeader && uaHeader.substring(uaHeader.indexOf(':') + 1).trim();\n        }\n        if (userAgent) {\n            attributes[SemanticAttributes_1.SemanticAttributes.USER_AGENT_ORIGINAL] = userAgent;\n        }\n        // Get attributes from the hook if present\n        const hookAttributes = (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.startSpanHook) === null || _a === void 0 ? void 0 : _a.call(config, request); }, e => e && this._diag.error('caught startSpanHook error: ', e), true);\n        if (hookAttributes) {\n            Object.entries(hookAttributes).forEach(([key, val]) => {\n                attributes[key] = val;\n            });\n        }\n        // Check if parent span is required via config and:\n        // - if a parent is required but not present, we use a `NoopSpan` to still\n        //   propagate context without recording it.\n        // - create a span otherwise\n        const activeCtx = api_1.context.active();\n        const currentSpan = api_1.trace.getSpan(activeCtx);\n        let span;\n        if (config.requireParentforSpans &&\n            (!currentSpan || !api_1.trace.isSpanContextValid(currentSpan.spanContext()))) {\n            span = api_1.trace.wrapSpanContext(api_1.INVALID_SPAN_CONTEXT);\n        }\n        else {\n            span = this.tracer.startSpan(requestMethod === '_OTHER' ? 'HTTP' : requestMethod, {\n                kind: api_1.SpanKind.CLIENT,\n                attributes: attributes,\n            }, activeCtx);\n        }\n        // Execute the request hook if defined\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.requestHook) === null || _a === void 0 ? void 0 : _a.call(config, span, request); }, e => e && this._diag.error('caught requestHook error: ', e), true);\n        // Context propagation goes last so no hook can tamper\n        // the propagation headers\n        const requestContext = api_1.trace.setSpan(api_1.context.active(), span);\n        const addedHeaders = {};\n        api_1.propagation.inject(requestContext, addedHeaders);\n        const headerEntries = Object.entries(addedHeaders);\n        for (let i = 0; i < headerEntries.length; i++) {\n            const [k, v] = headerEntries[i];\n            if (typeof request.addHeader === 'function') {\n                request.addHeader(k, v);\n            }\n            else if (typeof request.headers === 'string') {\n                request.headers += `${k}: ${v}\\r\\n`;\n            }\n            else if (Array.isArray(request.headers)) {\n                // undici@6.11.0 accidentally, briefly removed `request.addHeader()`.\n                request.headers.push(k, v);\n            }\n        }\n        this._recordFromReq.set(request, { span, attributes, startTime });\n    }\n    // This is the 2nd message we receive for each request. It is fired when connection with\n    // the remote is established and about to send the first byte. Here we do have info about the\n    // remote address and port so we can populate some `network.*` attributes into the span\n    onRequestHeaders({ request, socket }) {\n        var _a;\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const config = this.getConfig();\n        const { span } = record;\n        const { remoteAddress, remotePort } = socket;\n        const spanAttributes = {\n            [SemanticAttributes_1.SemanticAttributes.NETWORK_PEER_ADDRESS]: remoteAddress,\n            [SemanticAttributes_1.SemanticAttributes.NETWORK_PEER_PORT]: remotePort,\n        };\n        // After hooks have been processed (which may modify request headers)\n        // we can collect the headers based on the configuration\n        if ((_a = config.headersToSpanAttributes) === null || _a === void 0 ? void 0 : _a.requestHeaders) {\n            const headersToAttribs = new Set(config.headersToSpanAttributes.requestHeaders.map(n => n.toLowerCase()));\n            // headers could be in form\n            // ['name: value', ...] for v5\n            // ['name', 'value', ...] for v6\n            const rawHeaders = Array.isArray(request.headers)\n                ? request.headers\n                : request.headers.split('\\r\\n');\n            rawHeaders.forEach((h, idx) => {\n                const sepIndex = h.indexOf(':');\n                const hasSeparator = sepIndex !== -1;\n                const name = (hasSeparator ? h.substring(0, sepIndex) : h).toLowerCase();\n                const value = hasSeparator\n                    ? h.substring(sepIndex + 1)\n                    : rawHeaders[idx + 1];\n                if (headersToAttribs.has(name)) {\n                    spanAttributes[`http.request.header.${name}`] = value.trim();\n                }\n            });\n        }\n        span.setAttributes(spanAttributes);\n    }\n    // This is the 3rd message we get for each request and it's fired when the server\n    // headers are received, body may not be accessible yet.\n    // From the response headers we can set the status and content length\n    onResponseHeaders({ request, response, }) {\n        var _a, _b;\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes } = record;\n        const spanAttributes = {\n            [SemanticAttributes_1.SemanticAttributes.HTTP_RESPONSE_STATUS_CODE]: response.statusCode,\n        };\n        const config = this.getConfig();\n        // Execute the response hook if defined\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.responseHook) === null || _a === void 0 ? void 0 : _a.call(config, span, { request, response }); }, e => e && this._diag.error('caught responseHook error: ', e), true);\n        const headersToAttribs = new Set();\n        if ((_a = config.headersToSpanAttributes) === null || _a === void 0 ? void 0 : _a.responseHeaders) {\n            (_b = config.headersToSpanAttributes) === null || _b === void 0 ? void 0 : _b.responseHeaders.forEach(name => headersToAttribs.add(name.toLowerCase()));\n        }\n        for (let idx = 0; idx < response.headers.length; idx = idx + 2) {\n            const name = response.headers[idx].toString().toLowerCase();\n            const value = response.headers[idx + 1];\n            if (headersToAttribs.has(name)) {\n                spanAttributes[`http.response.header.${name}`] = value.toString();\n            }\n            if (name === 'content-length') {\n                const contentLength = Number(value.toString());\n                if (!isNaN(contentLength)) {\n                    spanAttributes['http.response.header.content-length'] = contentLength;\n                }\n            }\n        }\n        span.setAttributes(spanAttributes);\n        span.setStatus({\n            code: response.statusCode >= 400\n                ? api_1.SpanStatusCode.ERROR\n                : api_1.SpanStatusCode.UNSET,\n        });\n        record.attributes = Object.assign(attributes, spanAttributes);\n    }\n    // This is the last event we receive if the request went without any errors\n    onDone({ request }) {\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes, startTime } = record;\n        // End the span\n        span.end();\n        this._recordFromReq.delete(request);\n        // Record metrics\n        this.recordRequestDuration(attributes, startTime);\n    }\n    // This is the event we get when something is wrong in the request like\n    // - invalid options when calling `fetch` global API or any undici method for request\n    // - connectivity errors such as unreachable host\n    // - requests aborted through an `AbortController.signal`\n    // NOTE: server errors are considered valid responses and it's the lib consumer\n    // who should deal with that.\n    onError({ request, error }) {\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes, startTime } = record;\n        // NOTE: in `undici@6.3.0` when request aborted the error type changes from\n        // a custom error (`RequestAbortedError`) to a built-in `DOMException` carrying\n        // some differences:\n        // - `code` is from DOMEXception (ABORT_ERR: 20)\n        // - `message` changes\n        // - stacktrace is smaller and contains node internal frames\n        span.recordException(error);\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: error.message,\n        });\n        span.end();\n        this._recordFromReq.delete(request);\n        // Record metrics (with the error)\n        attributes[SemanticAttributes_1.SemanticAttributes.ERROR_TYPE] = error.message;\n        this.recordRequestDuration(attributes, startTime);\n    }\n    recordRequestDuration(attributes, startTime) {\n        // Time to record metrics\n        const metricsAttributes = {};\n        // Get the attribs already in span attributes\n        const keysToCopy = [\n            SemanticAttributes_1.SemanticAttributes.HTTP_RESPONSE_STATUS_CODE,\n            SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD,\n            SemanticAttributes_1.SemanticAttributes.SERVER_ADDRESS,\n            SemanticAttributes_1.SemanticAttributes.SERVER_PORT,\n            SemanticAttributes_1.SemanticAttributes.URL_SCHEME,\n            SemanticAttributes_1.SemanticAttributes.ERROR_TYPE,\n        ];\n        keysToCopy.forEach(key => {\n            if (key in attributes) {\n                metricsAttributes[key] = attributes[key];\n            }\n        });\n        // Take the duration and record it\n        const durationSeconds = (0, core_1.hrTimeToMilliseconds)((0, core_1.hrTimeDuration)(startTime, (0, core_1.hrTime)())) / 1000;\n        this._httpClientDurationHistogram.record(durationSeconds, metricsAttributes);\n    }\n    getRequestMethod(original) {\n        const knownMethods = {\n            CONNECT: true,\n            OPTIONS: true,\n            HEAD: true,\n            GET: true,\n            POST: true,\n            PUT: true,\n            PATCH: true,\n            DELETE: true,\n            TRACE: true,\n        };\n        if (original.toUpperCase() in knownMethods) {\n            return original.toUpperCase();\n        }\n        return '_OTHER';\n    }\n}\nexports.UndiciInstrumentation = UndiciInstrumentation;\n//# sourceMappingURL=undici.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.10.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-undici';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js ***!
  \**************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SemanticAttributes = void 0;\n// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2\nexports.SemanticAttributes = {\n    /**\n     * State of the HTTP connection in the HTTP connection pool.\n     */\n    HTTP_CONNECTION_STATE: 'http.connection.state',\n    /**\n    * Describes a class of error the operation ended with.\n    *\n    * Note: The `error.type` SHOULD be predictable and SHOULD have low cardinality.\n  Instrumentations SHOULD document the list of errors they report.\n  \n  The cardinality of `error.type` within one instrumentation library SHOULD be low.\n  Telemetry consumers that aggregate data from multiple instrumentation libraries and applications\n  should be prepared for `error.type` to have high cardinality at query time when no\n  additional filters are applied.\n  \n  If the operation has completed successfully, instrumentations SHOULD NOT set `error.type`.\n  \n  If a specific domain defines its own set of error identifiers (such as HTTP or gRPC status codes),\n  it&#39;s RECOMMENDED to:\n  \n  * Use a domain-specific attribute\n  * Set `error.type` to capture all errors, regardless of whether they are defined within the domain-specific set or not.\n    */\n    ERROR_TYPE: 'error.type',\n    /**\n     * The size of the request payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n     */\n    HTTP_REQUEST_BODY_SIZE: 'http.request.body.size',\n    /**\n    * HTTP request method.\n    *\n    * Note: HTTP request method value SHOULD be &#34;known&#34; to the instrumentation.\n  By default, this convention defines &#34;known&#34; methods as the ones listed in [RFC9110](https://www.rfc-editor.org/rfc/rfc9110.html#name-methods)\n  and the PATCH method defined in [RFC5789](https://www.rfc-editor.org/rfc/rfc5789.html).\n  \n  If the HTTP request method is not known to instrumentation, it MUST set the `http.request.method` attribute to `_OTHER`.\n  \n  If the HTTP instrumentation could end up converting valid HTTP request methods to `_OTHER`, then it MUST provide a way to override\n  the list of known HTTP methods. If this override is done via environment variable, then the environment variable MUST be named\n  OTEL_INSTRUMENTATION_HTTP_KNOWN_METHODS and support a comma-separated list of case-sensitive known HTTP methods\n  (this list MUST be a full override of the default known method, it is not a list of known methods in addition to the defaults).\n  \n  HTTP method names are case-sensitive and `http.request.method` attribute value MUST match a known HTTP method name exactly.\n  Instrumentations for specific web frameworks that consider HTTP methods to be case insensitive, SHOULD populate a canonical equivalent.\n  Tracing instrumentations that do so, MUST also set `http.request.method_original` to the original value.\n    */\n    HTTP_REQUEST_METHOD: 'http.request.method',\n    /**\n     * Original HTTP method sent by the client in the request line.\n     */\n    HTTP_REQUEST_METHOD_ORIGINAL: 'http.request.method_original',\n    /**\n     * The ordinal number of request resending attempt (for any reason, including redirects).\n     *\n     * Note: The resend count SHOULD be updated each time an HTTP request gets resent by the client, regardless of what was the cause of the resending (e.g. redirection, authorization failure, 503 Server Unavailable, network issues, or any other).\n     */\n    HTTP_REQUEST_RESEND_COUNT: 'http.request.resend_count',\n    /**\n     * The size of the response payload body in bytes. This is the number of bytes transferred excluding headers and is often, but not always, present as the [Content-Length](https://www.rfc-editor.org/rfc/rfc9110.html#field.content-length) header. For requests using transport encoding, this should be the compressed size.\n     */\n    HTTP_RESPONSE_BODY_SIZE: 'http.response.body.size',\n    /**\n     * [HTTP response status code](https://tools.ietf.org/html/rfc7231#section-6).\n     */\n    HTTP_RESPONSE_STATUS_CODE: 'http.response.status_code',\n    /**\n    * The matched route, that is, the path template in the format used by the respective server framework.\n    *\n    * Note: MUST NOT be populated when this is not supported by the HTTP server framework as the route attribute should have low-cardinality and the URI path can NOT substitute it.\n  SHOULD include the [application root](/docs/http/http-spans.md#http-server-definitions) if there is one.\n    */\n    HTTP_ROUTE: 'http.route',\n    /**\n     * Peer address of the network connection - IP address or Unix domain socket name.\n     */\n    NETWORK_PEER_ADDRESS: 'network.peer.address',\n    /**\n     * Peer port number of the network connection.\n     */\n    NETWORK_PEER_PORT: 'network.peer.port',\n    /**\n     * [OSI application layer](https://osi-model.com/application-layer/) or non-OSI equivalent.\n     *\n     * Note: The value SHOULD be normalized to lowercase.\n     */\n    NETWORK_PROTOCOL_NAME: 'network.protocol.name',\n    /**\n     * Version of the protocol specified in `network.protocol.name`.\n     *\n     * Note: `network.protocol.version` refers to the version of the protocol used and might be different from the protocol client&#39;s version. If the HTTP client has a version of `0.27.2`, but sends HTTP version `1.1`, this attribute should be set to `1.1`.\n     */\n    NETWORK_PROTOCOL_VERSION: 'network.protocol.version',\n    /**\n     * Server domain name if available without reverse DNS lookup; otherwise, IP address or Unix domain socket name.\n     *\n     * Note: When observed from the client side, and when communicating through an intermediary, `server.address` SHOULD represent the server address behind any intermediaries, for example proxies, if it&#39;s available.\n     */\n    SERVER_ADDRESS: 'server.address',\n    /**\n     * Server port number.\n     *\n     * Note: When observed from the client side, and when communicating through an intermediary, `server.port` SHOULD represent the server port behind any intermediaries, for example proxies, if it&#39;s available.\n     */\n    SERVER_PORT: 'server.port',\n    /**\n    * Absolute URL describing a network resource according to [RFC3986](https://www.rfc-editor.org/rfc/rfc3986).\n    *\n    * Note: For network calls, URL usually has `scheme://host[:port][path][?query][#fragment]` format, where the fragment is not transmitted over HTTP, but if it is known, it SHOULD be included nevertheless.\n  `url.full` MUST NOT contain credentials passed via URL in form of `https://username:<EMAIL>/`. In such case username and password SHOULD be redacted and attribute&#39;s value SHOULD be `https://REDACTED:<EMAIL>/`.\n  `url.full` SHOULD capture the absolute URL when it is available (or can be reconstructed) and SHOULD NOT be validated or modified except for sanitizing purposes.\n    */\n    URL_FULL: 'url.full',\n    /**\n     * The [URI path](https://www.rfc-editor.org/rfc/rfc3986#section-3.3) component.\n     */\n    URL_PATH: 'url.path',\n    /**\n     * The [URI query](https://www.rfc-editor.org/rfc/rfc3986#section-3.4) component.\n     *\n     * Note: Sensitive content provided in query string SHOULD be scrubbed when instrumentations can identify it.\n     */\n    URL_QUERY: 'url.query',\n    /**\n     * The [URI scheme](https://www.rfc-editor.org/rfc/rfc3986#section-3.1) component identifying the used protocol.\n     */\n    URL_SCHEME: 'url.scheme',\n    /**\n     * Value of the [HTTP User-Agent](https://www.rfc-editor.org/rfc/rfc9110.html#field.user-agent) header sent by the client.\n     */\n    USER_AGENT_ORIGINAL: 'user_agent.original',\n};\n//# sourceMappingURL=SemanticAttributes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js ***!
  \*******************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./undici */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js\"), exports);\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BvcGVudGVsZW1ldHJ5K2luc3RydW1lbnRhdF9lYTQyMDIzZmViNjMwY2Y4MWY5N2JkY2UxOGE0NmU5NC9ub2RlX21vZHVsZXMvQG9wZW50ZWxlbWV0cnkvaW5zdHJ1bWVudGF0aW9uLXVuZGljaS9idWlsZC9zcmMvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW53YXJcXERvY3VtZW50c1xcMiBGT0xERVJTIEZPUiBDVUJFTlRcXEN1YmVudHdlYlxcY3ViZW50XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAb3BlbnRlbGVtZXRyeStpbnN0cnVtZW50YXRfZWE0MjAyM2ZlYjYzMGNmODFmOTdiZGNlMThhNDZlOTRcXG5vZGVfbW9kdWxlc1xcQG9wZW50ZWxlbWV0cnlcXGluc3RydW1lbnRhdGlvbi11bmRpY2lcXGJ1aWxkXFxzcmNcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.UndiciInstrumentation = void 0;\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst diagch = __webpack_require__(/*! diagnostics_channel */ \"diagnostics_channel\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst instrumentation_1 = __webpack_require__(/*! @opentelemetry/instrumentation */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/index.js\");\nconst api_1 = __webpack_require__(/*! @opentelemetry/api */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js\");\n/** @knipignore */\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js\");\nconst SemanticAttributes_1 = __webpack_require__(/*! ./enums/SemanticAttributes */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/enums/SemanticAttributes.js\");\nconst core_1 = __webpack_require__(/*! @opentelemetry/core */ \"(ssr)/../../node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js\");\n// A combination of https://github.com/elastic/apm-agent-nodejs and\n// https://github.com/gadget-inc/opentelemetry-instrumentations/blob/main/packages/opentelemetry-instrumentation-undici/src/index.ts\nclass UndiciInstrumentation extends instrumentation_1.InstrumentationBase {\n    constructor(config = {}) {\n        super(version_1.PACKAGE_NAME, version_1.PACKAGE_VERSION, config);\n        this._recordFromReq = new WeakMap();\n    }\n    // No need to instrument files/modules\n    init() {\n        return undefined;\n    }\n    disable() {\n        super.disable();\n        this._channelSubs.forEach(sub => sub.unsubscribe());\n        this._channelSubs.length = 0;\n    }\n    enable() {\n        // \"enabled\" handling is currently a bit messy with InstrumentationBase.\n        // If constructed with `{enabled: false}`, this `.enable()` is still called,\n        // and `this.getConfig().enabled !== this.isEnabled()`, creating confusion.\n        //\n        // For now, this class will setup for instrumenting if `.enable()` is\n        // called, but use `this.getConfig().enabled` to determine if\n        // instrumentation should be generated. This covers the more likely common\n        // case of config being given a construction time, rather than later via\n        // `instance.enable()`, `.disable()`, or `.setConfig()` calls.\n        super.enable();\n        // This method is called by the super-class constructor before ours is\n        // called. So we need to ensure the property is initalized.\n        this._channelSubs = this._channelSubs || [];\n        // Avoid to duplicate subscriptions\n        if (this._channelSubs.length > 0) {\n            return;\n        }\n        this.subscribeToChannel('undici:request:create', this.onRequestCreated.bind(this));\n        this.subscribeToChannel('undici:client:sendHeaders', this.onRequestHeaders.bind(this));\n        this.subscribeToChannel('undici:request:headers', this.onResponseHeaders.bind(this));\n        this.subscribeToChannel('undici:request:trailers', this.onDone.bind(this));\n        this.subscribeToChannel('undici:request:error', this.onError.bind(this));\n    }\n    _updateMetricInstruments() {\n        this._httpClientDurationHistogram = this.meter.createHistogram('http.client.request.duration', {\n            description: 'Measures the duration of outbound HTTP requests.',\n            unit: 's',\n            valueType: api_1.ValueType.DOUBLE,\n            advice: {\n                explicitBucketBoundaries: [\n                    0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1, 2.5, 5,\n                    7.5, 10,\n                ],\n            },\n        });\n    }\n    subscribeToChannel(diagnosticChannel, onMessage) {\n        var _a;\n        // `diagnostics_channel` had a ref counting bug until v18.19.0.\n        // https://github.com/nodejs/node/pull/47520\n        const [major, minor] = process.version\n            .replace('v', '')\n            .split('.')\n            .map(n => Number(n));\n        const useNewSubscribe = major > 18 || (major === 18 && minor >= 19);\n        let unsubscribe;\n        if (useNewSubscribe) {\n            (_a = diagch.subscribe) === null || _a === void 0 ? void 0 : _a.call(diagch, diagnosticChannel, onMessage);\n            unsubscribe = () => { var _a; return (_a = diagch.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(diagch, diagnosticChannel, onMessage); };\n        }\n        else {\n            const channel = diagch.channel(diagnosticChannel);\n            channel.subscribe(onMessage);\n            unsubscribe = () => channel.unsubscribe(onMessage);\n        }\n        this._channelSubs.push({\n            name: diagnosticChannel,\n            unsubscribe,\n        });\n    }\n    // This is the 1st message we receive for each request (fired after request creation). Here we will\n    // create the span and populate some atttributes, then link the span to the request for further\n    // span processing\n    onRequestCreated({ request }) {\n        // Ignore if:\n        // - instrumentation is disabled\n        // - ignored by config\n        // - method is 'CONNECT'\n        const config = this.getConfig();\n        const enabled = config.enabled !== false;\n        const shouldIgnoreReq = (0, instrumentation_1.safeExecuteInTheMiddle)(() => {\n            var _a;\n            return !enabled ||\n                request.method === 'CONNECT' ||\n                ((_a = config.ignoreRequestHook) === null || _a === void 0 ? void 0 : _a.call(config, request));\n        }, e => e && this._diag.error('caught ignoreRequestHook error: ', e), true);\n        if (shouldIgnoreReq) {\n            return;\n        }\n        const startTime = (0, core_1.hrTime)();\n        let requestUrl;\n        try {\n            requestUrl = new url_1.URL(request.path, request.origin);\n        }\n        catch (err) {\n            this._diag.warn('could not determine url.full:', err);\n            // Skip instrumenting this request.\n            return;\n        }\n        const urlScheme = requestUrl.protocol.replace(':', '');\n        const requestMethod = this.getRequestMethod(request.method);\n        const attributes = {\n            [SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD]: requestMethod,\n            [SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD_ORIGINAL]: request.method,\n            [SemanticAttributes_1.SemanticAttributes.URL_FULL]: requestUrl.toString(),\n            [SemanticAttributes_1.SemanticAttributes.URL_PATH]: requestUrl.pathname,\n            [SemanticAttributes_1.SemanticAttributes.URL_QUERY]: requestUrl.search,\n            [SemanticAttributes_1.SemanticAttributes.URL_SCHEME]: urlScheme,\n        };\n        const schemePorts = { https: '443', http: '80' };\n        const serverAddress = requestUrl.hostname;\n        const serverPort = requestUrl.port || schemePorts[urlScheme];\n        attributes[SemanticAttributes_1.SemanticAttributes.SERVER_ADDRESS] = serverAddress;\n        if (serverPort && !isNaN(Number(serverPort))) {\n            attributes[SemanticAttributes_1.SemanticAttributes.SERVER_PORT] = Number(serverPort);\n        }\n        // Get user agent from headers\n        let userAgent;\n        if (Array.isArray(request.headers)) {\n            const idx = request.headers.findIndex(h => h.toLowerCase() === 'user-agent');\n            if (idx >= 0) {\n                userAgent = request.headers[idx + 1];\n            }\n        }\n        else if (typeof request.headers === 'string') {\n            const headers = request.headers.split('\\r\\n');\n            const uaHeader = headers.find(h => h.toLowerCase().startsWith('user-agent'));\n            userAgent =\n                uaHeader && uaHeader.substring(uaHeader.indexOf(':') + 1).trim();\n        }\n        if (userAgent) {\n            attributes[SemanticAttributes_1.SemanticAttributes.USER_AGENT_ORIGINAL] = userAgent;\n        }\n        // Get attributes from the hook if present\n        const hookAttributes = (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.startSpanHook) === null || _a === void 0 ? void 0 : _a.call(config, request); }, e => e && this._diag.error('caught startSpanHook error: ', e), true);\n        if (hookAttributes) {\n            Object.entries(hookAttributes).forEach(([key, val]) => {\n                attributes[key] = val;\n            });\n        }\n        // Check if parent span is required via config and:\n        // - if a parent is required but not present, we use a `NoopSpan` to still\n        //   propagate context without recording it.\n        // - create a span otherwise\n        const activeCtx = api_1.context.active();\n        const currentSpan = api_1.trace.getSpan(activeCtx);\n        let span;\n        if (config.requireParentforSpans &&\n            (!currentSpan || !api_1.trace.isSpanContextValid(currentSpan.spanContext()))) {\n            span = api_1.trace.wrapSpanContext(api_1.INVALID_SPAN_CONTEXT);\n        }\n        else {\n            span = this.tracer.startSpan(requestMethod === '_OTHER' ? 'HTTP' : requestMethod, {\n                kind: api_1.SpanKind.CLIENT,\n                attributes: attributes,\n            }, activeCtx);\n        }\n        // Execute the request hook if defined\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.requestHook) === null || _a === void 0 ? void 0 : _a.call(config, span, request); }, e => e && this._diag.error('caught requestHook error: ', e), true);\n        // Context propagation goes last so no hook can tamper\n        // the propagation headers\n        const requestContext = api_1.trace.setSpan(api_1.context.active(), span);\n        const addedHeaders = {};\n        api_1.propagation.inject(requestContext, addedHeaders);\n        const headerEntries = Object.entries(addedHeaders);\n        for (let i = 0; i < headerEntries.length; i++) {\n            const [k, v] = headerEntries[i];\n            if (typeof request.addHeader === 'function') {\n                request.addHeader(k, v);\n            }\n            else if (typeof request.headers === 'string') {\n                request.headers += `${k}: ${v}\\r\\n`;\n            }\n            else if (Array.isArray(request.headers)) {\n                // undici@6.11.0 accidentally, briefly removed `request.addHeader()`.\n                request.headers.push(k, v);\n            }\n        }\n        this._recordFromReq.set(request, { span, attributes, startTime });\n    }\n    // This is the 2nd message we receive for each request. It is fired when connection with\n    // the remote is established and about to send the first byte. Here we do have info about the\n    // remote address and port so we can populate some `network.*` attributes into the span\n    onRequestHeaders({ request, socket }) {\n        var _a;\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const config = this.getConfig();\n        const { span } = record;\n        const { remoteAddress, remotePort } = socket;\n        const spanAttributes = {\n            [SemanticAttributes_1.SemanticAttributes.NETWORK_PEER_ADDRESS]: remoteAddress,\n            [SemanticAttributes_1.SemanticAttributes.NETWORK_PEER_PORT]: remotePort,\n        };\n        // After hooks have been processed (which may modify request headers)\n        // we can collect the headers based on the configuration\n        if ((_a = config.headersToSpanAttributes) === null || _a === void 0 ? void 0 : _a.requestHeaders) {\n            const headersToAttribs = new Set(config.headersToSpanAttributes.requestHeaders.map(n => n.toLowerCase()));\n            // headers could be in form\n            // ['name: value', ...] for v5\n            // ['name', 'value', ...] for v6\n            const rawHeaders = Array.isArray(request.headers)\n                ? request.headers\n                : request.headers.split('\\r\\n');\n            rawHeaders.forEach((h, idx) => {\n                const sepIndex = h.indexOf(':');\n                const hasSeparator = sepIndex !== -1;\n                const name = (hasSeparator ? h.substring(0, sepIndex) : h).toLowerCase();\n                const value = hasSeparator\n                    ? h.substring(sepIndex + 1)\n                    : rawHeaders[idx + 1];\n                if (headersToAttribs.has(name)) {\n                    spanAttributes[`http.request.header.${name}`] = value.trim();\n                }\n            });\n        }\n        span.setAttributes(spanAttributes);\n    }\n    // This is the 3rd message we get for each request and it's fired when the server\n    // headers are received, body may not be accessible yet.\n    // From the response headers we can set the status and content length\n    onResponseHeaders({ request, response, }) {\n        var _a, _b;\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes } = record;\n        const spanAttributes = {\n            [SemanticAttributes_1.SemanticAttributes.HTTP_RESPONSE_STATUS_CODE]: response.statusCode,\n        };\n        const config = this.getConfig();\n        // Execute the response hook if defined\n        (0, instrumentation_1.safeExecuteInTheMiddle)(() => { var _a; return (_a = config.responseHook) === null || _a === void 0 ? void 0 : _a.call(config, span, { request, response }); }, e => e && this._diag.error('caught responseHook error: ', e), true);\n        const headersToAttribs = new Set();\n        if ((_a = config.headersToSpanAttributes) === null || _a === void 0 ? void 0 : _a.responseHeaders) {\n            (_b = config.headersToSpanAttributes) === null || _b === void 0 ? void 0 : _b.responseHeaders.forEach(name => headersToAttribs.add(name.toLowerCase()));\n        }\n        for (let idx = 0; idx < response.headers.length; idx = idx + 2) {\n            const name = response.headers[idx].toString().toLowerCase();\n            const value = response.headers[idx + 1];\n            if (headersToAttribs.has(name)) {\n                spanAttributes[`http.response.header.${name}`] = value.toString();\n            }\n            if (name === 'content-length') {\n                const contentLength = Number(value.toString());\n                if (!isNaN(contentLength)) {\n                    spanAttributes['http.response.header.content-length'] = contentLength;\n                }\n            }\n        }\n        span.setAttributes(spanAttributes);\n        span.setStatus({\n            code: response.statusCode >= 400\n                ? api_1.SpanStatusCode.ERROR\n                : api_1.SpanStatusCode.UNSET,\n        });\n        record.attributes = Object.assign(attributes, spanAttributes);\n    }\n    // This is the last event we receive if the request went without any errors\n    onDone({ request }) {\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes, startTime } = record;\n        // End the span\n        span.end();\n        this._recordFromReq.delete(request);\n        // Record metrics\n        this.recordRequestDuration(attributes, startTime);\n    }\n    // This is the event we get when something is wrong in the request like\n    // - invalid options when calling `fetch` global API or any undici method for request\n    // - connectivity errors such as unreachable host\n    // - requests aborted through an `AbortController.signal`\n    // NOTE: server errors are considered valid responses and it's the lib consumer\n    // who should deal with that.\n    onError({ request, error }) {\n        const record = this._recordFromReq.get(request);\n        if (!record) {\n            return;\n        }\n        const { span, attributes, startTime } = record;\n        // NOTE: in `undici@6.3.0` when request aborted the error type changes from\n        // a custom error (`RequestAbortedError`) to a built-in `DOMException` carrying\n        // some differences:\n        // - `code` is from DOMEXception (ABORT_ERR: 20)\n        // - `message` changes\n        // - stacktrace is smaller and contains node internal frames\n        span.recordException(error);\n        span.setStatus({\n            code: api_1.SpanStatusCode.ERROR,\n            message: error.message,\n        });\n        span.end();\n        this._recordFromReq.delete(request);\n        // Record metrics (with the error)\n        attributes[SemanticAttributes_1.SemanticAttributes.ERROR_TYPE] = error.message;\n        this.recordRequestDuration(attributes, startTime);\n    }\n    recordRequestDuration(attributes, startTime) {\n        // Time to record metrics\n        const metricsAttributes = {};\n        // Get the attribs already in span attributes\n        const keysToCopy = [\n            SemanticAttributes_1.SemanticAttributes.HTTP_RESPONSE_STATUS_CODE,\n            SemanticAttributes_1.SemanticAttributes.HTTP_REQUEST_METHOD,\n            SemanticAttributes_1.SemanticAttributes.SERVER_ADDRESS,\n            SemanticAttributes_1.SemanticAttributes.SERVER_PORT,\n            SemanticAttributes_1.SemanticAttributes.URL_SCHEME,\n            SemanticAttributes_1.SemanticAttributes.ERROR_TYPE,\n        ];\n        keysToCopy.forEach(key => {\n            if (key in attributes) {\n                metricsAttributes[key] = attributes[key];\n            }\n        });\n        // Take the duration and record it\n        const durationSeconds = (0, core_1.hrTimeToMilliseconds)((0, core_1.hrTimeDuration)(startTime, (0, core_1.hrTime)())) / 1000;\n        this._httpClientDurationHistogram.record(durationSeconds, metricsAttributes);\n    }\n    getRequestMethod(original) {\n        const knownMethods = {\n            CONNECT: true,\n            OPTIONS: true,\n            HEAD: true,\n            GET: true,\n            POST: true,\n            PUT: true,\n            PATCH: true,\n            DELETE: true,\n            TRACE: true,\n        };\n        if (original.toUpperCase() in knownMethods) {\n            return original.toUpperCase();\n        }\n        return '_OTHER';\n    }\n}\nexports.UndiciInstrumentation = UndiciInstrumentation;\n//# sourceMappingURL=undici.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/undici.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js":
/*!*********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js ***!
  \*********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PACKAGE_NAME = exports.PACKAGE_VERSION = void 0;\n// this is autogenerated file, see scripts/version-update.js\nexports.PACKAGE_VERSION = '0.10.1';\nexports.PACKAGE_NAME = '@opentelemetry/instrumentation-undici';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@opentelemetry+instrumentat_ea42023feb630cf81f97bdce18a46e94/node_modules/@opentelemetry/instrumentation-undici/build/src/version.js\n");

/***/ })

};
;