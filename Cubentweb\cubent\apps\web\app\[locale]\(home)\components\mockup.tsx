'use client';

import Image from 'next/image';
import Link from 'next/link';
import { MoveRight, X } from 'lucide-react';
import { useState } from 'react';

// Feature data
const features = [
  {
    id: 1,
    title: "Purpose-built for product development",
    image: "/images/cubent-feature-1.png",
    alt: "Context Intelligence",
    description: "<PERSON><PERSON><PERSON> understands your entire codebase context, making intelligent suggestions that align with your project's architecture and patterns.",
    details: [
      "Deep codebase analysis and understanding",
      "Context-aware code suggestions",
      "Architecture pattern recognition",
      "Smart refactoring recommendations",
      "Cross-file dependency tracking"
    ]
  },
  {
    id: 2,
    title: "Designed to move fast",
    image: "/images/cubent-feature-2.png",
    alt: "AI Screenshot Analysis",
    description: "Transform screenshots and designs into working code instantly. <PERSON><PERSON><PERSON> analyzes visual elements and generates pixel-perfect implementations.",
    details: [
      "Screenshot to code conversion",
      "Design system integration",
      "Component library generation",
      "Responsive layout creation",
      "Style guide adherence"
    ]
  },
  {
    id: 3,
    title: "Crafted to perfection",
    image: "/images/cubent-feature-3.png",
    alt: "Smart Code Editing",
    description: "Experience intelligent code editing that understands your intent. <PERSON><PERSON><PERSON> provides contextual suggestions and automated improvements.",
    details: [
      "Intelligent code completion",
      "Automated code optimization",
      "Bug detection and fixes",
      "Performance improvements",
      "Code quality enhancements"
    ]
  }
];

export const Mockup = () => {
  const [selectedFeature, setSelectedFeature] = useState<typeof features[0] | null>(null);

  return (
  <div className="w-full">
    <div className="container mx-auto px-2 sm:px-4 lg:px-6">
      <div className="flex flex-col items-center justify-center gap-2 py-4">
        <div className="relative w-full max-w-7xl">
          <div className="relative overflow-hidden">
            <Image
              src="/images/Cubent.Dev.gif"
              alt="Cubent Editor Interface - Code editing with AI assistance"
              width={1200}
              height={800}
              className="w-full h-auto object-cover rounded-lg"
              priority
              unoptimized
            />
            {/* Soft glow effect */}
            <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 rounded-2xl blur-xl opacity-30 -z-10" />
          </div>
        </div>
      </div>
    </div>

    {/* Made for modern product teams section */}
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
      {/* Top section - Title on left, description on right */}
      <div className="flex flex-col lg:flex-row gap-8 lg:gap-16 items-start mb-16 lg:mb-20 max-w-5xl mx-auto">
        {/* Left side - Title */}
        <div className="flex-1 max-w-md">
          <h2 className="text-4xl lg:text-5xl font-regular tracking-tighter text-white">
            Made for modern product teams
          </h2>
        </div>

        {/* Right side - Description and link */}
        <div className="flex-1 max-w-lg">
          <p className="text-lg text-muted-foreground leading-relaxed mb-4">
            Cubent is shaped by the practices and principles that distinguish world-class product teams from the rest: relentless focus, fast execution, and a commitment to the quality of craft.
          </p>
          <Link href="#" className="text-white hover:text-muted-foreground transition-colors inline-flex items-center gap-2">
            Make the switch <MoveRight className="h-4 w-4" />
          </Link>
        </div>
      </div>

      {/* Bottom section - Three feature cards in a row */}
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <div
              key={feature.id}
              onClick={() => setSelectedFeature(feature)}
              className={`group relative bg-[#1a1a1a] rounded-3xl overflow-hidden hover:bg-[#222222] transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-black/50 cursor-pointer ${
                index === 2 ? 'md:col-span-2 lg:col-span-1' : ''
              }`}
            >
              <div className="relative aspect-square w-full overflow-hidden">
                <Image
                  src={feature.image}
                  alt={feature.alt}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-medium text-white mb-2">
                  {feature.title}
                </h3>
              </div>
              <div className="absolute bottom-5 right-5 w-12 h-12 border border-white/20 rounded-full flex items-center justify-center text-white/70 group-hover:bg-white/10 group-hover:text-white transition-all duration-300">
                <span className="text-2xl leading-none">+</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>

    {/* Modal */}
    {selectedFeature && (
      <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-[#1a1a1a] rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
          {/* Modal Header */}
          <div className="relative">
            <button
              onClick={() => setSelectedFeature(null)}
              className="absolute top-6 right-6 z-10 w-10 h-10 bg-black/20 hover:bg-black/40 rounded-full flex items-center justify-center text-white/70 hover:text-white transition-all duration-200"
            >
              <X size={20} />
            </button>

            {/* Feature Image */}
            <div className="relative h-80 w-full overflow-hidden">
              <Image
                src={selectedFeature.image}
                alt={selectedFeature.alt}
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-[#1a1a1a] via-transparent to-transparent" />
            </div>
          </div>

          {/* Modal Content */}
          <div className="p-8 max-h-[50vh] overflow-y-auto">
            <h2 className="text-3xl font-bold text-white mb-4">
              {selectedFeature.title}
            </h2>

            <p className="text-gray-300 text-lg mb-8 leading-relaxed">
              {selectedFeature.description}
            </p>

            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-white mb-4">Key Features:</h3>
              <ul className="space-y-3">
                {selectedFeature.details.map((detail, index) => (
                  <li key={index} className="flex items-start gap-3 text-gray-300">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                    <span className="leading-relaxed">{detail}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="mt-8 pt-6 border-t border-white/10">
              <p className="text-gray-400 text-sm">
                Experience the power of AI-driven development with Cubent's advanced features designed to accelerate your workflow and improve code quality.
              </p>
            </div>
          </div>
        </div>
      </div>
    )}
  </div>
  );
};
