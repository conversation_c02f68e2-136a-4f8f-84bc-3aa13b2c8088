'use client';

import Image from 'next/image';
import Link from 'next/link';
import { MoveRight } from 'lucide-react';

export const Mockup = () => (
  <div className="w-full">
    <div className="container mx-auto px-2 sm:px-4 lg:px-6">
      <div className="flex flex-col items-center justify-center gap-2 py-4">
        <div className="relative w-full max-w-7xl">
          <div className="relative overflow-hidden">
            <Image
              src="/images/Cubent.Dev.gif"
              alt="Cubent Editor Interface - Code editing with AI assistance"
              width={1200}
              height={800}
              className="w-full h-auto object-cover rounded-lg"
              priority
              unoptimized
            />
            {/* Soft glow effect */}
            <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 rounded-2xl blur-xl opacity-30 -z-10" />
          </div>
        </div>
      </div>
    </div>

    {/* Made for modern product teams section */}
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
      {/* Top section - Title on left, description on right */}
      <div className="flex flex-col lg:flex-row gap-8 lg:gap-16 items-start mb-16 lg:mb-20 max-w-5xl mx-auto">
        {/* Left side - Title */}
        <div className="flex-1 max-w-md">
          <h2 className="text-4xl lg:text-5xl font-regular tracking-tighter text-white">
            Made for modern product teams
          </h2>
        </div>

        {/* Right side - Description and link */}
        <div className="flex-1 max-w-lg">
          <p className="text-lg text-muted-foreground leading-relaxed mb-4">
            Cubent is shaped by the practices and principles that distinguish world-class product teams from the rest: relentless focus, fast execution, and a commitment to the quality of craft.
          </p>
          <Link href="#" className="text-white hover:text-muted-foreground transition-colors inline-flex items-center gap-2">
            Make the switch <MoveRight className="h-4 w-4" />
          </Link>
        </div>
      </div>

      {/* Bottom section - Three feature cards in a row */}
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Card 1: Context Intelligence */}
          <div className="group relative bg-[#1a1a1a] rounded-3xl overflow-hidden hover:bg-[#222222] transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-black/50 cursor-pointer">
            <div className="relative aspect-square w-full overflow-hidden">
              <Image
                src="/images/cubent-feature-1.png"
                alt="Context Intelligence"
                fill
                className="object-cover"
              />
            </div>
            <div className="p-6">
              <h3 className="text-xl font-medium text-white mb-2">
                Purpose-built for product development
              </h3>
            </div>
            <div className="absolute bottom-5 right-5 w-12 h-12 border border-white/20 rounded-full flex items-center justify-center text-white/70 group-hover:bg-white/10 group-hover:text-white transition-all duration-300">
              <span className="text-2xl leading-none">+</span>
            </div>
          </div>

          {/* Card 2: AI Screenshot Analysis */}
          <div className="group relative bg-[#1a1a1a] rounded-3xl overflow-hidden hover:bg-[#222222] transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-black/50 cursor-pointer">
            <div className="relative aspect-square w-full overflow-hidden">
              <Image
                src="/images/cubent-feature-2.png"
                alt="AI Screenshot Analysis"
                fill
                className="object-cover"
              />
            </div>
            <div className="p-6">
              <h3 className="text-xl font-medium text-white mb-2">
                Designed to move fast
              </h3>
            </div>
            <div className="absolute bottom-5 right-5 w-12 h-12 border border-white/20 rounded-full flex items-center justify-center text-white/70 group-hover:bg-white/10 group-hover:text-white transition-all duration-300">
              <span className="text-2xl leading-none">+</span>
            </div>
          </div>

          {/* Card 3: Smart Code Editing */}
          <div className="group relative bg-[#1a1a1a] rounded-3xl overflow-hidden hover:bg-[#222222] transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-black/50 cursor-pointer md:col-span-2 lg:col-span-1">
            <div className="relative aspect-square w-full overflow-hidden">
              <Image
                src="/images/cubent-feature-3.png"
                alt="Smart Code Editing"
                fill
                className="object-cover"
              />
            </div>
            <div className="p-6">
              <h3 className="text-xl font-medium text-white mb-2">
                Crafted to perfection
              </h3>
            </div>
            <div className="absolute bottom-5 right-5 w-12 h-12 border border-white/20 rounded-full flex items-center justify-center text-white/70 group-hover:bg-white/10 group-hover:text-white transition-all duration-300">
              <span className="text-2xl leading-none">+</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);
